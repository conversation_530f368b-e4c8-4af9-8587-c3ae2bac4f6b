#!/usr/bin/env python3
"""
Test the Onnyx Event Logging and Analytics

This script tests the Onnyx Event Logging and Analytics by simulating blocks and transactions.
"""

import os
import sys
import time
import json
import random
from typing import Dict, Any, List

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analytics.event_logger import EventLogger
from identity.registry import IdentityRegistry

def create_test_block(index: int, proposer: str) -> Dict[str, Any]:
    """
    Create a test block.
    
    Args:
        index: The block index
        proposer: The proposer ID
    
    Returns:
        The test block
    """
    return {
        "index": index,
        "hash": f"0x{random.randint(0, 0xffffffff):08x}",
        "timestamp": int(time.time()),
        "previous_hash": f"0x{random.randint(0, 0xffffffff):08x}",
        "nonce": random.randint(0, 1000),
        "signed_by": proposer,
        "signature": f"0x{random.randint(0, 0xffffffff):08x}",
        "transactions": []
    }

def create_test_transaction(op: str, from_id: str, to_id: str = None) -> Dict[str, Any]:
    """
    Create a test transaction.
    
    Args:
        op: The operation
        from_id: The sender ID
        to_id: The recipient ID (optional)
    
    Returns:
        The test transaction
    """
    tx = {
        "txid": f"tx_{random.randint(0, 0xffffffff):08x}",
        "op": op,
        "from": from_id,
        "timestamp": int(time.time())
    }
    
    if to_id:
        tx["to"] = to_id
    
    if op == "OP_MINT":
        tx["token_id"] = f"{from_id.upper()}_TOKEN"
        tx["amount"] = random.randint(10, 100)
        tx["to"] = to_id or from_id
    
    elif op == "OP_SEND":
        tx["token_id"] = f"{from_id.upper()}_TOKEN"
        tx["amount"] = random.randint(1, 50)
        tx["to"] = to_id
    
    elif op == "OP_BURN":
        tx["token_id"] = f"{from_id.upper()}_TOKEN"
        tx["amount"] = random.randint(1, 20)
    
    elif op == "OP_SCROLL":
        tx["data"] = {
            "title": f"Proposal by {from_id}",
            "category": random.choice(["economic", "protocol", "community", "membership", "other"]),
            "description": f"This is a test proposal by {from_id}"
        }
    
    elif op == "OP_VOTE":
        tx["data"] = {
            "scroll_id": f"scroll_{random.randint(0, 0xffffffff):08x}",
            "vote": random.choice(["yes", "no", "abstain"])
        }
    
    elif op == "OP_IDENTITY":
        tx["data"] = {
            "identity_id": from_id,
            "name": f"{from_id.capitalize()}"
        }
    
    elif op == "OP_GRANT_REPUTATION":
        tx["amount"] = random.randint(1, 10)
        tx["to"] = to_id
    
    elif op == "OP_STAKE":
        tx["token_id"] = "ONX"
        tx["amount"] = random.randint(100, 1000)
    
    elif op == "OP_REWARD":
        tx["token_id"] = "ONX"
        tx["amount"] = 10
        tx["to"] = from_id
    
    return tx

def setup_test_data():
    """Set up test data for the event logging test."""
    # Create identity registry
    identity_registry = IdentityRegistry()
    
    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "Alice", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: Alice")
    
    try:
        bob = identity_registry.register_identity("bob", "Bob", "0x987654321fedcba")
        print(f"Created identity: Bob")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: Bob")
    
    try:
        charlie = identity_registry.register_identity("charlie", "Charlie", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")
    
    return identity_registry

def test_event_logging():
    """Test the event logging."""
    print("\nTesting Event Logging...")
    
    # Set up test data
    identity_registry = setup_test_data()
    
    # Create event logger
    event_logger = EventLogger("data/test_event_log.json")
    
    # Create test blocks and transactions
    identities = ["alice", "bob", "charlie"]
    
    for i in range(1, 11):
        # Select a random proposer
        proposer = random.choice(identities)
        
        # Create a block
        block = create_test_block(i, proposer)
        
        # Create transactions
        transactions = []
        
        # Add a reward transaction
        transactions.append(create_test_transaction("OP_REWARD", proposer))
        
        # Add random transactions
        for _ in range(random.randint(1, 10)):
            # Select a random sender
            sender = random.choice(identities)
            
            # Select a random recipient
            recipient = random.choice([id for id in identities if id != sender])
            
            # Select a random operation
            op = random.choice([
                "OP_MINT", "OP_SEND", "OP_BURN", "OP_SCROLL", "OP_VOTE",
                "OP_IDENTITY", "OP_GRANT_REPUTATION", "OP_STAKE"
            ])
            
            # Create a transaction
            tx = create_test_transaction(op, sender, recipient)
            
            # Add the transaction to the block
            transactions.append(tx)
        
        # Add the transactions to the block
        block["transactions"] = transactions
        
        # Log the block
        log_entry = event_logger.log_block(block, transactions, proposer)
        
        print(f"\nLogged block {i} with {len(transactions)} transactions")
        print(f"Proposer: {proposer}")
        print(f"Notable events: {len(log_entry['notable_events'])}")
    
    # Get logs
    logs = event_logger.get_logs()
    
    print(f"\nRetrieved {len(logs)} logs")
    
    # Generate summary
    summary = event_logger.generate_summary()
    
    print("\nSummary:")
    print(f"Blocks: {summary['blocks']}")
    print(f"Transactions: {summary['transactions']}")
    print(f"Token Mints: {summary['token_mints']}")
    print(f"Token Transfers: {summary['token_transfers']}")
    print(f"Token Burns: {summary['token_burns']}")
    print(f"Proposals: {summary['proposals']}")
    print(f"Votes: {summary['votes']}")
    print(f"Identities: {summary['identities']}")
    print(f"Reputation Grants: {summary['reputation_grants']}")
    print(f"Stakes: {summary['stakes']}")
    print(f"Rewards: {summary['rewards']}")
    print(f"Unique Proposers: {summary['unique_proposer_count']}")
    
    print("\nEvent Logging test completed successfully!")

if __name__ == "__main__":
    test_event_logging()
