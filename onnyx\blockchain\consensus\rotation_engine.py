"""
Onnyx Validator Rotation Engine

This module provides the ValidatorRotationEngine class for managing validator rotation.
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional

from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry
from identity.trust.etzem_engine import EtzemEngine
from shared.config.chain_parameters import chain_parameters

# Set up logging
logger = logging.getLogger("onnyx.consensus.rotation_engine")

class ValidatorRotationEngine:
    """
    ValidatorRotationEngine manages the rotation of validators.

    It determines which <PERSON><PERSON> is allowed to propose the next block.
    """

    def __init__(self, path: str = "data/rotation.json",
                 identity_registry_path: str = "data/identities.json",
                 sela_registry_path: str = "data/selas.json"):
        """
        Initialize the ValidatorRotationEngine.

        Args:
            path: Path to the rotation state JSON file
            identity_registry_path: Path to the identity registry JSON file
            sela_registry_path: Path to the Sela registry JSON file
        """
        self.path = path
        self.identity_registry = IdentityRegistry(identity_registry_path)
        self.sela_registry = SelaRegistry(sela_registry_path)
        self.etzem_engine = EtzemEngine()

        # Default state
        self.state = {
            "queue": [],
            "last_proposer": None,
            "height": 0,
            "last_update": 0,
            "update_interval": chain_parameters.get("validator_rotation_interval"),
            "min_etzem_score": chain_parameters.get("min_etzem_score"),
            "required_badges": chain_parameters.get("validator_badges")
        }

        # Load state
        self._load()

    def _load(self) -> Dict[str, Any]:
        """
        Load the rotation state from the file.

        Returns:
            The rotation state
        """
        try:
            if os.path.exists(self.path) and os.path.getsize(self.path) > 0:
                with open(self.path, "r") as f:
                    loaded_state = json.load(f)
                    # Update the state with the loaded values
                    self.state.update(loaded_state)
                    logger.info(f"Loaded rotation state from {self.path}")
            else:
                # Save the default state
                self._save()
                logger.info(f"Created default rotation state at {self.path}")
        except Exception as e:
            logger.error(f"Error loading rotation state: {str(e)}")

        return self.state

    def _save(self):
        """Save the rotation state to the file."""
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.path), exist_ok=True)

            with open(self.path, "w") as f:
                json.dump(self.state, f, indent=2)
                logger.info(f"Saved rotation state to {self.path}")
        except Exception as e:
            logger.error(f"Error saving rotation state: {str(e)}")

    def update_queue(self, selas: Optional[List[Dict[str, Any]]] = None):
        """
        Update the queue of eligible validators.

        Args:
            selas: List of Selas to consider for the queue. If None, all Selas will be considered.
        """
        current_time = int(time.time())

        # Check if it's time to update the queue
        if current_time - self.state["last_update"] < self.state["update_interval"]:
            logger.info("Queue update skipped: not time yet")
            return

        # Get all Selas if not provided
        if selas is None:
            selas = list(self.sela_registry.get_all_selas().values())

        # Filter eligible Selas
        eligible_selas = []
        for sela in selas:
            sela_id = sela.get("sela_id")
            founder_id = sela.get("founder")

            if not founder_id:
                continue

            # Get the founder's identity
            identity = self.identity_registry.get_identity(founder_id)
            if not identity:
                continue

            # Check Etzem score
            etzem_data = self.etzem_engine.compute_etzem(founder_id)
            etzem_score = etzem_data.get("etzem", 0)

            if etzem_score < self.state["min_etzem_score"]:
                logger.debug(f"Sela {sela_id} ineligible: Etzem score {etzem_score} < {self.state['min_etzem_score']}")
                continue

            # Check required badges
            badges = identity.get("badges", [])
            has_required_badges = all(badge in badges for badge in self.state["required_badges"])

            if not has_required_badges:
                logger.debug(f"Sela {sela_id} ineligible: missing required badges")
                continue

            # Add to eligible Selas
            eligible_selas.append({
                "sela_id": sela_id,
                "founder_id": founder_id,
                "etzem_score": etzem_score
            })

        # Sort by Sela ID for deterministic ordering
        eligible_selas.sort(key=lambda s: s["sela_id"])

        # Update the queue
        self.state["queue"] = [s["sela_id"] for s in eligible_selas]
        self.state["last_update"] = current_time
        self._save()

        logger.info(f"Updated validator queue: {len(self.state['queue'])} eligible Selas")
        return self.state["queue"]

    def get_next_validator(self, current_height: int) -> str:
        """
        Get the next validator for a given block height.

        Args:
            current_height: The current block height

        Returns:
            The Sela ID of the next validator

        Raises:
            Exception: If there are no eligible validators
        """
        # Update the queue if needed
        if not self.state["queue"]:
            self.update_queue()

        queue = self.state["queue"]
        if not queue:
            logger.error("No eligible validators in the queue")
            raise Exception("No eligible validators")

        # Calculate the index in the queue based on the block height
        idx = current_height % len(queue)
        next_validator = queue[idx]

        logger.info(f"Next validator for height {current_height}: {next_validator}")
        return next_validator

    def mark_proposed(self, sela_id: str, height: int):
        """
        Mark a block as proposed by a validator.

        Args:
            sela_id: The Sela ID of the proposer
            height: The block height
        """
        self.state["last_proposer"] = sela_id
        self.state["height"] = height
        self._save()

        logger.info(f"Marked block {height} as proposed by {sela_id}")

    def is_valid_proposer(self, sela_id: str, height: int) -> bool:
        """
        Check if a Sela is the valid proposer for a given block height.

        Args:
            sela_id: The Sela ID to check
            height: The block height

        Returns:
            True if the Sela is the valid proposer, False otherwise
        """
        try:
            next_validator = self.get_next_validator(height)
            return sela_id == next_validator
        except Exception:
            # If there's an error, default to allowing the proposal
            logger.warning(f"Error checking if {sela_id} is valid proposer for height {height}")
            return True

    def get_rotation_status(self) -> Dict[str, Any]:
        """
        Get the current rotation status.

        Returns:
            The rotation status
        """
        return {
            "queue": self.state["queue"],
            "last_proposer": self.state["last_proposer"],
            "height": self.state["height"],
            "last_update": self.state["last_update"],
            "update_interval": self.state["update_interval"],
            "min_etzem_score": self.state["min_etzem_score"],
            "required_badges": self.state["required_badges"]
        }

    def set_update_interval(self, interval: int):
        """
        Set the queue update interval.

        Args:
            interval: The update interval in seconds
        """
        self.state["update_interval"] = interval
        self._save()

        logger.info(f"Set queue update interval to {interval} seconds")

    def set_min_etzem_score(self, score: int):
        """
        Set the minimum Etzem score required for eligibility.

        Args:
            score: The minimum Etzem score
        """
        self.state["min_etzem_score"] = score
        self._save()

        logger.info(f"Set minimum Etzem score to {score}")

    def set_required_badges(self, badges: List[str]):
        """
        Set the required badges for eligibility.

        Args:
            badges: The required badges
        """
        self.state["required_badges"] = badges
        self._save()

        logger.info(f"Set required badges to {badges}")

# Create a global instance of the ValidatorRotationEngine
rotation_engine = ValidatorRotationEngine()
