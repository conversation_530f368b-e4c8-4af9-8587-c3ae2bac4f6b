"""
Onnyx Miner Main Module

This module provides the main entry point for the Onnyx Miner.
"""

import os
import sys
import logging
from typing import Dict, Any, List, Optional

from .config import get_config
from .miner import start_auto_mining, stop_auto_mining
from .gui.panel import start_gui

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("onnyx_miner.log")
    ]
)
logger = logging.getLogger("onnyx_miner.main")

def main():
    """Main entry point for the Onnyx Miner."""
    try:
        # Get the configuration
        config = get_config()
        
        # Check if auto-mining is enabled
        auto_mine = config.get("node", {}).get("auto_mine", False)
        
        if auto_mine:
            # Start auto-mining
            start_auto_mining()
        
        # Check if the GUI is enabled
        gui_enabled = config.get("gui", {}).get("enabled", True)
        
        if gui_enabled:
            # Start the GUI
            start_gui()
        else:
            # Just keep the process running
            import time
            while True:
                time.sleep(1)
    except KeyboardInterrupt:
        # Stop auto-mining
        stop_auto_mining()
        
        logger.info("Stopped Onnyx Miner")
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
