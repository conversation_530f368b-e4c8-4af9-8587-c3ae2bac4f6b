#!/usr/bin/env python3

"""
Script to update the transactions table schema.
"""

import os
import sys
import logging
import sqlite3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.update_transactions_table")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from data.db import db

def update_transactions_table():
    """Update the transactions table schema."""
    try:
        # Get a database connection
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Check if the transactions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
        if cursor.fetchone():
            # Drop the existing table
            logger.info("Dropping existing transactions table")
            cursor.execute("DROP TABLE IF EXISTS transactions")
        
        # Create the new transactions table
        logger.info("Creating new transactions table")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS transactions (
            tx_id TEXT PRIMARY KEY,
            timestamp INTEGER NOT NULL,
            op TEXT NOT NULL,
            data TEXT,
            sender TEXT,
            signature TEXT,
            status TEXT DEFAULT 'pending',
            block_hash TEXT,
            created_at INTEGER
        )
        """)
        
        # Create indexes
        logger.info("Creating indexes for transactions table")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_op ON transactions(op)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_block_hash ON transactions(block_hash)")
        
        # Commit the changes
        conn.commit()
        
        logger.info("Transactions table updated successfully")
        return True
    except Exception as e:
        logger.error(f"Error updating transactions table: {str(e)}")
        return False
    finally:
        conn.close()

def main():
    """Main entry point."""
    logger.info("Updating transactions table...")
    
    success = update_transactions_table()
    
    if success:
        logger.info("Transactions table update completed successfully")
    else:
        logger.error("Transactions table update failed")

if __name__ == "__main__":
    main()
