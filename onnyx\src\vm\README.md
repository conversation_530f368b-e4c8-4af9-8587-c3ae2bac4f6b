# OnnyxScript Virtual Machine (VM)

This module implements the OnnyxScript VM, a custom stack-based interpreter for validating all token operations without EVM or Solidity.

## Supported Opcodes

| Opcode              | Description |
|---------------------|-------------|
| `OP_SPAWN_TOKEN`     | Create new token under identity |
| `OP_MINT_TOKEN`      | Mint more units of a mintable token |
| `OP_TRANSFER_TOKEN`  | Transfer balance from one address to another |
| `OP_GET_TOKEN_METADATA` | Push token metadata to VM stack |

## Design

- Stateless execution model
- Identity-bound permissions enforced (e.g., only creator can mint)
- Uses the `TokenRegistry` and `TokenLedger` under the hood

## Example Script

```python
[
  ("OP_SPAWN_TOKEN", ("bobbux01", "BOB_BUX", "BOB", "id_bob", "FORKED", 100000, {"description": "Bob's Coffee Token"}, True)),
  ("OP_TRANSFER_TOKEN", ("bobbux01", "id_bob", "id_alice", 1000)),
  ("OP_GET_TOKEN_METADATA", "bobbux01")
]
```
