# src/db/init.py

from src.db.schema.identity import create_identity_tables
from src.db.schema.token import create_token_tables
from src.db.schema.chain import create_chain_tables
from src.db.schema.sela import create_sela_tables
from src.db.schema.zeman import create_zeman_tables
from src.db.schema.etzem import create_etzem_tables
from src.db.schema.governance import create_governance_tables
from src.db.schema.harashim import create_harashim_tables
from src.db.schema.roles import create_roles_tables
from src.db.schema.conversion import create_conversion_tables

def initialize_database(force=False):
    """
    Initialize the database.

    Args:
        force (bool): If True, drop existing tables before creating new ones.
    """
    from src.db.manager import db_manager

    if force:
        print("Forcing reinitialization of the database...")
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        # Drop all tables
        for table in tables:
            if table[0] != 'sqlite_sequence':  # Skip SQLite internal tables
                cursor.execute(f"DROP TABLE IF EXISTS {table[0]}")

        conn.commit()

    # Create tables
    create_identity_tables()
    create_token_tables()
    create_chain_tables()
    create_sela_tables()
    create_zeman_tables()
    create_etzem_tables()
    create_governance_tables()
    create_harashim_tables()
    create_roles_tables()
    create_conversion_tables()

    print("Database initialized successfully.")
