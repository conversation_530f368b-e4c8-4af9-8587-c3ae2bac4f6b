{% extends "base.html" %}

{% block title %}Admin Tools - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Admin Tools</h2>
    </div>
    
    <div class="card" style="margin-bottom: 2rem;">
        <p>This page provides administrative tools for managing the Onnyx blockchain. These tools are for testing and development purposes only.</p>
    </div>
    
    <div class="admin-tabs">
        <div class="tab-header">
            <button class="tab-button active" data-tab="transactions">Transactions</button>
            <button class="tab-button" data-tab="tokens">Tokens</button>
            <button class="tab-button" data-tab="identities">Identities</button>
            <button class="tab-button" data-tab="governance">Governance</button>
            <button class="tab-button" data-tab="mining">Mining</button>
        </div>
        
        <div class="tab-content active" id="transactions-tab">
            <h3>Submit Test Transaction</h3>
            <form action="{{ url_for('admin_submit_transaction') }}" method="post" class="admin-form">
                <div class="form-group">
                    <label for="tx-type">Transaction Type</label>
                    <select id="tx-type" name="type" required>
                        <option value="transfer">Transfer</option>
                        <option value="mint">Mint</option>
                        <option value="burn">Burn</option>
                        <option value="spawn">Spawn Token</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="tx-from">From Identity</label>
                    <input type="text" id="tx-from" name="from" placeholder="Identity ID" required>
                </div>
                
                <div class="form-group tx-to-group">
                    <label for="tx-to">To Identity</label>
                    <input type="text" id="tx-to" name="to" placeholder="Identity ID">
                </div>
                
                <div class="form-group tx-token-group">
                    <label for="tx-token">Token ID</label>
                    <input type="text" id="tx-token" name="token" placeholder="Token ID">
                </div>
                
                <div class="form-group tx-amount-group">
                    <label for="tx-amount">Amount</label>
                    <input type="number" id="tx-amount" name="amount" placeholder="Amount" min="1">
                </div>
                
                <div class="form-group tx-name-group" style="display: none;">
                    <label for="tx-name">Token Name</label>
                    <input type="text" id="tx-name" name="name" placeholder="Token Name">
                </div>
                
                <div class="form-group tx-symbol-group" style="display: none;">
                    <label for="tx-symbol">Token Symbol</label>
                    <input type="text" id="tx-symbol" name="symbol" placeholder="Token Symbol">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Submit Transaction</button>
                </div>
            </form>
            
            {% if tx_result %}
            <div class="result-card {% if tx_result.success %}success{% else %}error{% endif %}">
                <h4>Transaction Result</h4>
                <p>{{ tx_result.message }}</p>
                {% if tx_result.txid %}
                <p>Transaction ID: <a href="{{ url_for('tx_detail', txid=tx_result.txid) }}">{{ tx_result.txid }}</a></p>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <div class="tab-content" id="tokens-tab">
            <h3>Create Test Token</h3>
            <form action="{{ url_for('admin_create_token') }}" method="post" class="admin-form">
                <div class="form-group">
                    <label for="token-name">Token Name</label>
                    <input type="text" id="token-name" name="name" placeholder="Token Name" required>
                </div>
                
                <div class="form-group">
                    <label for="token-symbol">Token Symbol</label>
                    <input type="text" id="token-symbol" name="symbol" placeholder="Token Symbol" required>
                </div>
                
                <div class="form-group">
                    <label for="token-creator">Creator Identity</label>
                    <input type="text" id="token-creator" name="creator" placeholder="Identity ID" required>
                </div>
                
                <div class="form-group">
                    <label for="token-type">Token Type</label>
                    <select id="token-type" name="type" required>
                        <option value="fungible">Fungible</option>
                        <option value="non-fungible">Non-Fungible</option>
                        <option value="soulbound">Soulbound</option>
                        <option value="governance">Governance</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="token-supply">Initial Supply</label>
                    <input type="number" id="token-supply" name="supply" placeholder="Initial Supply" min="1" required>
                </div>
                
                <div class="form-group">
                    <label for="token-max-supply">Max Supply</label>
                    <input type="number" id="token-max-supply" name="max_supply" placeholder="Max Supply" min="1">
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="token-mintable" name="mintable" checked>
                        <label for="token-mintable">Mintable</label>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="token-burnable" name="burnable" checked>
                        <label for="token-burnable">Burnable</label>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="token-transferable" name="transferable" checked>
                        <label for="token-transferable">Transferable</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Create Token</button>
                </div>
            </form>
            
            {% if token_result %}
            <div class="result-card {% if token_result.success %}success{% else %}error{% endif %}">
                <h4>Token Creation Result</h4>
                <p>{{ token_result.message }}</p>
                {% if token_result.token_id %}
                <p>Token ID: <a href="{{ url_for('token_detail', token_id=token_result.token_id) }}">{{ token_result.token_id }}</a></p>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <div class="tab-content" id="identities-tab">
            <h3>Create Test Identity</h3>
            <form action="{{ url_for('admin_create_identity') }}" method="post" class="admin-form">
                <div class="form-group">
                    <label for="identity-name">Identity Name</label>
                    <input type="text" id="identity-name" name="name" placeholder="Identity Name" required>
                </div>
                
                <div class="form-group">
                    <label for="identity-reputation">Initial Reputation</label>
                    <input type="number" id="identity-reputation" name="reputation" placeholder="Initial Reputation" min="0" max="100" value="10">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Create Identity</button>
                </div>
            </form>
            
            {% if identity_result %}
            <div class="result-card {% if identity_result.success %}success{% else %}error{% endif %}">
                <h4>Identity Creation Result</h4>
                <p>{{ identity_result.message }}</p>
                {% if identity_result.identity_id %}
                <p>Identity ID: <a href="{{ url_for('identity_detail', identity_id=identity_result.identity_id) }}">{{ identity_result.identity_id }}</a></p>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <div class="tab-content" id="governance-tab">
            <h3>Propose Voice Scroll</h3>
            <form action="{{ url_for('admin_propose_scroll') }}" method="post" class="admin-form">
                <div class="form-group">
                    <label for="scroll-title">Proposal Title</label>
                    <input type="text" id="scroll-title" name="title" placeholder="Proposal Title" required>
                </div>
                
                <div class="form-group">
                    <label for="scroll-description">Description</label>
                    <textarea id="scroll-description" name="description" placeholder="Proposal Description" rows="5" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="scroll-proposer">Proposer Identity</label>
                    <input type="text" id="scroll-proposer" name="proposer" placeholder="Identity ID" required>
                </div>
                
                <div class="form-group">
                    <label for="scroll-category">Category</label>
                    <select id="scroll-category" name="category" required>
                        <option value="parameter">Parameter Change</option>
                        <option value="feature">Feature Request</option>
                        <option value="council">Council Action</option>
                        <option value="economic">Economic Policy</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="scroll-expiry">Expiry (days)</label>
                    <input type="number" id="scroll-expiry" name="expiry_days" placeholder="Expiry in Days" min="1" max="30" value="7">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Propose Scroll</button>
                </div>
            </form>
            
            {% if scroll_result %}
            <div class="result-card {% if scroll_result.success %}success{% else %}error{% endif %}">
                <h4>Proposal Result</h4>
                <p>{{ scroll_result.message }}</p>
                {% if scroll_result.scroll_id %}
                <p>Scroll ID: {{ scroll_result.scroll_id }}</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <div class="tab-content" id="mining-tab">
            <h3>Mine New Block</h3>
            <form action="{{ url_for('admin_mine_block') }}" method="post" class="admin-form">
                <div class="form-group">
                    <label for="mine-txs">Number of Transactions to Include</label>
                    <input type="number" id="mine-txs" name="num_txs" placeholder="Number of Transactions" min="0" max="100" value="5">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn-primary">Mine Block</button>
                </div>
            </form>
            
            {% if mining_result %}
            <div class="result-card {% if mining_result.success %}success{% else %}error{% endif %}">
                <h4>Mining Result</h4>
                <p>{{ mining_result.message }}</p>
                {% if mining_result.block_index %}
                <p>Block Index: {{ mining_result.block_index }}</p>
                <p>Block Hash: {{ mining_result.block_hash }}</p>
                <p>Transactions: {{ mining_result.num_txs }}</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
        
        // Transaction form dynamic fields
        const txTypeSelect = document.getElementById('tx-type');
        const txToGroup = document.querySelector('.tx-to-group');
        const txTokenGroup = document.querySelector('.tx-token-group');
        const txAmountGroup = document.querySelector('.tx-amount-group');
        const txNameGroup = document.querySelector('.tx-name-group');
        const txSymbolGroup = document.querySelector('.tx-symbol-group');
        
        txTypeSelect.addEventListener('change', function() {
            const txType = txTypeSelect.value;
            
            // Reset all fields
            txToGroup.style.display = 'block';
            txTokenGroup.style.display = 'block';
            txAmountGroup.style.display = 'block';
            txNameGroup.style.display = 'none';
            txSymbolGroup.style.display = 'none';
            
            // Adjust fields based on transaction type
            if (txType === 'transfer') {
                // Show all relevant fields for transfer
                txToGroup.style.display = 'block';
                txTokenGroup.style.display = 'block';
                txAmountGroup.style.display = 'block';
            } else if (txType === 'mint' || txType === 'burn') {
                // Hide 'to' field for mint/burn
                txToGroup.style.display = 'none';
                txTokenGroup.style.display = 'block';
                txAmountGroup.style.display = 'block';
            } else if (txType === 'spawn') {
                // Show token creation fields for spawn
                txToGroup.style.display = 'none';
                txTokenGroup.style.display = 'none';
                txNameGroup.style.display = 'block';
                txSymbolGroup.style.display = 'block';
            }
        });
    });
</script>
{% endblock %}
