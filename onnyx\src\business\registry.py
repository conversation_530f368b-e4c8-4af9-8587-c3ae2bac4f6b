# src/business/registry.py

import time
import uuid
from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.identity.registry import IdentityRegistry
from src.tokens.ledger import TokenLedger
from src.business.stake import StakePolicy

class SelaRegistry:
    """
    Registry for managing Sela business entities.
    """

    def __init__(self,
                 identity_registry: Optional[IdentityRegistry] = None,
                 token_ledger: Optional[TokenLedger] = None,
                 stake_policy: Optional[StakePolicy] = None):
        """
        Initialize the SelaRegistry.

        Args:
            identity_registry: The identity registry
            token_ledger: The token ledger
            stake_policy: The stake policy
        """
        self.identities = identity_registry or IdentityRegistry()
        self.ledger = token_ledger or TokenLedger()
        self.stake_policy = stake_policy or StakePolicy()
        self.db = db_manager.get_connection()

    def register_sela(self,
                      name: str,
                      founder_id: str,
                      sela_type: str,
                      sector: str,
                      description: str = "",
                      metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Register a new Sela.

        Args:
            name: The Sela name
            founder_id: The founder's identity ID
            sela_type: The Sela type (e.g., "BUSINESS", "NONPROFIT", "COMMUNITY")
            sector: The Sela sector (e.g., "RETAIL", "TECHNOLOGY", "EDUCATION")
            description: The Sela description
            metadata: Additional metadata

        Returns:
            The newly created Sela

        Raises:
            Exception: If the founder does not exist or the stake is insufficient
        """
        # Check if the founder exists
        founder = self.identities.get_identity(founder_id)
        if not founder:
            raise Exception(f"Identity with ID '{founder_id}' not found")

        # Check if the name is already taken
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM selas WHERE name = ?",
            (name,)
        )
        if cursor.fetchone():
            raise Exception(f"Sela with name '{name}' already exists")

        # Calculate the required stake
        required_stake = self.stake_policy.calculate_stake(founder_id, sela_type, sector)

        # Check if the founder has enough ONX tokens
        onx_balance = self.ledger.get_balance(founder_id, "ONX")
        if onx_balance < required_stake:
            raise Exception(f"Insufficient ONX tokens: {onx_balance} available, {required_stake} required")

        # Generate a unique Sela ID
        sela_id = str(uuid.uuid4())

        # Create the Sela
        sela = {
            "sela_id": sela_id,
            "name": name,
            "founder_id": founder_id,
            "type": sela_type,
            "sector": sector,
            "description": description,
            "metadata": metadata or {},
            "members": [founder_id],
            "created_at": int(time.time()),
            "stake_amount": required_stake,
            "services": [],
            "status": "ACTIVE"
        }

        # Save to database
        cursor.execute(
            """
            INSERT INTO selas
            (sela_id, name, founder_id, type, sector, description, metadata, created_at, stake_amount, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                sela["sela_id"],
                sela["name"],
                sela["founder_id"],
                sela["type"],
                sela["sector"],
                sela["description"],
                str(sela["metadata"]),
                sela["created_at"],
                sela["stake_amount"],
                sela["status"]
            )
        )

        # Add the founder as a member
        cursor.execute(
            """
            INSERT INTO sela_members
            (sela_id, identity_id, role, joined_at)
            VALUES (?, ?, ?, ?)
            """,
            (
                sela["sela_id"],
                founder_id,
                "FOUNDER",
                sela["created_at"]
            )
        )

        # Stake the ONX tokens
        self.ledger.debit(founder_id, "ONX", required_stake)

        # Record the stake
        cursor.execute(
            """
            INSERT INTO sela_stakes
            (sela_id, identity_id, amount, staked_at)
            VALUES (?, ?, ?, ?)
            """,
            (
                sela["sela_id"],
                founder_id,
                required_stake,
                sela["created_at"]
            )
        )

        self.db.commit()

        # Add the SELA_FOUNDER badge to the founder
        try:
            self.identities.add_badge(founder_id, "SELA_FOUNDER")
        except Exception:
            # Badge already exists
            pass

        return sela

    def get_sela(self, sela_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a Sela by ID.

        Args:
            sela_id: The Sela ID

        Returns:
            The Sela or None if it does not exist
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM selas WHERE sela_id = ?",
            (sela_id,)
        )
        row = cursor.fetchone()

        if not row:
            return None

        # Get the members
        cursor.execute(
            "SELECT identity_id, role, joined_at FROM sela_members WHERE sela_id = ?",
            (sela_id,)
        )
        members = [{
            "identity_id": member["identity_id"],
            "role": member["role"],
            "joined_at": member["joined_at"]
        } for member in cursor.fetchall()]

        # Get the services
        cursor.execute(
            "SELECT service_id, name, description, created_at FROM sela_services WHERE sela_id = ?",
            (sela_id,)
        )
        services = [{
            "service_id": service["service_id"],
            "name": service["name"],
            "description": service["description"],
            "created_at": service["created_at"]
        } for service in cursor.fetchall()]

        return {
            "sela_id": row["sela_id"],
            "name": row["name"],
            "founder_id": row["founder_id"],
            "type": row["type"],
            "sector": row["sector"],
            "description": row["description"],
            "metadata": eval(row["metadata"]),
            "created_at": row["created_at"],
            "stake_amount": row["stake_amount"],
            "status": row["status"],
            "members": members,
            "services": services
        }

    def get_selas_by_founder(self, founder_id: str) -> List[Dict[str, Any]]:
        """
        Get all Selas founded by an identity.

        Args:
            founder_id: The founder's identity ID

        Returns:
            A list of Selas
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT sela_id FROM selas WHERE founder_id = ?",
            (founder_id,)
        )
        rows = cursor.fetchall()

        return [self.get_sela(row["sela_id"]) for row in rows]

    def get_selas_by_member(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get all Selas an identity is a member of.

        Args:
            identity_id: The identity ID

        Returns:
            A list of Selas
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT sela_id FROM sela_members WHERE identity_id = ?",
            (identity_id,)
        )
        rows = cursor.fetchall()

        return [self.get_sela(row["sela_id"]) for row in rows]

    def add_member(self, sela_id: str, identity_id: str, role: str = "MEMBER") -> Optional[Dict[str, Any]]:
        """
        Add a member to a Sela.

        Args:
            sela_id: The Sela ID
            identity_id: The identity ID
            role: The member's role

        Returns:
            The updated Sela or None if the Sela does not exist

        Raises:
            Exception: If the identity does not exist or is already a member
        """
        # Check if the Sela exists
        sela = self.get_sela(sela_id)
        if not sela:
            return None

        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the identity is already a member
        for member in sela["members"]:
            if member["identity_id"] == identity_id:
                raise Exception(f"Identity with ID '{identity_id}' is already a member of Sela with ID '{sela_id}'")

        # Add the member
        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT INTO sela_members
            (sela_id, identity_id, role, joined_at)
            VALUES (?, ?, ?, ?)
            """,
            (
                sela_id,
                identity_id,
                role,
                int(time.time())
            )
        )
        self.db.commit()

        # Add the SELA_MEMBER badge to the identity
        try:
            self.identities.add_badge(identity_id, "SELA_MEMBER")
        except Exception:
            # Badge already exists
            pass

        return self.get_sela(sela_id)

    def remove_member(self, sela_id: str, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Remove a member from a Sela.

        Args:
            sela_id: The Sela ID
            identity_id: The identity ID

        Returns:
            The updated Sela or None if the Sela does not exist

        Raises:
            Exception: If the identity is the founder or is not a member
        """
        # Check if the Sela exists
        sela = self.get_sela(sela_id)
        if not sela:
            return None

        # Check if the identity is the founder
        if sela["founder_id"] == identity_id:
            raise Exception(f"Cannot remove the founder from Sela with ID '{sela_id}'")

        # Check if the identity is a member
        is_member = False
        for member in sela["members"]:
            if member["identity_id"] == identity_id:
                is_member = True
                break

        if not is_member:
            raise Exception(f"Identity with ID '{identity_id}' is not a member of Sela with ID '{sela_id}'")

        # Remove the member
        cursor = self.db.cursor()
        cursor.execute(
            "DELETE FROM sela_members WHERE sela_id = ? AND identity_id = ?",
            (sela_id, identity_id)
        )
        self.db.commit()

        return self.get_sela(sela_id)

    def add_service(self, sela_id: str, name: str, description: str = "") -> Optional[Dict[str, Any]]:
        """
        Add a service to a Sela.

        Args:
            sela_id: The Sela ID
            name: The service name
            description: The service description

        Returns:
            The updated Sela or None if the Sela does not exist

        Raises:
            Exception: If a service with the same name already exists
        """
        # Check if the Sela exists
        sela = self.get_sela(sela_id)
        if not sela:
            return None

        # Check if a service with the same name already exists
        for service in sela["services"]:
            if service["name"] == name:
                raise Exception(f"Service with name '{name}' already exists in Sela with ID '{sela_id}'")

        # Generate a unique service ID
        service_id = str(uuid.uuid4())

        # Add the service
        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT INTO sela_services
            (service_id, sela_id, name, description, created_at)
            VALUES (?, ?, ?, ?, ?)
            """,
            (
                service_id,
                sela_id,
                name,
                description,
                int(time.time())
            )
        )
        self.db.commit()

        return self.get_sela(sela_id)
