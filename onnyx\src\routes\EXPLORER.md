# Onnyx Explorer API

This document describes the Explorer API endpoints for the Onnyx blockchain.

## Transaction Endpoints

### GET /token/transactions

Get a list of transactions, optionally filtered by token ID, identity ID, or transaction ID.

**Query Parameters**:
- `token_id`: (optional) Filter transactions by token ID
- `identity_id`: (optional) Filter transactions by identity ID
- `txid`: (optional) Get a specific transaction by ID
- `limit`: (optional) Maximum number of transactions to return
- `offset`: (optional) Number of transactions to skip

**Response**:
```json
{
  "transactions": [
    {
      "txid": "tx_1234567890abcdef_1631234567",
      "timestamp": 1631234567,
      "type": "forktoken",
      "payload": {
        "token_id": "token_id",
        "name": "Token Name",
        "symbol": "TKN",
        "creator_identity": "identity_id",
        "token_type": "FORKED",
        "supply": 1000,
        "metadata": {
          "description": "Token description"
        },
        "mintable": true,
        "transferable": true
      }
    },
    {
      "txid": "tx_abcdef1234567890_1631234568",
      "timestamp": 1631234568,
      "type": "minttoken",
      "payload": {
        "token_id": "token_id",
        "amount": 500,
        "to_address": "identity_id",
        "caller_identity": "identity_id"
      }
    }
  ]
}
```

### GET /identity/transactions

Get transactions for a specific identity.

**Query Parameters**:
- `identity_id`: The identity ID
- `limit`: (optional) Maximum number of transactions to return
- `offset`: (optional) Number of transactions to skip

**Response**:
```json
{
  "identity_id": "identity_id",
  "transactions": [
    {
      "txid": "tx_1234567890abcdef_1631234567",
      "timestamp": 1631234567,
      "type": "createidentity",
      "payload": {
        "identity_id": "identity_id",
        "name": "Identity Name",
        "public_key": "public_key",
        "metadata": {
          "type": "individual",
          "description": "Identity description"
        }
      }
    },
    {
      "txid": "tx_abcdef1234567890_1631234568",
      "timestamp": 1631234568,
      "type": "forktoken",
      "payload": {
        "token_id": "token_id",
        "name": "Token Name",
        "symbol": "TKN",
        "creator_identity": "identity_id",
        "token_type": "FORKED",
        "supply": 1000,
        "metadata": {
          "description": "Token description"
        },
        "mintable": true,
        "transferable": true
      }
    }
  ]
}
```

## Explorer Endpoints

### GET /explorer/stats

Get overall statistics for the Onnyx blockchain.

**Response**:
```json
{
  "blockchain": {
    "height": 100,
    "latest_block_hash": "a70f8e8b0d0208c70c30d0b830300f7482a7d70aa146da8baf6f062a4b51adccb",
    "latest_block_timestamp": 1631234567
  },
  "tokens": {
    "count": 50,
    "total_supply": 1000000,
    "holder_count": 100
  },
  "identities": {
    "count": 200
  },
  "transactions": {
    "count": 500
  }
}
```

### GET /explorer/search

Search for tokens, identities, or transactions by ID or name.

**Query Parameters**:
- `query`: The search query

**Response**:
```json
{
  "tokens": [
    {
      "token_id": "token_id",
      "name": "Token Name",
      "symbol": "TKN",
      "creator_identity": "identity_id",
      "type": "FORKED",
      "supply": 1000
    }
  ],
  "identities": [
    {
      "identity_id": "identity_id",
      "name": "Identity Name",
      "public_key": "public_key"
    }
  ],
  "transactions": [
    {
      "txid": "tx_1234567890abcdef_1631234567",
      "timestamp": 1631234567,
      "type": "forktoken",
      "payload": {
        "token_id": "token_id",
        "name": "Token Name",
        "symbol": "TKN",
        "creator_identity": "identity_id"
      }
    }
  ]
}
```

### GET /explorer/recent

Get recent activity on the Onnyx blockchain.

**Response**:
```json
{
  "transactions": [
    {
      "txid": "tx_1234567890abcdef_1631234567",
      "timestamp": 1631234567,
      "type": "forktoken",
      "payload": {
        "token_id": "token_id",
        "name": "Token Name",
        "symbol": "TKN",
        "creator_identity": "identity_id"
      }
    }
  ],
  "tokens": [
    {
      "token_id": "token_id",
      "name": "Token Name",
      "symbol": "TKN",
      "creator_identity": "identity_id",
      "type": "FORKED",
      "supply": 1000
    }
  ],
  "identities": [
    {
      "identity_id": "identity_id",
      "name": "Identity Name",
      "public_key": "public_key"
    }
  ]
}
```
