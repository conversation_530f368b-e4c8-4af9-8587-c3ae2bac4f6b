#!/usr/bin/env python3
"""
ONNYX Phase 1 Production Launch - System Reset
Clears all test data and prepares for official Genesis Identity creation.
"""

import sqlite3
import os
import time
from datetime import datetime

def reset_database():
    """Reset database for production launch."""
    print("🔄 Resetting ONNYX Database for Production Launch")
    print("=" * 60)

    db_path = "data/onnyx.db"

    # Backup existing database if it exists
    if os.path.exists(db_path):
        backup_path = f"data/onnyx_backup_{int(time.time())}.db"
        os.rename(db_path, backup_path)
        print(f"✅ Existing database backed up to: {backup_path}")

    # Create fresh database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create identities table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL
        )
    """)

    # Create selas table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS selas (
            sela_id TEXT PRIMARY KEY,
            identity_id TEXT NOT NULL,
            name TEXT UNIQUE NOT NULL,
            category TEXT NOT NULL,
            description TEXT,
            address TEXT,
            services TEXT,
            status TEXT DEFAULT 'active',
            trust_score REAL DEFAULT 0.0,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id)
        )
    """)

    # Create blocks table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS blocks (
            block_hash TEXT PRIMARY KEY,
            block_number INTEGER UNIQUE NOT NULL,
            previous_hash TEXT NOT NULL,
            data TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            created_at INTEGER NOT NULL
        )
    """)

    # Create mining table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS mining (
            mining_id TEXT PRIMARY KEY,
            identity_id TEXT NOT NULL,
            sela_id TEXT,
            mining_tier TEXT DEFAULT 'Basic CPU',
            mining_power REAL DEFAULT 1.0,
            blocks_mined INTEGER DEFAULT 0,
            total_rewards REAL DEFAULT 0.0,
            last_mining_at INTEGER,
            created_at INTEGER NOT NULL,
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id),
            FOREIGN KEY (sela_id) REFERENCES selas (sela_id)
        )
    """)

    # Create transactions table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS transactions (
            transaction_id TEXT PRIMARY KEY,
            from_identity TEXT,
            to_identity TEXT,
            amount REAL NOT NULL,
            transaction_type TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            block_hash TEXT,
            created_at INTEGER NOT NULL,
            FOREIGN KEY (from_identity) REFERENCES identities (identity_id),
            FOREIGN KEY (to_identity) REFERENCES identities (identity_id),
            FOREIGN KEY (block_hash) REFERENCES blocks (block_hash)
        )
    """)

    conn.commit()
    conn.close()

    print("✅ Fresh database created with production schema")
    print("✅ All tables initialized for production use")
    print("✅ Ready for Genesis Identity creation")

    return True

def verify_mining_system():
    """Verify mining system is operational."""
    print("\n⛏️ Verifying Mining System")
    print("=" * 40)

    try:
        # Test mining system components
        from blockchain.consensus.miner import BlockMiner
        from blockchain.wallet.wallet import Wallet

        # Test wallet generation
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()
        print("✅ Cryptographic key generation working")

        # Test miner initialization
        miner = BlockMiner()
        print("✅ Mining system initialized")

        print("✅ Mining system operational and ready")
        return True

    except Exception as e:
        print(f"❌ Mining system error: {e}")
        return False

def prepare_production_environment():
    """Prepare production environment."""
    print("\n🔧 Preparing Production Environment")
    print("=" * 40)

    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    print("✅ Data directory ready")

    # Ensure logs directory exists
    os.makedirs("logs", exist_ok=True)
    print("✅ Logs directory ready")

    # Set production timestamp
    production_start = int(time.time())
    with open("data/production_launch.txt", "w") as f:
        f.write(f"ONNYX Phase 1 Production Launch\n")
        f.write(f"Launch Time: {datetime.now().isoformat()}\n")
        f.write(f"Timestamp: {production_start}\n")
        f.write(f"Platform Founder: Djuvane Martin\n")
        f.write(f"Status: OFFICIAL PRODUCTION LAUNCH\n")

    print("✅ Production environment configured")
    print(f"✅ Launch timestamp: {production_start}")

    return True

def main():
    """Execute complete system reset for production launch."""
    print("🌟 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("=" * 70)
    print("OFFICIAL SYSTEM RESET AND PREPARATION")
    print("Platform Founder: Djuvane Martin")
    print("Launch Date:", datetime.now().strftime("%B %d, %Y"))
    print("=" * 70)

    # Execute reset steps
    steps = [
        ("Database Reset", reset_database),
        ("Mining System Verification", verify_mining_system),
        ("Production Environment", prepare_production_environment),
    ]

    success_count = 0
    for step_name, step_function in steps:
        try:
            if step_function():
                success_count += 1
            else:
                print(f"❌ {step_name} failed")
        except Exception as e:
            print(f"❌ {step_name} error: {e}")

    print(f"\n📊 System Reset Summary")
    print("=" * 40)
    print(f"Steps Completed: {success_count}/{len(steps)}")

    if success_count == len(steps):
        print("\n🎉 SYSTEM RESET COMPLETE!")
        print("✨ ONNYX Production Environment Ready:")
        print("   • Fresh database with production schema")
        print("   • Mining system operational")
        print("   • Production environment configured")
        print("   • Ready for Genesis Identity creation")
        print("\n🚀 PROCEED TO GENESIS IDENTITY CREATION")
        print("   Platform Founder: Djuvane Martin")
        print("   Organization: ONNYX Foundation")
        print("   Next: Create official Genesis Identity")
    else:
        print(f"\n⚠️ System reset incomplete. Please address issues above.")

    return success_count == len(steps)

if __name__ == "__main__":
    main()
