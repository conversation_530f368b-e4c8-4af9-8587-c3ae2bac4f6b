# src/node/crypto.py

import os
import base64
import hashlib
import json
import logging
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from ecdsa import Signing<PERSON><PERSON>, Veri<PERSON><PERSON><PERSON>, SECP256k1
from ecdsa.keys import BadSignatureError

# Configure logging
logger = logging.getLogger("onnyx.node.crypto")

def generate_keys(path: Optional[str] = None) -> Tu<PERSON>[Signing<PERSON><PERSON>, Verifying<PERSON>ey]:
    """
    Generate a new ECDSA key pair.
    
    Args:
        path: Path to save the keys. If None, keys will not be saved.
        
    Returns:
        A tuple of (private_key, public_key).
    """
    # Generate a new key pair
    sk = SigningKey.generate(curve=SECP256k1)
    vk = sk.verifying_key
    
    # Save the keys if a path is provided
    if path:
        os.makedirs(path, exist_ok=True)
        
        # Save the private key
        with open(os.path.join(path, "private.pem"), "wb") as f:
            f.write(sk.to_pem())
        
        # Save the public key
        with open(os.path.join(path, "public.pem"), "wb") as f:
            f.write(vk.to_pem())
        
        logger.info(f"Generated and saved keys to {path}")
    
    return sk, vk

def load_keys(path: str) -> Tuple[SigningKey, VerifyingKey]:
    """
    Load ECDSA keys from files.
    
    Args:
        path: Path to the directory containing the keys.
        
    Returns:
        A tuple of (private_key, public_key).
        
    Raises:
        FileNotFoundError: If the key files are not found.
        ValueError: If the key files are invalid.
    """
    try:
        # Load the private key
        with open(os.path.join(path, "private.pem"), "rb") as f:
            sk = SigningKey.from_pem(f.read())
        
        # Load the public key
        with open(os.path.join(path, "public.pem"), "rb") as f:
            vk = VerifyingKey.from_pem(f.read())
        
        logger.info(f"Loaded keys from {path}")
        
        return sk, vk
    except FileNotFoundError as e:
        logger.error(f"Key files not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error loading keys: {str(e)}")
        raise ValueError(f"Invalid key files: {str(e)}")

def sign_message(message: Dict[str, Any], private_key: SigningKey) -> str:
    """
    Sign a message with a private key.
    
    Args:
        message: The message to sign.
        private_key: The private key to sign with.
        
    Returns:
        The base64-encoded signature.
    """
    # Create a canonical representation of the message for signing
    message_json = json.dumps(message, sort_keys=True)
    
    # Calculate the SHA-256 hash of the message
    message_hash = hashlib.sha256(message_json.encode()).digest()
    
    # Sign the hash with the private key
    signature = private_key.sign(message_hash)
    
    # Encode the signature as base64
    signature_b64 = base64.b64encode(signature).decode()
    
    return signature_b64

def verify_signature(message: Dict[str, Any], signature: str, public_key: VerifyingKey) -> bool:
    """
    Verify a message signature with a public key.
    
    Args:
        message: The message to verify.
        signature: The base64-encoded signature.
        public_key: The public key to verify with.
        
    Returns:
        True if the signature is valid, False otherwise.
    """
    try:
        # Create a canonical representation of the message for verification
        message_json = json.dumps(message, sort_keys=True)
        
        # Calculate the SHA-256 hash of the message
        message_hash = hashlib.sha256(message_json.encode()).digest()
        
        # Decode the signature from base64
        signature_bytes = base64.b64decode(signature)
        
        # Verify the signature with the public key
        public_key.verify(signature_bytes, message_hash)
        
        return True
    except BadSignatureError:
        logger.warning("Invalid signature")
        return False
    except Exception as e:
        logger.error(f"Error verifying signature: {str(e)}")
        return False

def public_key_to_pem(public_key: VerifyingKey) -> str:
    """
    Convert a public key to PEM format.
    
    Args:
        public_key: The public key to convert.
        
    Returns:
        The public key in PEM format.
    """
    return public_key.to_pem().decode()

def public_key_from_pem(pem: str) -> VerifyingKey:
    """
    Convert a PEM string to a public key.
    
    Args:
        pem: The PEM string to convert.
        
    Returns:
        The public key.
        
    Raises:
        ValueError: If the PEM string is invalid.
    """
    try:
        return VerifyingKey.from_pem(pem.encode())
    except Exception as e:
        logger.error(f"Error converting PEM to public key: {str(e)}")
        raise ValueError(f"Invalid PEM string: {str(e)}")

def public_key_to_bytes(public_key: VerifyingKey) -> bytes:
    """
    Convert a public key to bytes.
    
    Args:
        public_key: The public key to convert.
        
    Returns:
        The public key as bytes.
    """
    return public_key.to_string()

def public_key_from_bytes(key_bytes: bytes) -> VerifyingKey:
    """
    Convert bytes to a public key.
    
    Args:
        key_bytes: The bytes to convert.
        
    Returns:
        The public key.
        
    Raises:
        ValueError: If the bytes are invalid.
    """
    try:
        return VerifyingKey.from_string(key_bytes, curve=SECP256k1)
    except Exception as e:
        logger.error(f"Error converting bytes to public key: {str(e)}")
        raise ValueError(f"Invalid public key bytes: {str(e)}")

def public_key_to_base64(public_key: VerifyingKey) -> str:
    """
    Convert a public key to base64.
    
    Args:
        public_key: The public key to convert.
        
    Returns:
        The base64-encoded public key.
    """
    return base64.b64encode(public_key_to_bytes(public_key)).decode()

def public_key_from_base64(base64_key: str) -> VerifyingKey:
    """
    Convert a base64 string to a public key.
    
    Args:
        base64_key: The base64 string to convert.
        
    Returns:
        The public key.
        
    Raises:
        ValueError: If the base64 string is invalid.
    """
    try:
        key_bytes = base64.b64decode(base64_key)
        return public_key_from_bytes(key_bytes)
    except Exception as e:
        logger.error(f"Error converting base64 to public key: {str(e)}")
        raise ValueError(f"Invalid base64 public key: {str(e)}")

class KeyManager:
    """
    Manages keys for the node.
    """
    
    def __init__(self, keys_dir: Optional[str] = None):
        """
        Initialize the key manager.
        
        Args:
            keys_dir: Directory to store keys. If None, a default directory will be used.
        """
        if keys_dir is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.keys_dir = os.path.join(data_dir, "keys")
        else:
            self.keys_dir = keys_dir
        
        # Create the keys directory if it doesn't exist
        os.makedirs(self.keys_dir, exist_ok=True)
        
        # Initialize keys
        self.private_key = None
        self.public_key = None
        
        # Initialize peer keys
        self.peer_keys = {}
        
        # Load or generate keys
        self._load_or_generate_keys()
    
    def _load_or_generate_keys(self):
        """Load or generate keys for the node."""
        try:
            # Try to load existing keys
            self.private_key, self.public_key = load_keys(self.keys_dir)
            logger.info("Loaded existing keys")
        except (FileNotFoundError, ValueError):
            # Generate new keys if loading fails
            logger.info("Generating new keys")
            self.private_key, self.public_key = generate_keys(self.keys_dir)
    
    def sign_message(self, message: Dict[str, Any]) -> str:
        """
        Sign a message with the node's private key.
        
        Args:
            message: The message to sign.
            
        Returns:
            The base64-encoded signature.
            
        Raises:
            ValueError: If the node has no private key.
        """
        if not self.private_key:
            raise ValueError("No private key available for signing")
        
        return sign_message(message, self.private_key)
    
    def verify_message(self, message: Dict[str, Any], signature: str, node_id: str) -> bool:
        """
        Verify a message signature with a peer's public key.
        
        Args:
            message: The message to verify.
            signature: The base64-encoded signature.
            node_id: The ID of the node that sent the message.
            
        Returns:
            True if the signature is valid, False otherwise.
            
        Raises:
            ValueError: If the node has no public key for the peer.
        """
        # Get the peer's public key
        public_key = self.get_peer_key(node_id)
        if not public_key:
            raise ValueError(f"No public key available for peer {node_id}")
        
        return verify_signature(message, signature, public_key)
    
    def add_peer_key(self, node_id: str, public_key: VerifyingKey):
        """
        Add a peer's public key.
        
        Args:
            node_id: The ID of the peer.
            public_key: The peer's public key.
        """
        self.peer_keys[node_id] = public_key
        
        # Save the peer's public key to a file
        peer_key_path = os.path.join(self.keys_dir, f"peer_{node_id}.pem")
        with open(peer_key_path, "wb") as f:
            f.write(public_key.to_pem())
        
        logger.info(f"Added public key for peer {node_id}")
    
    def get_peer_key(self, node_id: str) -> Optional[VerifyingKey]:
        """
        Get a peer's public key.
        
        Args:
            node_id: The ID of the peer.
            
        Returns:
            The peer's public key, or None if not found.
        """
        # Check if the key is already loaded
        if node_id in self.peer_keys:
            return self.peer_keys[node_id]
        
        # Try to load the key from a file
        peer_key_path = os.path.join(self.keys_dir, f"peer_{node_id}.pem")
        try:
            with open(peer_key_path, "rb") as f:
                public_key = VerifyingKey.from_pem(f.read())
            
            # Cache the key
            self.peer_keys[node_id] = public_key
            
            return public_key
        except (FileNotFoundError, ValueError):
            logger.warning(f"No public key found for peer {node_id}")
            return None
    
    def get_public_key_pem(self) -> str:
        """
        Get the node's public key in PEM format.
        
        Returns:
            The public key in PEM format.
            
        Raises:
            ValueError: If the node has no public key.
        """
        if not self.public_key:
            raise ValueError("No public key available")
        
        return public_key_to_pem(self.public_key)
    
    def get_public_key_base64(self) -> str:
        """
        Get the node's public key in base64 format.
        
        Returns:
            The base64-encoded public key.
            
        Raises:
            ValueError: If the node has no public key.
        """
        if not self.public_key:
            raise ValueError("No public key available")
        
        return public_key_to_base64(self.public_key)

# Create a global instance of the key manager
key_manager = KeyManager()
