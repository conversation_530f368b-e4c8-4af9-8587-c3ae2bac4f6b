"""
Onnyx Miner Wallet Module

This module provides functions for managing wallets and balances.
"""

import os
import json
import logging
import requests
from typing import Dict, Any, List, Optional

from .config import get_config
from .keys import load_identity_keys

# Set up logging
logger = logging.getLogger("onnyx_miner.wallet")

def load_wallet(identity_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Load wallet information for an identity.
    
    Args:
        identity_id: The identity ID (optional, uses the configured identity if not provided)
    
    Returns:
        Wallet information
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Use the configured identity if none is provided
        if identity_id is None:
            identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            raise ValueError("No identity ID provided or configured")
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the balances
        balances_url = f"{api_url}/api/balances/{identity_id}"
        balances_response = requests.get(balances_url)
        balances_response.raise_for_status()
        balances = balances_response.json().get("balances", {})
        
        # Get the identity
        identity_url = f"{api_url}/api/identity/{identity_id}"
        identity_response = requests.get(identity_url)
        identity_response.raise_for_status()
        identity = identity_response.json().get("identity", {})
        
        # Get the badges
        badges = identity.get("badges", [])
        
        # Get the stakes
        stakes_url = f"{api_url}/api/stakes/{identity_id}"
        stakes_response = requests.get(stakes_url)
        stakes_response.raise_for_status()
        stakes = stakes_response.json().get("stakes", [])
        
        # Create the wallet information
        wallet = {
            "identity_id": identity_id,
            "name": identity.get("name", identity_id),
            "balances": balances,
            "badges": badges,
            "stakes": stakes,
            "etzem_score": identity.get("etzem_score", 0)
        }
        
        return wallet
    except Exception as e:
        logger.error(f"Error loading wallet: {str(e)}")
        
        # Return a default wallet if there's an error
        return {
            "identity_id": identity_id,
            "name": identity_id,
            "balances": {},
            "badges": [],
            "stakes": [],
            "etzem_score": 0
        }

def get_balance(identity_id: str, token_id: str) -> float:
    """
    Get the balance of a token for an identity.
    
    Args:
        identity_id: The identity ID
        token_id: The token ID
    
    Returns:
        The balance
    """
    try:
        # Get the wallet
        wallet = load_wallet(identity_id)
        
        # Get the balance
        return wallet.get("balances", {}).get(token_id, 0)
    except Exception as e:
        logger.error(f"Error getting balance: {str(e)}")
        return 0

def transfer_tokens(from_id: str, to_id: str, token_id: str, amount: float) -> bool:
    """
    Transfer tokens from one identity to another.
    
    Args:
        from_id: The sender's identity ID
        to_id: The recipient's identity ID
        token_id: The token ID
        amount: The amount to transfer
    
    Returns:
        True if the transfer was successful, False otherwise
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Load the keys
        private_key_pem, _ = load_identity_keys(from_id)
        
        # Create the transaction
        tx = {
            "from": from_id,
            "to": to_id,
            "token_id": token_id,
            "amount": amount,
            "op": "OP_SEND"
        }
        
        # Send the transaction
        tx_url = f"{api_url}/api/transactions"
        tx_response = requests.post(tx_url, json=tx)
        tx_response.raise_for_status()
        
        return True
    except Exception as e:
        logger.error(f"Error transferring tokens: {str(e)}")
        return False

def stake_tokens(identity_id: str, token_id: str, amount: float, duration: int) -> bool:
    """
    Stake tokens.
    
    Args:
        identity_id: The identity ID
        token_id: The token ID
        amount: The amount to stake
        duration: The duration of the stake in seconds
    
    Returns:
        True if the stake was successful, False otherwise
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Load the keys
        private_key_pem, _ = load_identity_keys(identity_id)
        
        # Create the transaction
        tx = {
            "from": identity_id,
            "token_id": token_id,
            "amount": amount,
            "duration": duration,
            "op": "OP_STAKE"
        }
        
        # Send the transaction
        tx_url = f"{api_url}/api/transactions"
        tx_response = requests.post(tx_url, json=tx)
        tx_response.raise_for_status()
        
        return True
    except Exception as e:
        logger.error(f"Error staking tokens: {str(e)}")
        return False

def get_transaction_history(identity_id: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get transaction history for an identity.
    
    Args:
        identity_id: The identity ID
        limit: The maximum number of transactions to return
    
    Returns:
        A list of transactions
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the transactions
        tx_url = f"{api_url}/api/transactions?identity_id={identity_id}&limit={limit}"
        tx_response = requests.get(tx_url)
        tx_response.raise_for_status()
        
        return tx_response.json().get("transactions", [])
    except Exception as e:
        logger.error(f"Error getting transaction history: {str(e)}")
        return []
