"""
Onnyx Mikvah Engine Module

This module provides the MikvahEngine class for managing token minting.
"""

import os
import json
from typing import Dict, List, Any, Optional

from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry
from governance.etzem_engine import EtzemEngine
from yovel.limits import YovelLimiter

class MikvahEngine:
    """
    MikvahEngine manages token minting in the Onnyx ecosystem.
    
    The Mikvah Engine ensures that only eligible identities can mint tokens.
    """
    
    def __init__(self, identity_registry: IdentityRegistry, sela_registry: SelaRegistry, 
                 etzem_engine: EtzemEngine, yovel_limiter: YovelLimiter):
        """
        Initialize the MikvahEngine.
        
        Args:
            identity_registry: The identity registry
            sela_registry: The Sela registry
            etzem_engine: The Etzem engine
            yovel_limiter: The Yovel limiter
        """
        self.identity_registry = identity_registry
        self.sela_registry = sela_registry
        self.etzem_engine = etzem_engine
        self.yovel_limiter = yovel_limiter
    
    def check_mint_eligibility(self, identity_id: str, token_type: str) -> Dict[str, Any]:
        """
        Check if an identity is eligible to mint a token.
        
        Args:
            identity_id: The identity ID
            token_type: The type of token to mint (e.g., "LOYALTY", "ACCESS", "CURRENCY")
        
        Returns:
            A dictionary containing eligibility information
        
        Raises:
            Exception: If the identity does not exist
        """
        identity = self.identity_registry.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the identity is a member of a Sela
        is_sela_member = False
        if "founded_selas" in identity and identity["founded_selas"]:
            is_sela_member = True
        elif "joined_selas" in identity and identity["joined_selas"]:
            is_sela_member = True
        
        # Calculate the Etzem score
        etzem_data = self.etzem_engine.calculate_etzem_score(identity_id)
        etzem_score = etzem_data["final_etzem"]
        
        # Get the Yovel mint cap
        mint_cap = self.yovel_limiter.calculate_mint_cap(identity_id)
        
        # Check eligibility based on token type
        eligibility = {
            "is_eligible": False,
            "reason": "",
            "mint_cap": mint_cap,
            "etzem_score": etzem_score
        }
        
        if token_type == "LOYALTY":
            # Loyalty tokens require Sela membership
            if is_sela_member:
                eligibility["is_eligible"] = True
            else:
                eligibility["reason"] = "Identity is not a member of a Sela"
        
        elif token_type == "ACCESS":
            # Access tokens require Sela membership
            if is_sela_member:
                eligibility["is_eligible"] = True
            else:
                eligibility["reason"] = "Identity is not a member of a Sela"
        
        elif token_type == "CURRENCY":
            # Currency tokens require Sela membership and high Etzem score
            if not is_sela_member:
                eligibility["reason"] = "Identity is not a member of a Sela"
            elif etzem_score < 70:
                eligibility["reason"] = f"Etzem score ({etzem_score}) is below the threshold (70)"
            else:
                eligibility["is_eligible"] = True
        
        else:
            eligibility["reason"] = f"Unknown token type: {token_type}"
        
        return eligibility
    
    def mint_token(self, identity_id: str, token_id: str, token_type: str, 
                   supply: int, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mint a token.
        
        Args:
            identity_id: The identity ID of the minter
            token_id: The token ID
            token_type: The type of token (e.g., "LOYALTY", "ACCESS", "CURRENCY")
            supply: The initial supply
            metadata: Additional metadata for the token
        
        Returns:
            The minted token
        
        Raises:
            Exception: If the identity is not eligible to mint the token
        """
        # Check eligibility
        eligibility = self.check_mint_eligibility(identity_id, token_type)
        
        if not eligibility["is_eligible"]:
            raise Exception(f"Identity '{identity_id}' is not eligible to mint {token_type} tokens: {eligibility['reason']}")
        
        # Check if the supply is within the mint cap
        if supply > eligibility["mint_cap"]:
            raise Exception(f"Supply ({supply}) exceeds mint cap ({eligibility['mint_cap']})")
        
        # Create the token
        token = {
            "token_id": token_id,
            "type": token_type,
            "creator": identity_id,
            "supply": supply,
            "metadata": metadata
        }
        
        # Log the activity
        self.etzem_engine.log_activity(
            identity_id,
            "TOKEN_MINT",
            {
                "token_id": token_id,
                "token_type": token_type,
                "supply": supply
            }
        )
        
        return token
