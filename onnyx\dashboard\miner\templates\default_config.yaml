# Onnyx Miner Configuration

# Identity configuration
identity:
  id: ""  # Your identity ID
  name: ""  # Your identity name
  keys_dir: "keys"  # Directory to store keys

# Sela configuration
sela:
  id: ""  # Your Sela ID
  name: ""  # Your Sela name
  type: "BUSINESS"  # Sela type (BUSINESS, COMMUNITY, DAO)

# Node configuration
node:
  api_url: "http://localhost:8000"  # API URL
  p2p_port: 9000  # P2P port
  data_dir: "data"  # Data directory
  auto_mine: false  # Enable automatic mining
  mine_interval: 60  # Mining interval in seconds

# GUI configuration
gui:
  enabled: true  # Enable GUI
  port: 5005  # GUI port
  host: "127.0.0.1"  # GUI host

# Logging configuration
logging:
  level: "INFO"  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  file: "onnyx_miner.log"  # Log file
