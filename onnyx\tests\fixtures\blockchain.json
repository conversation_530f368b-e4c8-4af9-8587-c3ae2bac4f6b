{"blocks": [{"hash": "0000000000000000000000000000000000000000000000000000000000000000", "height": 0, "previous_hash": "0000000000000000000000000000000000000000000000000000000000000000", "timestamp": 1620000000, "difficulty": 1, "nonce": 0, "miner": "genesis", "transactions": ["genesis_tx"], "merkle_root": "0000000000000000000000000000000000000000000000000000000000000000", "size": 0, "version": "1.0"}, {"hash": "0000000000000000000000000000000000000000000000000000000000000001", "height": 1, "previous_hash": "0000000000000000000000000000000000000000000000000000000000000000", "timestamp": 1620000060, "difficulty": 1, "nonce": 123, "miner": "test_sela", "transactions": ["tx_1", "tx_2"], "merkle_root": "0000000000000000000000000000000000000000000000000000000000000001", "size": 256, "version": "1.0"}], "transactions": {"genesis_tx": {"tx_id": "genesis_tx", "block_hash": "0000000000000000000000000000000000000000000000000000000000000000", "timestamp": 1620000000, "op": "OP_GENESIS", "data": {"tokens": ["TEST_ONX"], "identities": ["test_identity", "test_identity_2", "test_sela"]}, "sender": "genesis", "signature": "", "status": "confirmed", "created_at": 1620000000}, "tx_1": {"tx_id": "tx_1", "block_hash": "0000000000000000000000000000000000000000000000000000000000000001", "timestamp": 1620000030, "op": "OP_MINT", "data": {"token_id": "TEST_ONX", "amount": 1000, "to_address": "test_identity"}, "sender": "test_identity", "signature": "signature_1", "status": "confirmed", "created_at": 1620000030}, "tx_2": {"tx_id": "tx_2", "block_hash": "0000000000000000000000000000000000000000000000000000000000000001", "timestamp": 1620000045, "op": "OP_SEND", "data": {"token_id": "TEST_ONX", "amount": 100, "from_address": "test_identity", "to_address": "test_identity_2"}, "sender": "test_identity", "signature": "signature_2", "status": "confirmed", "created_at": 1620000045}}}