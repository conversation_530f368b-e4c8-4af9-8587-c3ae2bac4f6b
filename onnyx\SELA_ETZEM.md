# Onnyx Sela and Etzem Systems

This document describes the Sela Registry, Identity Registry, Etzem Engine, Role Progression Logic, and Mikvah Engine systems in the Onnyx blockchain.

## Sela Registry

The Sela Registry is a system for managing business entities (Selas) in the Onnyx ecosystem. A Sela is a business entity that can offer services, have members, and mint tokens.

### Components

- **SelaRegistry**: Manages Selas in the Onnyx ecosystem
  - `register_sela`: Register a new Sela
  - `join_sela`: Add an identity to a Sela
  - `leave_sela`: Remove an identity from a Sela
  - `add_service`: Add a service to a Sela
  - `remove_service`: Remove a service from a Sela
  - `update_role`: Update the role of an identity in a Sela
  - `get_sela`: Get a Sela by ID
  - `get_selas_by_founder`: Get all Selas founded by an identity
  - `get_selas_by_member`: Get all Selas that an identity is a member of
  - `get_all_selas`: Get all Selas

### API Endpoints

- `POST /api/sela/register`: Register a new Sela
- `POST /api/sela/join`: Add an identity to a Sela
- `POST /api/sela/leave`: Remove an identity from a Sela
- `POST /api/sela/{sela_id}/service`: Add a service to a Sela
- `DELETE /api/sela/{sela_id}/service`: Remove a service from a Sela
- `GET /api/sela/{sela_id}`: Get a Sela by ID
- `GET /api/selas`: Get all Selas
- `GET /api/selas/founder/{founder_id}`: Get all Selas founded by an identity
- `GET /api/selas/member/{identity_id}`: Get all Selas that an identity is a member of

## Identity Registry

The Identity Registry is a system for managing identities in the Onnyx ecosystem. An identity is a unique, persistent entity that can participate in the Onnyx ecosystem.

### Components

- **IdentityRegistry**: Manages identities in the Onnyx ecosystem
  - `register_identity`: Register a new identity
  - `get_identity`: Get an identity by ID
  - `update_identity`: Update an identity
  - `add_badge`: Add a badge to an identity
  - `remove_badge`: Remove a badge from an identity
  - `update_reputation`: Update the reputation of an identity
  - `update_etzem_score`: Update the Etzem score of an identity
  - `link_sela`: Link an identity to a Sela
  - `unlink_sela`: Unlink an identity from a Sela
  - `get_all_identities`: Get all identities

### API Endpoints

- `POST /api/identity/register`: Register a new identity
- `GET /api/identity/{identity_id}`: Get an identity by ID
- `GET /api/identities`: Get all identities
- `POST /api/identity/{identity_id}/badge`: Add a badge to an identity
- `DELETE /api/identity/{identity_id}/badge`: Remove a badge from an identity
- `POST /api/identity/{identity_id}/reputation`: Update the reputation of an identity
- `GET /api/identity/{identity_id}/etzem`: Get the Etzem score of an identity
- `GET /api/identity/{identity_id}/roles`: Get the role eligibility of an identity
- `POST /api/identity/{identity_id}/roles`: Update the role badges of an identity
- `POST /api/roles/update-all`: Update the role badges of all identities

## Etzem Engine

The Etzem Engine is a system for calculating Etzem scores for identities. The Etzem score is a measure of an identity's trustworthiness and contribution to the Onnyx ecosystem.

### Components

- **EtzemEngine**: Calculates Etzem scores for identities
  - `log_activity`: Log an activity for an identity
  - `get_activities`: Get activities for an identity
  - `calculate_consistency_score`: Calculate the consistency score for an identity
  - `calculate_token_impact`: Calculate the token impact score for an identity
  - `calculate_reputation`: Calculate the reputation score for an identity
  - `calculate_labor_contribution`: Calculate the labor contribution score for an identity
  - `calculate_etzem_score`: Calculate the Etzem score for an identity

### Etzem Score Components

- **Consistency Score**: Measures how consistently an identity participates in the Onnyx ecosystem
- **Token Impact**: Measures the impact of an identity's token-related activities
- **Reputation**: Measures the reputation an identity has received from others
- **Labor Contribution**: Measures the amount of work an identity has contributed

## Role Progression Logic

The Role Progression Logic is a system for assigning roles to identities based on their Etzem scores and other criteria.

### Components

- **RoleProgression**: Manages role progression for identities
  - `check_role_eligibility`: Check role eligibility for an identity
  - `update_role_badges`: Update role badges for an identity
  - `update_all_role_badges`: Update role badges for all identities

### Role Thresholds

- **PROPOSAL_ELIGIBLE**: Etzem score >= 50
- **GUARDIAN_ELIGIBLE**: Etzem score >= 70
- **VALIDATOR_ELIGIBLE**: Etzem score >= 85 and has STAKER badge

## Mikvah Engine

The Mikvah Engine is a system for managing token minting in the Onnyx ecosystem. The Mikvah Engine ensures that only eligible identities can mint tokens.

### Components

- **MikvahEngine**: Manages token minting in the Onnyx ecosystem
  - `check_mint_eligibility`: Check if an identity is eligible to mint a token
  - `mint_token`: Mint a token

### Token Types

- **LOYALTY**: Requires Sela membership
- **ACCESS**: Requires Sela membership
- **CURRENCY**: Requires Sela membership and high Etzem score (>= 70)

## Yovel Limiter

The Yovel Limiter is a system for calculating token mint limits for identities. The Yovel Limiter ensures that identities can only mint a limited number of tokens.

### Components

- **YovelLimiter**: Calculates token mint limits for identities
  - `calculate_mint_cap`: Calculate the mint cap for an identity

### Mint Cap Calculation

The mint cap is calculated based on the following factors:
- Base mint cap (1000)
- Etzem score (higher score means higher cap)
- Badges (STAKER, VALIDATOR_ELIGIBLE, GUARDIAN_ELIGIBLE, SELA_FOUNDER)

## Usage

### Running the API Server

```bash
python run_server.py --port 8888
```

### Testing the Systems

```bash
# Test the Sela Registry
python test_sela.py

# Test the Etzem Engine
python test_etzem.py

# Test the Role Progression Logic
python test_roles.py

# Test the Mikvah Engine
python test_mikvah.py
```
