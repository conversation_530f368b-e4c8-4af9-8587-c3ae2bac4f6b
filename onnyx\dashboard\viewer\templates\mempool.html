{% extends "base.html" %}

{% block title %}Mempool - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Pending Transactions (Mempool)</h2>
        <div class="auto-refresh-controls">
            <button id="manual-refresh" class="refresh-button" title="Refresh now">
                <i class="fas fa-sync-alt"></i>
            </button>
            <div class="auto-refresh-toggle">
                <input type="checkbox" id="auto-refresh-toggle">
                <label for="auto-refresh-toggle">Auto</label>
            </div>
            <div class="auto-refresh-interval">
                <input type="number" id="auto-refresh-interval" min="5" max="300" step="5" value="30">
                <span>sec</span>
            </div>
            <div class="auto-refresh-info">
                <span id="auto-refresh-status">Auto-refresh disabled</span>
                <span id="auto-refresh-countdown"></span>
            </div>
        </div>
    </div>

    <div class="card" style="margin-bottom: 2rem;">
        <p>The mempool contains transactions that have been submitted to the network but have not yet been included in a block. These transactions are waiting to be mined.</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Transaction ID</th>
                <th>Type</th>
                <th>From</th>
                <th>To</th>
                <th>Status</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            {% for tx in txs %}
            <tr>
                <td><a href="{{ url_for('tx_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a></td>
                <td>
                    <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
                </td>
                <td>
                    {% if tx.from %}
                    <a href="{{ url_for('identity_detail', identity_id=tx.from) }}">{{ tx.from[:10] }}...</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>
                    {% if tx.to %}
                    <a href="{{ url_for('identity_detail', identity_id=tx.to) }}">{{ tx.to[:10] }}...</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>
                    <span class="status-tag status-pending">Pending</span>
                </td>
                <td>{{ format_timestamp(tx.timestamp) if tx.timestamp else "N/A" }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="6" style="text-align: center;">No pending transactions in the mempool</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
