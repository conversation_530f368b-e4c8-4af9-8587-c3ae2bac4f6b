# Onnyx API Documentation

## Overview

The Onnyx node provides a JSON-RPC API for interacting with the blockchain. This document outlines the available API endpoints and their usage.

## Connection

- **Default RPC Port**: 8332 (mainnet), 18332 (testnet), 28332 (devnet)
- **Authentication**: Basic HTTP authentication using username and password

## General Methods

### `getinfo`

Returns general information about the node and blockchain.

**Parameters**: None

**Response**:
```json
{
  "version": "0.1.0",
  "protocolversion": 70001,
  "blocks": 1000,
  "timeoffset": 0,
  "connections": 8,
  "difficulty": 1.0,
  "testnet": false,
  "relayfee": 0.00001,
  "errors": ""
}
```

### `getblockcount`

Returns the current block height.

**Parameters**: None

**Response**:
```json
1000
```

### `getblockhash`

Returns the block hash for the given block height.

**Parameters**:
1. `height` (number, required): The block height

**Response**:
```json
"********0019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f"
```

### `getblock`

Returns information about the block with the given hash.

**Parameters**:
1. `blockhash` (string, required): The block hash
2. `verbosity` (number, optional, default=1): 0 for hex-encoded data, 1 for a JSON object, 2 for JSON object with transaction data

**Response** (verbosity=1):
```json
{
  "hash": "********0019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f",
  "confirmations": 1,
  "size": 285,
  "height": 0,
  "version": 1,
  "merkleroot": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b",
  "tx": [
    "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
  ],
  "time": 1231006505,
  "nonce": 2083236893,
  "bits": "1d00ffff",
  "difficulty": 1,
  "previousblockhash": null,
  "nextblockhash": "********839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048"
}
```

## Transaction Methods

### `getrawtransaction`

Returns the raw transaction data.

**Parameters**:
1. `txid` (string, required): The transaction id
2. `verbose` (boolean, optional, default=false): If false, return a string, otherwise return a JSON object

**Response** (verbose=true):
```json
{
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b",
  "hash": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b",
  "version": 1,
  "size": 204,
  "locktime": 0,
  "vin": [
    {
      "coinbase": "04ffff001d0104455468652054696d65732030332f4a616e2f32303039204368616e63656c6c6f72206f6e206272696e6b206f66207365636f6e64206261696c6f757420666f722062616e6b73",
      "sequence": 4294967295
    }
  ],
  "vout": [
    {
      "value": 50.********,
      "n": 0,
      "scriptPubKey": {
        "asm": "04678afdb0fe5548271967f1a67130b7105cd6a828e03909a67962e0ea1f61deb649f6bc3f4cef38c4f35504e51ec112de5c384df7ba0b8d578a4c702b6bf11d5f OP_CHECKSIG",
        "hex": "4104678afdb0fe5548271967f1a67130b7105cd6a828e03909a67962e0ea1f61deb649f6bc3f4cef38c4f35504e51ec112de5c384df7ba0b8d578a4c702b6bf11d5fac",
        "reqSigs": 1,
        "type": "pubkey",
        "addresses": [
          "1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa"
        ]
      }
    }
  ],
  "blockhash": "********0019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f",
  "confirmations": 1,
  "time": 1231006505,
  "blocktime": 1231006505
}
```

### `sendrawtransaction`

Submits a raw transaction to the network.

**Parameters**:
1. `hexstring` (string, required): The hex-encoded transaction data
2. `allowhighfees` (boolean, optional, default=false): Allow high fees

**Response**:
```json
"txid"
```

### `createrawtransaction`

Creates a raw transaction spending the given inputs and creating the given outputs.

**Parameters**:
1. `inputs` (array, required): The inputs
2. `outputs` (object, required): The outputs
3. `locktime` (number, optional, default=0): Raw locktime

**Response**:
```json
"hex"
```

## Wallet Methods

### `getbalance`

Returns the total available balance in the wallet.

**Parameters**:
1. `account` (string, optional): DEPRECATED. The account name
2. `minconf` (number, optional, default=1): Only include transactions confirmed at least this many times

**Response**:
```json
0.********
```

### `sendtoaddress`

Sends an amount to a given address.

**Parameters**:
1. `address` (string, required): The Onnyx address to send to
2. `amount` (number, required): The amount in ONX to send
3. `comment` (string, optional): A comment used to store what the transaction is for
4. `comment_to` (string, optional): A comment to store the name of the person or organization to which you're sending the transaction

**Response**:
```json
"txid"
```

## Identity Methods

### `createidentity`

Creates a new identity.

**Parameters**:
1. `name` (string, required): The identity name
2. `metadata` (object, optional): Additional identity metadata

**Response**:
```json
{
  "identity_id": "1a2b3c4d5e6f7g8h9i0j",
  "name": "MyIdentity",
  "public_key": "02d0de0aaeaefad02b8bdc8a01a1b8b11c696bd3d66a2c5f10780d95b7df42645c",
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `getidentity`

Returns information about the identity with the given ID.

**Parameters**:
1. `identity_id` (string, required): The identity ID

**Response**:
```json
{
  "identity_id": "1a2b3c4d5e6f7g8h9i0j",
  "name": "MyIdentity",
  "public_key": "02d0de0aaeaefad02b8bdc8a01a1b8b11c696bd3d66a2c5f10780d95b7df42645c",
  "reputation": {
    "score": 95,
    "transactions": 124,
    "minted_tokens": 3,
    "followers": 12
  },
  "metadata": {
    "description": "My identity description",
    "url": "https://example.com",
    "type": "individual"
  },
  "tokens": [
    {
      "token_id": "a1b2c3d4e5f6g7h8i9j0",
      "name": "MyToken",
      "symbol": "MTK",
      "balance": 1000,
      "type": "membership",
      "logo_url": "https://example.com/logo.png",
      "creation_block": 1000,
      "activity": {
        "transfers": 50,
        "holders": 25
      }
    }
  ]
}
```

### `updateidentity`

Updates an existing identity.

**Parameters**:
1. `identity_id` (string, required): The identity ID
2. `field` (string, required): The field to update
3. `value` (string, required): The new value

**Response**:
```json
{
  "identity_id": "1a2b3c4d5e6f7g8h9i0j",
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `grantreputation`

Grants reputation or a soulbound badge to an identity.

**Parameters**:
1. `to_identity` (string, required): The identity ID to grant reputation to
2. `reputation_type` (string, required): Type of reputation (e.g., CONTRIBUTOR, VOTER, TRUSTED)
3. `value` (number, required): Reputation value or score
4. `issuer_identity` (string, required): The identity ID of the issuer

**Response**:
```json
{
  "to_identity": "1a2b3c4d5e6f7g8h9i0j",
  "reputation_type": "CONTRIBUTOR",
  "value": 10,
  "issuer_identity": "b2c3d4e5f6g7h8i9j0k1",
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `identityscore`

Returns the composite rank and detailed reputation metrics for an identity.

**Parameters**:
1. `identity_id` (string, required): The identity ID

**Response**:
```json
{
  "identity_id": "1a2b3c4d5e6f7g8h9i0j",
  "composite_score": 87,
  "rank_percentile": 92,
  "metrics": {
    "transaction_volume": 15000,
    "token_activity": 78,
    "network_trust": 91,
    "longevity_days": 120,
    "badges": [
      {
        "type": "CONTRIBUTOR",
        "value": 10,
        "issuer": "b2c3d4e5f6g7h8i9j0k1",
        "timestamp": 1631234567
      }
    ]
  }
}
```

## Token Methods

### `forktoken`

Creates a new token linked to an identity (identity fork).

**Parameters**:
1. `creator_identity` (string, required): The identity ID of the token creator
2. `name` (string, required): The token name
3. `symbol` (string, required): The token symbol
4. `decimals` (number, required): The number of decimal places
5. `initial_supply` (number, required): The initial token supply
6. `metadata` (object, optional): Additional token metadata

**Response**:
```json
{
  "token_id": "a1b2c3d4e5f6g7h8i9j0",
  "name": "MyToken",
  "symbol": "MTK",
  "creator_identity": "1a2b3c4d5e6f7g8h9i0j",
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `gettoken`

Returns information about the token with the given ID.

**Parameters**:
1. `token_id` (string, required): The token ID

**Response**:
```json
{
  "token_id": "a1b2c3d4e5f6g7h8i9j0",
  "name": "MyToken",
  "symbol": "MTK",
  "decimals": 8,
  "supply": 1000000,
  "owner": "1a2b3c4d5e6f7g8h9i0j",
  "metadata": {
    "description": "My token description",
    "url": "https://example.com",
    "logo": "https://example.com/logo.png"
  }
}
```

### `sendtoken`

Sends tokens to a given address.

**Parameters**:
1. `token_id` (string, required): The token ID
2. `address` (string, required): The recipient address
3. `amount` (number, required): The amount to send
4. `memo` (string, optional): A memo or purpose for the transaction
5. `category` (string, optional): A category for the transaction (e.g., "payment", "gift", "reward")

**Response**:
```json
{
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `gettokenbalance`

Returns the token balance for the given address along with metadata.

**Parameters**:
1. `token_id` (string, required): The token ID
2. `address` (string, required): The address

**Response**:
```json
{
  "balance": 1000.********,
  "token_metadata": {
    "name": "MyToken",
    "symbol": "MTK",
    "decimals": 8,
    "logo_url": "https://example.com/logo.png",
    "type": "membership"
  }
}
```

### `minttoken`

Mints additional tokens (if allowed by token rules).

**Parameters**:
1. `token_id` (string, required): The token ID
2. `amount` (number, required): The amount to mint
3. `to_address` (string, required): The recipient address
4. `caller_identity` (string, required): The identity ID of the caller (must have permission)

**Response**:
```json
{
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `burntoken`

Burns tokens (if allowed by token rules).

**Parameters**:
1. `token_id` (string, required): The token ID
2. `amount` (number, required): The amount to burn
3. `from_address` (string, required): The address to burn from
4. `caller_identity` (string, required): The identity ID of the caller (must have permission)

**Response**:
```json
{
  "txid": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
}
```

### `tokenregistry`

Returns a paginated list of all tokens and their ancestry.

**Parameters**:
1. `creator_identity` (string, optional): Filter by creator identity ID
2. `type` (string, optional): Filter by token type (membership, reputation, etc.)
3. `limit` (number, optional, default=100): Maximum number of results to return
4. `offset` (number, optional, default=0): Number of results to skip

**Response**:
```json
{
  "total": 1000,
  "tokens": [
    {
      "token_id": "a1b2c3d4e5f6g7h8i9j0",
      "name": "MyToken",
      "symbol": "MTK",
      "creator_identity": "1a2b3c4d5e6f7g8h9i0j",
      "creation_block": 1000,
      "creation_time": 1631234567,
      "type": "membership",
      "total_supply": 1000000,
      "holders_count": 100,
      "transaction_count": 500,
      "ancestry": {
        "forked_from": null,
        "forks": [
          "b2c3d4e5f6g7h8i9j0k1"
        ]
      }
    }
  ]
}
```
