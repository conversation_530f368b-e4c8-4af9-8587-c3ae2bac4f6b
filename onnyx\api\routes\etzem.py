"""
Onnyx Etzem Routes

This module provides API routes for Etzem operations.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional

from identity.trust.etzem_engine import EtzemEngine
from identity.registry import IdentityRegistry
from tokens.ledger.ledger.ledger.ledger import TokenLedger
from sela.registry.sela_registry import SelaRegistry
from governance.etzem_engine import EtzemEngine as ActivityEtzemEngine

# Create router
router = APIRouter()

# Create instances
identities = IdentityRegistry()
ledger = TokenLedger()
selas = SelaRegistry()
activity_etzem = ActivityEtzemEngine()
etzem = EtzemEngine(identities, ledger, selas, activity_etzem)

@router.get("/etzem/{identity_id}")
def get_etzem(identity_id: str) -> Dict[str, Any]:
    """
    Get the Etzem trust score for an identity.

    Args:
        identity_id: The identity ID

    Returns:
        The Etzem trust score
    """
    result = etzem.compute_etzem(identity_id)
    if not result:
        raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' not found")

    return result

@router.post("/etzem/{identity_id}/badges")
def update_etzem_badges(identity_id: str) -> Dict[str, Any]:
    """
    Update the Etzem badges for an identity.

    Args:
        identity_id: The identity ID

    Returns:
        The updated identity
    """
    result = etzem.update_etzem_badges(identity_id)
    if not result:
        raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' not found")

    return {
        "status": "badges_updated",
        "identity": result
    }

@router.post("/etzem/badges/update-all")
def update_all_etzem_badges() -> Dict[str, Any]:
    """
    Update the Etzem badges for all identities.

    Returns:
        The number of identities updated
    """
    result = etzem.update_all_etzem_badges()

    return {
        "status": "all_badges_updated",
        "count": len(result)
    }

@router.get("/leaderboard")
def get_leaderboard(limit: int = Query(10, ge=1, le=100)) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get the Etzem leaderboard.

    Args:
        limit: The maximum number of identities to return

    Returns:
        The Etzem leaderboard
    """
    result = etzem.get_leaderboard(limit)

    return {
        "leaderboard": result
    }

@router.get("/thresholds")
def get_etzem_thresholds() -> Dict[str, Dict[str, int]]:
    """
    Get the Etzem thresholds for badges.

    Returns:
        The Etzem thresholds
    """
    return {
        "thresholds": etzem.etzem_thresholds
    }
