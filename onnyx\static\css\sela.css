/* Onnyx Sela Creation Styles */

/* Sela Benefits */
.sela-benefits {
    margin-bottom: 3rem;
}

.sela-benefits h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.sela-benefits .benefit-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.sela-benefits .benefit-card h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.sela-benefits .benefit-card p {
    margin-bottom: 1rem;
}

.sela-benefits .benefit-card blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    font-style: italic;
    color: var(--dark-color);
}

@media (min-width: 768px) {
    .sela-benefits {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .sela-benefits h2 {
        grid-column: 1 / -1;
    }
    
    .sela-benefits .benefit-card {
        margin-bottom: 0;
    }
}

/* Sela Examples */
.sela-examples {
    margin-bottom: 3rem;
}

.sela-examples h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.example-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 2rem;
}

.example-header {
    background-color: var(--dark-color);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.example-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.example-tribe {
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.example-content {
    padding: 1.5rem;
}

.example-description {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.example-details, .example-benefits {
    margin-bottom: 1.5rem;
}

.example-details h4, .example-benefits h4 {
    color: var(--dark-color);
    margin-bottom: 0.75rem;
}

.example-details ul, .example-benefits ul {
    padding-left: 1.5rem;
}

.example-details li, .example-benefits li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

@media (min-width: 992px) {
    .example-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .example-description {
        grid-column: 1 / -1;
    }
}

/* Sela Creation Form */
.sela-creation-form {
    margin-bottom: 3rem;
}

.sela-creation-form h2 {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.form-intro {
    text-align: center;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.sela-form {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    max-width: 800px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input[type="text"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 0.5rem;
    margin-top: 0.25rem;
}

.form-actions {
    text-align: center;
    margin-top: 2rem;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.submit-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Success Modal */
.success-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.success-modal.active {
    display: flex;
}

.success-modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 90%;
    max-width: 600px;
    padding: 2rem;
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.success-modal-content h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.success-modal-content p {
    margin-bottom: 1.5rem;
}

.success-modal-actions {
    margin-top: 2rem;
}

.success-modal-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.success-modal-btn:hover {
    background-color: var(--secondary-color);
}

/* Notification */
.notification {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: 1rem;
    color: #999;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
