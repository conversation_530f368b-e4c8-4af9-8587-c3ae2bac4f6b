# src/routes/token.py

import logging
import time
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from models.transaction import Transaction
from models.mempool import MempoolTransaction
from models.identity import Identity
from src.tokens.registry import TokenRegistry
from src.tokens.ledger import TokenLedger
from src.vm.onnyxscript import OnnyxScriptVM
from src.wallet.wallet import OnnyxWallet

# Set up logging
logger = logging.getLogger("onnyx.routes.token")

# Initialize components
registry = TokenRegistry()
ledger = TokenLedger(token_registry=registry)
vm = OnnyxScriptVM(registry=registry, ledger=ledger)
wallet = OnnyxWallet()  # Used for local verification for now

# Create router
token_router = APIRouter(prefix="/token", tags=["token"])

# Token creation request model
class ForkTokenRequest(BaseModel):
    creator_identity: str
    name: str
    symbol: str
    token_type: str
    supply: int
    decimals: int = 2
    mintable: bool = False
    transferable: bool = True
    metadata: dict = {}
    message: str
    signature: str

@token_router.post("/forktoken")
def fork_token(req: ForkTokenRequest):
    """
    Create a new token (fork) linked to an identity.
    Requires a signature to verify the creator's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Add to mempool first
    mempool_data = {
        "op": "OP_SPAWN_TOKEN",
        "data": {
            "name": req.name,
            "symbol": req.symbol,
            "creator_id": req.creator_identity,
            "token_type": req.token_type,
            "supply": req.supply,
            "metadata": req.metadata,
            "mintable": req.mintable,
            "transferable": req.transferable
        },
        "sender": req.creator_identity,
        "signature": req.signature
    }

    # Create a mempool transaction
    mempool_tx = MempoolTransaction.create(
        tx_id=f"mem_{int(time.time()*1000)}",
        timestamp=int(time.time()),
        op="OP_SPAWN_TOKEN",
        data=mempool_data["data"],
        sender=mempool_data["sender"],
        signature=mempool_data["signature"]
    )

    try:
        # Create token using VM
        script = [
            ("OP_SPAWN_TOKEN", (
                req.name,
                req.symbol,
                req.creator_identity,
                req.token_type,
                req.supply,
                req.metadata,
                req.mintable,
                req.transferable
            ))
        ]

        result = vm.execute(script)
        token_id = result[0]

        # Get the created token
        token = registry.get_token(token_id)

        # Create a transaction record
        tx = Transaction.create(
            op="OP_SPAWN_TOKEN",
            data={
                "token_id": token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_id": token.creator_id,
                "token_type": req.token_type,
                "supply": token.supply,
                "metadata": token.metadata,
                "mintable": req.mintable,
                "transferable": req.transferable
            },
            sender=req.creator_identity,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {
            "status": "success",
            "token_id": token_id,
            "name": token.name,
            "symbol": token.symbol,
            "creator_id": token.creator_id,
            "txid": tx.tx_id
        }
    except Exception as e:
        logger.error(f"Error creating token: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# Mint token request model
class MintTokenRequest(BaseModel):
    token_id: str
    amount: int
    to_address: str
    caller_identity: str
    message: str
    signature: str

@token_router.post("/minttoken")
def mint_token(req: MintTokenRequest):
    """
    Mint additional tokens.
    Requires a signature to verify the caller's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Add to mempool first
    mempool_data = {
        "token_id": req.token_id,
        "amount": req.amount,
        "to_id": req.to_address,
        "minter_id": req.caller_identity
    }

    # Create a mempool transaction
    mempool_tx = MempoolTransaction.create(
        tx_id=f"mem_{int(time.time()*1000)}",
        timestamp=int(time.time()),
        op="OP_MINT",
        data=mempool_data,
        sender=req.caller_identity,
        signature=req.signature
    )

    try:
        # Get the token
        token = registry.get_token(req.token_id)
        if not token:
            raise HTTPException(status_code=404, detail="Token not found")

        # Mint the tokens
        token.mint(req.to_address, req.amount)

        # Create a transaction record
        tx = Transaction.create(
            op="OP_MINT",
            data=mempool_data,
            sender=req.caller_identity,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {"status": "success", "txid": tx.tx_id}
    except Exception as e:
        logger.error(f"Error minting tokens: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# Send token request model
class SendTokenRequest(BaseModel):
    token_id: str
    from_address: str
    to_address: str
    amount: int
    memo: str = None
    message: str
    signature: str

@token_router.post("/sendtoken")
def send_token(req: SendTokenRequest):
    """
    Transfer tokens from one address to another.
    Requires a signature to verify the sender's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Check balance
    balance = ledger.get_balance(req.from_address, req.token_id)
    if balance < req.amount:
        raise HTTPException(status_code=400, detail="Insufficient balance")

    # Add to mempool first
    mempool_data = {
        "token_id": req.token_id,
        "from_id": req.from_address,
        "to_id": req.to_address,
        "amount": req.amount,
        "memo": req.memo
    }

    # Create a mempool transaction
    mempool_tx = MempoolTransaction.create(
        tx_id=f"mem_{int(time.time()*1000)}",
        timestamp=int(time.time()),
        op="OP_SEND",
        data=mempool_data,
        sender=req.from_address,
        signature=req.signature
    )

    try:
        # Get the token
        token = registry.get_token(req.token_id)
        if not token:
            raise HTTPException(status_code=404, detail="Token not found")

        # Transfer the tokens
        token.transfer(req.from_address, req.to_address, req.amount)

        # Create a transaction record
        tx = Transaction.create(
            op="OP_SEND",
            data=mempool_data,
            sender=req.from_address,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {"status": "success", "txid": tx.tx_id}
    except Exception as e:
        logger.error(f"Error sending tokens: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# Burn token request model
class BurnTokenRequest(BaseModel):
    token_id: str
    from_address: str
    amount: int
    burner_identity: str
    message: str
    signature: str

@token_router.post("/burntoken")
def burn_token(req: BurnTokenRequest):
    """
    Burn tokens.
    Requires a signature to verify the burner's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Check balance
    balance = ledger.get_balance(req.from_address, req.token_id)
    if balance < req.amount:
        raise HTTPException(status_code=400, detail="Insufficient balance")

    # Add to mempool first
    mempool_data = {
        "token_id": req.token_id,
        "from_id": req.from_address,
        "amount": req.amount,
        "burner_id": req.burner_identity
    }

    # Create a mempool transaction
    mempool_tx = MempoolTransaction.create(
        tx_id=f"mem_{int(time.time()*1000)}",
        timestamp=int(time.time()),
        op="OP_BURN",
        data=mempool_data,
        sender=req.burner_identity,
        signature=req.signature
    )

    try:
        # Get the token
        token = registry.get_token(req.token_id)
        if not token:
            raise HTTPException(status_code=404, detail="Token not found")

        # Burn the tokens
        token.burn(req.from_address, req.amount)

        # Create a transaction record
        tx = Transaction.create(
            op="OP_BURN",
            data=mempool_data,
            sender=req.burner_identity,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {"status": "success", "txid": tx.tx_id}
    except Exception as e:
        logger.error(f"Error burning tokens: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@token_router.get("/gettokenbalance")
def get_token_balance(token_id: str, address: str):
    """
    Get the balance of a token for an address.
    """
    balance = ledger.get_balance(address, token_id)
    token = registry.get_token(token_id)

    return {
        "balance": balance,
        "token_metadata": token.metadata if token else {}
    }

@token_router.get("/tokeninfo")
def get_token_info(token_id: str):
    """
    Get detailed information about a token.
    """
    token = registry.get_token(token_id)
    if not token:
        raise HTTPException(status_code=404, detail="Token not found")

    # Get token balances
    balances = token.get_balances()

    # Count holders
    holders_count = len(balances)

    # Get transaction count
    token_txs = token.get_transactions(limit=1000)
    transaction_count = len(token_txs)

    # Format token data
    return {
        "token_id": token.token_id,
        "name": token.name,
        "symbol": token.symbol,
        "creator_id": token.creator_id,
        "category": token.category,
        "supply": token.supply,
        "decimals": token.decimals,
        "mintable": token.metadata.get("mintable", False),
        "transferable": token.metadata.get("transferable", True),
        "metadata": token.metadata,
        "holders_count": holders_count,
        "transaction_count": transaction_count,
        "created_at": token.created_at
    }

@token_router.get("/tokenregistry")
def token_registry(creator_id: str = None, category: str = None, limit: int = 100, offset: int = 0):
    """
    Get a list of tokens, optionally filtered by creator or category.
    """
    # Get tokens based on filters
    if creator_id:
        tokens = registry.get_tokens_by_creator(creator_id)
    elif category:
        tokens = registry.get_tokens_by_category(category)
    else:
        tokens = registry.list_tokens()

    # Apply pagination
    total = len(tokens)
    tokens = tokens[offset:offset+limit]

    # Format response
    result = {
        "total": total,
        "tokens": []
    }

    for token in tokens:
        # Get token balances
        balances = token.get_balances()

        # Count holders
        holders_count = len(balances)

        # Get transaction count
        token_txs = token.get_transactions(limit=1000)
        transaction_count = len(token_txs)

        # Format token data
        token_data = {
            "token_id": token.token_id,
            "name": token.name,
            "symbol": token.symbol,
            "creator_id": token.creator_id,
            "creation_time": token.created_at,
            "category": token.category,
            "total_supply": token.supply,
            "decimals": token.decimals,
            "holders_count": holders_count,
            "transaction_count": transaction_count,
            "metadata": token.metadata
        }

        result["tokens"].append(token_data)

    return result

@token_router.get("/transactions")
def list_transactions(
    token_id: str = Query(None, description="Filter transactions by token ID"),
    identity_id: str = Query(None, description="Filter transactions by identity ID"),
    txid: str = Query(None, description="Get a specific transaction by ID"),
    limit: int = Query(100, description="Maximum number of transactions to return"),
    offset: int = Query(0, description="Number of transactions to skip")
):
    """
    Get a list of transactions, optionally filtered by token ID, identity ID, or transaction ID.
    """
    if txid:
        # Get a specific transaction
        tx = Transaction.get_by_id(txid)
        if tx:
            return {
                "tx_id": tx.tx_id,
                "timestamp": tx.timestamp,
                "op": tx.op,
                "data": tx.data,
                "sender": tx.sender,
                "status": tx.status,
                "block_hash": tx.block_hash,
                "created_at": tx.created_at
            }
        else:
            raise HTTPException(status_code=404, detail="Transaction not found")
    elif token_id:
        # Get transactions for a specific token
        token = registry.get_token(token_id)
        if not token:
            raise HTTPException(status_code=404, detail="Token not found")

        transactions = token.get_transactions(limit=limit, offset=offset)
        return {
            "transactions": transactions
        }
    elif identity_id:
        # Get transactions for a specific identity
        identity = Identity.get_by_id(identity_id)
        if not identity:
            raise HTTPException(status_code=404, detail="Identity not found")

        transactions = identity.get_transactions(limit=limit, offset=offset)
        return {
            "transactions": transactions
        }
    else:
        # Get all transactions
        transactions = Transaction.get_all()

        # Apply pagination
        transactions = transactions[offset:offset+limit]

        # Convert to dictionaries
        tx_list = []
        for tx in transactions:
            tx_list.append({
                "tx_id": tx.tx_id,
                "timestamp": tx.timestamp,
                "op": tx.op,
                "data": tx.data,
                "sender": tx.sender,
                "status": tx.status,
                "block_hash": tx.block_hash,
                "created_at": tx.created_at
            })

        return {
            "transactions": tx_list
        }
