-- Onnyx Database Schema (Fixed - No Circular Dependencies)

-- Core tables first (no foreign key dependencies)

-- Identities table (core table - no foreign keys initially)
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    public_key TEXT NOT NULL,
    email TEXT,
    nation_id TEXT,  -- Will be populated later, no FK constraint initially
    metadata TEXT NOT NULL DEFAULT '{}',  -- JSON object with identity metadata
    status TEXT NOT NULL DEFAULT 'active',
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Create index on identity name
CREATE INDEX IF NOT EXISTS idx_identities_name ON identities(name);
CREATE INDEX IF NOT EXISTS idx_identities_nation ON identities(nation_id);

-- Transactions table (core table)
CREATE TABLE IF NOT EXISTS transactions (
    tx_id TEXT PRIMARY KEY,
    block_hash TEXT,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',  -- <PERSON>SON object with transaction data
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at INTEGER NOT NULL
);

-- Create index on transaction sender
CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);

-- Blocks table (core table)
CREATE TABLE IF NOT EXISTS blocks (
    block_hash TEXT PRIMARY KEY,
    block_height INTEGER NOT NULL,
    previous_hash TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    difficulty INTEGER NOT NULL DEFAULT 1,
    nonce INTEGER NOT NULL DEFAULT 0,
    miner TEXT NOT NULL,
    transactions TEXT NOT NULL DEFAULT '[]',  -- JSON array of transaction IDs
    merkle_root TEXT NOT NULL,
    size INTEGER NOT NULL DEFAULT 0,
    version TEXT NOT NULL DEFAULT '1.0',
    created_at INTEGER NOT NULL
);

-- Create index on block height
CREATE INDEX IF NOT EXISTS idx_blocks_height ON blocks(block_height);

-- Mempool table (core table)
CREATE TABLE IF NOT EXISTS mempool (
    tx_id TEXT PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',  -- JSON object with transaction data
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Tokens table (depends on identities)
CREATE TABLE IF NOT EXISTS tokens (
    token_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    creator_id TEXT NOT NULL,
    supply INTEGER NOT NULL DEFAULT 0,
    category TEXT NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}',  -- JSON object with token metadata
    FOREIGN KEY (creator_id) REFERENCES identities(identity_id)
);

-- Create index on token creator
CREATE INDEX IF NOT EXISTS idx_tokens_creator ON tokens(creator_id);

-- Token balances table
CREATE TABLE IF NOT EXISTS token_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    balance INTEGER NOT NULL DEFAULT 0,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (token_id) REFERENCES tokens(token_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    UNIQUE(token_id, identity_id)
);

-- Create index on token balances
CREATE INDEX IF NOT EXISTS idx_token_balances_token ON token_balances(token_id);
CREATE INDEX IF NOT EXISTS idx_token_balances_identity ON token_balances(identity_id);

-- Sela business registry table
CREATE TABLE IF NOT EXISTS selas (
    sela_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    stake_amount INTEGER NOT NULL DEFAULT 0,
    stake_token_id TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}',  -- JSON object with Sela metadata
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (stake_token_id) REFERENCES tokens(token_id)
);

-- Create index on Sela identity
CREATE INDEX IF NOT EXISTS idx_selas_identity ON selas(identity_id);

-- Nations table (depends on identities, no circular dependency)
CREATE TABLE IF NOT EXISTS nations (
    nation_id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    founder_id TEXT NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}',  -- JSON object with nation metadata
    status TEXT NOT NULL DEFAULT 'active',
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (founder_id) REFERENCES identities(identity_id)
);

-- Create index on nation name
CREATE INDEX IF NOT EXISTS idx_nations_name ON nations(name);

-- Activity ledger table
CREATE TABLE IF NOT EXISTS activity_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    activity_type TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',  -- JSON object with activity data
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Create index on activity ledger
CREATE INDEX IF NOT EXISTS idx_activity_ledger_identity ON activity_ledger(identity_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_timestamp ON activity_ledger(timestamp);

-- Etzem trust scores table
CREATE TABLE IF NOT EXISTS etzem_scores (
    identity_id TEXT PRIMARY KEY,
    composite_score INTEGER NOT NULL DEFAULT 0,
    zeman_score INTEGER NOT NULL DEFAULT 0,
    reputation_score INTEGER NOT NULL DEFAULT 0,
    sela_score INTEGER NOT NULL DEFAULT 0,
    token_activity_score INTEGER NOT NULL DEFAULT 0,
    governance_score INTEGER NOT NULL DEFAULT 0,
    longevity_score INTEGER NOT NULL DEFAULT 0,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Voice Scrolls (governance proposals) table
CREATE TABLE IF NOT EXISTS voice_scrolls (
    scroll_id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    proposer_id TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'PROPOSED',  -- PROPOSED, VOTING, PASSED, REJECTED, IMPLEMENTED
    proposal_type TEXT NOT NULL,
    params TEXT NOT NULL DEFAULT '{}',  -- JSON object with proposal parameters
    voting_start INTEGER NOT NULL,
    voting_end INTEGER NOT NULL,
    implementation_time INTEGER,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (proposer_id) REFERENCES identities(identity_id)
);

-- Create index on Voice Scrolls status
CREATE INDEX IF NOT EXISTS idx_voice_scrolls_status ON voice_scrolls(status);

-- Event logs table (no foreign keys for flexibility)
CREATE TABLE IF NOT EXISTS event_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',  -- JSON object with event data
    timestamp INTEGER NOT NULL
);

-- Create index on event logs
CREATE INDEX IF NOT EXISTS idx_event_logs_type ON event_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_event_logs_entity ON event_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_event_logs_timestamp ON event_logs(timestamp);

-- Chain parameters table
CREATE TABLE IF NOT EXISTS chain_parameters (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    default_value TEXT NOT NULL,
    description TEXT,
    category TEXT,
    last_updated INTEGER,
    last_updated_by TEXT,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (last_updated_by) REFERENCES identities(identity_id)
);

-- Create index on chain parameters category
CREATE INDEX IF NOT EXISTS idx_chain_parameters_category ON chain_parameters(category);
