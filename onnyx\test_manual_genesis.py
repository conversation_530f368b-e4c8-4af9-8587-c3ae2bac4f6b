#!/usr/bin/env python3
"""
Manual Genesis Identity Creation Test
Simple test to verify Genesis Identity creation works.
"""

import requests
import time

def test_manual_genesis():
    """Test Genesis Identity creation manually."""
    print("🌟 Manual Genesis Identity Creation Test")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Check if Genesis registration page loads
    print("1. Testing Genesis registration page...")
    try:
        response = requests.get(f"{base_url}/auth/register/genesis")
        if response.status_code == 200:
            print("   ✅ Genesis registration page loads")
        else:
            print(f"   ❌ Genesis registration page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 2: Check email validation API
    print("2. Testing email validation API...")
    try:
        unique_email = f"test-{int(time.time())}@example.com"
        response = requests.post(f"{base_url}/auth/api/validate/email", 
                               json={"email": unique_email})
        if response.status_code == 200:
            data = response.json()
            if data.get('valid'):
                print("   ✅ Email validation API works")
            else:
                print(f"   ❌ Email validation failed: {data.get('message')}")
        else:
            print(f"   ❌ Email validation API failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Check registration choice page
    print("3. Testing registration choice page...")
    try:
        response = requests.get(f"{base_url}/register")
        if response.status_code == 200:
            if 'Genesis Identity' in response.text:
                print("   ✅ Registration choice page shows Genesis option")
            else:
                print("   ⚠️ Genesis option not visible (may already exist)")
        else:
            print(f"   ❌ Registration choice page failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Check if Genesis Identity already exists
    print("4. Checking Genesis Identity status...")
    try:
        # Try to access the Genesis page - if it redirects, Genesis exists
        response = requests.get(f"{base_url}/auth/register/genesis", allow_redirects=False)
        if response.status_code == 302:
            print("   ⚠️ Genesis Identity already exists (redirected)")
        elif response.status_code == 200:
            print("   ✅ Genesis Identity creation available")
        else:
            print(f"   ❌ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n📋 Manual Test Instructions:")
    print("=" * 50)
    print("To manually test Genesis Identity creation:")
    print("1. Open browser to: http://127.0.0.1:5000/register")
    print("2. Click 'Create Genesis' button (if visible)")
    print("3. Fill out the form with:")
    print("   - Name: Your Name")
    print("   - Email: <EMAIL>")
    print("   - Organization: Your Organization (optional)")
    print("   - Purpose: (pre-filled)")
    print("4. Check the terms checkbox")
    print("5. Click 'Create Genesis Identity'")
    print("6. Verify success page appears")
    print("7. Download the private key file")
    print("\n🔧 If Genesis Identity already exists:")
    print("   - Check database for existing Platform Founder")
    print("   - Or reset database to test creation")
    
    return True

if __name__ == "__main__":
    test_manual_genesis()
