#!/usr/bin/env python3
"""
Simple ONNYX Navigation Test

Tests navigation functionality by checking HTML content and CSS.
"""

import requests
import re
from bs4 import BeautifulSoup

def test_navigation_fixes():
    """Test navigation fixes without browser automation."""
    base_url = "http://127.0.0.1:5000"
    results = []
    
    print("🚀 Testing ONNYX Navigation Fixes")
    print("=" * 50)
    
    try:
        # Test server connection
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            results.append("✅ Flask server is running and accessible")
        else:
            results.append(f"❌ Server returned status code: {response.status_code}")
            return results
            
    except Exception as e:
        results.append(f"❌ Cannot connect to server: {e}")
        return results
    
    # Parse HTML content
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Test 1: Check if main navigation exists
    nav_links = soup.find_all('a', class_='nav-link')
    if len(nav_links) >= 3:
        results.append(f"✅ Found {len(nav_links)} navigation links")
        
        # Check for specific navigation items
        nav_texts = [link.get_text().strip() for link in nav_links]
        expected_nav = ['Home', 'Validators', 'Explorer']
        
        for expected in expected_nav:
            if expected in nav_texts:
                results.append(f"✅ '{expected}' navigation link found")
            else:
                results.append(f"❌ '{expected}' navigation link missing")
    else:
        results.append(f"❌ Expected at least 3 nav links, found {len(nav_links)}")
    
    # Test 2: Check if guest user buttons exist
    access_portal = soup.find('a', string=re.compile(r'Access Portal'))
    verify_identity = soup.find('a', string=re.compile(r'Verify Identity'))
    
    if access_portal:
        results.append("✅ 'Access Portal' button found")
    else:
        results.append("❌ 'Access Portal' button missing")
        
    if verify_identity:
        results.append("✅ 'Verify Identity' button found")
    else:
        results.append("❌ 'Verify Identity' button missing")
    
    # Test 3: Check if navigation has proper structure
    nav_element = soup.find('nav')
    if nav_element:
        nav_classes = nav_element.get('class', [])
        if 'glass-nav' in nav_classes:
            results.append("✅ Navigation has glass-nav styling class")
        else:
            results.append("❌ Navigation missing glass-nav styling class")
    else:
        results.append("❌ No nav element found")
    
    # Test 4: Check if Alpine.js is loaded
    alpine_script = soup.find('script', src=re.compile(r'alpinejs'))
    if alpine_script:
        results.append("✅ Alpine.js script tag found")
    else:
        results.append("❌ Alpine.js script tag missing")
    
    # Test 5: Check if main.js is loaded
    main_script = soup.find('script', src=re.compile(r'main\.js'))
    if main_script:
        results.append("✅ Main.js script tag found")
    else:
        results.append("❌ Main.js script tag missing")
    
    # Test 6: Check if dropdown elements exist (should be hidden when logged out)
    dropdown_button = soup.find(id='user-dropdown-button')
    dropdown_menu = soup.find(id='user-dropdown-menu')
    
    if not dropdown_button and not dropdown_menu:
        results.append("✅ User dropdown properly hidden when logged out")
    else:
        results.append("❌ User dropdown elements found when should be hidden")
    
    # Test 7: Check CSS file
    try:
        css_response = requests.get(f"{base_url}/static/css/main.css", timeout=5)
        if css_response.status_code == 200:
            results.append("✅ CSS file accessible")
            
            css_content = css_response.text
            
            # Check for navigation-specific CSS
            if 'nav-link' in css_content:
                results.append("✅ Navigation CSS styles found")
            else:
                results.append("❌ Navigation CSS styles missing")
                
            if 'user-dropdown' in css_content:
                results.append("✅ Dropdown CSS styles found")
            else:
                results.append("❌ Dropdown CSS styles missing")
                
        else:
            results.append(f"❌ CSS file not accessible: {css_response.status_code}")
    except Exception as e:
        results.append(f"❌ Error accessing CSS file: {e}")
    
    # Print results
    print("\n📊 Test Results:")
    print("=" * 50)
    for result in results:
        print(result)
    
    # Summary
    passed = len([r for r in results if r.startswith("✅")])
    failed = len([r for r in results if r.startswith("❌")])
    
    print(f"\n📈 Summary: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Navigation fixes are working.")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    return results

if __name__ == "__main__":
    test_navigation_fixes()
