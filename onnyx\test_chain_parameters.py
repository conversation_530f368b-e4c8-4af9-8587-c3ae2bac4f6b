#!/usr/bin/env python3
"""
Test the Onnyx Scroll-Controlled Chain Parameters

This script tests the Onnyx Scroll-Controlled Chain Parameters by simulating governance proposals.
"""

import os
import sys
import time
import json
from typing import Dict, Any, List

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.chain_parameters import ChainParameters, DEFAULTS
from governance.voice_scroll import VoiceScrolls
from identity.registry import IdentityRegistry
from trust.etzem_engine import EtzemEngine

def setup_test_data():
    """Set up test data for the chain parameters test."""
    # Create identity registry
    identity_registry = IdentityRegistry()
    
    # Create Etzem engine
    etzem_engine = EtzemEngine()
    
    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "Alice", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: <PERSON>")
    
    try:
        bob = identity_registry.register_identity("bob", "<PERSON>", "0x987654321fedcba")
        print(f"Created identity: Bob")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: Bob")
    
    try:
        charlie = identity_registry.register_identity("charlie", "Charlie", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")
    
    # Add COUNCIL_MEMBER_BADGE to Alice
    alice = identity_registry.get_identity("alice")
    badges = alice.get("badges", [])
    if "COUNCIL_MEMBER_BADGE" not in badges:
        badges.append("COUNCIL_MEMBER_BADGE")
        alice["badges"] = badges
        identity_registry.identities["alice"] = alice
        identity_registry._save()
        print(f"Added COUNCIL_MEMBER_BADGE to alice")
    
    # Mock the Etzem engine
    def mock_compute_etzem(identity_id):
        if identity_id == "alice":
            return {
                "identity_id": "alice",
                "consistency": 9.33,
                "tx_score": 20.0,
                "trust_weight": 16.0,
                "badge_bonus": 12,
                "sela_participation": 15,
                "token_impact": 10,
                "etzem": 82.33
            }
        elif identity_id == "bob":
            return {
                "identity_id": "bob",
                "consistency": 6.33,
                "tx_score": 20.0,
                "trust_weight": 10.0,
                "badge_bonus": 4,
                "sela_participation": 10,
                "token_impact": 10,
                "etzem": 60.33
            }
        else:
            return {
                "identity_id": "charlie",
                "consistency": 3.33,
                "tx_score": 4.2,
                "trust_weight": 4.0,
                "badge_bonus": 0,
                "sela_participation": 0,
                "token_impact": 5.0,
                "etzem": 16.53
            }
    
    etzem_engine.compute_etzem = mock_compute_etzem
    
    return identity_registry, etzem_engine

def test_chain_parameters():
    """Test the chain parameters."""
    print("\nTesting Chain Parameters...")
    
    # Create chain parameters
    chain_parameters = ChainParameters("data/test_chain_params.json")
    
    # Get all parameters
    params = chain_parameters.all()
    
    print("\nDefault Chain Parameters:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # Set a parameter
    chain_parameters.set("block_reward", 8)
    
    # Get the parameter
    block_reward = chain_parameters.get("block_reward")
    
    print(f"\nUpdated block_reward: {block_reward}")
    
    # Reset the parameter
    chain_parameters.reset("block_reward")
    
    # Get the parameter
    block_reward = chain_parameters.get("block_reward")
    
    print(f"Reset block_reward: {block_reward}")
    
    # Update multiple parameters
    chain_parameters.update({
        "block_reward": 12,
        "mint_cap_factor": 1.5,
        "quorum_percent": 60
    })
    
    # Get the parameters
    block_reward = chain_parameters.get("block_reward")
    mint_cap_factor = chain_parameters.get("mint_cap_factor")
    quorum_percent = chain_parameters.get("quorum_percent")
    
    print(f"\nUpdated multiple parameters:")
    print(f"  block_reward: {block_reward}")
    print(f"  mint_cap_factor: {mint_cap_factor}")
    print(f"  quorum_percent: {quorum_percent}")
    
    # Reset all parameters
    chain_parameters.reset_all()
    
    # Get all parameters
    params = chain_parameters.all()
    
    print("\nReset All Chain Parameters:")
    for key, value in params.items():
        if key in ["block_reward", "mint_cap_factor", "quorum_percent"]:
            print(f"  {key}: {value}")
    
    print("\nChain Parameters test completed successfully!")

def test_voice_scrolls():
    """Test the voice scrolls."""
    print("\nTesting Voice Scrolls...")
    
    # Set up test data
    identity_registry, etzem_engine = setup_test_data()
    
    # Create voice scrolls
    voice_scrolls = VoiceScrolls("data/test_scrolls.json")
    
    # Create a scroll
    try:
        scroll = voice_scrolls.create_scroll(
            creator_id="alice",
            title="Reduce block reward",
            description="Cut block reward to 8 ONX to reduce inflation",
            category="economic",
            expiry_days=7,
            effect={
                "param": "block_reward",
                "value": 8
            }
        )
        
        print(f"\nCreated scroll: {scroll['id']}")
        print(f"  Title: {scroll['title']}")
        print(f"  Description: {scroll['description']}")
        print(f"  Category: {scroll['category']}")
        print(f"  Effect: {scroll['effect']}")
    except Exception as e:
        print(f"Error creating scroll: {str(e)}")
        # Get an existing scroll
        scrolls = voice_scrolls.get_scrolls(category="economic")
        if scrolls:
            scroll = scrolls[0]
            print(f"\nUsing existing scroll: {scroll['id']}")
            print(f"  Title: {scroll['title']}")
            print(f"  Description: {scroll['description']}")
            print(f"  Category: {scroll['category']}")
            print(f"  Effect: {scroll.get('effect', 'None')}")
        else:
            print("No existing scrolls found")
            return
    
    # Vote on the scroll
    try:
        # Alice votes yes
        voice_scrolls.vote_on_scroll(
            identity_id="alice",
            scroll_id=scroll["id"],
            decision="yes"
        )
        
        # Bob votes yes
        voice_scrolls.vote_on_scroll(
            identity_id="bob",
            scroll_id=scroll["id"],
            decision="yes"
        )
        
        # Charlie votes no
        voice_scrolls.vote_on_scroll(
            identity_id="charlie",
            scroll_id=scroll["id"],
            decision="no"
        )
        
        print("\nVotes recorded")
    except Exception as e:
        print(f"Error voting on scroll: {str(e)}")
    
    # Tally the scroll
    try:
        tally_result = voice_scrolls.tally_scroll(scroll["id"])
        
        print("\nTally Result:")
        print(f"  Yes: {tally_result['tally']['yes']}")
        print(f"  No: {tally_result['tally']['no']}")
        print(f"  Abstain: {tally_result['tally']['abstain']}")
        print(f"  Total Weight: {tally_result['total_weight']}")
        print(f"  Yes Ratio: {tally_result['yes_ratio']:.2f}")
        print(f"  Outcome: {tally_result['outcome']}")
    except Exception as e:
        print(f"Error tallying scroll: {str(e)}")
    
    # Resolve the scroll
    try:
        # Force the scroll to be closed
        scroll = voice_scrolls.get_scroll(scroll["id"])
        scroll["status"] = "closed"
        voice_scrolls.scrolls[scroll["id"]] = scroll
        voice_scrolls._save_scrolls()
        
        resolved_scroll = voice_scrolls.resolve_scroll(scroll["id"])
        
        print("\nResolved Scroll:")
        print(f"  Status: {resolved_scroll['status']}")
        print(f"  Outcome: {resolved_scroll['outcome']}")
        
        # Check if the chain parameter was updated
        chain_parameters = ChainParameters("data/test_chain_params.json")
        block_reward = chain_parameters.get("block_reward")
        
        print(f"\nUpdated block_reward: {block_reward}")
    except Exception as e:
        print(f"Error resolving scroll: {str(e)}")
    
    print("\nVoice Scrolls test completed successfully!")

if __name__ == "__main__":
    test_chain_parameters()
    test_voice_scrolls()
