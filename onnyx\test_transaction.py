#!/usr/bin/env python3
"""
Test transaction creation functionality.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_transaction_creation():
    """Test creating a transaction."""
    print("Testing transaction creation...")
    
    try:
        from shared.models.transaction import Transaction
        
        # Create a test transaction
        tx = Transaction.create(
            op="OP_TRANSFER",
            data={"amount": 100, "token_id": "ONX", "to": "test_recipient"},
            sender="test_sender",
            signature="test_signature"
        )
        
        print(f"✅ Transaction created successfully!")
        print(f"   TX ID: {tx.tx_id}")
        print(f"   Operation: {tx.op}")
        print(f"   Sender: {tx.sender}")
        print(f"   Status: {tx.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transaction creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mempool():
    """Test mempool functionality."""
    print("\nTesting mempool...")
    
    try:
        from blockchain.node.mempool import Mempool
        
        mempool = Mempool()
        
        # Add a test transaction
        test_tx = {
            "tx_id": "test_tx_123",
            "op": "OP_TRANSFER",
            "data": {"amount": 50, "token_id": "ONX"},
            "sender": "test_sender",
            "timestamp": 1234567890
        }
        
        mempool.add_transaction(test_tx)
        print(f"✅ Transaction added to mempool")
        
        # Get all transactions
        all_txs = mempool.get_all()
        print(f"✅ Mempool contains {len(all_txs)} transactions")
        
        return True
        
    except Exception as e:
        print(f"❌ Mempool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Transaction Functionality")
    print("=" * 40)
    
    success = True
    
    if not test_transaction_creation():
        success = False
    
    if not test_mempool():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All transaction tests passed!")
    else:
        print("⚠️ Some transaction tests failed.")
    
    return success

if __name__ == "__main__":
    main()
