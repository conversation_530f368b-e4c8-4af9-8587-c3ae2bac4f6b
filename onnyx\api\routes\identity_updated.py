"""
Onnyx Identity Routes

This module provides API routes for Identity operations.
"""

import logging
import uuid
import time
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from identity.registry import IdentityRegistry
from governance.etzem_engine import EtzemEngine
from governance.role_progression import RoleProgression

from shared.models.identity import Identity
from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.routes.identity")

# Create router
router = APIRouter()

# Create instances
identities = IdentityRegistry()
etzem_engine = EtzemEngine()
role_progression = RoleProgression(identities, etzem_engine)

@router.post("/identity/register")
def register_identity(
    identity_id: str = None,
    name: str = Body(...),
    public_key: str = Body(...),
    metadata: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Register a new identity.

    Args:
        identity_id: The identity ID (optional, will be generated if not provided)
        name: The identity name
        public_key: The identity's public key
        metadata: Additional metadata for the identity

    Returns:
        Information about the registered identity
    """
    try:
        logger.info(f"Registering identity: {name}")

        # Generate identity ID if not provided
        if not identity_id:
            identity_id = f"id_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            logger.debug(f"Generated identity ID: {identity_id}")

        # Prepare metadata
        if metadata is None:
            metadata = {}

        # Create the identity in the database
        identity = Identity.create(
            identity_id=identity_id,
            name=name,
            public_key=public_key,
            metadata=metadata
        )

        # Register the identity with the registry
        identities.register_identity(identity_id, name, public_key, metadata)

        logger.info(f"Identity registered: {identity_id}")
        return {
            "status": "created",
            "identity": {
                "identity_id": identity.identity_id,
                "name": identity.name,
                "public_key": identity.public_key,
                "metadata": identity.metadata
            }
        }
    except Exception as e:
        logger.error(f"Error registering identity: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/identity/{identity_id}")
def get_identity(identity_id: str) -> Dict[str, Any]:
    """
    Get an identity by ID.

    Args:
        identity_id: The identity ID

    Returns:
        The identity
    """
    logger.info(f"Getting identity: {identity_id}")

    identity = Identity.get_by_id(identity_id)
    if not identity:
        logger.warning(f"Identity with ID '{identity_id}' not found")
        raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' not found")

    return {
        "identity_id": identity.identity_id,
        "name": identity.name,
        "public_key": identity.public_key,
        "metadata": identity.metadata
    }

@router.get("/identities")
def list_identities() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all identities.

    Returns:
        All identities in the registry
    """
    logger.info("Listing all identities")

    identities_list = Identity.get_all()

    return {
        "identities": [
            {
                "identity_id": identity.identity_id,
                "name": identity.name,
                "public_key": identity.public_key,
                "metadata": identity.metadata
            }
            for identity in identities_list
        ]
    }

@router.post("/identity/{identity_id}/badge")
def add_badge(
    identity_id: str,
    badge: str = Body(...)
) -> Dict[str, Any]:
    """
    Add a badge to an identity.

    Args:
        identity_id: The identity ID
        badge: The badge to add

    Returns:
        Information about the updated identity
    """
    try:
        logger.info(f"Adding badge {badge} to identity {identity_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Add the badge to the identity's metadata
        if "badges" not in identity.metadata:
            identity.metadata["badges"] = []

        if badge not in identity.metadata["badges"]:
            identity.metadata["badges"].append(badge)
            identity.save()

        # Add the badge to the registry
        identities.add_badge(identity_id, badge)

        logger.info(f"Badge {badge} added to identity {identity_id}")
        return {
            "status": "badge_added",
            "identity_id": identity_id,
            "badge": badge
        }
    except Exception as e:
        logger.error(f"Error adding badge: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/identity/{identity_id}/badge")
def remove_badge(
    identity_id: str,
    badge: str = Query(...)
) -> Dict[str, Any]:
    """
    Remove a badge from an identity.

    Args:
        identity_id: The identity ID
        badge: The badge to remove

    Returns:
        Information about the updated identity
    """
    try:
        logger.info(f"Removing badge {badge} from identity {identity_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Remove the badge from the identity's metadata
        if "badges" in identity.metadata and badge in identity.metadata["badges"]:
            identity.metadata["badges"].remove(badge)
            identity.save()

        # Remove the badge from the registry
        identities.remove_badge(identity_id, badge)

        logger.info(f"Badge {badge} removed from identity {identity_id}")
        return {
            "status": "badge_removed",
            "identity_id": identity_id,
            "badge": badge
        }
    except Exception as e:
        logger.error(f"Error removing badge: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/identity/{identity_id}/reputation")
def update_reputation(
    identity_id: str,
    reputation: int = Body(...),
    reason: Optional[str] = Body(None)
) -> Dict[str, Any]:
    """
    Update the reputation of an identity.

    Args:
        identity_id: The identity ID
        reputation: The reputation change (can be positive or negative)
        reason: The reason for the reputation change

    Returns:
        Information about the updated identity
    """
    try:
        logger.info(f"Updating reputation for identity {identity_id} by {reputation}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Update the reputation in the identity's metadata
        if "reputation" not in identity.metadata:
            identity.metadata["reputation"] = {}

        current_score = identity.metadata["reputation"].get("score", 0)
        identity.metadata["reputation"]["score"] = current_score + reputation

        # Add the reputation change to the history
        if "history" not in identity.metadata["reputation"]:
            identity.metadata["reputation"]["history"] = []

        identity.metadata["reputation"]["history"].append({
            "change": reputation,
            "reason": reason,
            "timestamp": int(time.time())
        })

        identity.save()

        # Update the reputation in the registry
        identities.update_reputation(identity_id, current_score + reputation)

        # Create a reputation transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="REPUTATION",
            recipient=identity_id,
            amount=reputation,
            status="CONFIRMED",
            data={
                "op": "OP_GRANT_REPUTATION",
                "to_id": identity_id,
                "amount": reputation,
                "reason": reason
            }
        )

        logger.info(f"Reputation updated for identity {identity_id}")
        return {
            "status": "reputation_updated",
            "identity_id": identity_id,
            "reputation": identity.metadata["reputation"]["score"],
            "change": reputation
        }
    except Exception as e:
        logger.error(f"Error updating reputation: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/identity/{identity_id}/etzem")
def get_etzem_score(identity_id: str) -> Dict[str, Any]:
    """
    Get the Etzem score of an identity.

    Args:
        identity_id: The identity ID

    Returns:
        The Etzem score
    """
    try:
        logger.info(f"Getting Etzem score for identity {identity_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Calculate the Etzem score
        etzem_score = etzem_engine.calculate_etzem_score(identity_id)

        # Store the Etzem score in the identity's metadata
        if "etzem" not in identity.metadata:
            identity.metadata["etzem"] = {}

        identity.metadata["etzem"]["score"] = etzem_score
        identity.metadata["etzem"]["last_updated"] = int(time.time())
        identity.save()

        logger.info(f"Etzem score for identity {identity_id}: {etzem_score}")
        return {
            "identity_id": identity_id,
            "etzem_score": etzem_score
        }
    except Exception as e:
        logger.error(f"Error getting Etzem score: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/identity/{identity_id}/roles")
def get_role_eligibility(identity_id: str) -> Dict[str, Any]:
    """
    Get the role eligibility of an identity.

    Args:
        identity_id: The identity ID

    Returns:
        The role eligibility
    """
    try:
        logger.info(f"Getting role eligibility for identity {identity_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check role eligibility
        eligibility = role_progression.check_role_eligibility(identity_id)

        logger.info(f"Role eligibility for identity {identity_id}: {eligibility}")
        return {
            "identity_id": identity_id,
            "eligibility": eligibility
        }
    except Exception as e:
        logger.error(f"Error getting role eligibility: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/identity/{identity_id}/roles")
def update_role_badges(identity_id: str) -> Dict[str, Any]:
    """
    Update the role badges of an identity.

    Args:
        identity_id: The identity ID

    Returns:
        The updated identity
    """
    try:
        logger.info(f"Updating role badges for identity {identity_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Update role badges
        role_progression.update_role_badges(identity_id)

        # Update the identity in the database
        identity = Identity.get_by_id(identity_id)

        logger.info(f"Role badges updated for identity {identity_id}")
        return {
            "status": "roles_updated",
            "identity": {
                "identity_id": identity.identity_id,
                "name": identity.name,
                "public_key": identity.public_key,
                "metadata": identity.metadata
            }
        }
    except Exception as e:
        logger.error(f"Error updating role badges: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/roles/update-all")
def update_all_role_badges() -> Dict[str, Any]:
    """
    Update the role badges of all identities.

    Returns:
        The updated identities
    """
    try:
        logger.info("Updating role badges for all identities")

        # Update role badges for all identities
        updated_identities = role_progression.update_all_role_badges()

        # Get the count of updated identities
        count = len(updated_identities)

        logger.info(f"Role badges updated for {count} identities")
        return {
            "status": "all_roles_updated",
            "count": count
        }
    except Exception as e:
        logger.error(f"Error updating all role badges: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/identity/{identity_id}/sela")
def register_sela(
    identity_id: str,
    name: str = Body(...),
    description: str = Body(...),
    category: str = Body(...),
    metadata: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Register a new Sela for an identity.

    Args:
        identity_id: The identity ID
        name: The Sela name
        description: The Sela description
        category: The Sela category
        metadata: Additional metadata for the Sela

    Returns:
        Information about the registered Sela
    """
    try:
        logger.info(f"Registering Sela {name} for identity {identity_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Generate a Sela ID
        sela_id = f"sela_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Prepare metadata
        if metadata is None:
            metadata = {}

        # Add the Sela to the identity's metadata
        if "founded_selas" not in identity.metadata:
            identity.metadata["founded_selas"] = []

        identity.metadata["founded_selas"].append(sela_id)
        identity.save()

        # Create a Sela registration transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SELA_REGISTRATION",
            sender=identity_id,
            status="CONFIRMED",
            data={
                "op": "OP_REGISTER_SELA",
                "sela_id": sela_id,
                "name": name,
                "description": description,
                "category": category,
                "founder": identity_id,
                "metadata": metadata
            }
        )

        logger.info(f"Sela registered: {sela_id}")
        return {
            "status": "sela_registered",
            "sela_id": sela_id,
            "name": name,
            "description": description,
            "category": category,
            "founder": identity_id
        }
    except Exception as e:
        logger.error(f"Error registering Sela: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
