version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: onnyx-api
    container_name: onnyx-api
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - onnyx-data:/data
    environment:
      - ONNYX_ENV=production
      - ONNYX_DATA_DIR=/data
    command: --host 0.0.0.0 --port 5000 --workers 4 --log-level info
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  node:
    build:
      context: .
      dockerfile: Dockerfile
    image: onnyx-node
    container_name: onnyx-node
    restart: unless-stopped
    ports:
      - "5001:5001"
    volumes:
      - onnyx-data:/data
    environment:
      - ONNYX_ENV=production
      - ONNYX_DATA_DIR=/data
    command: python run_node.py --host 0.0.0.0 --port 5001 --log-level info
    depends_on:
      - api

  sela-miner:
    build:
      context: .
      dockerfile: Dockerfile
    image: onnyx-sela-miner
    container_name: onnyx-sela-miner
    restart: unless-stopped
    volumes:
      - onnyx-data:/data
    environment:
      - ONNYX_ENV=production
      - ONNYX_DATA_DIR=/data
    command: python run_sela_miner.py --log-level info
    depends_on:
      - node

  transaction-viewer:
    build:
      context: .
      dockerfile: Dockerfile
    image: onnyx-transaction-viewer
    container_name: onnyx-transaction-viewer
    restart: unless-stopped
    ports:
      - "5002:5002"
    volumes:
      - onnyx-data:/data
    environment:
      - ONNYX_ENV=production
      - ONNYX_DATA_DIR=/data
    command: python run_transaction_viewer.py --host 0.0.0.0 --port 5002 --log-level info
    depends_on:
      - api

volumes:
  onnyx-data:
    name: onnyx-data
