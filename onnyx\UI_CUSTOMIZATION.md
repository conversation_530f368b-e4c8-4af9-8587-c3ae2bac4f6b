# Onnyx API UI Customization

This document describes the customization of the Onnyx API documentation UI.

## Overview

The Onnyx API documentation UI has been customized to provide a more professional and branded experience. The customization includes:

- Custom header with Onnyx logo
- Custom styling with Onnyx brand colors
- Custom footer with information about the Onnyx blockchain
- Responsive design for mobile and desktop

## Components

### Custom CSS

The custom CSS file (`api/static/custom.css`) defines the styling for the API documentation UI. It includes:

- Onnyx brand colors
- Custom header and footer styles
- Swagger UI customizations
- Responsive design adjustments

### Custom HTML Template

The custom HTML template (`api/templates/custom_docs.html`) defines the structure of the API documentation UI. It includes:

- Custom header with Onnyx logo
- Swagger UI container
- Custom footer with information about the Onnyx blockchain

### Static Files

The static files directory (`api/static`) contains:

- Onnyx logo (`onnyx_logo.png`)
- Custom CSS file (`custom.css`)
- Favicon (`favicon.png`)

## Implementation

### FastAPI Configuration

The FastAPI application is configured to use the custom template and serve static files:

```python
# Mount static files
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "static")), name="static")

# Custom docs route
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    return templates.TemplateResponse(
        "custom_docs.html",
        {
            "request": request,
            "title": "Onnyx Blockchain API",
            "openapi_url": app.openapi_url,
            "swagger_ui_css": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
            "oauth2_redirect_url": app.swagger_ui_oauth2_redirect_url,
            "csrf_token": "",
        }
    )
```

### Root Endpoint Redirect

The root endpoint is configured to redirect to the API documentation:

```python
@app.get("/")
async def read_root():
    """
    Root endpoint - redirects to docs.
    
    Returns:
        RedirectResponse: Redirect to docs
    """
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/docs")
```

## Usage

To run the API server with the custom UI:

```bash
python run_server.py --port 8890
```

Then open a browser and navigate to:

```
http://127.0.0.1:8890/
```

## Customization

### Changing the Logo

To change the logo, replace the `onnyx_logo.png` file in the `api/static` directory.

### Changing the Colors

To change the colors, edit the CSS variables in the `api/static/custom.css` file:

```css
:root {
  --onnyx-primary: #3a506b;
  --onnyx-secondary: #5bc0be;
  --onnyx-accent: #ffa62b;
  --onnyx-dark: #1c2541;
  --onnyx-light: #f5f5f5;
  --onnyx-text: #333333;
  --onnyx-text-light: #f5f5f5;
  --onnyx-border: #e0e0e0;
}
```

### Changing the Footer Content

To change the footer content, edit the footer section in the `api/templates/custom_docs.html` file:

```html
<footer class="onnyx-footer">
    <div class="onnyx-footer-content">
        <h2 class="onnyx-footer-title">Onnyx Blockchain</h2>
        <p class="onnyx-footer-description">
            Onnyx is a next-generation blockchain platform focused on identity-centric economics, 
            real-world business integration, and sustainable governance. Our mission is to create 
            a blockchain ecosystem that rewards productive economic activity and fosters trust.
        </p>
        <div class="onnyx-footer-links">
            <a href="#" class="onnyx-footer-link">Documentation</a>
            <a href="#" class="onnyx-footer-link">GitHub</a>
            <a href="#" class="onnyx-footer-link">Community</a>
            <a href="#" class="onnyx-footer-link">Governance</a>
        </div>
        <p class="onnyx-footer-copyright">
            &copy; 2025 Onnyx Blockchain. All rights reserved.
        </p>
    </div>
</footer>
```
