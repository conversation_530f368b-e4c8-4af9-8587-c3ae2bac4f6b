"""
Onnyx Chain Parameters Routes

This module provides API routes for chain parameters.
"""

import logging
import time
import uuid
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from shared.config.chain_parameters import chain_parameters
from governance.voice_scroll import voice_scrolls

from shared.models.chain_parameter import ChainParameter
from shared.models.identity import Identity
from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.routes.chain_parameters")

# Create router
router = APIRouter()

@router.get("/chain-parameters")
def get_chain_parameters() -> Dict[str, Any]:
    """
    Get all chain parameters.

    Returns:
        All chain parameters
    """
    logger.info("Getting all chain parameters")

    # Get all parameters from the database
    parameters = ChainParameter.get_all()

    # If no parameters exist, initialize them from the chain_parameters module
    if not parameters:
        logger.info("Initializing chain parameters")

        # Get all parameters from the chain_parameters module
        all_params = chain_parameters.all()

        # Create parameters in the database
        parameters = []
        for key, value in all_params.items():
            # Get the default value and description
            default_value = chain_parameters.get_default(key)
            description = chain_parameters.get_description(key)
            category = chain_parameters.get_category(key)

            # Create the parameter in the database
            parameter = ChainParameter.create(
                key=key,
                value=value,
                default_value=default_value,
                description=description,
                category=category
            )

            parameters.append(parameter)

    logger.info(f"Retrieved {len(parameters)} chain parameters")
    return {
        "parameters": [param.to_dict() for param in parameters]
    }

@router.get("/chain-parameters/{key}")
def get_chain_parameter(key: str) -> Dict[str, Any]:
    """
    Get a chain parameter.

    Args:
        key: The parameter key

    Returns:
        The parameter value
    """
    logger.info(f"Getting chain parameter: {key}")

    # Get the parameter from the database
    parameter = ChainParameter.get_by_id(key)

    # If the parameter doesn't exist, try to get it from the chain_parameters module
    if not parameter:
        logger.info(f"Chain parameter {key} not found in database, checking module")

        # Get the parameter from the chain_parameters module
        value = chain_parameters.get(key)
        if value is None:
            logger.warning(f"Chain parameter {key} not found")
            raise HTTPException(status_code=404, detail=f"Chain parameter '{key}' not found")

        # Get the default value and description
        default_value = chain_parameters.get_default(key)
        description = chain_parameters.get_description(key)
        category = chain_parameters.get_category(key)

        # Create the parameter in the database
        parameter = ChainParameter.create(
            key=key,
            value=value,
            default_value=default_value,
            description=description,
            category=category
        )

    logger.info(f"Retrieved chain parameter {key}")
    return parameter.to_dict()

@router.post("/chain-parameters/{key}")
def set_chain_parameter(
    key: str,
    value: Any = Body(..., embed=True),
    updated_by: Optional[str] = Body(None)
) -> Dict[str, Any]:
    """
    Set a chain parameter.

    Args:
        key: The parameter key
        value: The parameter value
        updated_by: The identity ID of the updater (optional)

    Returns:
        The updated parameter
    """
    try:
        logger.info(f"Setting chain parameter {key} to {value}")

        # Check if the updater exists
        if updated_by:
            identity = Identity.get_by_id(updated_by)
            if not identity:
                logger.warning(f"Identity {updated_by} not found")
                raise Exception(f"Identity {updated_by} not found")

        # Set the parameter in the chain_parameters module
        chain_parameters.set(key, value)

        # Get the parameter from the database
        parameter = ChainParameter.get_by_id(key)

        # If the parameter doesn't exist, create it
        if not parameter:
            logger.info(f"Chain parameter {key} not found in database, creating")

            # Get the default value and description
            default_value = chain_parameters.get_default(key)
            description = chain_parameters.get_description(key)
            category = chain_parameters.get_category(key)

            # Create the parameter in the database
            parameter = ChainParameter.create(
                key=key,
                value=value,
                default_value=default_value,
                description=description,
                category=category,
                last_updated_by=updated_by
            )
        else:
            # Update the parameter in the database
            parameter.update(value, updated_by)

        # Create a transaction for the parameter update
        if updated_by:
            tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            Transaction.create(
                tx_id=tx_id,
                tx_type="PARAMETER_UPDATE",
                sender=updated_by,
                status="CONFIRMED",
                data={
                    "op": "OP_SET_PARAMETER",
                    "key": key,
                    "value": value,
                    "previous_value": parameter.value
                }
            )

        logger.info(f"Chain parameter {key} set to {value}")
        return {
            "key": key,
            "value": value,
            "status": "success",
            "parameter": parameter.to_dict()
        }
    except Exception as e:
        logger.error(f"Error setting chain parameter: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/chain-parameters/reset/{key}")
def reset_chain_parameter(
    key: str,
    updated_by: Optional[str] = Body(None)
) -> Dict[str, Any]:
    """
    Reset a chain parameter to its default value.

    Args:
        key: The parameter key
        updated_by: The identity ID of the updater (optional)

    Returns:
        The reset parameter
    """
    try:
        logger.info(f"Resetting chain parameter {key}")

        # Check if the updater exists
        if updated_by:
            identity = Identity.get_by_id(updated_by)
            if not identity:
                logger.warning(f"Identity {updated_by} not found")
                raise Exception(f"Identity {updated_by} not found")

        # Reset the parameter in the chain_parameters module
        chain_parameters.reset(key)

        # Get the parameter from the database
        parameter = ChainParameter.get_by_id(key)

        # If the parameter doesn't exist, create it
        if not parameter:
            logger.info(f"Chain parameter {key} not found in database, creating")

            # Get the default value and description
            default_value = chain_parameters.get_default(key)
            description = chain_parameters.get_description(key)
            category = chain_parameters.get_category(key)

            # Create the parameter in the database
            parameter = ChainParameter.create(
                key=key,
                value=default_value,
                default_value=default_value,
                description=description,
                category=category,
                last_updated_by=updated_by
            )
        else:
            # Reset the parameter in the database
            parameter.reset()
            if updated_by:
                parameter.last_updated_by = updated_by
                parameter.save()

        # Create a transaction for the parameter reset
        if updated_by:
            tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            Transaction.create(
                tx_id=tx_id,
                tx_type="PARAMETER_RESET",
                sender=updated_by,
                status="CONFIRMED",
                data={
                    "op": "OP_RESET_PARAMETER",
                    "key": key,
                    "value": parameter.value
                }
            )

        logger.info(f"Chain parameter {key} reset to {parameter.value}")
        return {
            "key": key,
            "value": parameter.value,
            "status": "reset",
            "parameter": parameter.to_dict()
        }
    except Exception as e:
        logger.error(f"Error resetting chain parameter: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/chain-parameters/reset-all")
def reset_all_chain_parameters(
    updated_by: Optional[str] = Body(None)
) -> Dict[str, Any]:
    """
    Reset all chain parameters to their default values.

    Args:
        updated_by: The identity ID of the updater (optional)

    Returns:
        The reset parameters
    """
    try:
        logger.info("Resetting all chain parameters")

        # Check if the updater exists
        if updated_by:
            identity = Identity.get_by_id(updated_by)
            if not identity:
                logger.warning(f"Identity {updated_by} not found")
                raise Exception(f"Identity {updated_by} not found")

        # Reset all parameters in the chain_parameters module
        chain_parameters.reset_all()

        # Get all parameters from the database
        parameters = ChainParameter.get_all()

        # Reset all parameters in the database
        for parameter in parameters:
            parameter.reset()
            if updated_by:
                parameter.last_updated_by = updated_by
                parameter.save()

        # Create a transaction for the parameter reset
        if updated_by:
            tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            Transaction.create(
                tx_id=tx_id,
                tx_type="PARAMETER_RESET_ALL",
                sender=updated_by,
                status="CONFIRMED",
                data={
                    "op": "OP_RESET_ALL_PARAMETERS"
                }
            )

        logger.info("All chain parameters reset")
        return {
            "parameters": [param.to_dict() for param in parameters],
            "status": "reset"
        }
    except Exception as e:
        logger.error(f"Error resetting all chain parameters: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/chain-parameters/propose")
def propose_chain_parameter_change(
    creator_id: str = Body(...),
    title: str = Body(...),
    description: str = Body(...),
    param: str = Body(...),
    value: Any = Body(...),
    expiry_days: int = Body(7)
) -> Dict[str, Any]:
    """
    Propose a chain parameter change.

    Args:
        creator_id: The identity ID of the creator
        title: The title of the scroll
        description: The description of the scroll
        param: The parameter to change
        value: The new value for the parameter
        expiry_days: The number of days until the scroll expires

    Returns:
        The created scroll
    """
    try:
        logger.info(f"Proposing chain parameter change: {param} to {value}")

        # Check if the creator exists
        identity = Identity.get_by_id(creator_id)
        if not identity:
            logger.warning(f"Identity {creator_id} not found")
            raise Exception(f"Identity {creator_id} not found")

        # Check if the parameter exists
        parameter = ChainParameter.get_by_id(param)
        if not parameter:
            # Try to get the parameter from the chain_parameters module
            module_value = chain_parameters.get(param)
            if module_value is None:
                logger.warning(f"Chain parameter {param} not found")
                raise Exception(f"Chain parameter {param} not found")

            # Get the default value and description
            default_value = chain_parameters.get_default(param)
            description = chain_parameters.get_description(param)
            category = chain_parameters.get_category(param)

            # Create the parameter in the database
            parameter = ChainParameter.create(
                key=param,
                value=module_value,
                default_value=default_value,
                description=description,
                category=category
            )

        # Create a scroll with an effect
        scroll = voice_scrolls.create_scroll(
            creator_id=creator_id,
            title=title,
            description=description,
            category="economic",
            expiry_days=expiry_days,
            effect={
                "param": param,
                "value": value
            }
        )

        # Create a transaction for the parameter change proposal
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="PARAMETER_PROPOSAL",
            sender=creator_id,
            status="CONFIRMED",
            data={
                "op": "OP_PROPOSE_PARAMETER_CHANGE",
                "scroll_id": scroll["scroll_id"],
                "param": param,
                "value": value,
                "current_value": parameter.value
            }
        )

        logger.info(f"Chain parameter change proposed: {param} to {value}")
        return {
            "scroll": scroll,
            "status": "proposed"
        }
    except Exception as e:
        logger.error(f"Error proposing chain parameter change: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/chain-parameters/proposals")
def get_chain_parameter_proposals(
    status: Optional[str] = None,
    outcome: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get chain parameter proposals.

    Args:
        status: Filter by status (optional)
        outcome: Filter by outcome (optional)

    Returns:
        A list of chain parameter proposals
    """
    try:
        logger.info(f"Getting chain parameter proposals with status={status}, outcome={outcome}")

        # Get scrolls with category "economic"
        scrolls = voice_scrolls.get_scrolls(
            status=status,
            category="economic",
            outcome=outcome
        )

        # Filter scrolls with an effect
        proposals = [s for s in scrolls if "effect" in s and "param" in s["effect"]]

        # Enhance proposals with parameter details
        enhanced_proposals = []
        for proposal in proposals:
            param_key = proposal["effect"]["param"]
            param_value = proposal["effect"]["value"]

            # Get the parameter from the database
            parameter = ChainParameter.get_by_id(param_key)

            # Add parameter details to the proposal
            enhanced_proposal = proposal.copy()
            enhanced_proposal["parameter"] = parameter.to_dict() if parameter else None
            enhanced_proposal["current_value"] = parameter.value if parameter else None
            enhanced_proposal["proposed_value"] = param_value

            enhanced_proposals.append(enhanced_proposal)

        logger.info(f"Retrieved {len(enhanced_proposals)} chain parameter proposals")
        return {
            "proposals": enhanced_proposals
        }
    except Exception as e:
        logger.error(f"Error getting chain parameter proposals: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/chain-parameters/categories")
def get_chain_parameter_categories() -> Dict[str, List[str]]:
    """
    Get all chain parameter categories.

    Returns:
        A list of chain parameter categories
    """
    try:
        logger.info("Getting chain parameter categories")

        # Get all parameters from the database
        parameters = ChainParameter.get_all()

        # Extract unique categories
        categories = list(set(param.category for param in parameters))

        logger.info(f"Retrieved {len(categories)} chain parameter categories")
        return {
            "categories": categories
        }
    except Exception as e:
        logger.error(f"Error getting chain parameter categories: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/chain-parameters/category/{category}")
def get_chain_parameters_by_category(category: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get chain parameters by category.

    Args:
        category: The category to filter by

    Returns:
        A list of chain parameters in the category
    """
    try:
        logger.info(f"Getting chain parameters in category: {category}")

        # Get parameters by category from the database
        parameters = ChainParameter.find_by_category(category)

        logger.info(f"Retrieved {len(parameters)} chain parameters in category {category}")
        return {
            "category": category,
            "parameters": [param.to_dict() for param in parameters]
        }
    except Exception as e:
        logger.error(f"Error getting chain parameters by category: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
