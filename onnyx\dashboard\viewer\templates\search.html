{% extends "base.html" %}

{% block title %}Search Results - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Search Results for "{{ query }}"</h2>
    </div>
    
    {% if not query %}
    <div class="card">
        <p>Enter a search query to find transactions, identities, or tokens.</p>
    </div>
    {% elif not results or (results.transactions|length == 0 and results.identities|length == 0 and results.tokens|length == 0) %}
    <div class="card">
        <p>No results found for "{{ query }}".</p>
    </div>
    {% else %}
    
    {% if results.transactions|length > 0 %}
    <div class="section">
        <div class="section-header">
            <h3 class="section-title">Transactions ({{ results.transactions|length }})</h3>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Transaction ID</th>
                    <th>Block</th>
                    <th>Type</th>
                    <th>From</th>
                    <th>To</th>
                </tr>
            </thead>
            <tbody>
                {% for tx in results.transactions %}
                <tr>
                    <td><a href="{{ url_for('tx_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a></td>
                    <td>{{ tx.block }}</td>
                    <td>
                        <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
                    </td>
                    <td>
                        {% if tx.data.from %}
                        <a href="{{ url_for('identity_detail', identity_id=tx.data.from) }}">{{ tx.data.from[:10] }}...</a>
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    <td>
                        {% if tx.data.to %}
                        <a href="{{ url_for('identity_detail', identity_id=tx.data.to) }}">{{ tx.data.to[:10] }}...</a>
                        {% else %}
                        -
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    {% if results.identities|length > 0 %}
    <div class="section">
        <div class="section-header">
            <h3 class="section-title">Identities ({{ results.identities|length }})</h3>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Identity ID</th>
                    <th>Name</th>
                    <th>Reputation Score</th>
                </tr>
            </thead>
            <tbody>
                {% for identity in results.identities %}
                <tr>
                    <td><a href="{{ url_for('identity_detail', identity_id=identity.id) }}">{{ identity.id[:10] }}...</a></td>
                    <td>{{ identity.name }}</td>
                    <td>{{ identity.reputation if identity.reputation is defined else "N/A" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    {% if results.tokens|length > 0 %}
    <div class="section">
        <div class="section-header">
            <h3 class="section-title">Tokens ({{ results.tokens|length }})</h3>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Token ID</th>
                    <th>Name</th>
                    <th>Symbol</th>
                    <th>Creator</th>
                    <th>Type</th>
                </tr>
            </thead>
            <tbody>
                {% for token in results.tokens %}
                <tr>
                    <td><a href="{{ url_for('token_detail', token_id=token.id) }}">{{ token.id[:10] }}...</a></td>
                    <td>{{ token.name }}</td>
                    <td>{{ token.symbol }}</td>
                    <td>
                        {% if token.creator %}
                        <a href="{{ url_for('identity_detail', identity_id=token.creator) }}">{{ token.creator[:10] }}...</a>
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    <td>{{ token.type }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    {% endif %}
</div>
{% endblock %}
