"""
Onnyx API Deployment Script

This script deploys the Onnyx API to a production environment.
"""

import argparse
import logging
import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("onnyx.deploy_api")

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Deploy the Onnyx API")
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to run the API on (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=5000,
        help="Port to run the API on (default: 5000)"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Number of worker processes (default: 4)"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Log level (default: info)"
    )
    parser.add_argument(
        "--env",
        type=str,
        default="production",
        choices=["development", "staging", "production"],
        help="Deployment environment (default: production)"
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        default="data",
        help="Data directory (default: data)"
    )
    parser.add_argument(
        "--backup",
        action="store_true",
        help="Backup data before deployment (default: False)"
    )
    parser.add_argument(
        "--service-name",
        type=str,
        default="onnyx-api",
        help="Systemd service name (default: onnyx-api)"
    )
    parser.add_argument(
        "--use-systemd",
        action="store_true",
        help="Use systemd to manage the service (default: False)"
    )
    return parser.parse_args()

def backup_data(data_dir):
    """Backup data directory."""
    logger.info(f"Backing up data directory: {data_dir}")
    
    # Create backup directory if it doesn't exist
    backup_dir = Path("backups")
    backup_dir.mkdir(exist_ok=True)
    
    # Create backup filename with timestamp
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    backup_file = backup_dir / f"onnyx-data-{timestamp}.tar.gz"
    
    # Create backup
    try:
        subprocess.run(
            ["tar", "-czf", str(backup_file), data_dir],
            check=True
        )
        logger.info(f"Backup created: {backup_file}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error creating backup: {e}")
        sys.exit(1)

def create_systemd_service(args):
    """Create systemd service file."""
    logger.info(f"Creating systemd service: {args.service_name}")
    
    # Get current directory
    current_dir = os.path.abspath(os.path.dirname(__file__))
    
    # Create service file content
    service_content = f"""[Unit]
Description=Onnyx API
After=network.target

[Service]
User={os.getenv('USER')}
WorkingDirectory={current_dir}
ExecStart={sys.executable} -m gunicorn api.api_updated:app -b {args.host}:{args.port} -w {args.workers} --log-level {args.log_level}
Restart=always
Environment="ONNYX_ENV={args.env}"
Environment="ONNYX_DATA_DIR={args.data_dir}"

[Install]
WantedBy=multi-user.target
"""
    
    # Write service file
    service_file = f"/tmp/{args.service_name}.service"
    with open(service_file, "w") as f:
        f.write(service_content)
    
    # Install service
    try:
        subprocess.run(
            ["sudo", "mv", service_file, f"/etc/systemd/system/{args.service_name}.service"],
            check=True
        )
        subprocess.run(
            ["sudo", "systemctl", "daemon-reload"],
            check=True
        )
        subprocess.run(
            ["sudo", "systemctl", "enable", args.service_name],
            check=True
        )
        logger.info(f"Systemd service created: {args.service_name}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error creating systemd service: {e}")
        sys.exit(1)

def start_service(service_name):
    """Start systemd service."""
    logger.info(f"Starting service: {service_name}")
    
    try:
        subprocess.run(
            ["sudo", "systemctl", "start", service_name],
            check=True
        )
        logger.info(f"Service started: {service_name}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error starting service: {e}")
        sys.exit(1)

def install_dependencies():
    """Install dependencies."""
    logger.info("Installing dependencies")
    
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            check=True
        )
        logger.info("Dependencies installed")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error installing dependencies: {e}")
        sys.exit(1)

def create_data_directory(data_dir):
    """Create data directory."""
    logger.info(f"Creating data directory: {data_dir}")
    
    # Create data directory if it doesn't exist
    Path(data_dir).mkdir(exist_ok=True)
    
    # Create subdirectories
    for subdir in ["db", "logs", "config"]:
        Path(data_dir, subdir).mkdir(exist_ok=True)
    
    logger.info(f"Data directory created: {data_dir}")

def main():
    """Deploy the Onnyx API."""
    args = parse_args()
    
    # Set log level
    log_level = getattr(logging, args.log_level.upper())
    logging.getLogger("onnyx").setLevel(log_level)
    
    logger.info(f"Deploying Onnyx API in {args.env} environment")
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    logger.info(f"Workers: {args.workers}")
    logger.info(f"Log level: {args.log_level.upper()}")
    logger.info(f"Data directory: {args.data_dir}")
    
    # Create data directory
    create_data_directory(args.data_dir)
    
    # Backup data if requested
    if args.backup:
        backup_data(args.data_dir)
    
    # Install dependencies
    install_dependencies()
    
    # Create systemd service if requested
    if args.use_systemd:
        create_systemd_service(args)
        start_service(args.service_name)
        logger.info(f"Onnyx API deployed and running as systemd service: {args.service_name}")
        logger.info(f"Check status with: sudo systemctl status {args.service_name}")
        logger.info(f"View logs with: sudo journalctl -u {args.service_name}")
    else:
        # Run the API directly
        logger.info("Starting Onnyx API")
        os.environ["ONNYX_ENV"] = args.env
        os.environ["ONNYX_DATA_DIR"] = args.data_dir
        
        try:
            subprocess.run(
                [
                    sys.executable, "-m", "gunicorn", "api.api_updated:app",
                    "-b", f"{args.host}:{args.port}",
                    "-w", str(args.workers),
                    "--log-level", args.log_level
                ],
                check=True
            )
        except KeyboardInterrupt:
            logger.info("Onnyx API stopped")
        except subprocess.CalledProcessError as e:
            logger.error(f"Error running Onnyx API: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
