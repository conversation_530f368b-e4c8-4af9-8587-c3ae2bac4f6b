"""
Onnyx Database-Backed Token Ledger Module

This module provides a SQLite-backed token ledger implementation.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.tokens.db_ledger")

class DBTokenLedger:
    """
    DBTokenLedger manages token balances in the Onnyx ecosystem using a SQLite database.
    """
    
    def __init__(self):
        """Initialize the DBTokenLedger."""
        # Check if the balances table exists
        if not db.table_exists("balances"):
            logger.warning("Balances table does not exist.")
    
    def get_balance(self, identity_id: str, token_id: str) -> float:
        """
        Get the balance of a token for an identity.
        
        Args:
            identity_id: The identity ID
            token_id: The token ID
        
        Returns:
            The balance
        """
        try:
            # Query the database for the balance
            balance = db.query_one(
                "SELECT amount FROM balances WHERE identity_id = ? AND token_id = ?",
                (identity_id, token_id)
            )
            
            return balance["amount"] if balance else 0
        except Exception as e:
            logger.error(f"Error getting balance: {str(e)}")
            return 0
    
    def get_all_balances(self, identity_id: str) -> Dict[str, float]:
        """
        Get all token balances for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            A dictionary of token balances
        """
        try:
            # Query the database for the balances
            balances = db.query(
                "SELECT token_id, amount FROM balances WHERE identity_id = ?",
                (identity_id,)
            )
            
            return {balance["token_id"]: balance["amount"] for balance in balances}
        except Exception as e:
            logger.error(f"Error getting all balances: {str(e)}")
            return {}
    
    def get_token_holders(self, token_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all holders of a token.
        
        Args:
            token_id: The token ID
            limit: The maximum number of holders to return
            offset: The number of holders to skip
        
        Returns:
            A list of holders with their balances
        """
        try:
            # Query the database for the holders
            holders = db.query(
                """
                SELECT b.identity_id, b.amount, i.name
                FROM balances b
                JOIN identities i ON b.identity_id = i.id
                WHERE b.token_id = ? AND b.amount > 0
                ORDER BY b.amount DESC
                LIMIT ? OFFSET ?
                """,
                (token_id, limit, offset)
            )
            
            return holders
        except Exception as e:
            logger.error(f"Error getting token holders: {str(e)}")
            return []
    
    def credit(self, identity_id: str, token_id: str, amount: float) -> bool:
        """
        Credit tokens to an identity.
        
        Args:
            identity_id: The identity ID
            token_id: The token ID
            amount: The amount to credit
        
        Returns:
            True if the tokens were credited, False otherwise
        """
        try:
            if amount <= 0:
                return False
            
            # Check if the identity has a balance for this token
            current_balance = self.get_balance(identity_id, token_id)
            
            if current_balance > 0:
                # Update the balance
                db.update(
                    "balances",
                    {
                        "amount": current_balance + amount,
                        "updated_at": int(time.time())
                    },
                    "identity_id = ? AND token_id = ?",
                    (identity_id, token_id)
                )
            else:
                # Insert a new balance
                db.insert("balances", {
                    "identity_id": identity_id,
                    "token_id": token_id,
                    "amount": amount,
                    "updated_at": int(time.time())
                })
            
            return True
        except Exception as e:
            logger.error(f"Error crediting tokens: {str(e)}")
            return False
    
    def debit(self, identity_id: str, token_id: str, amount: float) -> bool:
        """
        Debit tokens from an identity.
        
        Args:
            identity_id: The identity ID
            token_id: The token ID
            amount: The amount to debit
        
        Returns:
            True if the tokens were debited, False otherwise
        """
        try:
            if amount <= 0:
                return False
            
            # Check if the identity has enough tokens
            current_balance = self.get_balance(identity_id, token_id)
            
            if current_balance < amount:
                return False
            
            # Update the balance
            db.update(
                "balances",
                {
                    "amount": current_balance - amount,
                    "updated_at": int(time.time())
                },
                "identity_id = ? AND token_id = ?",
                (identity_id, token_id)
            )
            
            return True
        except Exception as e:
            logger.error(f"Error debiting tokens: {str(e)}")
            return False
    
    def transfer(self, from_id: str, to_id: str, token_id: str, amount: float) -> bool:
        """
        Transfer tokens from one identity to another.
        
        Args:
            from_id: The sender's identity ID
            to_id: The recipient's identity ID
            token_id: The token ID
            amount: The amount to transfer
        
        Returns:
            True if the tokens were transferred, False otherwise
        """
        try:
            if amount <= 0:
                return False
            
            # Check if the sender has enough tokens
            if not self.debit(from_id, token_id, amount):
                return False
            
            # Credit the recipient
            if not self.credit(to_id, token_id, amount):
                # Rollback the debit
                self.credit(from_id, token_id, amount)
                return False
            
            return True
        except Exception as e:
            logger.error(f"Error transferring tokens: {str(e)}")
            return False
    
    def get_total_supply(self, token_id: str) -> float:
        """
        Get the total supply of a token.
        
        Args:
            token_id: The token ID
        
        Returns:
            The total supply
        """
        try:
            # Query the database for the total supply
            result = db.query_one(
                "SELECT SUM(amount) as total FROM balances WHERE token_id = ?",
                (token_id,)
            )
            
            return result["total"] if result and result["total"] is not None else 0
        except Exception as e:
            logger.error(f"Error getting total supply: {str(e)}")
            return 0
    
    def get_transaction_history(self, identity_id: Optional[str] = None, token_id: Optional[str] = None, 
                               limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get transaction history.
        
        Args:
            identity_id: Filter by identity ID (optional)
            token_id: Filter by token ID (optional)
            limit: The maximum number of transactions to return
            offset: The number of transactions to skip
        
        Returns:
            A list of transactions
        """
        try:
            # Build the query
            query = "SELECT * FROM transactions WHERE 1=1"
            params = []
            
            if identity_id:
                query += " AND (sender = ? OR recipient = ?)"
                params.extend([identity_id, identity_id])
            
            if token_id:
                query += " AND token_id = ?"
                params.append(token_id)
            
            query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            # Query the database for the transactions
            transactions = db.query(query, tuple(params))
            
            # Process the transactions
            for tx in transactions:
                # Parse the data field
                tx["data"] = json.loads(tx["data"]) if tx["data"] else {}
                
                # Rename fields to match the expected format
                if "sender" in tx:
                    tx["from"] = tx["sender"]
                    del tx["sender"]
                
                if "recipient" in tx:
                    tx["to"] = tx["recipient"]
                    del tx["recipient"]
            
            return transactions
        except Exception as e:
            logger.error(f"Error getting transaction history: {str(e)}")
            return []

# Create a global instance of the DBTokenLedger
db_token_ledger = DBTokenLedger()
