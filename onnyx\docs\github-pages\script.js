// ONNYX Platform - GitHub Pages Frontend
// Connects to Render backend for dynamic functionality

// Configuration
let API_BASE = 'https://your-onnyx-app.onrender.com'; // Update with your Render URL

// Auto-detect environment
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    API_BASE = 'http://127.0.0.1:5000';
}

// Global state
let networkData = {
    validators: [],
    blocks: [],
    stats: {}
};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 ONNYX Platform initialized');
    console.log('Frontend: GitHub Pages');
    console.log('Backend:', API_BASE);
    
    // Load initial data
    loadNetworkStats();
    loadValidators();
    loadBlockchainData();
    
    // Setup periodic updates
    setInterval(loadNetworkStats, 30000); // Update every 30 seconds
    setInterval(loadBlockchainData, 60000); // Update every minute
    
    // Setup navigation
    setupNavigation();
    setupScrollEffects();
});

// Navigation functions
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Scroll to section
            const targetId = this.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function toggleMobileMenu() {
    const navMenu = document.getElementById('navMenu');
    const mobileToggle = document.querySelector('.mobile-toggle');
    
    navMenu.classList.toggle('active');
    mobileToggle.classList.toggle('active');
}

// Backend connection functions
function redirectToBackend(path) {
    const fullUrl = `${API_BASE}${path}`;
    console.log('Redirecting to backend:', fullUrl);
    window.open(fullUrl, '_blank');
}

async function fetchFromBackend(endpoint) {
    try {
        const response = await fetch(`${API_BASE}${endpoint}`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Backend fetch error (${endpoint}):`, error);
        return null;
    }
}

// Data loading functions
async function loadNetworkStats() {
    console.log('Loading network statistics...');
    
    try {
        const stats = await fetchFromBackend('/api/network/stats');
        
        if (stats) {
            updateNetworkStats(stats);
            updateNetworkStatus(true);
        } else {
            // Fallback to static data
            updateNetworkStats({
                validators: 3,
                blocks: 4,
                transactions: 5,
                onx_supply: 1000000,
                network_hash_rate: '16x'
            });
            updateNetworkStatus(false);
        }
    } catch (error) {
        console.error('Failed to load network stats:', error);
        updateNetworkStatus(false);
    }
}

async function loadValidators() {
    console.log('Loading validators...');
    
    const loadingElement = document.getElementById('validatorsLoading');
    const gridElement = document.getElementById('validatorsGrid');
    
    if (loadingElement) loadingElement.style.display = 'block';
    if (gridElement) gridElement.innerHTML = '';
    
    try {
        const validators = await fetchFromBackend('/api/validators');
        
        if (validators && validators.length > 0) {
            networkData.validators = validators;
            renderValidators(validators);
        } else {
            // Fallback to static validator data
            renderStaticValidators();
        }
    } catch (error) {
        console.error('Failed to load validators:', error);
        renderStaticValidators();
    } finally {
        if (loadingElement) loadingElement.style.display = 'none';
    }
}

async function loadBlockchainData() {
    console.log('Loading blockchain data...');
    
    const loadingElement = document.getElementById('blocksLoading');
    const blocksElement = document.getElementById('blocksList');
    
    if (loadingElement) loadingElement.style.display = 'block';
    
    try {
        const blocks = await fetchFromBackend('/api/blocks/latest');
        
        if (blocks && blocks.length > 0) {
            networkData.blocks = blocks;
            renderBlocks(blocks);
        } else {
            // Fallback to static block data
            renderStaticBlocks();
        }
    } catch (error) {
        console.error('Failed to load blockchain data:', error);
        renderStaticBlocks();
    } finally {
        if (loadingElement) loadingElement.style.display = 'none';
    }
}

// UI update functions
function updateNetworkStats(stats) {
    // Update hero stats
    const elements = {
        validatorCount: stats.validators || 3,
        blockCount: stats.blocks || 4,
        onxSupply: formatNumber(stats.onx_supply || 1000000),
        totalBlocks: stats.blocks || 4,
        totalTransactions: stats.transactions || 5,
        activeValidators: stats.validators || 3,
        networkHashRate: stats.network_hash_rate || '16x'
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
    
    // Update footer stats
    const footerElements = {
        footerValidators: stats.validators || 3,
        footerBlocks: stats.blocks || 4,
        footerSupply: formatNumber(stats.onx_supply || 1000000),
        footerUptime: '99.9%'
    };
    
    Object.entries(footerElements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

function updateNetworkStatus(isOnline) {
    const statusDot = document.getElementById('networkStatus');
    const statusText = document.getElementById('networkStatusText');
    
    if (statusDot && statusText) {
        if (isOnline) {
            statusDot.className = 'status-dot active';
            statusText.textContent = 'Network Online';
        } else {
            statusDot.className = 'status-dot';
            statusText.textContent = 'Demo Mode';
        }
    }
}

function renderValidators(validators) {
    const gridElement = document.getElementById('validatorsGrid');
    if (!gridElement) return;
    
    gridElement.innerHTML = validators.map(validator => `
        <div class="validator-card">
            <div class="card-header">
                <div class="validator-info">
                    <h3 class="validator-name">${validator.name}</h3>
                    <div class="validator-meta">
                        <span class="category">${validator.category}</span>
                        <span class="status ${validator.status}">${validator.status.toUpperCase()}</span>
                    </div>
                </div>
                <div class="mining-badge ${validator.mining_tier}">${validator.mining_tier.replace('_', ' ').toUpperCase()}</div>
            </div>
            
            <div class="card-body">
                <p class="validator-description">${validator.description || 'Professional business validator'}</p>
                
                <div class="contact-info">
                    <div class="contact-item">
                        <span class="contact-label">Owner:</span>
                        <span class="contact-value">${validator.owner_name}</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">Email:</span>
                        <span class="contact-value">${validator.owner_email}</span>
                    </div>
                    ${validator.phone ? `
                    <div class="contact-item">
                        <span class="contact-label">Phone:</span>
                        <span class="contact-value">${validator.phone}</span>
                    </div>
                    ` : ''}
                    ${validator.website ? `
                    <div class="contact-item">
                        <span class="contact-label">Website:</span>
                        <span class="contact-value">${validator.website}</span>
                    </div>
                    ` : ''}
                </div>
                
                <div class="mining-stats">
                    <div class="stat-row">
                        <div class="stat-col">
                            <div class="stat-value">${validator.trust_score || 85}</div>
                            <div class="stat-label">Trust Score</div>
                        </div>
                        <div class="stat-col">
                            <div class="stat-value">${validator.mining_power || 3}x</div>
                            <div class="stat-label">Mining Power</div>
                        </div>
                    </div>
                    <div class="stat-row">
                        <div class="stat-col">
                            <div class="stat-value">${formatNumber(validator.onx_balance || 0)}</div>
                            <div class="stat-label">ONX Balance</div>
                        </div>
                        <div class="stat-col">
                            <div class="stat-value">${validator.blocks_mined || 0}</div>
                            <div class="stat-label">Blocks Mined</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderStaticValidators() {
    const staticValidators = [
        {
            name: 'ONNYX Foundation',
            category: 'Platform',
            status: 'active',
            mining_tier: 'pro',
            description: 'Platform founder and core infrastructure provider for the ONNYX ecosystem.',
            owner_name: 'Djuvane Martin',
            owner_email: '<EMAIL>',
            trust_score: 95,
            mining_power: 10,
            onx_balance: 500000,
            blocks_mined: 3
        },
        {
            name: 'GetTwisted Hair Studios',
            category: 'Services',
            status: 'active',
            mining_tier: 'optimized',
            description: 'Professional hair styling and beauty services with cutting-edge techniques.',
            owner_name: 'Michael Williams',
            owner_email: '<EMAIL>',
            phone: '(*************',
            website: 'gettwistedhair.com',
            trust_score: 85,
            mining_power: 3,
            onx_balance: 1000,
            blocks_mined: 0
        },
        {
            name: 'Sheryl Williams Hair Replacement',
            category: 'Healthcare',
            status: 'active',
            mining_tier: 'optimized',
            description: 'Specialized hair replacement and restoration services for medical and cosmetic needs.',
            owner_name: 'Sheryl Williams',
            owner_email: '<EMAIL>',
            phone: '(*************',
            website: 'sherylwilliamshair.com',
            trust_score: 85,
            mining_power: 3,
            onx_balance: 1000,
            blocks_mined: 0
        }
    ];
    
    renderValidators(staticValidators);
}

function renderBlocks(blocks) {
    const blocksElement = document.getElementById('blocksList');
    if (!blocksElement) return;
    
    blocksElement.innerHTML = blocks.map(block => `
        <div class="block-item">
            <div class="block-info">
                <div class="block-number">#${block.block_number}</div>
                <div class="block-hash">${block.block_hash.substring(0, 16)}...</div>
            </div>
            <div class="block-meta">
                <div class="block-time">${formatTime(block.timestamp)}</div>
                <div class="block-txs">${block.transaction_count || 1} tx${block.transaction_count !== 1 ? 's' : ''}</div>
            </div>
        </div>
    `).join('');
}

function renderStaticBlocks() {
    const staticBlocks = [
        { block_number: 3, block_hash: 'c81859e77b33cb0e', timestamp: Date.now() - 120000, transaction_count: 1 },
        { block_number: 2, block_hash: 'e2094d57b9d04e7f', timestamp: Date.now() - 300000, transaction_count: 3 },
        { block_number: 1, block_hash: 'c3c2cc2e50362df9', timestamp: Date.now() - 480000, transaction_count: 1 }
    ];
    
    renderBlocks(staticBlocks);
}

// Utility functions
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function formatTime(timestamp) {
    const now = Date.now();
    const diff = now - (timestamp * 1000);
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} min ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    
    const days = Math.floor(hours / 24);
    return `${days} day${days !== 1 ? 's' : ''} ago`;
}

// Scroll effects
function setupScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe all sections
    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Frontend error:', e.error);
});

// Debug information
console.log('🔧 ONNYX Frontend Configuration:');
console.log('- GitHub Pages hosting');
console.log('- Backend API:', API_BASE);
console.log('- Environment:', window.location.hostname);
console.log('- Version: 1.0.0');
