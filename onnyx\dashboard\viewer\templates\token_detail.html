{% extends "base.html" %}

{% block title %}Token Details - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="detail-header">
    <div>
        <h1 class="detail-title">{{ token.name }} ({{ token.symbol }})</h1>
        <div class="detail-subtitle">{{ token.id }}</div>
    </div>
</div>

<div class="detail-meta">
    {% if token.creator %}
    <div class="meta-item">
        <i class="fas fa-user"></i>
        <span>Creator: <a href="{{ url_for('identity_detail', identity_id=token.creator) }}">{{ token.creator[:10] }}...</a></span>
    </div>
    {% endif %}
    
    {% if token.type %}
    <div class="meta-item">
        <i class="fas fa-tag"></i>
        <span>Type: {{ token.type }}</span>
    </div>
    {% endif %}
    
    {% if token.supply is defined %}
    <div class="meta-item">
        <i class="fas fa-coins"></i>
        <span>Supply: {{ token.supply }}</span>
    </div>
    {% endif %}
    
    {% if token.created_at %}
    <div class="meta-item">
        <i class="fas fa-calendar"></i>
        <span>Created: {{ format_timestamp(token.created_at) }}</span>
    </div>
    {% endif %}
</div>

<div class="detail-section">
    <h2 class="detail-section-title">Token Information</h2>
    
    <div class="property-list">
        <div class="property-item">
            <div class="property-label">ID</div>
            <div class="property-value">{{ token.id }}</div>
        </div>
        
        <div class="property-item">
            <div class="property-label">Name</div>
            <div class="property-value">{{ token.name }}</div>
        </div>
        
        <div class="property-item">
            <div class="property-label">Symbol</div>
            <div class="property-value">{{ token.symbol }}</div>
        </div>
        
        {% if token.creator %}
        <div class="property-item">
            <div class="property-label">Creator</div>
            <div class="property-value">
                <a href="{{ url_for('identity_detail', identity_id=token.creator) }}">{{ token.creator }}</a>
            </div>
        </div>
        {% endif %}
        
        {% if token.type %}
        <div class="property-item">
            <div class="property-label">Type</div>
            <div class="property-value">{{ token.type }}</div>
        </div>
        {% endif %}
        
        {% if token.supply is defined %}
        <div class="property-item">
            <div class="property-label">Supply</div>
            <div class="property-value">{{ token.supply }}</div>
        </div>
        {% endif %}
        
        {% if token.max_supply is defined %}
        <div class="property-item">
            <div class="property-label">Max Supply</div>
            <div class="property-value">{{ token.max_supply }}</div>
        </div>
        {% endif %}
        
        {% if token.mintable is defined %}
        <div class="property-item">
            <div class="property-label">Mintable</div>
            <div class="property-value">{{ "Yes" if token.mintable else "No" }}</div>
        </div>
        {% endif %}
        
        {% if token.burnable is defined %}
        <div class="property-item">
            <div class="property-label">Burnable</div>
            <div class="property-value">{{ "Yes" if token.burnable else "No" }}</div>
        </div>
        {% endif %}
        
        {% if token.transferable is defined %}
        <div class="property-item">
            <div class="property-label">Transferable</div>
            <div class="property-value">{{ "Yes" if token.transferable else "No" }}</div>
        </div>
        {% endif %}
    </div>
</div>

{% if token.metadata %}
<div class="detail-section">
    <h2 class="detail-section-title">Metadata</h2>
    
    <div class="property-list">
        {% for key, value in token.metadata.items() %}
        <div class="property-item">
            <div class="property-label">{{ key }}</div>
            <div class="property-value">{{ value }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<div class="detail-section">
    <h2 class="detail-section-title">Related Transactions</h2>
    
    <table>
        <thead>
            <tr>
                <th>Transaction ID</th>
                <th>Block</th>
                <th>Type</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            {% for tx in related_txs %}
            <tr>
                <td><a href="{{ url_for('tx_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a></td>
                <td>{{ tx.block }}</td>
                <td>
                    <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
                </td>
                <td>{{ format_timestamp(tx.timestamp) }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="4" style="text-align: center;">No related transactions found</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<div class="detail-section">
    <h2 class="detail-section-title">Raw Token Data</h2>
    
    <div class="json-viewer">{{ token|tojson(indent=2) }}</div>
</div>
{% endblock %}
