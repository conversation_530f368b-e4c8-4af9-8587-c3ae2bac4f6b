#!/usr/bin/env python3
"""
Pre-Genesis Identity Creation Check

Comprehensive verification that all systems are ready for Genesis Identity creation.
"""

import os
import sys
import requests
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def check_system_services():
    """Check that all required services are running."""
    print("🔧 Checking System Services...")
    
    services = [
        ("Web Frontend", "http://127.0.0.1:5000/", "ONNYX"),
        ("API Backend", "http://127.0.0.1:8000/health", "status"),
        ("Identity Registration", "http://127.0.0.1:5000/auth/register/identity", "Identity"),
        ("Blockchain Explorer", "http://127.0.0.1:5000/explorer", "Explorer")
    ]
    
    results = []
    for name, url, expected in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200 and expected in response.text:
                print(f"  ✅ {name}: Running")
                results.append(True)
            else:
                print(f"  ❌ {name}: Not responding correctly")
                results.append(False)
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def check_database_state():
    """Check database is in clean production state."""
    print("\n💾 Checking Database State...")
    
    try:
        # Check table counts
        blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
        transactions = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
        identities = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
        selas = db.query_one("SELECT COUNT(*) as count FROM selas")['count']
        
        print(f"  📦 Blocks: {blocks}")
        print(f"  📝 Transactions: {transactions}")
        print(f"  👤 Identities: {identities}")
        print(f"  🏢 Selas: {selas}")
        
        # Verify clean state (should have genesis block but no identities/selas)
        if blocks >= 1 and identities == 0 and selas == 0:
            print("  ✅ Database in clean production state")
            return True
        elif identities > 0:
            print("  ⚠️  Database contains existing identities")
            print("     This may be expected if Genesis Identity already created")
            return True
        else:
            print("  ❌ Database not in expected state")
            return False
            
    except Exception as e:
        print(f"  ❌ Database error: {e}")
        return False

def check_mining_activity():
    """Check that blockchain mining is active."""
    print("\n⛏️  Checking Mining Activity...")
    
    try:
        # Get initial block count
        initial_blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
        print(f"  📊 Initial blocks: {initial_blocks}")
        
        # Wait for new block
        print("  ⏳ Waiting for new block (10 seconds)...")
        time.sleep(10)
        
        # Check if new block was mined
        final_blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
        print(f"  📊 Final blocks: {final_blocks}")
        
        if final_blocks > initial_blocks:
            print("  ✅ Mining is active - new block detected")
            return True
        else:
            print("  ⚠️  No new blocks mined in 10 seconds")
            print("     Mining may be slow or inactive")
            return False
            
    except Exception as e:
        print(f"  ❌ Mining check error: {e}")
        return False

def check_logo_integration():
    """Check that logo integration is working."""
    print("\n🎨 Checking Logo Integration...")
    
    try:
        # Check logo files exist
        logo_files = [
            "web/static/images/onnyx_logo.png",
            "web/static/images/favicon.ico",
            "web/static/images/apple-touch-icon.png"
        ]
        
        files_ok = True
        for file_path in logo_files:
            if os.path.exists(file_path):
                print(f"  ✅ {file_path}")
            else:
                print(f"  ❌ {file_path} missing")
                files_ok = False
        
        # Check web pages contain logo references
        response = requests.get("http://127.0.0.1:5000/auth/register/identity", timeout=5)
        if "onnyx_logo.png" in response.text:
            print("  ✅ Logo integrated in registration page")
            return files_ok
        else:
            print("  ❌ Logo not found in registration page")
            return False
            
    except Exception as e:
        print(f"  ❌ Logo check error: {e}")
        return False

def check_key_generation():
    """Check that key generation system is working."""
    print("\n🔐 Checking Key Generation System...")
    
    try:
        # Test the registration page loads
        response = requests.get("http://127.0.0.1:5000/auth/register/identity", timeout=5)
        
        if response.status_code == 200:
            # Check for key generation elements
            if "Generate" in response.text or "Key" in response.text:
                print("  ✅ Registration form accessible")
                print("  ✅ Key generation system ready")
                return True
            else:
                print("  ⚠️  Registration form loaded but key generation unclear")
                return True
        else:
            print(f"  ❌ Registration page error: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Key generation check error: {e}")
        return False

def main():
    """Run comprehensive pre-Genesis checks."""
    print("👑 ONNYX PRE-GENESIS IDENTITY CHECK")
    print("=" * 50)
    print("Verifying all systems are ready for Genesis Identity creation...\n")
    
    checks = [
        ("System Services", check_system_services),
        ("Database State", check_database_state),
        ("Mining Activity", check_mining_activity),
        ("Logo Integration", check_logo_integration),
        ("Key Generation", check_key_generation)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"\n❌ {check_name} check failed: {e}")
            results.append(False)
    
    print("\n📊 PRE-GENESIS CHECK SUMMARY")
    print("=" * 40)
    
    passed = sum(results)
    total = len(results)
    
    for i, (check_name, _) in enumerate(checks):
        status = "✅ READY" if results[i] else "❌ ISSUE"
        print(f"{check_name}: {status}")
    
    print(f"\nOverall Status: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL SYSTEMS READY FOR GENESIS IDENTITY!")
        print("=" * 50)
        print("✅ Platform is fully operational")
        print("✅ Database is in clean production state")
        print("✅ Mining is active and processing transactions")
        print("✅ Logo integration is complete and functional")
        print("✅ Key generation system is ready")
        print()
        print("🚀 READY TO CREATE GENESIS IDENTITY")
        print("Navigate to: http://127.0.0.1:5000/auth/register/identity")
        print()
        print("💡 Tip: Run 'python scripts/monitor_genesis_creation.py'")
        print("   to monitor the creation process in real-time")
        return 0
    else:
        print("\n⚠️  SOME SYSTEMS NEED ATTENTION")
        print("Please resolve the issues above before creating Genesis Identity")
        return 1

if __name__ == "__main__":
    sys.exit(main())
