#!/usr/bin/env python3
"""
Modern Navigation Design Test Suite

Tests the completely redesigned navigation bar with:
- Modern glassmorphism design
- Enhanced user experience
- Professional layout
- Responsive behavior
"""

import requests
import json
from bs4 import BeautifulSoup

def test_modern_navigation():
    """Test the modern navigation design implementation."""
    base_url = "http://127.0.0.1:5000"
    
    print("🎨 Testing Modern Navigation Design")
    print("=" * 50)
    
    results = []
    
    try:
        # Test server connectivity
        response = requests.get(base_url, timeout=10)
        if response.status_code != 200:
            results.append(f"❌ Server not accessible: {response.status_code}")
            return results
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Test 1: Modern Navigation Structure
        print("1. Testing modern navigation structure...")
        modern_nav = soup.find('nav', class_='modern-nav')
        if modern_nav:
            results.append("✅ Modern navigation container found")
            
            # Check nav container
            nav_container = modern_nav.find('div', class_='nav-container')
            if nav_container:
                results.append("✅ Navigation container structure found")
            else:
                results.append("❌ Navigation container missing")
        else:
            results.append("❌ Modern navigation container not found")
            return results
        
        # Test 2: Logo Section
        print("2. Testing logo section...")
        nav_left = soup.find('div', class_='nav-left')
        if nav_left:
            results.append("✅ Left navigation section found")
            
            logo_link = nav_left.find('a', class_='nav-logo-link')
            if logo_link:
                results.append("✅ Logo link found")
                
                logo_container = logo_link.find('div', class_='nav-logo-container')
                logo_text = logo_link.find('span', class_='nav-logo-text')
                
                if logo_container and logo_text:
                    results.append("✅ Logo container and text found")
                    if 'ONNYX' in logo_text.get_text():
                        results.append("✅ Logo text displays 'ONNYX'")
                    else:
                        results.append("❌ Logo text incorrect")
                else:
                    results.append("❌ Logo components missing")
            else:
                results.append("❌ Logo link not found")
        else:
            results.append("❌ Left navigation section not found")
        
        # Test 3: Center Navigation Links
        print("3. Testing center navigation links...")
        nav_center = soup.find('div', class_='nav-center')
        if nav_center:
            results.append("✅ Center navigation section found")
            
            nav_links_container = nav_center.find('div', class_='nav-links-container')
            if nav_links_container:
                results.append("✅ Navigation links container found")
                
                modern_links = nav_links_container.find_all('a', class_='nav-link-modern')
                if len(modern_links) >= 3:
                    results.append(f"✅ Found {len(modern_links)} modern navigation links")
                    
                    # Check for expected links
                    link_texts = [link.get_text().strip() for link in modern_links]
                    expected_links = ['Home', 'Validators', 'Explorer']
                    
                    for expected in expected_links:
                        if any(expected in text for text in link_texts):
                            results.append(f"✅ '{expected}' link found")
                        else:
                            results.append(f"❌ '{expected}' link missing")
                    
                    # Check for icons
                    icons = nav_links_container.find_all('span', class_='nav-link-icon')
                    if len(icons) >= 3:
                        results.append("✅ Navigation link icons found")
                    else:
                        results.append("❌ Navigation link icons missing")
                else:
                    results.append(f"❌ Insufficient navigation links: {len(modern_links)}")
            else:
                results.append("❌ Navigation links container not found")
        else:
            results.append("❌ Center navigation section not found")
        
        # Test 4: Right Section (Guest Buttons)
        print("4. Testing right section...")
        nav_right = soup.find('div', class_='nav-right')
        if nav_right:
            results.append("✅ Right navigation section found")
            
            # Check for guest buttons (when not logged in)
            guest_buttons = nav_right.find('div', class_='guest-buttons')
            if guest_buttons:
                results.append("✅ Guest buttons container found")
                
                guest_button_links = guest_buttons.find_all('a', class_='guest-button')
                if len(guest_button_links) >= 2:
                    results.append(f"✅ Found {len(guest_button_links)} guest buttons")
                    
                    button_texts = [btn.get_text().strip() for btn in guest_button_links]
                    if any('Access Portal' in text for text in button_texts):
                        results.append("✅ 'Access Portal' button found")
                    if any('Verify Identity' in text for text in button_texts):
                        results.append("✅ 'Verify Identity' button found")
                else:
                    results.append(f"❌ Insufficient guest buttons: {len(guest_button_links)}")
            else:
                results.append("❌ Guest buttons container not found")
        else:
            results.append("❌ Right navigation section not found")
        
        # Test 5: CSS Classes and Styling
        print("5. Testing CSS classes...")
        
        # Check for modern navigation classes
        required_classes = [
            'modern-nav', 'nav-container', 'nav-left', 'nav-center', 'nav-right',
            'nav-logo-link', 'nav-logo-container', 'nav-logo-text',
            'nav-links-container', 'nav-link-modern', 'nav-link-icon', 'nav-link-text',
            'guest-buttons', 'guest-button'
        ]
        
        page_html = str(soup)
        found_classes = []
        missing_classes = []
        
        for css_class in required_classes:
            if css_class in page_html:
                found_classes.append(css_class)
            else:
                missing_classes.append(css_class)
        
        results.append(f"✅ Found {len(found_classes)}/{len(required_classes)} required CSS classes")
        
        if missing_classes:
            results.append(f"❌ Missing CSS classes: {', '.join(missing_classes)}")
        
        # Test 6: Mobile Menu Toggle
        print("6. Testing mobile menu toggle...")
        mobile_toggle = soup.find('div', class_='mobile-menu-toggle')
        if mobile_toggle:
            results.append("✅ Mobile menu toggle found")
            
            toggle_btn = mobile_toggle.find('button', class_='mobile-toggle-btn')
            hamburger = mobile_toggle.find('div', class_='hamburger')
            
            if toggle_btn and hamburger:
                results.append("✅ Mobile toggle button and hamburger found")
            else:
                results.append("❌ Mobile toggle components missing")
        else:
            results.append("❌ Mobile menu toggle not found")
        
    except Exception as e:
        results.append(f"❌ Error during testing: {e}")
    
    return results

def test_logged_in_navigation():
    """Test navigation when user is logged in."""
    base_url = "http://127.0.0.1:5000"
    
    print("\n👤 Testing Logged-in Navigation")
    print("=" * 50)
    
    results = []
    
    try:
        # Create test session and login
        session = requests.Session()
        
        # Create test identity
        test_data = {
            'name': 'Modern Nav Test User',
            'email': '<EMAIL>',
            'role': 'Business Owner'
        }
        
        session.post(f"{base_url}/auth/register/identity", data=test_data, timeout=10)
        
        # Login
        login_data = {'email': '<EMAIL>'}
        login_response = session.post(f"{base_url}/auth/login", data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            results.append("✅ Successfully logged in")
            
            # Get logged-in page
            logged_in_response = session.get(base_url, timeout=10)
            soup = BeautifulSoup(logged_in_response.content, 'html.parser')
            
            # Test user profile section
            user_profile = soup.find('div', class_='user-profile-section')
            if user_profile:
                results.append("✅ User profile section found")
                
                profile_button = user_profile.find('button', class_='user-profile-button')
                if profile_button:
                    results.append("✅ User profile button found")
                    
                    # Check user avatar and info
                    user_avatar = profile_button.find('div', class_='user-avatar')
                    user_info = profile_button.find('div', class_='user-info')
                    
                    if user_avatar and user_info:
                        results.append("✅ User avatar and info found")
                        
                        user_initial = user_avatar.find('span', class_='user-initial')
                        user_name = user_info.find('span', class_='user-name')
                        user_status = user_info.find('span', class_='user-status')
                        
                        if user_initial and user_name and user_status:
                            results.append("✅ User profile components complete")
                            results.append(f"✅ User initial: '{user_initial.get_text().strip()}'")
                            results.append(f"✅ User name: '{user_name.get_text().strip()}'")
                            results.append(f"✅ User status: '{user_status.get_text().strip()}'")
                        else:
                            results.append("❌ User profile components incomplete")
                    else:
                        results.append("❌ User avatar or info missing")
                else:
                    results.append("❌ User profile button not found")
                
                # Test dropdown menu
                dropdown_menu = user_profile.find('div', class_='user-dropdown-menu')
                if dropdown_menu:
                    results.append("✅ User dropdown menu found")
                    
                    dropdown_items = dropdown_menu.find_all('a', class_='dropdown-item')
                    if len(dropdown_items) >= 4:
                        results.append(f"✅ Found {len(dropdown_items)} dropdown items")
                        
                        item_texts = [item.get_text().strip() for item in dropdown_items]
                        expected_items = ['Dashboard', 'My Identity', 'My Validators', 'Logout']
                        
                        for expected in expected_items:
                            if any(expected in text for text in item_texts):
                                results.append(f"✅ '{expected}' dropdown item found")
                            else:
                                results.append(f"❌ '{expected}' dropdown item missing")
                    else:
                        results.append(f"❌ Insufficient dropdown items: {len(dropdown_items)}")
                else:
                    results.append("❌ User dropdown menu not found")
            else:
                results.append("❌ User profile section not found")
            
            # Test enhanced navigation links for logged-in users
            nav_center = soup.find('div', class_='nav-center')
            if nav_center:
                modern_links = nav_center.find_all('a', class_='nav-link-modern')
                if len(modern_links) >= 5:
                    results.append(f"✅ Enhanced navigation with {len(modern_links)} links for logged-in user")
                    
                    link_texts = [link.get_text().strip() for link in modern_links]
                    logged_in_links = ['Dashboard', 'Auto-Mining']
                    
                    for expected in logged_in_links:
                        if any(expected in text for text in link_texts):
                            results.append(f"✅ '{expected}' link available for logged-in user")
                        else:
                            results.append(f"❌ '{expected}' link missing for logged-in user")
                else:
                    results.append(f"❌ Navigation not enhanced for logged-in user: {len(modern_links)} links")
        else:
            results.append(f"❌ Login failed: {login_response.status_code}")
    
    except Exception as e:
        results.append(f"❌ Error during logged-in testing: {e}")
    
    return results

if __name__ == "__main__":
    print("🚀 Modern Navigation Design Test Suite")
    print("=" * 60)
    
    # Test logged-out navigation
    guest_results = test_modern_navigation()
    
    # Test logged-in navigation
    user_results = test_logged_in_navigation()
    
    # Combine results
    all_results = guest_results + user_results
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = len([r for r in all_results if r.startswith("✅")])
    failed = len([r for r in all_results if r.startswith("❌")])
    
    for result in all_results:
        print(result)
    
    print(f"\n📈 Summary: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All modern navigation tests passed! New design is working perfectly.")
    else:
        print("⚠️ Some tests failed. Please review the results above.")
