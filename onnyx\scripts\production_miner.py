#!/usr/bin/env python3
"""
ONNYX Production Miner

Simple blockchain miner for production environment.
Continuously mines blocks and processes transactions from the mempool.
"""

import os
import sys
import json
import time
import hashlib
import logging
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.production_miner")

class ProductionMiner:
    """Simple production blockchain miner."""

    def __init__(self):
        self.miner_id = "ONNYX_GENESIS_MINER"
        self.mining_interval = 10  # seconds
        self.running = False

    def get_latest_block(self):
        """Get the latest block from the database."""
        try:
            latest = db.query_one("""
                SELECT * FROM blocks
                ORDER BY block_height DESC
                LIMIT 1
            """)
            return latest
        except Exception as e:
            logger.debug(f"No blocks found or error: {e}")
            return None

    def get_pending_transactions(self):
        """Get pending transactions from the database."""
        try:
            pending = db.query("""
                SELECT * FROM transactions
                WHERE status = 'pending'
                ORDER BY created_at ASC
                LIMIT 10
            """)
            return pending
        except Exception as e:
            logger.debug(f"No pending transactions or error: {e}")
            return []

    def calculate_hash(self, height, timestamp, previous_hash, transactions, miner, nonce=0):
        """Calculate block hash."""
        data = f"{height}{timestamp}{previous_hash}{json.dumps(transactions, sort_keys=True)}{miner}{nonce}"
        return hashlib.sha256(data.encode()).hexdigest()

    def create_genesis_block(self):
        """Create the genesis block if none exists."""
        try:
            timestamp = int(time.time())
            transactions_json = json.dumps([])
            merkle_root = hashlib.sha256(transactions_json.encode()).hexdigest()
            genesis_hash = self.calculate_hash(0, timestamp, "0", [], "GENESIS")

            block_data = {
                'block_hash': genesis_hash,
                'block_height': 0,
                'previous_hash': '0',
                'timestamp': timestamp,
                'miner': 'GENESIS',
                'transactions': transactions_json,
                'merkle_root': merkle_root,
                'nonce': 0,
                'difficulty': 1,
                'created_at': timestamp
            }

            db.insert('blocks', block_data)
            logger.info(f"✅ Genesis block created: {genesis_hash}")
            return block_data

        except Exception as e:
            logger.error(f"❌ Failed to create genesis block: {e}")
            raise

    def mine_block(self):
        """Mine a new block."""
        try:
            # Get latest block
            latest_block = self.get_latest_block()

            # Create genesis if no blocks exist
            if not latest_block:
                return self.create_genesis_block()

            # Get pending transactions
            pending_txs = self.get_pending_transactions()

            # Prepare new block
            new_height = latest_block['block_height'] + 1
            timestamp = int(time.time())
            previous_hash = latest_block['block_hash']

            # Create transaction list for block
            tx_list = []
            for tx in pending_txs:
                tx_list.append({
                    'tx_id': tx['tx_id'],
                    'op': tx['op'],
                    'sender': tx['sender']
                })

            # Create transactions JSON and merkle root
            transactions_json = json.dumps(tx_list)
            merkle_root = hashlib.sha256(transactions_json.encode()).hexdigest()

            # Calculate block hash
            block_hash = self.calculate_hash(
                new_height,
                timestamp,
                previous_hash,
                tx_list,
                self.miner_id
            )

            # Create block data
            block_data = {
                'block_hash': block_hash,
                'block_height': new_height,
                'previous_hash': previous_hash,
                'timestamp': timestamp,
                'miner': self.miner_id,
                'transactions': transactions_json,
                'merkle_root': merkle_root,
                'nonce': 0,
                'difficulty': 1,
                'created_at': timestamp
            }

            # Insert block into database
            db.insert('blocks', block_data)

            # Update transaction statuses
            for tx in pending_txs:
                db.update(
                    'transactions',
                    {'status': 'confirmed', 'block_hash': block_hash},
                    'tx_id = ?',
                    (tx['tx_id'],)
                )

            logger.info(f"⛏️  Block #{new_height} mined: {block_hash[:16]}... ({len(pending_txs)} txs)")
            return block_data

        except Exception as e:
            logger.error(f"❌ Failed to mine block: {e}")
            raise

    def start_mining(self):
        """Start the mining loop."""
        logger.info("🚀 ONNYX Production Miner Starting")
        logger.info(f"⚙️  Mining interval: {self.mining_interval} seconds")
        logger.info(f"🏷️  Miner ID: {self.miner_id}")
        logger.info("=" * 60)

        self.running = True

        try:
            while self.running:
                try:
                    # Mine a new block
                    block = self.mine_block()

                    # Update blockchain.json cache
                    self.update_blockchain_cache()

                    # Wait for next mining cycle
                    time.sleep(self.mining_interval)

                except KeyboardInterrupt:
                    logger.info("⏹️  Mining stopped by user")
                    break
                except Exception as e:
                    logger.error(f"❌ Mining error: {e}")
                    time.sleep(5)  # Wait before retrying

        finally:
            self.running = False
            logger.info("🛑 Mining stopped")

    def update_blockchain_cache(self):
        """Update the blockchain.json cache file."""
        try:
            # Get all blocks
            blocks = db.query("""
                SELECT * FROM blocks
                ORDER BY block_height ASC
            """)

            # Write to cache file
            with open('blockchain.json', 'w') as f:
                json.dump(blocks, f, indent=2)

        except Exception as e:
            logger.debug(f"Failed to update blockchain cache: {e}")

    def get_stats(self):
        """Get mining statistics."""
        try:
            stats = {
                'total_blocks': db.query_one("SELECT COUNT(*) as count FROM blocks")['count'],
                'total_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions")['count'],
                'pending_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'pending'")['count'],
                'confirmed_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'confirmed'")['count']
            }
            return stats
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}

def main():
    """Main entry point."""
    try:
        miner = ProductionMiner()

        # Show initial stats
        stats = miner.get_stats()
        logger.info(f"📊 Initial Stats: {stats['total_blocks']} blocks, {stats['total_transactions']} transactions")

        # Start mining
        miner.start_mining()

    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
