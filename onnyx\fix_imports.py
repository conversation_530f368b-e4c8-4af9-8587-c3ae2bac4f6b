#!/usr/bin/env python3
"""
Script to fix import statements in the restructured Onnyx codebase.
"""

import os
import re
import glob

def fix_imports_in_file(file_path):
    """Fix import statements in a single file."""
    print(f"Fixing imports in: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Fix common import patterns
    replacements = [
        # Models imports
        (r'from models\.', 'from shared.models.'),
        (r'import models\.', 'import shared.models.'),
        
        # Data/DB imports
        (r'from data\.db import', 'from shared.db.db import'),
        (r'from data\.', 'from shared.db.'),
        (r'import data\.', 'import shared.db.'),
        
        # Config imports
        (r'from config\.', 'from shared.config.'),
        (r'import config\.', 'import shared.config.'),
        
        # Analytics imports
        (r'from analytics\.', 'from shared.utils.'),
        (r'import analytics\.', 'import shared.utils.'),
        
        # Routes imports (for API files)
        (r'from routes\.', 'from api.routes.'),
        (r'import routes\.', 'import api.routes.'),
        
        # Blockchain component imports
        (r'from chain\.', 'from blockchain.core.'),
        (r'from consensus\.', 'from blockchain.consensus.'),
        (r'from vm\.', 'from blockchain.vm.'),
        (r'from wallet\.', 'from blockchain.wallet.'),
        (r'from node\.', 'from blockchain.node.'),
        
        # Governance imports
        (r'from governance\.', 'from governance.'),
        
        # Token imports
        (r'from tokens\.', 'from tokens.ledger.'),
        
        # Trust/Etzem imports
        (r'from trust\.', 'from identity.trust.'),
        
        # Business/Sela imports
        (r'from business\.', 'from sela.registry.'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ Updated imports in {file_path}")
        return True
    else:
        print(f"  - No changes needed in {file_path}")
        return False

def main():
    """Main function to fix imports across the codebase."""
    print("🔧 Fixing import statements in restructured Onnyx codebase...")
    
    # Directories to process
    directories = [
        'shared/models',
        'shared/config', 
        'shared/db',
        'shared/utils',
        'api/handlers',
        'api/routes',
        'blockchain',
        'dashboard',
        'governance',
        'identity',
        'sela',
        'tokens',
        'runtime'
    ]
    
    total_files = 0
    updated_files = 0
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\nProcessing directory: {directory}")
            
            # Find all Python files
            pattern = os.path.join(directory, '**', '*.py')
            python_files = glob.glob(pattern, recursive=True)
            
            for file_path in python_files:
                # Skip __pycache__ directories
                if '__pycache__' in file_path:
                    continue
                    
                total_files += 1
                if fix_imports_in_file(file_path):
                    updated_files += 1
        else:
            print(f"Directory not found: {directory}")
    
    print(f"\n✅ Import fixing complete!")
    print(f"   Total files processed: {total_files}")
    print(f"   Files updated: {updated_files}")

if __name__ == '__main__':
    main()
