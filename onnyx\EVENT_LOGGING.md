# Onnyx Event Logging & Analytics

## Overview

Every block mined in Onnyx generates a detailed event log, including:

- ✅ Block proposer & timestamp
- 📦 Transactions processed
- 🪙 Tokens minted
- 🗳 Proposals & votes
- 📘 Notable economic activity

Weekly summaries enable transparent reporting of network growth, usage, and governance.

## Event Log Format

Each block generates an event log entry with the following format:

```json
{
  "block_index": 123,
  "block_hash": "0x123456789abcdef",
  "timestamp": 1746795034,
  "timestamp_human": "2025-05-09 12:30:34",
  "proposer": "alice",
  "tx_count": 5,
  "token_mints": 2,
  "token_transfers": 1,
  "token_burns": 0,
  "proposals": 1,
  "votes": 3,
  "identities": 0,
  "reputation_grants": 0,
  "stakes": 0,
  "rewards": 1,
  "notable_events": [
    "MINT: 100 BOB_BUX to bob",
    "MINT: 50 ALICE_COIN to alice",
    "TRANSFER: 25 BOB_BUX from bob to charlie",
    "PROPOSAL: 'Reduce mint cap' (economic) by alice",
    "VOTE: alice voted yes on scroll_1a2b3c_1712341234",
    "VOTE: bob voted yes on scroll_1a2b3c_1712341234",
    "VOTE: charlie voted no on scroll_1a2b3c_1712341234",
    "REWARD: 10 ONX to alice"
  ]
}
```

## Components

### EventLogger

The EventLogger class logs blockchain events for analytics purposes. It provides methods for:

- Logging blocks and transactions
- Extracting notable events
- Getting event logs
- Generating summaries

### Weekly Summary Generator

The Weekly Summary Generator script generates a weekly summary of the Onnyx blockchain. It formats the summary as a Markdown document and can save it to a file.

## API Endpoints

### Get Event Logs

```
GET /api/analytics/logs?limit=100&offset=0
```

Returns a list of event logs.

### Get Event Log by Block Index

```
GET /api/analytics/logs/{block_index}
```

Returns the event log for a specific block.

### Get Summary

```
GET /api/analytics/summary?days=7
```

Returns a summary of event logs for a given number of days.

### Get Proposers

```
GET /api/analytics/proposers?days=7
```

Returns a list of unique proposers for a given number of days.

### Get Transaction Statistics

```
GET /api/analytics/transactions?days=7
```

Returns transaction statistics for a given number of days.

### Get Block Statistics

```
GET /api/analytics/blocks?days=7
```

Returns block statistics for a given number of days.

## Usage

### Logging Events

The EventLogger is automatically used by the mining process to log events for each block:

```python
from analytics.event_logger import event_logger

# Log a block
event_logger.log_block(block, block["transactions"], proposer_id)
```

### Generating Summaries

You can generate summaries using the EventLogger:

```python
from analytics.event_logger import event_logger

# Generate a summary for the last 7 days
summary = event_logger.generate_summary(days=7)
```

### Running the Weekly Summary Generator

```bash
python generate_weekly_summary.py --output weekly_summary.md --days 7
```

## Weekly Summary Example

```markdown
# Onnyx Blockchain 7-Day Summary
Generated on: 2025-05-16 15:45:22

## Overview
- Period: 2025-05-09 12:30:34 to 2025-05-16 15:45:22
- Blocks: 1008
- Transactions: 5432
- Unique Proposers: 12

## Transaction Breakdown
- Token Mints: 256
- Token Transfers: 3210
- Token Burns: 45
- Proposals: 8
- Votes: 124
- Identities: 32
- Reputation Grants: 18
- Stakes: 67
- Rewards: 1008

## Proposers
- alice
- bob
- charlie
- dave
- eve
- frank
- grace
- heidi
- ivan
- judy
- karl
- lisa

## Statistics
- Average Transactions per Block: 5.39
- Blocks per Day: 144.00
```

## Benefits

- **Transparency**: Every block and transaction is logged with detailed information
- **Auditability**: The event log provides a complete history of the blockchain
- **Analytics**: The event log can be used to generate statistics and insights
- **Reporting**: Weekly summaries provide a high-level overview of blockchain activity
- **Governance**: The event log tracks proposals and votes for governance purposes

## Integration with Other Components

### Mining

The mining process logs events for each block:

```python
# Log the block event
event_logger.log_block(block, block["transactions"], identity_id)
```

### API

The API provides endpoints for accessing event logs and summaries:

```python
@router.get("/analytics/logs")
def get_logs(limit: Optional[int] = Query(100, ge=1, le=1000), offset: int = Query(0, ge=0)):
    logs = event_logger.get_logs(limit=limit, offset=offset)
    return {"logs": logs, "count": len(logs), "limit": limit, "offset": offset}
```

### Web Interface

The event logs and summaries can be displayed in a web interface for easy access and visualization.
