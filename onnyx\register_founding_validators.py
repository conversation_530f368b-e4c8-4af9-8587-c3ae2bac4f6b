#!/usr/bin/env python3
"""
ONNYX Phase 1 Production Launch - Founding Business Validators Registration
Registers the founding business validators for the ONNYX trusted business network.
"""

import sqlite3
import json
import time
import uuid
from datetime import datetime

def get_platform_founder_id():
    """Get the Platform Founder identity ID."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        cursor.execute("SELECT identity_id FROM identities WHERE metadata LIKE '%Platform Founder%'")
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    except Exception as e:
        print(f"Error getting Platform Founder ID: {e}")
        return None

def register_business_validator(business_data, owner_identity_id):
    """Register a business validator in the database."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        sela_id = str(uuid.uuid4())
        current_time = int(time.time())
        
        # Insert business validator (Sela)
        cursor.execute("""
            INSERT INTO selas (sela_id, identity_id, name, category, description, address, services, status, trust_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            sela_id,
            owner_identity_id,
            business_data['name'],
            business_data['category'],
            business_data['description'],
            business_data.get('address', ''),
            json.dumps(business_data.get('services', [])),
            'active',
            100.0,  # Starting trust score
            current_time,
            current_time
        ))
        
        # Initialize mining for this validator
        mining_id = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO mining (mining_id, identity_id, sela_id, mining_tier, mining_power, blocks_mined, total_rewards, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            mining_id,
            owner_identity_id,
            sela_id,
            business_data.get('mining_tier', 'ONNYX Optimized'),
            business_data.get('mining_power', 3.0),
            0,
            0.0,
            current_time
        ))
        
        conn.commit()
        conn.close()
        
        return sela_id
        
    except Exception as e:
        print(f"Error registering business validator: {e}")
        return None

def register_founding_validators():
    """Register all founding business validators."""
    print("🏢 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("=" * 70)
    print("FOUNDING BUSINESS VALIDATORS REGISTRATION")
    print("Establishing the trusted business network foundation")
    print("Date:", datetime.now().strftime("%B %d, %Y at %I:%M %p"))
    print("=" * 70)
    
    # Get Platform Founder ID
    platform_founder_id = get_platform_founder_id()
    if not platform_founder_id:
        print("❌ Platform Founder not found. Cannot proceed.")
        return False
    
    print(f"✅ Platform Founder ID: {platform_founder_id}")
    print()
    
    # Define founding business validators
    founding_businesses = [
        {
            'name': 'ONNYX',
            'category': 'Blockchain Technology',
            'description': 'Trusted business network platform providing quantum-resistant cryptographic identities and decentralized validation for secure commerce.',
            'address': 'Digital Platform - Global Network',
            'services': ['Blockchain Platform', 'Identity Verification', 'Business Validation', 'Secure Commerce', 'Decentralized Mining'],
            'mining_tier': 'ONNYX Pro',
            'mining_power': 10.0
        },
        {
            'name': 'Epinnox',
            'category': 'Financial Technology',
            'description': 'Innovative fintech solutions providing advanced financial services, payment processing, and blockchain-based financial infrastructure.',
            'address': 'Fintech Hub - Digital Services',
            'services': ['Payment Processing', 'Financial Services', 'Blockchain Finance', 'Digital Banking', 'Investment Solutions'],
            'mining_tier': 'ONNYX Pro',
            'mining_power': 8.0
        },
        {
            'name': 'Investment Club',
            'category': 'Investment Services',
            'description': 'Professional investment management and advisory services, specializing in blockchain technology investments and portfolio management.',
            'address': 'Investment Advisory Center',
            'services': ['Investment Management', 'Portfolio Advisory', 'Blockchain Investments', 'Financial Planning', 'Asset Management'],
            'mining_tier': 'ONNYX Optimized',
            'mining_power': 5.0
        },
        {
            'name': 'Sheryl Williams Hair Replacement',
            'category': 'Beauty & Hair Services',
            'description': 'Professional hair replacement services providing high-quality hair solutions and personalized styling for clients seeking natural-looking results.',
            'address': 'Professional Hair Studio',
            'services': ['Hair Replacement', 'Hair Restoration', 'Custom Styling', 'Hair Consultation', 'Professional Hair Care'],
            'mining_tier': 'ONNYX Optimized',
            'mining_power': 3.0
        },
        {
            'name': 'GetTwisted Hair Studios',
            'category': 'Beauty & Hair Services',
            'description': 'Full-service hair salon offering professional hair styling, cutting, coloring, and specialized hair treatments in a modern studio environment.',
            'address': 'Modern Hair Studio & Salon',
            'services': ['Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Hair Treatments', 'Salon Services'],
            'mining_tier': 'ONNYX Optimized',
            'mining_power': 3.0
        }
    ]
    
    registered_validators = []
    
    print("📝 Registering founding business validators...")
    print()
    
    for i, business in enumerate(founding_businesses, 1):
        print(f"{i}. Registering {business['name']}...")
        
        sela_id = register_business_validator(business, platform_founder_id)
        
        if sela_id:
            registered_validators.append({
                'sela_id': sela_id,
                'name': business['name'],
                'category': business['category'],
                'mining_tier': business['mining_tier'],
                'mining_power': business['mining_power']
            })
            print(f"   ✅ {business['name']} registered successfully")
            print(f"      Sela ID: {sela_id}")
            print(f"      Category: {business['category']}")
            print(f"      Mining Tier: {business['mining_tier']} ({business['mining_power']}x power)")
        else:
            print(f"   ❌ Failed to register {business['name']}")
        
        print()
    
    return registered_validators

def verify_validator_registration():
    """Verify all validators were registered successfully."""
    print("🔍 Verifying Business Validator Registration")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Count registered validators
        cursor.execute("SELECT COUNT(*) FROM selas")
        validator_count = cursor.fetchone()[0]
        
        # Get all validators
        cursor.execute("SELECT name, category, status FROM selas ORDER BY created_at")
        validators = cursor.fetchall()
        
        # Count mining records
        cursor.execute("SELECT COUNT(*) FROM mining WHERE sela_id IS NOT NULL")
        mining_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ Total Validators Registered: {validator_count}")
        print(f"✅ Mining Records Created: {mining_count}")
        print()
        
        if validators:
            print("📋 Registered Business Validators:")
            for i, (name, category, status) in enumerate(validators, 1):
                print(f"   {i}. {name} ({category}) - Status: {status}")
        
        return validator_count >= 5  # Should have 5 founding validators
        
    except Exception as e:
        print(f"❌ Error verifying registration: {e}")
        return False

def display_network_status():
    """Display the current network status."""
    print("\n🌐 ONNYX TRUSTED BUSINESS NETWORK STATUS")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Get network statistics
        cursor.execute("SELECT COUNT(*) FROM identities")
        identity_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM selas")
        validator_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM blocks")
        block_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM mining")
        mining_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(mining_power) FROM mining")
        total_mining_power = cursor.fetchone()[0] or 0
        
        conn.close()
        
        print(f"📊 Network Statistics:")
        print(f"   • Total Identities: {identity_count}")
        print(f"   • Business Validators: {validator_count}")
        print(f"   • Blockchain Blocks: {block_count}")
        print(f"   • Mining Nodes: {mining_count}")
        print(f"   • Total Mining Power: {total_mining_power}x")
        print()
        print(f"🎯 Network Status: PRODUCTION READY")
        print(f"⛓️ Genesis Block: #0 (ESTABLISHED)")
        print(f"👑 Platform Founder: Djuvane Martin")
        print(f"🏢 Founding Validators: {validator_count} ACTIVE")
        
    except Exception as e:
        print(f"❌ Error getting network status: {e}")

def main():
    """Execute founding business validator registration."""
    print("🚀 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("🏢 FOUNDING BUSINESS VALIDATORS REGISTRATION")
    print()
    
    # Register all founding validators
    registered_validators = register_founding_validators()
    
    if registered_validators and len(registered_validators) >= 5:
        # Verify registration
        verification_success = verify_validator_registration()
        
        if verification_success:
            print("\n🎉 FOUNDING VALIDATORS REGISTRATION - MISSION ACCOMPLISHED!")
            print("✨ All founding business validators successfully registered")
            print("⛓️ ONNYX trusted business network is fully operational")
            print("🏢 Ready for mining operations and block validation")
            
            display_network_status()
            
            print("\n🚀 PHASE 1 PRODUCTION LAUNCH STATUS:")
            print("   ✅ System Reset Complete")
            print("   ✅ Genesis Identity Created (Djuvane Martin)")
            print("   ✅ Genesis Block #0 Established")
            print("   ✅ Founding Validators Registered (5)")
            print("   ✅ Mining System Initialized")
            print("   ✅ Trusted Business Network Operational")
            print()
            print("📋 NEXT: Initialize Mining Operations")
            print("   • Configure mining tiers and performance boosts")
            print("   • Begin blockchain validation and block creation")
            print("   • Test complete validator network")
            print("   • Verify production network stability")
            print()
            print("🌟 ONNYX TRUSTED BUSINESS NETWORK FOUNDATION COMPLETE!")
            
            return True
        else:
            print("\n⚠️ Validator registration verification failed")
            return False
    else:
        print("\n❌ Founding validator registration incomplete")
        print(f"Registered: {len(registered_validators) if registered_validators else 0}/5")
        return False

if __name__ == "__main__":
    main()
