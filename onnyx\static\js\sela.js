/**
 * Onnyx Sela Creation JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the Sela creation form
    initSelaForm();
});

/**
 * Initialize the Sela creation form
 */
function initSelaForm() {
    // Get the form element
    const form = document.getElementById('sela-form');
    
    // Add event listener for form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate the form
            if (validateSelaForm()) {
                // Submit the form
                submitSelaForm();
            }
        });
    }
    
    // Add event listener for token name input
    const tokenNameInput = document.getElementById('token-name');
    const tokenSymbolInput = document.getElementById('token-symbol');
    
    if (tokenNameInput && tokenSymbolInput) {
        tokenNameInput.addEventListener('input', function() {
            // Generate a token symbol from the token name
            if (tokenNameInput.value && !tokenSymbolInput.value) {
                const words = tokenNameInput.value.split(' ');
                let symbol = '';
                
                if (words.length === 1) {
                    // If it's a single word, take the first 3-5 letters
                    symbol = words[0].substring(0, Math.min(5, words[0].length)).toUpperCase();
                } else {
                    // If it's multiple words, take the first letter of each word
                    words.forEach(word => {
                        if (symbol.length < 5 && word) {
                            symbol += word[0].toUpperCase();
                        }
                    });
                }
                
                tokenSymbolInput.value = symbol;
            }
        });
    }
    
    // Add event listener for category selection
    const categorySelect = document.getElementById('sela-category');
    
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            // Update the form based on the selected category
            updateFormForCategory(categorySelect.value);
        });
    }
    
    // Pre-fill the form with data from local storage
    prefillFormFromLocalStorage();
}

/**
 * Validate the Sela creation form
 * @returns {boolean} Whether the form is valid
 */
function validateSelaForm() {
    // Get the form elements
    const selaName = document.getElementById('sela-name');
    const selaDescription = document.getElementById('sela-description');
    const selaCategory = document.getElementById('sela-category');
    const tokenName = document.getElementById('token-name');
    const tokenSymbol = document.getElementById('token-symbol');
    const covenantAgreement = document.getElementById('covenant-agreement');
    const sovereigntyDeclaration = document.getElementById('sovereignty-declaration');
    
    // Validate the required fields
    let isValid = true;
    
    if (!selaName.value) {
        showFieldError(selaName, 'Please enter a name for your Sela');
        isValid = false;
    } else {
        clearFieldError(selaName);
    }
    
    if (!selaDescription.value) {
        showFieldError(selaDescription, 'Please enter a description for your Sela');
        isValid = false;
    } else {
        clearFieldError(selaDescription);
    }
    
    if (!selaCategory.value) {
        showFieldError(selaCategory, 'Please select a category for your Sela');
        isValid = false;
    } else {
        clearFieldError(selaCategory);
    }
    
    // Validate the token fields if they are filled
    if (tokenName.value && !tokenSymbol.value) {
        showFieldError(tokenSymbol, 'Please enter a symbol for your token');
        isValid = false;
    } else if (tokenSymbol.value && !tokenName.value) {
        showFieldError(tokenName, 'Please enter a name for your token');
        isValid = false;
    } else {
        clearFieldError(tokenName);
        clearFieldError(tokenSymbol);
    }
    
    // Validate the checkboxes
    if (!covenantAgreement.checked) {
        showFieldError(covenantAgreement.parentNode, 'Please agree to operate your Sela according to covenant principles');
        isValid = false;
    } else {
        clearFieldError(covenantAgreement.parentNode);
    }
    
    if (!sovereigntyDeclaration.checked) {
        showFieldError(sovereigntyDeclaration.parentNode, 'Please declare your business sovereign');
        isValid = false;
    } else {
        clearFieldError(sovereigntyDeclaration.parentNode);
    }
    
    return isValid;
}

/**
 * Show an error message for a form field
 * @param {HTMLElement} field - The form field
 * @param {string} message - The error message
 */
function showFieldError(field, message) {
    // Remove any existing error message
    clearFieldError(field);
    
    // Create the error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    
    // Add the error message after the field
    field.parentNode.appendChild(errorElement);
    
    // Add the error class to the field
    field.classList.add('error');
}

/**
 * Clear the error message for a form field
 * @param {HTMLElement} field - The form field
 */
function clearFieldError(field) {
    // Remove the error class from the field
    field.classList.remove('error');
    
    // Remove any existing error message
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        field.parentNode.removeChild(errorElement);
    }
}

/**
 * Update the form based on the selected category
 * @param {string} category - The selected category
 */
function updateFormForCategory(category) {
    // Get the token name and symbol inputs
    const tokenNameInput = document.getElementById('token-name');
    const tokenSymbolInput = document.getElementById('token-symbol');
    
    // Update the token name and symbol placeholders based on the category
    if (category === 'food') {
        tokenNameInput.placeholder = 'e.g., Kitchen Tokens';
        tokenSymbolInput.placeholder = 'e.g., KTC';
    } else if (category === 'crafts') {
        tokenNameInput.placeholder = 'e.g., Craftsman Tokens';
        tokenSymbolInput.placeholder = 'e.g., CRT';
    } else if (category === 'education') {
        tokenNameInput.placeholder = 'e.g., Wisdom Tokens';
        tokenSymbolInput.placeholder = 'e.g., WDM';
    } else if (category === 'services') {
        tokenNameInput.placeholder = 'e.g., Service Tokens';
        tokenSymbolInput.placeholder = 'e.g., SVC';
    } else if (category === 'retail') {
        tokenNameInput.placeholder = 'e.g., Shop Tokens';
        tokenSymbolInput.placeholder = 'e.g., SHP';
    } else if (category === 'tech') {
        tokenNameInput.placeholder = 'e.g., Tech Tokens';
        tokenSymbolInput.placeholder = 'e.g., TCH';
    } else if (category === 'health') {
        tokenNameInput.placeholder = 'e.g., Wellness Tokens';
        tokenSymbolInput.placeholder = 'e.g., WLN';
    } else if (category === 'agriculture') {
        tokenNameInput.placeholder = 'e.g., Harvest Tokens';
        tokenSymbolInput.placeholder = 'e.g., HRV';
    } else if (category === 'construction') {
        tokenNameInput.placeholder = 'e.g., Builder Tokens';
        tokenSymbolInput.placeholder = 'e.g., BLD';
    } else {
        tokenNameInput.placeholder = 'Your loyalty token name';
        tokenSymbolInput.placeholder = '3-5 letter symbol';
    }
}

/**
 * Pre-fill the form with data from local storage
 */
function prefillFormFromLocalStorage() {
    // Get the selected nation and tribe from local storage
    const selectedNation = localStorage.getItem('selectedNation');
    const selectedTribe = localStorage.getItem('selectedTribe');
    
    // If there is a selected nation and tribe, update the form
    if (selectedNation && selectedTribe) {
        // Add a field to display the selected nation and tribe
        const formGroup = document.createElement('div');
        formGroup.className = 'form-group';
        
        const label = document.createElement('label');
        label.textContent = 'Your Nation and Tribe';
        
        const value = document.createElement('div');
        value.className = 'selected-nation-tribe';
        value.textContent = `${selectedTribe} of ${selectedNation}`;
        
        formGroup.appendChild(label);
        formGroup.appendChild(value);
        
        // Insert it at the beginning of the form
        const form = document.getElementById('sela-form');
        form.insertBefore(formGroup, form.firstChild);
    }
}

/**
 * Submit the Sela creation form
 */
function submitSelaForm() {
    // Get the form data
    const selaName = document.getElementById('sela-name').value;
    const selaDescription = document.getElementById('sela-description').value;
    const selaCategory = document.getElementById('sela-category').value;
    const tokenName = document.getElementById('token-name').value;
    const tokenSymbol = document.getElementById('token-symbol').value;
    
    // Get the selected nation and tribe from local storage
    const selectedNation = localStorage.getItem('selectedNation');
    const selectedTribe = localStorage.getItem('selectedTribe');
    
    // Create the Sela data object
    const selaData = {
        name: selaName,
        description: selaDescription,
        category: selaCategory,
        tokenName: tokenName,
        tokenSymbol: tokenSymbol,
        nation: selectedNation,
        tribe: selectedTribe,
        createdAt: new Date().toISOString()
    };
    
    // Save the Sela data to local storage
    localStorage.setItem('selaData', JSON.stringify(selaData));
    
    // Show a success message
    showSuccessModal(selaName);
}

/**
 * Show a success modal
 * @param {string} selaName - The name of the created Sela
 */
function showSuccessModal(selaName) {
    // Create the modal
    const modal = document.createElement('div');
    modal.className = 'success-modal';
    
    // Create the modal content
    const modalContent = `
        <div class="success-modal-content">
            <div class="success-icon">✓</div>
            <h3>Sela Created Successfully!</h3>
            <p>Congratulations! Your Sela "${selaName}" has been created and is now protected by the Onnyx covenant principles.</p>
            <p>Your business is now operating under tribal jurisdiction and is protected from unjust seizure, taxation, and manipulation by Caesar.</p>
            <div class="success-modal-actions">
                <button class="success-modal-btn">Continue to Protection Framework</button>
            </div>
        </div>
    `;
    
    // Set the modal content
    modal.innerHTML = modalContent;
    
    // Add the modal to the body
    document.body.appendChild(modal);
    
    // Show the modal
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
    
    // Add event listener to the continue button
    const continueButton = modal.querySelector('.success-modal-btn');
    continueButton.addEventListener('click', () => {
        // Close the modal
        modal.classList.remove('active');
        
        // Redirect to the next step
        setTimeout(() => {
            window.location.href = '/onboarding/sovereignty/protection.html';
        }, 300);
    });
}
