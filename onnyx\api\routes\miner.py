"""
Onnyx Miner Routes

This module provides API routes for mining operations.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any

from blockchain.consensus.miner import BlockMiner

# Create router
miner_router = APIRouter()

# Create miner instance
miner = BlockMiner()

@miner_router.post("/mine")
def mine_block(proposer_id: str = Query(..., description="The identity ID of the block proposer")) -> Dict[str, Any]:
    """
    Mine a new block.
    
    Args:
        proposer_id: The identity ID of the block proposer
    
    Returns:
        Information about the mined block
    """
    try:
        block = miner.create_block(proposer_id)
        
        return {
            "status": "block created",
            "index": block["index"],
            "hash": block["hash"],
            "tx_count": len(block["transactions"]),
            "timestamp": block["timestamp"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@miner_router.get("/stats")
def get_mining_stats() -> Dict[str, Any]:
    """
    Get mining statistics.
    
    Returns:
        Mining statistics
    """
    return miner.get_mining_stats()

@miner_router.get("/latest-blocks/{count}")
def get_latest_blocks(count: int = 10) -> Dict[str, Any]:
    """
    Get the latest blocks.
    
    Args:
        count: The number of blocks to get
    
    Returns:
        The latest blocks
    """
    if count <= 0 or count > 100:
        raise HTTPException(status_code=400, detail="Count must be between 1 and 100")
    
    blocks = miner.get_latest_blocks(count)
    
    return {
        "count": len(blocks),
        "blocks": blocks
    }
