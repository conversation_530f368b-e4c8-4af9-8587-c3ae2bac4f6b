#!/usr/bin/env python3
"""
ONNYX Missing Database Tables Creation
Creates mempool and tokens tables for complete transaction processing.
"""

import sqlite3
import time
import json

def create_mempool_table(conn):
    """Create the mempool table for pending transactions."""
    print("🔧 Creating mempool table...")

    cursor = conn.cursor()

    # Create mempool table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS mempool (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id TEXT UNIQUE NOT NULL,
            transaction_type TEXT NOT NULL,
            from_identity TEXT,
            to_identity TEXT,
            amount REAL,
            token_id TEXT,
            data TEXT,
            status TEXT DEFAULT 'pending',
            priority INTEGER DEFAULT 1,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            expires_at INTEGER,
            signature TEXT,
            hash TEXT
        )
    """)

    # Create indexes for performance
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_mempool_status ON mempool(status)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_mempool_created ON mempool(created_at)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_mempool_priority ON mempool(priority DESC)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_mempool_expires ON mempool(expires_at)")

    print("✅ Mempool table created successfully")
    return True

def create_tokens_table(conn):
    """Create the tokens table for balance tracking."""
    print("🔧 Creating tokens table...")

    cursor = conn.cursor()

    # Create tokens table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            identity_id TEXT NOT NULL,
            token_id TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0.0,
            locked_balance REAL NOT NULL DEFAULT 0.0,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            UNIQUE(identity_id, token_id)
        )
    """)

    # Create indexes for performance
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_tokens_identity ON tokens(identity_id)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_tokens_token_id ON tokens(token_id)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_tokens_balance ON tokens(balance)")

    print("✅ Tokens table created successfully")
    return True

def create_transactions_table(conn):
    """Create or update the transactions table for transaction history."""
    print("🔧 Checking transactions table...")

    cursor = conn.cursor()

    # Check if transactions table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
    table_exists = cursor.fetchone() is not None

    if table_exists:
        print("   ℹ️ Transactions table already exists, checking schema...")

        # Get current schema
        cursor.execute("PRAGMA table_info(transactions)")
        columns = {col[1]: col[2] for col in cursor.fetchall()}

        # Add missing columns
        missing_columns = []
        required_columns = {
            'id': 'INTEGER',
            'block_number': 'INTEGER',
            'token_id': 'TEXT',
            'fee': 'REAL',
            'data': 'TEXT',
            'signature': 'TEXT',
            'hash': 'TEXT',
            'confirmed_at': 'INTEGER'
        }

        for col_name, col_type in required_columns.items():
            if col_name not in columns:
                missing_columns.append((col_name, col_type))

        # Add missing columns
        for col_name, col_type in missing_columns:
            try:
                default_value = "DEFAULT 0.0" if col_type == "REAL" else ""
                cursor.execute(f"ALTER TABLE transactions ADD COLUMN {col_name} {col_type} {default_value}")
                print(f"   ✅ Added column: {col_name} ({col_type})")
            except Exception as e:
                print(f"   ⚠️ Could not add column {col_name}: {e}")

        print("✅ Transactions table schema updated")
    else:
        # Create new table
        cursor.execute("""
            CREATE TABLE transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id TEXT UNIQUE NOT NULL,
                block_number INTEGER,
                block_hash TEXT,
                transaction_type TEXT NOT NULL,
                from_identity TEXT,
                to_identity TEXT,
                amount REAL,
                token_id TEXT,
                fee REAL DEFAULT 0.0,
                data TEXT,
                status TEXT DEFAULT 'confirmed',
                signature TEXT,
                hash TEXT,
                created_at INTEGER NOT NULL,
                confirmed_at INTEGER
            )
        """)
        print("✅ Transactions table created successfully")

    # Create indexes for performance (only if they don't exist)
    indexes = [
        ("idx_transactions_from", "from_identity"),
        ("idx_transactions_to", "to_identity"),
        ("idx_transactions_type", "transaction_type"),
        ("idx_transactions_status", "status"),
        ("idx_transactions_created", "created_at")
    ]

    for idx_name, idx_column in indexes:
        try:
            cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON transactions({idx_column})")
        except Exception as e:
            print(f"   ⚠️ Could not create index {idx_name}: {e}")

    return True

def initialize_genesis_tokens(conn):
    """Initialize token balances for genesis accounts."""
    print("🔧 Initializing genesis token balances...")

    cursor = conn.cursor()
    current_time = int(time.time())

    # Get all identities
    cursor.execute("SELECT identity_id, name FROM identities")
    identities = cursor.fetchall()

    for identity_id, name in identities:
        # Initialize ONX balance (genesis allocation)
        cursor.execute("""
            INSERT OR IGNORE INTO tokens (identity_id, token_id, balance, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """, (identity_id, "ONX", 0.0, current_time, current_time))

        print(f"   ✅ Initialized ONX balance for {name}")

    print("✅ Genesis token balances initialized")
    return True

def add_sample_transactions(conn):
    """Add some sample transactions to demonstrate the system."""
    print("🔧 Adding sample transactions to mempool...")

    cursor = conn.cursor()
    current_time = int(time.time())

    # Get identities for sample transactions
    cursor.execute("SELECT identity_id, name FROM identities LIMIT 3")
    identities = cursor.fetchall()

    if len(identities) >= 2:
        # Sample transaction 1: Token transfer
        sample_tx_1 = {
            "transaction_id": f"tx_{current_time}_001",
            "transaction_type": "transfer",
            "from_identity": identities[0][0],
            "to_identity": identities[1][0],
            "amount": 5.0,
            "token_id": "ONX",
            "data": json.dumps({
                "memo": "Sample token transfer",
                "from_name": identities[0][1],
                "to_name": identities[1][1]
            }),
            "status": "pending",
            "priority": 2,
            "created_at": current_time,
            "updated_at": current_time,
            "expires_at": current_time + 3600  # 1 hour expiry
        }

        cursor.execute("""
            INSERT INTO mempool (transaction_id, transaction_type, from_identity, to_identity,
                               amount, token_id, data, status, priority, created_at, updated_at, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            sample_tx_1["transaction_id"], sample_tx_1["transaction_type"],
            sample_tx_1["from_identity"], sample_tx_1["to_identity"],
            sample_tx_1["amount"], sample_tx_1["token_id"], sample_tx_1["data"],
            sample_tx_1["status"], sample_tx_1["priority"], sample_tx_1["created_at"],
            sample_tx_1["updated_at"], sample_tx_1["expires_at"]
        ))

        # Sample transaction 2: Business service payment
        if len(identities) >= 3:
            sample_tx_2 = {
                "transaction_id": f"tx_{current_time}_002",
                "transaction_type": "payment",
                "from_identity": identities[0][0],
                "to_identity": identities[2][0],
                "amount": 25.0,
                "token_id": "ONX",
                "data": json.dumps({
                    "memo": "Hair styling service payment",
                    "service": "Professional hair styling",
                    "from_name": identities[0][1],
                    "to_name": identities[2][1]
                }),
                "status": "pending",
                "priority": 1,
                "created_at": current_time + 1,
                "updated_at": current_time + 1,
                "expires_at": current_time + 7200  # 2 hour expiry
            }

            cursor.execute("""
                INSERT INTO mempool (transaction_id, transaction_type, from_identity, to_identity,
                                   amount, token_id, data, status, priority, created_at, updated_at, expires_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                sample_tx_2["transaction_id"], sample_tx_2["transaction_type"],
                sample_tx_2["from_identity"], sample_tx_2["to_identity"],
                sample_tx_2["amount"], sample_tx_2["token_id"], sample_tx_2["data"],
                sample_tx_2["status"], sample_tx_2["priority"], sample_tx_2["created_at"],
                sample_tx_2["updated_at"], sample_tx_2["expires_at"]
            ))

        print("✅ Sample transactions added to mempool")
    else:
        print("⚠️ Not enough identities for sample transactions")

    return True

def verify_table_creation(conn):
    """Verify all tables were created successfully."""
    print("\n🔍 Verifying table creation...")

    cursor = conn.cursor()

    # Check mempool table
    cursor.execute("SELECT COUNT(*) FROM mempool")
    mempool_count = cursor.fetchone()[0]
    print(f"✅ Mempool table: {mempool_count} transactions")

    # Check tokens table
    cursor.execute("SELECT COUNT(*) FROM tokens")
    tokens_count = cursor.fetchone()[0]
    print(f"✅ Tokens table: {tokens_count} token records")

    # Check transactions table
    cursor.execute("SELECT COUNT(*) FROM transactions")
    transactions_count = cursor.fetchone()[0]
    print(f"✅ Transactions table: {transactions_count} confirmed transactions")

    # Show sample data
    if mempool_count > 0:
        cursor.execute("SELECT transaction_id, transaction_type, amount, token_id FROM mempool LIMIT 3")
        sample_mempool = cursor.fetchall()
        print("\n📋 Sample Mempool Transactions:")
        for tx in sample_mempool:
            print(f"   • {tx[0]}: {tx[1]} - {tx[2]} {tx[3]}")

    if tokens_count > 0:
        cursor.execute("""
            SELECT t.identity_id, i.name, t.token_id, t.balance
            FROM tokens t
            JOIN identities i ON t.identity_id = i.identity_id
            LIMIT 5
        """)
        sample_tokens = cursor.fetchall()
        print("\n💰 Token Balances:")
        for token in sample_tokens:
            print(f"   • {token[1]}: {token[3]} {token[2]}")

    return True

def main():
    """Create missing database tables for complete ONNYX functionality."""
    print("🚀 ONNYX DATABASE INFRASTRUCTURE COMPLETION")
    print("=" * 60)
    print("Creating missing tables for complete transaction processing")
    print()

    try:
        # Connect to production database
        conn = sqlite3.connect("data/onnyx.db")
        conn.row_factory = sqlite3.Row  # Enable column access by name

        print("✅ Connected to production database")

        # Create missing tables
        success_count = 0

        if create_mempool_table(conn):
            success_count += 1

        if create_tokens_table(conn):
            success_count += 1

        if create_transactions_table(conn):
            success_count += 1

        # Initialize data
        if initialize_genesis_tokens(conn):
            success_count += 1

        if add_sample_transactions(conn):
            success_count += 1

        # Commit all changes
        conn.commit()
        print("\n✅ All database changes committed")

        # Verify creation
        verify_table_creation(conn)

        conn.close()

        print(f"\n📊 COMPLETION SUMMARY")
        print("=" * 30)
        print(f"✅ Database operations completed: {success_count}/5")
        print("✅ Mempool table: Ready for transaction processing")
        print("✅ Tokens table: Ready for balance tracking")
        print("✅ Transactions table: Ready for transaction history")
        print("✅ Genesis balances: Initialized for all identities")
        print("✅ Sample transactions: Added for testing")

        print(f"\n🎉 DATABASE INFRASTRUCTURE COMPLETE!")
        print("🔗 All mining system warnings eliminated")
        print("💰 Token reward tracking now functional")
        print("📝 Transaction processing fully enabled")
        print("🚀 Ready for Phase 1 production operations!")

        return True

    except Exception as e:
        print(f"❌ Error creating database tables: {e}")
        return False

if __name__ == "__main__":
    main()
