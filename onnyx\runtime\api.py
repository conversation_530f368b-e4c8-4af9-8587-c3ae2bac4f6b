#!/usr/bin/env python3
"""
Onnyx API Server Entry Point

This is the main entry point for running the Onnyx API server.
"""

import sys
import os
import argparse

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def main():
    """Main API server entry point."""
    parser = argparse.ArgumentParser(description='Onnyx API Server')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    try:
        # Import the API module from the new structure
        from api.handlers.api import app
        
        # Configure the app
        if args.debug:
            app.config['DEBUG'] = True
        
        # Run the server
        print(f"Starting Onnyx API server on {args.host}:{args.port}")
        app.run(host=args.host, port=args.port, debug=args.debug)
        
    except ImportError as e:
        print(f"Error importing API module: {e}")
        print("Please ensure the API module is properly configured in the new structure.")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting API server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
