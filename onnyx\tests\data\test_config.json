{"chain_params": {"block_reward": 5, "reward_token": "TEST_ONX", "quorum_percent": 30, "vote_pass_ratio": 0.5, "mint_cap_factor": 2.0, "min_etzem_score": 10, "validator_badges": ["TEST_VALIDATOR_BADGE"], "council_badges": ["TEST_COUNCIL_BADGE"], "guardian_badges": ["TEST_GUARDIAN_BADGE"], "proposal_badges": ["TEST_PROPOSAL_BADGE"], "validator_rotation_interval": 60, "scroll_voting_period": 300, "scroll_implementation_delay": 60, "max_token_supply": 10000, "min_stake_amount": 10, "stake_lock_period": 300, "max_mempool_size": 100, "max_block_size": 10000, "target_block_time": 10, "difficulty_adjustment_period": 10, "max_transaction_size": 1000, "max_transactions_per_block": 100}, "network_params": {"networks": {"testnet": {"name": "Onnyx Test Network", "chain_id": "onnyx-test-1", "p2p_port": 38333, "rpc_port": 38332, "seed_nodes": ["127.0.0.1:38333"], "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"}}, "protocol_version": "0.1.0", "min_peer_version": "0.1.0", "max_connections": 5, "connection_timeout_seconds": 5, "handshake_timeout_seconds": 2}, "node_params": {"node_id": "test_node", "node_port": 38080, "node_host": "localhost", "node_peers": [], "node_public_key": "", "node_private_key": "", "data_dir": "tests/data", "max_peers": 5, "sync_interval": 10, "broadcast_interval": 5, "mempool_sync_interval": 5, "block_sync_interval": 10, "peer_discovery_interval": 30, "heartbeat_interval": 5, "connection_timeout": 5, "max_mempool_size": 100, "max_block_size": 10000, "max_message_size": 102400, "log_level": "DEBUG"}, "sela_params": {"sela_id": "test_sela", "identity_id": "test_identity", "private_key_path": "tests/data/keys/test_sela.pem", "api_port": 38888, "role": "validator", "auto_mine": false, "mine_interval": 10, "activity_log_path": "tests/data/activity_log.json", "validator_rotation": {"enabled": true, "min_etzem_score": 10, "required_badges": ["TEST_SELA_FOUNDER", "TEST_VALIDATOR_BADGE"]}}}