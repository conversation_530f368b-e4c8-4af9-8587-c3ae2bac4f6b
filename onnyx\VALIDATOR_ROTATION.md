# Onnyx Validator Rotation Engine

## Overview

The Validator Rotation Engine is a system that fairly selects which <PERSON><PERSON> (miner) is allowed to propose the next block in the Onnyx blockchain.

## Features

- **Round-Robin Scheduler**: Rotates among eligible Selas in order
- **Eligibility Checks**: Only <PERSON><PERSON> with sufficient <PERSON>tzem or VALIDATOR badge can participate
- **Timed Slots**: Optionally restrict each proposer to a fixed block height or time window
- **On-Chain Record**: Last proposer and rotation queue stored on-chain or locally for enforcement

## Components

### ValidatorRotationEngine

The ValidatorRotationEngine manages the rotation of validators. It determines which <PERSON><PERSON> is allowed to propose the next block.

#### State

The rotation engine maintains the following state:

```json
{
  "queue": ["alices_salon", "bobs_barbershop", "charlies_cafe"],
  "last_proposer": "bobs_barbershop",
  "height": 42,
  "last_update": 1746801307,
  "update_interval": 3600,
  "min_etzem_score": 30,
  "required_badges": ["VALIDATOR_ELIGIBLE_BADGE"]
}
```

#### Methods

- **update_queue()**: Update the queue of eligible validators
- **get_next_validator(height)**: Get the next validator for a given block height
- **is_valid_proposer(sela_id, height)**: Check if a Sela is the valid proposer for a given block height
- **mark_proposed(sela_id, height)**: Mark a block as proposed by a validator
- **get_rotation_status()**: Get the current rotation status
- **set_update_interval(interval)**: Set the queue update interval
- **set_min_etzem_score(score)**: Set the minimum Etzem score required for eligibility
- **set_required_badges(badges)**: Set the required badges for eligibility

### Integration with Mining

The Validator Rotation Engine is integrated with the mining process to ensure that only the designated validator can propose a block at a given height.

```python
def mine_block(identity_id, sela_id, private_key_pem, enforce_rotation=True):
    # Check if it's this Sela's turn to propose a block
    if enforce_rotation and sela_id:
        if not rotation_engine.is_valid_proposer(sela_id, current_height):
            next_validator = rotation_engine.get_next_validator(current_height)
            raise Exception(f"Not your turn — current proposer should be: {next_validator}")

    # Create a new block
    block = create_block(identity_id, sela_id, private_key_pem)

    # Mark the block as proposed
    rotation_engine.mark_proposed(sela_id, block["index"])

    return block
```

## API Endpoints

### Get Rotation Status

```
GET /api/rotation/status
```

Returns the current rotation status.

Example response:

```json
{
  "queue": ["alices_salon", "bobs_barbershop", "charlies_cafe"],
  "last_proposer": "bobs_barbershop",
  "height": 42,
  "last_update": 1746801307,
  "update_interval": 3600,
  "min_etzem_score": 30,
  "required_badges": ["VALIDATOR_ELIGIBLE_BADGE"]
}
```

### Get Next Validator

```
GET /api/rotation/next-validator/{height}
```

Returns the next validator for a given block height.

Example response:

```json
{
  "height": 43,
  "next_validator": "charlies_cafe",
  "sela": {
    "sela_id": "charlies_cafe",
    "name": "Charlie's Cafe",
    "founder": "charlie",
    "type": "BUSINESS",
    "token_type": "CAFE_TOKEN"
  },
  "founder": {
    "identity_id": "charlie",
    "name": "Charlie",
    "public_key": "0xabcdef123456789",
    "badges": ["VALIDATOR_ELIGIBLE_BADGE"]
  }
}
```

### Update Queue

```
POST /api/rotation/update-queue
```

Updates the queue of eligible validators.

Example response:

```json
{
  "queue": ["alices_salon", "bobs_barbershop", "charlies_cafe"],
  "queue_details": [
    {
      "sela_id": "alices_salon",
      "sela": {
        "sela_id": "alices_salon",
        "name": "Alice's Salon",
        "founder": "alice",
        "type": "BUSINESS",
        "token_type": "SALON_TOKEN"
      },
      "founder": {
        "identity_id": "alice",
        "name": "Alice",
        "public_key": "0x123456789abcdef",
        "badges": ["VALIDATOR_ELIGIBLE_BADGE"]
      }
    },
    ...
  ]
}
```

### Set Update Interval

```
POST /api/rotation/set-update-interval
```

Sets the queue update interval.

Example request:

```json
{
  "interval": 3600
}
```

### Set Minimum Etzem Score

```
POST /api/rotation/set-min-etzem-score
```

Sets the minimum Etzem score required for eligibility.

Example request:

```json
{
  "score": 30
}
```

### Set Required Badges

```
POST /api/rotation/set-required-badges
```

Sets the required badges for eligibility.

Example request:

```json
{
  "badges": ["VALIDATOR_ELIGIBLE_BADGE"]
}
```

### Check if Valid Proposer

```
GET /api/rotation/is-valid-proposer/{sela_id}/{height}
```

Checks if a Sela is the valid proposer for a given block height.

Example response:

```json
{
  "sela_id": "charlies_cafe",
  "height": 43,
  "is_valid_proposer": true,
  "next_validator": "charlies_cafe"
}
```

## Usage

### Running the Test Script

```bash
python test_rotation_engine.py
```

### Using the Validator Rotation Engine

```python
from consensus.rotation_engine import rotation_engine

# Update the queue
rotation_engine.update_queue()

# Get the next validator
next_validator = rotation_engine.get_next_validator(current_height)

# Check if a Sela is the valid proposer
is_valid = rotation_engine.is_valid_proposer(sela_id, current_height)

# Mark a block as proposed
rotation_engine.mark_proposed(sela_id, block["index"])

# Get the rotation status
status = rotation_engine.get_rotation_status()
```

## Validator Selection Process

1. **Eligibility**: A Sela is eligible to be a validator if:
   - The founder's identity has the required badges (e.g., VALIDATOR_ELIGIBLE_BADGE)
   - The founder's Etzem score is above the minimum threshold

2. **Queue Formation**: Eligible Selas are sorted by Sela ID for deterministic ordering

3. **Validator Selection**: The validator for a given block height is selected using a round-robin approach:
   ```
   index = height % len(queue)
   next_validator = queue[index]
   ```

4. **Enforcement**: When a Sela attempts to mine a block, the system checks if it's their turn:
   ```
   if not rotation_engine.is_valid_proposer(sela_id, height):
       raise Exception("Not your turn")
   ```

## Benefits

- **Fairness**: Ensures that all eligible Selas have an equal opportunity to propose blocks
- **Predictability**: Makes it possible to predict which Sela will propose the next block
- **Security**: Prevents a single Sela from dominating block production
- **Efficiency**: Eliminates the need for competitive mining (Proof of Work)
- **Governance**: Allows the community to control who can participate in block production
