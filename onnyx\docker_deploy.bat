@echo off
setlocal enabledelayedexpansion

:: Onnyx Docker Deployment Script for Windows

:: Print banner
echo.
echo   ____  _   _ _   ___   ____  __
echo  / __ \^| \ ^| ^| \ ^| \ \ / /\ \/ /
echo ^| ^|  ^| ^|  \^| ^|  \^| ^|\ V /  \  / 
echo ^| ^|  ^| ^| . ` ^| . ` ^| ^> ^<   /  \ 
echo ^| ^|__^| ^| ^|\  ^| ^|\  ^|/ . \ / /\ \
echo  \____/^|_^| \_^|_^| \_/_/ \_/_/  \_\
echo.
echo Onnyx Docker Deployment Script for Windows
echo =========================================
echo.

:: Check if Docker is installed
where docker >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Docker is not installed.
    echo Please install Docker and try again.
    exit /b 1
)

:: Check if Docker Compose is installed
where docker-compose >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Docker Compose is not installed.
    echo Please install Docker Compose and try again.
    exit /b 1
)

:: Parse arguments
set BACKUP=false
set REBUILD=false
set SERVICES=

:parse_args
if "%~1"=="" goto :end_parse_args
if "%~1"=="--backup" (
    set BACKUP=true
    shift
    goto :parse_args
)
if "%~1"=="--rebuild" (
    set REBUILD=true
    shift
    goto :parse_args
)
if "%~1"=="--services" (
    set SERVICES=%~2
    shift
    shift
    goto :parse_args
)
echo Error: Unknown option %~1
echo Usage: %0 [--backup] [--rebuild] [--services service1,service2,...]
exit /b 1

:end_parse_args

:: Backup data if requested
if "%BACKUP%"=="true" (
    echo Backing up data...
    
    :: Create backup directory if it doesn't exist
    if not exist backups mkdir backups
    
    :: Create backup filename with timestamp
    for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /format:list') do set datetime=%%I
    set TIMESTAMP=%datetime:~0,8%-%datetime:~8,6%
    set BACKUP_FILE=backups\onnyx-data-%TIMESTAMP%.tar.gz
    
    :: Create backup
    docker run --rm -v onnyx-data:/data -v %CD%\backups:/backup alpine tar -czf /backup/onnyx-data-%TIMESTAMP%.tar.gz /data
    
    if %ERRORLEVEL% equ 0 (
        echo Backup created: %BACKUP_FILE%
    ) else (
        echo Error creating backup.
        exit /b 1
    )
)

:: Rebuild images if requested
if "%REBUILD%"=="true" (
    echo Rebuilding Docker images...
    
    if "%SERVICES%"=="" (
        :: Rebuild all images
        docker-compose build --no-cache
    ) else (
        :: Rebuild specific services
        set SERVICES_SPACE=!SERVICES:,= !
        docker-compose build --no-cache !SERVICES_SPACE!
    )
    
    if %ERRORLEVEL% equ 0 (
        echo Docker images rebuilt successfully.
    ) else (
        echo Error rebuilding Docker images.
        exit /b 1
    )
)

:: Start containers
echo Starting Onnyx containers...

if "%SERVICES%"=="" (
    :: Start all services
    docker-compose up -d
) else (
    :: Start specific services
    set SERVICES_SPACE=!SERVICES:,= !
    docker-compose up -d !SERVICES_SPACE!
)

if %ERRORLEVEL% equ 0 (
    echo Onnyx containers started successfully.
    
    :: Show container status
    echo.
    echo Container Status:
    docker-compose ps
    
    :: Show API URL
    for /f "tokens=2 delims=:" %%p in ('docker-compose port api 5000 2^>nul') do set API_PORT=%%p
    if defined API_PORT (
        echo.
        echo Onnyx API is available at: http://localhost:!API_PORT!
    )
    
    :: Show Transaction Viewer URL
    for /f "tokens=2 delims=:" %%p in ('docker-compose port transaction-viewer 5002 2^>nul') do set TX_VIEWER_PORT=%%p
    if defined TX_VIEWER_PORT (
        echo Onnyx Transaction Viewer is available at: http://localhost:!TX_VIEWER_PORT!
    )
) else (
    echo Error starting Onnyx containers.
    exit /b 1
)

echo.
echo Deployment completed successfully.
echo Use 'docker-compose logs -f' to view logs.
echo Use 'docker-compose down' to stop and remove containers.

endlocal
