# Onnyx Transaction Viewer

A web-based explorer for the Onnyx blockchain, allowing users to browse transactions, blocks, tokens, and identities.

## Features

- **Home Page**: Summary statistics and quick links
- **Transactions**: View confirmed transactions with pagination
- **Mempool**: View pending transactions not yet in blocks
- **Tokens**: Browse all created Mikvah tokens
- **Identities**: Explore soulbound identities
- **Search**: Find transactions, tokens, or identities by ID or name
- **Detailed Views**: Examine specific transactions, identities, and tokens

## Folder Structure

```
onnyx_viewer/
├── app.py                # Flask application
├── templates/            # HTML templates
│   ├── base.html         # Base template with layout
│   ├── index.html        # Home page
│   ├── transactions.html # Transactions list
│   ├── mempool.html      # Mempool transactions
│   ├── tokens.html       # Tokens list
│   ├── identities.html   # Identities list
│   ├── tx_detail.html    # Transaction details
│   ├── token_detail.html # Token details
│   ├── identity_detail.html # Identity details
│   └── search.html       # Search results
├── static/               # Static assets
│   └── style.css         # CSS styles
└── data/                 # Data files
    ├── blockchain.json   # Blockchain data
    ├── mempool.json      # Mempool transactions
    ├── identities.json   # Identity registry
    └── tokens.json       # Token registry
```

## Running the Viewer

The viewer is integrated with the main Onnyx explorer script. To run it:

```bash
python onnyx-explorer.py
```

By default, it will:
1. Create the necessary data directory structure
2. Copy blockchain data from the main Onnyx data directory
3. Start the Flask server on http://127.0.0.1:8080

### Command Line Options

- `--host`: Host to bind to (default: 127.0.0.1)
- `--port`: Port to bind to (default: 8080)
- `--debug`: Run in debug mode
- `--no-copy`: Don't copy blockchain data from the main data directory
- `--use-web`: Use the original web module instead of this viewer

## Data Sources

The viewer reads data from the following JSON files:

- `blockchain.json`: Contains all confirmed blocks and their transactions
- `mempool.json`: Contains pending transactions not yet included in a block
- `identities.json`: Contains all identities in the system
- `tokens.json`: Contains all tokens in the system

These files are automatically copied from the main Onnyx data directory when the viewer starts.
