from flask import Flask, render_template, request, redirect, url_for, abort, jsonify
import json
import os
import datetime
import math
import time
import logging
import requests
import uuid
import hashlib
import random
import re
from data_sync import data_sync

# Configure logging
logger = logging.getLogger("onnyx.viewer.app")

app = Flask(__name__)

# Load data
def load_data(file_name):
    """Load data from a JSON file."""
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    file_path = os.path.join(data_dir, file_name)
    
    try:
        with open(file_path, "r") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading {file_name}: {str(e)}")
        return {}

@app.route("/")
def index():
    """Display the home page."""
    return render_template(
        "coming_soon.html",
        sela_id="home",
        sela_name="Onnyx Explorer",
        sela_type="EXPLORER",
        owner_name="Onnyx"
    )

@app.route("/sela/<sela_id>")
def sela_shop_window(sela_id):
    """Display a coming soon page for a Sela shop window."""
    # Extract Sela name and type from the ID
    sela_name = None
    sela_type = None
    owner_name = None
    
    # Try to parse the sela_id to get a readable name
    if sela_id:
        # Check if it's one of our demo Selas
        if sela_id == "alice_salon":
            sela_name = "Alice's Beauty Salon"
            sela_type = "BEAUTY"
            owner_name = "Alice"
        elif sela_id == "bob_barber":
            sela_name = "Bob's Barbershop"
            sela_type = "BARBER"
            owner_name = "Bob"
        elif sela_id == "charlie_cafe":
            sela_name = "Charlie's Cafe"
            sela_type = "CAFE"
            owner_name = "Charlie"
        else:
            # Try to make a readable name from the ID
            words = re.findall(r'[A-Za-z][a-z]*', sela_id)
            if words:
                sela_name = ' '.join(word.capitalize() for word in words)
                sela_type = "BUSINESS"
                owner_name = words[0].capitalize() if words else "Unknown"
    
    return render_template(
        "coming_soon.html",
        sela_id=sela_id,
        sela_name=sela_name or f"Sela {sela_id}",
        sela_type=sela_type or "BUSINESS",
        owner_name=owner_name or "Unknown"
    )

@app.route("/chain")
def chain():
    """Display the blockchain."""
    return render_template(
        "coming_soon.html",
        sela_id="chain",
        sela_name="Onnyx Blockchain",
        sela_type="CHAIN",
        owner_name="Onnyx"
    )

@app.route("/mempool")
def mempool():
    """Display the mempool."""
    return render_template(
        "coming_soon.html",
        sela_id="mempool",
        sela_name="Onnyx Mempool",
        sela_type="MEMPOOL",
        owner_name="Onnyx"
    )

@app.route("/identity/list")
def identity_list():
    """Display the list of identities."""
    return render_template(
        "coming_soon.html",
        sela_id="identities",
        sela_name="Onnyx Identities",
        sela_type="IDENTITIES",
        owner_name="Onnyx"
    )

@app.route("/token/list")
def token_list():
    """Display the list of tokens."""
    return render_template(
        "coming_soon.html",
        sela_id="tokens",
        sela_name="Onnyx Tokens",
        sela_type="TOKENS",
        owner_name="Onnyx"
    )

@app.route("/nodes")
def nodes():
    """Display network nodes information."""
    return render_template(
        "coming_soon.html",
        sela_id="network",
        sela_name="Onnyx Network",
        sela_type="NETWORK",
        owner_name="Onnyx"
    )

@app.route("/economic-structure")
def economic_structure():
    """Display economic structure information."""
    return render_template(
        "coming_soon.html",
        sela_id="economics",
        sela_name="Onnyx Economic Structure",
        sela_type="ECONOMICS",
        owner_name="Onnyx"
    )

@app.route("/admin")
def admin():
    """Display admin panel."""
    return render_template(
        "coming_soon.html",
        sela_id="admin",
        sela_name="Onnyx Admin Panel",
        sela_type="ADMIN",
        owner_name="Onnyx"
    )

@app.errorhandler(404)
def page_not_found(e):
    """Handle 404 errors by showing a coming soon page."""
    path = request.path
    
    # Check if this is a Sela shop window request
    sela_match = re.match(r'/sela/([a-zA-Z0-9_-]+)', path)
    if sela_match:
        sela_id = sela_match.group(1)
        return sela_shop_window(sela_id)
    
    # For other 404 errors, redirect to home
    return redirect(url_for('index'))
