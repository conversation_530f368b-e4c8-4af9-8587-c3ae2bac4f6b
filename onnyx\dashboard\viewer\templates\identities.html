{% extends "base.html" %}

{% block title %}Identities - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">All Identities</h2>
    </div>
    
    <div class="card" style="margin-bottom: 2rem;">
        <p>Onnyx identities are soulbound to their owners and serve as the foundation for the entire ecosystem. Identities build reputation through on-chain activity and can create tokens, participate in governance, and more.</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Identity ID</th>
                <th>Name</th>
                <th>Reputation Score</th>
                <th>Tokens Created</th>
                <th>Creation Date</th>
            </tr>
        </thead>
        <tbody>
            {% for identity in identities %}
            <tr>
                <td><a href="{{ url_for('identity_detail', identity_id=identity.id) }}">{{ identity.id[:10] }}...</a></td>
                <td>{{ identity.name }}</td>
                <td>{{ identity.reputation if identity.reputation is defined else "N/A" }}</td>
                <td>{{ identity.tokens_created if identity.tokens_created is defined else "N/A" }}</td>
                <td>{{ format_timestamp(identity.created_at) if identity.created_at is defined else "N/A" }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="5" style="text-align: center;">No identities found</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
