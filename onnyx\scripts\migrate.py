#!/usr/bin/env python3
"""
Onnyx Database Migration Script

This script applies database migrations to the Onnyx database.
"""

import os
import sys
import re
import sqlite3
import logging
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data.db import db

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("onnyx.scripts.migrate")

def get_migrations(migrations_dir: str) -> list:
    """
    Get a list of migration files.
    
    Args:
        migrations_dir: Path to the migrations directory
    
    Returns:
        A list of migration files sorted by version
    """
    # Ensure the migrations directory exists
    os.makedirs(migrations_dir, exist_ok=True)
    
    # Get a list of migration files
    migration_files = []
    
    for file in os.listdir(migrations_dir):
        if file.endswith('.sql'):
            # Extract the version number from the file name
            match = re.match(r'^(\d+)_.*\.sql$', file)
            
            if match:
                version = int(match.group(1))
                migration_files.append((version, os.path.join(migrations_dir, file)))
    
    # Sort the migration files by version
    migration_files.sort(key=lambda x: x[0])
    
    return migration_files

def get_applied_migrations(conn: sqlite3.Connection) -> set:
    """
    Get a set of applied migrations.
    
    Args:
        conn: SQLite connection
    
    Returns:
        A set of applied migration versions
    """
    # Create the migrations table if it doesn't exist
    conn.execute("""
        CREATE TABLE IF NOT EXISTS migrations (
            version INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            applied_at INTEGER NOT NULL
        )
    """)
    
    # Get a list of applied migrations
    cursor = conn.execute("SELECT version FROM migrations")
    
    return {row[0] for row in cursor.fetchall()}

def apply_migration(conn: sqlite3.Connection, version: int, migration_file: str) -> None:
    """
    Apply a migration.
    
    Args:
        conn: SQLite connection
        version: Migration version
        migration_file: Path to the migration file
    """
    # Read the migration file
    with open(migration_file, 'r') as f:
        migration = f.read()
    
    # Apply the migration
    conn.executescript(migration)
    
    # Record the migration
    conn.execute(
        "INSERT INTO migrations (version, name, applied_at) VALUES (?, ?, strftime('%s', 'now'))",
        (version, os.path.basename(migration_file))
    )
    
    # Commit the changes
    conn.commit()
    
    logger.info(f"Applied migration {version}: {os.path.basename(migration_file)}")

def migrate(db_path: str, migrations_dir: str) -> None:
    """
    Apply database migrations.
    
    Args:
        db_path: Path to the database file
        migrations_dir: Path to the migrations directory
    """
    # Get a list of migration files
    migration_files = get_migrations(migrations_dir)
    
    if not migration_files:
        logger.info("No migrations found.")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    
    try:
        # Get a set of applied migrations
        applied_migrations = get_applied_migrations(conn)
        
        # Apply the migrations
        for version, migration_file in migration_files:
            if version not in applied_migrations:
                apply_migration(conn, version, migration_file)
    finally:
        # Close the connection
        conn.close()

def main():
    """
    Main function to apply database migrations.
    """
    # Define paths
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    migrations_dir = os.path.join(root_dir, 'migrations')
    db_path = os.path.join(root_dir, 'runtime', 'onnyx.db')
    
    # Apply migrations
    migrate(db_path, migrations_dir)
    
    logger.info("Database migrations completed successfully.")

if __name__ == "__main__":
    main()
