"""
Onnyx Voice Scroll Routes

This module provides API routes for Voice Scrolls.
"""

import logging
import time
import uuid
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from governance.voice_scroll import voice_scrolls

from shared.models.voice_scroll import VoiceScroll
from shared.models.identity import Identity
from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.routes.voice_scroll")

# Create router
router = APIRouter()

@router.post("/governance/propose")
def propose_scroll(
    creator_id: str = Body(...),
    title: str = Body(...),
    description: str = Body(...),
    category: str = Body(...),
    expiry_days: int = Body(7),
    effect: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Propose a new Voice Scroll.

    Args:
        creator_id: The identity ID of the creator
        title: The title of the scroll
        description: The description of the scroll
        category: The category of the scroll
        expiry_days: The number of days until the scroll expires
        effect: The effect of the scroll (optional)

    Returns:
        The created scroll
    """
    try:
        logger.info(f"Proposing scroll: {title} by {creator_id}")

        # Check if the creator exists
        identity = Identity.get_by_id(creator_id)
        if not identity:
            logger.warning(f"Identity {creator_id} not found")
            raise Exception(f"Identity {creator_id} not found")

        # Create the scroll in the voice_scrolls module
        scroll = voice_scrolls.create_scroll(
            creator_id=creator_id,
            title=title,
            description=description,
            category=category,
            expiry_days=expiry_days,
            effect=effect
        )

        # Create the scroll in the database
        scroll_id = scroll["scroll_id"]
        db_scroll = VoiceScroll.create(
            scroll_id=scroll_id,
            creator_id=creator_id,
            title=title,
            description=description,
            category=category,
            expiry_days=expiry_days,
            effect=effect
        )

        # Create a transaction for the scroll proposal
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SCROLL_PROPOSAL",
            sender=creator_id,
            status="CONFIRMED",
            data={
                "op": "OP_PROPOSE_SCROLL",
                "scroll_id": scroll_id,
                "title": title,
                "category": category
            }
        )

        logger.info(f"Scroll proposed: {scroll_id}")
        return {
            "scroll": db_scroll.to_dict(),
            "status": "proposed"
        }
    except Exception as e:
        logger.error(f"Error proposing scroll: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/governance/vote")
def vote_on_scroll(
    identity_id: str = Body(...),
    scroll_id: str = Body(...),
    decision: str = Body(...)
) -> Dict[str, Any]:
    """
    Vote on a Voice Scroll.

    Args:
        identity_id: The identity ID of the voter
        scroll_id: The scroll ID
        decision: The decision ("yes", "no", or "abstain")

    Returns:
        The updated scroll
    """
    try:
        logger.info(f"Voting on scroll {scroll_id} by {identity_id}: {decision}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity {identity_id} not found")
            raise Exception(f"Identity {identity_id} not found")

        # Check if the scroll exists
        db_scroll = VoiceScroll.get_by_id(scroll_id)
        if not db_scroll:
            logger.warning(f"Scroll {scroll_id} not found")
            raise Exception(f"Scroll {scroll_id} not found")

        # Check if the decision is valid
        if decision not in ["yes", "no", "abstain"]:
            logger.warning(f"Invalid decision: {decision}")
            raise Exception(f"Invalid decision: {decision}. Must be 'yes', 'no', or 'abstain'")

        # Check if the scroll is active
        if db_scroll.status != "active":
            logger.warning(f"Scroll {scroll_id} is not active")
            raise Exception(f"Scroll {scroll_id} is not active")

        # Vote on the scroll in the voice_scrolls module
        scroll = voice_scrolls.vote_on_scroll(
            identity_id=identity_id,
            scroll_id=scroll_id,
            decision=decision
        )

        # Add the vote to the scroll in the database
        db_scroll.add_vote(identity_id, decision)

        # Create a transaction for the vote
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SCROLL_VOTE",
            sender=identity_id,
            status="CONFIRMED",
            data={
                "op": "OP_VOTE_SCROLL",
                "scroll_id": scroll_id,
                "decision": decision
            }
        )

        logger.info(f"Vote recorded on scroll {scroll_id} by {identity_id}: {decision}")
        return {
            "scroll": db_scroll.to_dict(),
            "status": "voted"
        }
    except Exception as e:
        logger.error(f"Error voting on scroll: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/scrolls")
def get_scrolls(
    status: Optional[str] = None,
    category: Optional[str] = None,
    creator: Optional[str] = None,
    outcome: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get Voice Scrolls.

    Args:
        status: Filter by status (optional)
        category: Filter by category (optional)
        creator: Filter by creator (optional)
        outcome: Filter by outcome (optional)

    Returns:
        A list of Voice Scrolls
    """
    try:
        logger.info(f"Getting scrolls with status={status}, category={category}, creator={creator}, outcome={outcome}")

        # Get scrolls from the database
        scrolls = []

        if status:
            scrolls = VoiceScroll.find_by_status(status)
        elif category:
            scrolls = VoiceScroll.find_by_category(category)
        elif creator:
            scrolls = VoiceScroll.find_by_creator(creator)
        elif outcome:
            scrolls = VoiceScroll.find_by_outcome(outcome)
        else:
            scrolls = VoiceScroll.get_all()

        # Filter scrolls if multiple filters are provided
        if status and scrolls:
            scrolls = [s for s in scrolls if s.status == status]
        if category and scrolls:
            scrolls = [s for s in scrolls if s.category == category]
        if creator and scrolls:
            scrolls = [s for s in scrolls if s.creator_id == creator]
        if outcome and scrolls:
            scrolls = [s for s in scrolls if s.outcome == outcome]

        logger.info(f"Retrieved {len(scrolls)} scrolls")
        return {
            "scrolls": [scroll.to_dict() for scroll in scrolls]
        }
    except Exception as e:
        logger.error(f"Error getting scrolls: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/scrolls/{scroll_id}")
def get_scroll(scroll_id: str) -> Dict[str, Any]:
    """
    Get a Voice Scroll by ID.

    Args:
        scroll_id: The scroll ID

    Returns:
        The Voice Scroll
    """
    try:
        logger.info(f"Getting scroll: {scroll_id}")

        # Get the scroll from the database
        scroll = VoiceScroll.get_by_id(scroll_id)
        if not scroll:
            logger.warning(f"Scroll {scroll_id} not found")
            raise HTTPException(status_code=404, detail=f"Voice Scroll with ID '{scroll_id}' not found")

        logger.info(f"Retrieved scroll {scroll_id}")
        return {
            "scroll": scroll.to_dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scroll: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/scrolls/{scroll_id}/tally")
def tally_scroll(scroll_id: str) -> Dict[str, Any]:
    """
    Tally the votes for a Voice Scroll.

    Args:
        scroll_id: The scroll ID

    Returns:
        The tally results
    """
    try:
        logger.info(f"Tallying votes for scroll: {scroll_id}")

        # Get the scroll from the database
        scroll = VoiceScroll.get_by_id(scroll_id)
        if not scroll:
            logger.warning(f"Scroll {scroll_id} not found")
            raise HTTPException(status_code=404, detail=f"Voice Scroll with ID '{scroll_id}' not found")

        # Tally the votes in the voice_scrolls module
        tally_result = voice_scrolls.tally_scroll(scroll_id)

        # Tally the votes in the database
        db_tally = scroll.tally_votes()

        # Combine the results
        combined_tally = {
            "scroll_id": scroll_id,
            "yes": db_tally["yes"],
            "no": db_tally["no"],
            "abstain": db_tally["abstain"],
            "total": db_tally["total"],
            "quorum_reached": tally_result.get("quorum_reached", False),
            "passed": tally_result.get("passed", False)
        }

        logger.info(f"Tallied votes for scroll {scroll_id}: {combined_tally}")
        return {
            "tally": combined_tally
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error tallying scroll: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/governance/resolve/{scroll_id}")
def resolve_scroll(scroll_id: str) -> Dict[str, Any]:
    """
    Resolve a Voice Scroll.

    Args:
        scroll_id: The scroll ID

    Returns:
        The resolved Voice Scroll
    """
    try:
        logger.info(f"Resolving scroll: {scroll_id}")

        # Get the scroll from the database
        scroll = VoiceScroll.get_by_id(scroll_id)
        if not scroll:
            logger.warning(f"Scroll {scroll_id} not found")
            raise HTTPException(status_code=404, detail=f"Voice Scroll with ID '{scroll_id}' not found")

        # Check if the scroll is active
        if scroll.status != "active":
            logger.warning(f"Scroll {scroll_id} is not active")
            raise Exception(f"Scroll {scroll_id} is not active")

        # Resolve the scroll in the voice_scrolls module
        voice_scrolls.resolve_scroll(scroll_id)

        # Tally the votes
        tally_result = voice_scrolls.tally_scroll(scroll_id)

        # Determine the outcome
        outcome = "no_votes"
        if tally_result.get("total", 0) > 0:
            outcome = "passed" if tally_result.get("passed", False) else "failed"

        # Resolve the scroll in the database
        scroll.resolve(outcome)

        # Create a transaction for the resolution
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SCROLL_RESOLUTION",
            status="CONFIRMED",
            data={
                "op": "OP_RESOLVE_SCROLL",
                "scroll_id": scroll_id,
                "outcome": outcome,
                "tally": tally_result
            }
        )

        # Apply the effect if the scroll passed
        if outcome == "passed" and scroll.effect:
            logger.info(f"Applying effect for scroll {scroll_id}: {scroll.effect}")

            # Create a transaction for the effect
            effect_tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            Transaction.create(
                tx_id=effect_tx_id,
                tx_type="SCROLL_EFFECT",
                status="CONFIRMED",
                data={
                    "op": "OP_APPLY_SCROLL_EFFECT",
                    "scroll_id": scroll_id,
                    "effect": scroll.effect
                }
            )

        logger.info(f"Resolved scroll {scroll_id} with outcome: {outcome}")
        return {
            "scroll": scroll.to_dict(),
            "status": "resolved",
            "outcome": outcome
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving scroll: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/categories")
def get_categories() -> Dict[str, List[str]]:
    """
    Get the available Voice Scroll categories.

    Returns:
        The available categories
    """
    logger.info("Getting Voice Scroll categories")

    categories = [
        "protocol",
        "economic",
        "community",
        "membership",
        "other"
    ]

    logger.info(f"Retrieved {len(categories)} categories")
    return {
        "categories": categories
    }

@router.get("/governance/outcomes")
def get_outcomes() -> Dict[str, List[str]]:
    """
    Get the available Voice Scroll outcomes.

    Returns:
        The available outcomes
    """
    logger.info("Getting Voice Scroll outcomes")

    outcomes = [
        "pending",
        "passed",
        "failed",
        "no_votes"
    ]

    logger.info(f"Retrieved {len(outcomes)} outcomes")
    return {
        "outcomes": outcomes
    }

@router.get("/governance/active-scrolls")
def get_active_scrolls() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all active Voice Scrolls.

    Returns:
        A list of active Voice Scrolls
    """
    try:
        logger.info("Getting active scrolls")

        # Get active scrolls from the database
        scrolls = VoiceScroll.find_by_status("active")

        logger.info(f"Retrieved {len(scrolls)} active scrolls")
        return {
            "scrolls": [scroll.to_dict() for scroll in scrolls]
        }
    except Exception as e:
        logger.error(f"Error getting active scrolls: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/resolved-scrolls")
def get_resolved_scrolls() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all resolved Voice Scrolls.

    Returns:
        A list of resolved Voice Scrolls
    """
    try:
        logger.info("Getting resolved scrolls")

        # Get resolved scrolls from the database
        scrolls = VoiceScroll.find_by_status("resolved")

        logger.info(f"Retrieved {len(scrolls)} resolved scrolls")
        return {
            "scrolls": [scroll.to_dict() for scroll in scrolls]
        }
    except Exception as e:
        logger.error(f"Error getting resolved scrolls: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
