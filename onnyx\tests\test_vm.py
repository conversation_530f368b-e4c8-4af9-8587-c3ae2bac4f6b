# tests/test_vm.py

import sys
import os
import unittest
import tempfile

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.tokens.schema import TokenType
from src.tokens.registry import TokenRegistry
from src.tokens.ledger import TokenLedger
from src.vm.onnyxscript import OnnyxScriptVM

class TestOnnyxScriptVM(unittest.TestCase):
    def setUp(self):
        # Create temporary files for testing
        self.registry_file = tempfile.NamedTemporaryFile(delete=False)
        self.registry_file.close()
        self.ledger_file = tempfile.NamedTemporaryFile(delete=False)
        self.ledger_file.close()

        # Initialize the token registry and ledger
        self.registry = TokenRegistry(db_path=self.registry_file.name)
        self.ledger = TokenLedger(db_path=self.ledger_file.name, token_registry=self.registry)

        # Initialize the VM
        self.vm = OnnyxScriptVM(registry=self.registry, ledger=self.ledger)

    def tearDown(self):
        # Clean up the temporary files
        os.unlink(self.registry_file.name)
        os.unlink(self.ledger_file.name)

    def test_spawn_token(self):
        # Create a script to spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]

        # Execute the script
        result = self.vm.execute(script)

        # Check that the token was created
        token_id = result[0]
        token = self.registry.get_token(token_id)

        self.assertIsNotNone(token)
        self.assertEqual(token.name, "Test Token")
        self.assertEqual(token.symbol, "TST")
        self.assertEqual(token.creator_identity, "identity123")
        self.assertEqual(token.token_type, TokenType.FORKED)
        self.assertEqual(token.supply, 1000)
        self.assertTrue(token.mintable)
        self.assertTrue(token.transferable)
        self.assertEqual(token.metadata["description"], "A test token")

        # Check that the initial supply was credited to the creator
        balance = self.ledger.get_balance("identity123", token_id)
        self.assertEqual(balance, 1000)

    def test_mint_token(self):
        # First, spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now mint additional tokens
        script = [
            ("OP_MINT_TOKEN", (token_id, 500, "address456", "identity123"))
        ]
        self.vm.execute(script)

        # Check that the tokens were minted
        token = self.registry.get_token(token_id)
        self.assertEqual(token.supply, 1500)

        # Check that the tokens were credited to the recipient
        balance = self.ledger.get_balance("address456", token_id)
        self.assertEqual(balance, 500)

    def test_transfer_token(self):
        # First, spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now transfer some tokens
        script = [
            ("OP_TRANSFER_TOKEN", (token_id, "identity123", "address456", 300, "Test transfer"))
        ]
        self.vm.execute(script)

        # Check that the tokens were transferred
        balance1 = self.ledger.get_balance("identity123", token_id)
        balance2 = self.ledger.get_balance("address456", token_id)
        self.assertEqual(balance1, 700)
        self.assertEqual(balance2, 300)

    def test_get_token_metadata(self):
        # First, spawn a token with metadata
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token", "url": "https://example.com"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now get the token metadata
        script = [
            ("OP_GET_TOKEN_METADATA", token_id)
        ]
        result = self.vm.execute(script)

        # Check that the metadata was retrieved
        metadata = result[0]
        self.assertEqual(metadata["description"], "A test token")
        self.assertEqual(metadata["url"], "https://example.com")

    def test_burn_token(self):
        # First, spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now burn some tokens
        script = [
            ("OP_BURN_TOKEN", (token_id, "identity123", 200, "identity123"))
        ]
        self.vm.execute(script)

        # Check that the tokens were burned
        token = self.registry.get_token(token_id)
        self.assertEqual(token.supply, 800)

        # Check that the tokens were debited from the address
        balance = self.ledger.get_balance("identity123", token_id)
        self.assertEqual(balance, 800)

    def test_get_token_balance(self):
        # First, spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now get the token balance
        script = [
            ("OP_GET_TOKEN_BALANCE", ("identity123", token_id))
        ]
        result = self.vm.execute(script)

        # Check that the balance was retrieved
        balance = result[0]
        self.assertEqual(balance, 1000)

    def test_get_token_supply(self):
        # First, spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now get the token supply
        script = [
            ("OP_GET_TOKEN_SUPPLY", token_id)
        ]
        result = self.vm.execute(script)

        # Check that the supply was retrieved
        supply = result[0]
        self.assertEqual(supply, 1000)

    def test_check_token_owner(self):
        # First, spawn a token
        script = [
            ("OP_SPAWN_TOKEN", ("Test Token", "TST", "identity123", TokenType.FORKED, 1000, {"description": "A test token"}, True, True))
        ]
        token_id = self.vm.execute(script)[0]

        # Now check if identity123 is the owner
        script = [
            ("OP_CHECK_TOKEN_OWNER", (token_id, "identity123"))
        ]
        result = self.vm.execute(script)

        # Check that the result is True
        is_owner = result[0]
        self.assertTrue(is_owner)

        # Check if identity456 is the owner (should be False)
        script = [
            ("OP_CHECK_TOKEN_OWNER", (token_id, "identity456"))
        ]
        result = self.vm.execute(script)

        # Check that the result is False
        is_owner = result[0]
        self.assertFalse(is_owner)

    def test_complex_script(self):
        # Create a script to spawn a token
        spawn_script = [
            ("OP_SPAWN_TOKEN", ("Complex Token", "CPX", "identity123", TokenType.FORKED, 1000, {"description": "A complex token"}, True, True))
        ]

        # Execute the script and get the token ID
        token_id = self.vm.execute(spawn_script)[0]

        # Create a script to mint additional tokens
        mint_script = [
            ("OP_MINT_TOKEN", (token_id, 500, "address456", "identity123"))
        ]
        self.vm.execute(mint_script)

        # Create a script to transfer tokens
        transfer_script = [
            ("OP_TRANSFER_TOKEN", (token_id, "identity123", "address789", 300, "Complex transfer"))
        ]
        self.vm.execute(transfer_script)

        # Create a script to get the token balance
        balance_script = [
            ("OP_GET_TOKEN_BALANCE", ("address789", token_id))
        ]
        result = self.vm.execute(balance_script)

        # Check that the balance is 300
        self.assertEqual(result[0], 300)

    def test_error_handling(self):
        # Test that errors are properly handled

        # Try to mint tokens for a non-existent token
        script = [
            ("OP_MINT_TOKEN", ("nonexistent", 500, "address456", "identity123"))
        ]

        # This should raise an exception
        with self.assertRaises(ValueError):
            self.vm.execute(script)

        # Check that the error was logged
        log = self.vm.get_execution_log()
        self.assertEqual(len(log), 1)
        self.assertEqual(log[0]["status"], "FAILED")
        self.assertIn("Token nonexistent not found", log[0]["error"])

if __name__ == "__main__":
    unittest.main()
