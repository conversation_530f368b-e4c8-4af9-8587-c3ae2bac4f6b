"""
Onnyx Chain Parameters Routes

This module provides API routes for chain parameters.
"""

from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from shared.config.chain_parameters import chain_parameters
from governance.voice_scroll import voice_scrolls

# Create router
router = APIRouter()

@router.get("/chain-parameters")
def get_chain_parameters() -> Dict[str, Any]:
    """
    Get all chain parameters.
    
    Returns:
        All chain parameters
    """
    return {
        "parameters": chain_parameters.all()
    }

@router.get("/chain-parameters/{key}")
def get_chain_parameter(key: str) -> Dict[str, Any]:
    """
    Get a chain parameter.
    
    Args:
        key: The parameter key
    
    Returns:
        The parameter value
    """
    value = chain_parameters.get(key)
    if value is None:
        raise HTTPException(status_code=404, detail=f"Chain parameter '{key}' not found")
    
    return {
        "key": key,
        "value": value
    }

@router.post("/chain-parameters/{key}")
def set_chain_parameter(key: str, value: Any = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set a chain parameter.
    
    Args:
        key: The parameter key
        value: The parameter value
    
    Returns:
        The updated parameter
    """
    try:
        chain_parameters.set(key, value)
        return {
            "key": key,
            "value": value,
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/chain-parameters/reset/{key}")
def reset_chain_parameter(key: str) -> Dict[str, Any]:
    """
    Reset a chain parameter to its default value.
    
    Args:
        key: The parameter key
    
    Returns:
        The reset parameter
    """
    try:
        chain_parameters.reset(key)
        return {
            "key": key,
            "value": chain_parameters.get(key),
            "status": "reset"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/chain-parameters/reset-all")
def reset_all_chain_parameters() -> Dict[str, Any]:
    """
    Reset all chain parameters to their default values.
    
    Returns:
        The reset parameters
    """
    try:
        chain_parameters.reset_all()
        return {
            "parameters": chain_parameters.all(),
            "status": "reset"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/chain-parameters/propose")
def propose_chain_parameter_change(
    creator_id: str = Body(...),
    title: str = Body(...),
    description: str = Body(...),
    param: str = Body(...),
    value: Any = Body(...),
    expiry_days: int = Body(7)
) -> Dict[str, Any]:
    """
    Propose a chain parameter change.
    
    Args:
        creator_id: The identity ID of the creator
        title: The title of the scroll
        description: The description of the scroll
        param: The parameter to change
        value: The new value for the parameter
        expiry_days: The number of days until the scroll expires
    
    Returns:
        The created scroll
    """
    try:
        # Create a scroll with an effect
        scroll = voice_scrolls.create_scroll(
            creator_id=creator_id,
            title=title,
            description=description,
            category="economic",
            expiry_days=expiry_days,
            effect={
                "param": param,
                "value": value
            }
        )
        
        return {
            "scroll": scroll,
            "status": "proposed"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/chain-parameters/proposals")
def get_chain_parameter_proposals(
    status: Optional[str] = None,
    outcome: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get chain parameter proposals.
    
    Args:
        status: Filter by status (optional)
        outcome: Filter by outcome (optional)
    
    Returns:
        A list of chain parameter proposals
    """
    try:
        # Get scrolls with category "economic"
        scrolls = voice_scrolls.get_scrolls(
            status=status,
            category="economic",
            outcome=outcome
        )
        
        # Filter scrolls with an effect
        proposals = [s for s in scrolls if "effect" in s and "param" in s["effect"]]
        
        return {
            "proposals": proposals
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
