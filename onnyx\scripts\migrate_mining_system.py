#!/usr/bin/env python3
"""
ONNYX Mining System Migration

Migrate the database to support the new Proof-of-Trust + Performance Boost Hybrid Mining system.
"""

import sqlite3
import sys
import os
from datetime import datetime

def migrate_database():
    """Migrate database to support hybrid mining system."""
    print("🚀 MIGRATING ONNYX MINING SYSTEM")
    print("=" * 50)

    # Database path
    db_path = "shared/db/onnyx.db"

    if not os.path.exists(db_path):
        print("❌ Database not found. Please run the system first to create the database.")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("📋 Checking current database schema...")

        # Check if selas table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='selas'")
        if not cursor.fetchone():
            print("❌ Selas table not found. Database may be corrupted.")
            return False

        # Check current selas table structure
        cursor.execute("PRAGMA table_info(selas)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   Current selas columns: {', '.join(columns)}")

        # Add new columns if they don't exist
        migrations_applied = []

        # Add mining_power column
        if 'mining_power' not in columns:
            print("   Adding mining_power column...")
            cursor.execute("ALTER TABLE selas ADD COLUMN mining_power INTEGER DEFAULT 1")
            migrations_applied.append("mining_power")

        # Add mining_tier column
        if 'mining_tier' not in columns:
            print("   Adding mining_tier column...")
            cursor.execute("ALTER TABLE selas ADD COLUMN mining_tier TEXT DEFAULT 'basic'")
            migrations_applied.append("mining_tier")

        # Add last_mining_activity column
        if 'last_mining_activity' not in columns:
            print("   Adding last_mining_activity column...")
            cursor.execute("ALTER TABLE selas ADD COLUMN last_mining_activity TIMESTAMP")
            migrations_applied.append("last_mining_activity")

        # Add mining_rewards_earned column for tracking
        if 'mining_rewards_earned' not in columns:
            print("   Adding mining_rewards_earned column...")
            cursor.execute("ALTER TABLE selas ADD COLUMN mining_rewards_earned REAL DEFAULT 0.0")
            migrations_applied.append("mining_rewards_earned")

        # Add blocks_mined column for performance tracking
        if 'blocks_mined' not in columns:
            print("   Adding blocks_mined column...")
            cursor.execute("ALTER TABLE selas ADD COLUMN blocks_mined INTEGER DEFAULT 0")
            migrations_applied.append("blocks_mined")

        # Create mining_tier_history table for audit logs
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mining_tier_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sela_id TEXT NOT NULL,
                old_tier TEXT,
                new_tier TEXT,
                old_mining_power INTEGER,
                new_mining_power INTEGER,
                changed_by TEXT,
                reason TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sela_id) REFERENCES selas (sela_id)
            )
        """)
        migrations_applied.append("mining_tier_history table")

        # Create mining_rewards table for detailed reward tracking
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mining_rewards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sela_id TEXT NOT NULL,
                block_height INTEGER NOT NULL,
                base_reward REAL NOT NULL,
                mining_power INTEGER NOT NULL,
                final_reward REAL NOT NULL,
                mining_tier TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sela_id) REFERENCES selas (sela_id)
            )
        """)
        migrations_applied.append("mining_rewards table")

        # Update existing validators to have proper mining tiers
        print("   Updating existing validators...")
        cursor.execute("SELECT sela_id, status FROM selas")
        existing_selas = cursor.fetchall()

        for sela_id, status in existing_selas:
            if status == 'active':
                # Set active validators to basic tier with mining power 1
                cursor.execute("""
                    UPDATE selas
                    SET mining_power = 1, mining_tier = 'basic', last_mining_activity = ?
                    WHERE sela_id = ?
                """, (datetime.now().isoformat(), sela_id))
                print(f"     Updated {sela_id} to basic mining tier")

        # Commit all changes
        conn.commit()

        print(f"\n✅ Migration completed successfully!")
        print(f"   Applied migrations: {', '.join(migrations_applied)}")
        print(f"   Updated {len(existing_selas)} existing validators")

        # Verify the migration
        print("\n🔍 Verifying migration...")
        cursor.execute("PRAGMA table_info(selas)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"   New selas columns: {', '.join(new_columns)}")

        # Check mining tier distribution
        cursor.execute("SELECT mining_tier, COUNT(*) FROM selas GROUP BY mining_tier")
        tier_distribution = cursor.fetchall()
        print(f"   Mining tier distribution: {dict(tier_distribution)}")

        return True

    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def rollback_migration():
    """Rollback the mining system migration (for testing purposes)."""
    print("🔄 ROLLING BACK MINING SYSTEM MIGRATION")
    print("=" * 50)

    db_path = "onnyx_blockchain.db"

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Note: SQLite doesn't support DROP COLUMN, so we'll just reset values
        print("   Resetting mining system columns...")

        cursor.execute("UPDATE selas SET mining_power = 1, mining_tier = 'basic'")
        cursor.execute("DROP TABLE IF EXISTS mining_tier_history")
        cursor.execute("DROP TABLE IF EXISTS mining_rewards")

        conn.commit()
        print("✅ Rollback completed")

        return True

    except Exception as e:
        print(f"❌ Rollback error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """Main entry point."""
    if len(sys.argv) > 1 and sys.argv[1] == "--rollback":
        success = rollback_migration()
    else:
        success = migrate_database()

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
