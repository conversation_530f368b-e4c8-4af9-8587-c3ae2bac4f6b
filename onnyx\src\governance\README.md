# Council of Twelve Tribes: Governance System

The Council of Twelve Tribes is a righteous governance system powered by identity, trust, and reputation.

> "No one person controls Onnyx. Twelve trusted identities guide its evolution — not through money, but through merit and trust."

## Overview

The Onnyx governance system consists of the following components:

- **Voice Scrolls**: On-chain governance proposals
- **Council Ballot**: Vote on proposals, weighted by Etzem
- **Etzem Score**: Voting power comes from merit, not token weight
- **Council of 12 Tribes**: Rotating set of respected identities
- **Proposal Types**: Protocol, Fees, Members, Economy, Community, etc.

## Components

### VoiceScrolls

The `VoiceScrolls` class manages on-chain governance proposals. It provides methods for:

- Creating proposals
- Voting on proposals
- Tallying votes
- Resolving proposals

### CouncilOfTwelve

The `CouncilOfTwelve` class manages the Council of Twelve Tribes. It provides methods for:

- Selecting council members
- Getting council members
- Checking if an identity is a council member
- Getting council term information

## Proposal Lifecycle

1. **Creation**: An identity with sufficient Etzem score creates a proposal (scroll)
2. **Voting**: Identities vote on the proposal, with voting power determined by their Etzem score
3. **Tallying**: Votes are tallied and the outcome is determined based on the votes
4. **Resolution**: After the voting period ends, the proposal is resolved based on the outcome

### Tallying and Resolution

The tallying system automatically determines if a proposal passes, fails, or is pending based on the following criteria:

- **Expiry Window**: Proposals expire 7 days after creation
- **Quorum Requirement**: A minimum vote weight of 50 is required for a valid vote
- **Pass Ratio**: 60% of yes votes (excluding abstains) are required to pass a proposal
- **Outcome Tagging**: Proposals are tagged as "open", "passed", "rejected", "expired", or "no_quorum"

When a proposal expires, its outcome is automatically determined based on the votes. If the quorum is not reached, the proposal is marked as "no_quorum". If the quorum is reached, the proposal is marked as "passed" or "rejected" based on the pass ratio.

## Council Selection

Council members are selected based on their Etzem score, not their wealth or token holdings. The top 12 identities by Etzem score are selected to be council members.

Council terms last for 30 days by default, after which a new council is selected.

## Voting Power

Voting power is determined by an identity's Etzem score, not their token holdings. This ensures that governance is based on merit and trust, not wealth.

Council members get double voting power to reflect their trusted status.

## API Endpoints

### Proposal Endpoints

- `POST /governance/propose` - Create a new proposal
- `POST /governance/vote` - Vote on a proposal
- `GET /governance/scrolls` - Get proposals
- `GET /governance/scrolls?status=open` - Get proposals by status
- `GET /governance/scrolls?outcome=passed` - Get proposals by outcome
- `GET /governance/scrolls?category=protocol` - Get proposals by category
- `GET /governance/scrolls?creator=identity_id` - Get proposals by creator
- `GET /governance/scrolls/{scroll_id}` - Get a proposal by ID
- `GET /governance/scrolls/{scroll_id}/tally` - Tally the votes for a proposal
- `POST /governance/resolve/{scroll_id}` - Resolve a proposal

### Council Endpoints

- `GET /governance/council/members` - Get the current council members
- `GET /governance/council/stats` - Get statistics about the current council
- `POST /governance/council/set_term_duration` - Set the duration of council terms
- `POST /governance/council/force_new_term` - Force the selection of a new council

### Miscellaneous Endpoints

- `GET /governance/categories` - Get the available proposal categories
- `GET /governance/outcomes` - Get the available proposal outcomes

## Example Usage

### Creating a Proposal

```json
POST /governance/propose
{
  "identity_id": "marcus_id",
  "title": "Lower ONX startup fees",
  "description": "Make it easier for businesses to join.",
  "category": "economy",
  "metadata": {},
  "message": "...",
  "signature": "..."
}
```

### Voting on a Proposal

```json
POST /governance/vote
{
  "identity_id": "bob_id",
  "scroll_id": "scroll_1712341234",
  "decision": "yes",
  "message": "...",
  "signature": "..."
}
```

### Getting Proposals

```
GET /governance/scrolls?status=open&category=economy
```

### Getting Council Members

```
GET /governance/council/members
```

## Proposal Categories

- **Protocol**: Changes to the Onnyx protocol
- **Fees**: Changes to transaction fees
- **Members**: Changes to council membership rules
- **Economy**: Changes to economic parameters
- **Community**: Community initiatives and events
- **Other**: Other proposals

## Example Output

### Proposal with Vote Status

```json
{
  "scroll": {
    "id": "scroll_1a2b3c_1712341234",
    "creator": "marcus_id",
    "title": "Lower ONX startup fees",
    "description": "Make it easier for businesses to join.",
    "category": "economy",
    "created_at": 1712341234,
    "status": "open",
    "votes": {
      "bob_id": {
        "decision": "yes",
        "weight": 120,
        "timestamp": 1712341300
      },
      "alice_id": {
        "decision": "yes",
        "weight": 85,
        "timestamp": 1712341400
      },
      "charlie_id": {
        "decision": "no",
        "weight": 95,
        "timestamp": 1712341500
      }
    },
    "metadata": {},
    "tally": {
      "yes": 205,
      "no": 95,
      "abstain": 0
    },
    "quorum": 50,
    "pass_ratio": 0.6,
    "expires_at": 1712946034,
    "outcome": "open"
  },
  "vote_status": {
    "total_votes": 3,
    "total_weight": 300,
    "yes_weight": 205,
    "no_weight": 95,
    "abstain_weight": 0,
    "yes_ratio": 0.6833333333333333,
    "quorum_reached": true,
    "pass_ratio_met": true,
    "expired": false,
    "time_remaining": 604800,
    "outcome": "open"
  }
}
```

### Tally Result

```json
{
  "status": "success",
  "tally": {
    "yes": 205,
    "no": 95,
    "abstain": 0
  },
  "vote_status": {
    "total_votes": 3,
    "total_weight": 300,
    "yes_weight": 205,
    "no_weight": 95,
    "abstain_weight": 0,
    "yes_ratio": 0.6833333333333333,
    "quorum_reached": true,
    "pass_ratio_met": true,
    "expired": false,
    "time_remaining": 604800,
    "outcome": "open"
  }
}
```

### Council Members

```json
{
  "members": [
    {
      "identity_id": "marcus_id",
      "name": "Marcus Garvey",
      "score": 350,
      "level": "Platinum"
    },
    {
      "identity_id": "alice_id",
      "name": "Alice Johnson",
      "score": 320,
      "level": "Platinum"
    },
    {
      "identity_id": "bob_id",
      "name": "Bob Smith",
      "score": 280,
      "level": "Gold"
    }
  ],
  "term_info": {
    "term_start": 1712341234,
    "term_end": 1714933234,
    "term_duration": 2592000,
    "term_remaining": 2592000,
    "term_progress": 0,
    "last_updated": 1712341234
  }
}
```

## Future Enhancements

- **Council of 12 Mode**: Add fixed trusted group voting
- **Proposal Types**: "Spend", "Upgrade", "Policy"
- **Public vote explorer**: Show open scrolls and results
