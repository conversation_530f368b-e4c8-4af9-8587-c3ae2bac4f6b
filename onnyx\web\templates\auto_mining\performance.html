{% extends "base.html" %}

{% block title %}Auto-Mining Performance - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .performance-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .performance-card:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
    
    .metric-value {
        font-family: 'Orbitron', monospace;
        font-weight: 700;
        font-size: 2rem;
        line-height: 1;
    }
    
    .metric-label {
        font-size: 0.875rem;
        color: var(--text-tertiary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-top: 0.5rem;
    }
    
    .performance-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .indicator-excellent {
        background: #22c55e;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
    }
    
    .indicator-good {
        background: #3b82f6;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    }
    
    .indicator-average {
        background: #f59e0b;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
    }
    
    .indicator-poor {
        background: #ef4444;
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
    }
    
    .chart-container {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .rewards-table {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        overflow: hidden;
    }
    
    .rewards-table th {
        background: rgba(0, 212, 255, 0.1);
        color: var(--cyber-cyan);
        font-family: 'Orbitron', monospace;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }
    
    .rewards-table td {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: var(--text-secondary);
    }
    
    .rewards-table tr:hover {
        background: rgba(255, 255, 255, 0.05);
    }
    
    .uptime-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
        margin-top: 0.5rem;
    }
    
    .uptime-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-blue));
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('auto_mining.dashboard') }}" 
               class="glass-button px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                ← Back to Dashboard
            </a>
        </div>
        
        <h1 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">
            📊 Auto-Mining Performance
        </h1>
        <p class="text-xl text-text-secondary">
            Detailed analytics and performance metrics for your validators
        </p>
    </div>

    <!-- Performance Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="performance-card text-center">
            <div class="metric-value text-cyber-cyan">
                {{ validators|length }}
            </div>
            <div class="metric-label">Total Validators</div>
        </div>
        
        <div class="performance-card text-center">
            <div class="metric-value text-green-400">
                {{ validators|selectattr('currently_mining')|list|length }}
            </div>
            <div class="metric-label">Active Miners</div>
        </div>
        
        <div class="performance-card text-center">
            <div class="metric-value text-cyber-purple">
                {{ "%.2f"|format(validators|sum(attribute='mining_rewards_earned')|default(0)) }}
            </div>
            <div class="metric-label">Total Earnings</div>
        </div>
        
        <div class="performance-card text-center">
            <div class="metric-value text-cyber-blue">
                {{ validators|sum(attribute='blocks_mined')|default(0) }}
            </div>
            <div class="metric-label">Blocks Mined</div>
        </div>
    </div>

    <!-- Validator Performance Details -->
    <div class="chart-container">
        <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">
            Validator Performance Breakdown
        </h2>
        
        {% if validators %}
        <div class="space-y-4">
            {% for validator in validators %}
            <div class="performance-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="performance-indicator 
                            {% if validator.avg_reward_per_block > 1.0 %}indicator-excellent
                            {% elif validator.avg_reward_per_block > 0.5 %}indicator-good
                            {% elif validator.avg_reward_per_block > 0.1 %}indicator-average
                            {% else %}indicator-poor{% endif %}">
                        </div>
                        <div>
                            <h3 class="text-lg font-orbitron font-bold text-white">
                                {{ validator.name }}
                            </h3>
                            <p class="text-sm text-text-tertiary">
                                {{ validator.mining_tier|title }} Tier • {{ validator.mining_power }}x Power
                            </p>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        {% if validator.currently_mining %}
                        <span class="badge-success">Mining</span>
                        {% else %}
                        <span class="badge-warning">Idle</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                        <div class="text-sm text-text-tertiary">Blocks Mined</div>
                        <div class="text-lg font-orbitron font-bold text-green-400">
                            {{ validator.blocks_mined or 0 }}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Total Earnings</div>
                        <div class="text-lg font-orbitron font-bold text-cyber-purple">
                            {{ "%.2f"|format(validator.mining_rewards_earned or 0) }}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Avg Reward/Block</div>
                        <div class="text-lg font-orbitron font-bold text-cyber-blue">
                            {{ "%.3f"|format(validator.avg_reward_per_block) }}
                        </div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Uptime</div>
                        <div class="text-lg font-orbitron font-bold text-cyber-cyan">
                            {{ validator.uptime }}
                        </div>
                    </div>
                </div>
                
                <!-- Uptime Visualization -->
                <div>
                    <div class="flex justify-between text-sm text-text-tertiary mb-1">
                        <span>Uptime Performance</span>
                        <span>
                            {% if validator.restart_attempts == 0 %}100%
                            {% elif validator.restart_attempts <= 2 %}95%
                            {% elif validator.restart_attempts <= 5 %}80%
                            {% else %}60%{% endif %}
                        </span>
                    </div>
                    <div class="uptime-bar">
                        <div class="uptime-fill" style="width: 
                            {% if validator.restart_attempts == 0 %}100%
                            {% elif validator.restart_attempts <= 2 %}95%
                            {% elif validator.restart_attempts <= 5 %}80%
                            {% else %}60%{% endif %}">
                        </div>
                    </div>
                    {% if validator.restart_attempts > 0 %}
                    <div class="text-xs text-orange-400 mt-1">
                        {{ validator.restart_attempts }} restart(s) detected
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="text-6xl mb-4">📊</div>
            <h3 class="text-xl font-orbitron font-bold text-text-secondary mb-2">
                No Performance Data
            </h3>
            <p class="text-text-tertiary">
                Start auto-mining to see performance analytics
            </p>
        </div>
        {% endif %}
    </div>

    <!-- Recent Mining Rewards -->
    <div class="chart-container">
        <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">
            Recent Mining Rewards
        </h2>
        
        {% if rewards_history %}
        <div class="rewards-table">
            <table class="w-full">
                <thead>
                    <tr>
                        <th class="text-left">Validator</th>
                        <th class="text-left">Block Height</th>
                        <th class="text-left">Mining Tier</th>
                        <th class="text-right">Reward</th>
                        <th class="text-right">Timestamp</th>
                    </tr>
                </thead>
                <tbody>
                    {% for reward in rewards_history %}
                    <tr>
                        <td>
                            <div class="font-medium text-white">
                                {% set validator = validators|selectattr('sela_id', 'equalto', reward.sela_id)|first %}
                                {% if validator %}
                                {{ validator.name }}
                                {% else %}
                                {{ reward.sela_id[:8] }}...
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="font-mono text-cyber-cyan">
                                #{{ reward.block_height }}
                            </span>
                        </td>
                        <td>
                            <span class="badge-info">
                                {{ reward.mining_tier|title }}
                            </span>
                        </td>
                        <td class="text-right">
                            <span class="font-mono text-green-400">
                                {{ "%.3f"|format(reward.final_reward) }}
                            </span>
                        </td>
                        <td class="text-right">
                            <span class="text-sm text-text-tertiary">
                                {{ reward.timestamp[:19] }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="text-6xl mb-4">💰</div>
            <h3 class="text-xl font-orbitron font-bold text-text-secondary mb-2">
                No Rewards Yet
            </h3>
            <p class="text-text-tertiary">
                Mining rewards will appear here once validators start earning
            </p>
        </div>
        {% endif %}
    </div>

    <!-- Performance Insights -->
    <div class="chart-container">
        <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">
            Performance Insights
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="performance-card">
                <h3 class="text-lg font-orbitron font-semibold text-cyber-purple mb-4">
                    🏆 Top Performer
                </h3>
                {% set top_validator = validators|sort(attribute='mining_rewards_earned', reverse=true)|first %}
                {% if top_validator %}
                <div class="flex items-center space-x-3 mb-3">
                    <div class="indicator-excellent performance-indicator"></div>
                    <div>
                        <div class="font-semibold text-white">{{ top_validator.name }}</div>
                        <div class="text-sm text-text-tertiary">
                            {{ "%.2f"|format(top_validator.mining_rewards_earned or 0) }} total earnings
                        </div>
                    </div>
                </div>
                <div class="text-sm text-text-tertiary">
                    This validator has earned the most rewards through consistent mining performance.
                </div>
                {% else %}
                <div class="text-text-tertiary">No data available</div>
                {% endif %}
            </div>
            
            <div class="performance-card">
                <h3 class="text-lg font-orbitron font-semibold text-cyber-blue mb-4">
                    ⚡ Most Active
                </h3>
                {% set most_active = validators|sort(attribute='blocks_mined', reverse=true)|first %}
                {% if most_active %}
                <div class="flex items-center space-x-3 mb-3">
                    <div class="indicator-good performance-indicator"></div>
                    <div>
                        <div class="font-semibold text-white">{{ most_active.name }}</div>
                        <div class="text-sm text-text-tertiary">
                            {{ most_active.blocks_mined or 0 }} blocks mined
                        </div>
                    </div>
                </div>
                <div class="text-sm text-text-tertiary">
                    This validator has mined the most blocks and shows high activity levels.
                </div>
                {% else %}
                <div class="text-text-tertiary">No data available</div>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 p-4 bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10 border border-cyber-cyan/30 rounded-lg">
            <h4 class="font-orbitron font-semibold text-cyber-cyan mb-2">💡 Optimization Tips</h4>
            <ul class="text-sm text-text-tertiary space-y-1">
                <li>• Upgrade to higher mining tiers for better rewards multipliers</li>
                <li>• Enable scheduled mining during off-peak hours for optimal performance</li>
                <li>• Monitor restart attempts - frequent restarts may indicate configuration issues</li>
                <li>• Consider adjusting mining intervals based on your validator's performance</li>
            </ul>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex space-x-4">
        <a href="{{ url_for('auto_mining.dashboard') }}" 
           class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
            🤖 Back to Auto-Mining
        </a>
        
        <a href="{{ url_for('sela.directory') }}" 
           class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
            🏢 View All Validators
        </a>
        
        <button onclick="window.print()" 
                class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
            🖨️ Export Report
        </button>
    </div>
</div>

<script>
// Auto-refresh performance data every 60 seconds
setInterval(() => {
    location.reload();
}, 60000);

// Add some interactivity to the performance cards
document.querySelectorAll('.performance-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-2px) scale(1)';
    });
});
</script>
{% endblock %}
