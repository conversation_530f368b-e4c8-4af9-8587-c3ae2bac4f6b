# src/db/schema/chain.py

from src.db.manager import db_manager

def create_chain_tables():
    """
    Create the tables for the blockchain system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Create blocks table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS blocks (
        block_id TEXT PRIMARY KEY,
        previous_block_id TEXT,
        timestamp INTEGER NOT NULL,
        miner_id TEXT NOT NULL,
        transactions TEXT NOT NULL,
        hash TEXT NOT NULL,
        nonce INTEGER NOT NULL,
        difficulty INTEGER NOT NULL,
        FOREIGN KEY (miner_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (previous_block_id) REFERENCES blocks (block_id) ON DELETE CASCADE
    )
    ''')
    
    # Create transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transactions (
        tx_id TEXT PRIMARY KEY,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        type TEXT NOT NULL,
        data TEXT NOT NULL,
        signature TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        block_id TEXT,
        FOREIGN KEY (sender_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (receiver_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (block_id) REFERENCES blocks (block_id) ON DELETE CASCADE
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_blocks_previous_block_id ON blocks (previous_block_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_blocks_timestamp ON blocks (timestamp)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_blocks_miner_id ON blocks (miner_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_sender_id ON transactions (sender_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_receiver_id ON transactions (receiver_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions (type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions (timestamp)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_block_id ON transactions (block_id)')
    
    conn.commit()
