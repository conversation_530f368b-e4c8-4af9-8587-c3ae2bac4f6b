"""
Onnyx Test Runner

This script runs the Onnyx tests.
"""

import argparse
import logging
import os
import sys
import unittest

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("onnyx.run_tests")

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Run the Onnyx tests")
    parser.add_argument(
        "--test-file",
        type=str,
        help="The test file to run (e.g., test_api_updated.py)"
    )
    parser.add_argument(
        "--test-class",
        type=str,
        help="The test class to run (e.g., TestAPIUpdated)"
    )
    parser.add_argument(
        "--test-method",
        type=str,
        help="The test method to run (e.g., test_root)"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Log level (default: info)"
    )
    return parser.parse_args()

def main():
    """Run the Onnyx tests."""
    args = parse_args()
    
    # Set log level
    log_level = getattr(logging, args.log_level.upper())
    logging.getLogger("onnyx").setLevel(log_level)
    
    # Change to the tests directory
    os.chdir(os.path.join(os.path.dirname(__file__), "tests"))
    
    # Discover and run tests
    if args.test_file:
        # Run a specific test file
        test_file = args.test_file
        if not test_file.endswith(".py"):
            test_file += ".py"
        
        if args.test_class:
            # Run a specific test class
            test_class = args.test_class
            
            if args.test_method:
                # Run a specific test method
                test_method = args.test_method
                test_name = f"{test_file[:-3]}.{test_class}.{test_method}"
            else:
                # Run all methods in the test class
                test_name = f"{test_file[:-3]}.{test_class}"
        else:
            # Run all classes in the test file
            test_name = test_file[:-3]
        
        logger.info(f"Running test: {test_name}")
        unittest.main(module=None, argv=["", test_name])
    else:
        # Run all tests
        logger.info("Running all tests")
        unittest.main(module=None, argv=["", "discover"])

if __name__ == "__main__":
    main()
