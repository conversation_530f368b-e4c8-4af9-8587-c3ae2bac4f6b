#!/usr/bin/env python3
"""
Configure <PERSON><PERSON> Miner

This script configures a registered Sela for mining operations.
"""

import os
import sys
import json
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.models.identity import Identity
from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.configure_sela_miner")

def get_available_selas():
    """Get all available Selas from the database."""
    selas = db.query("SELECT * FROM selas WHERE status = 'active'")
    return selas

def get_identity_by_id(identity_id: str):
    """Get identity by ID."""
    return Identity.get_by_id(identity_id)

def create_sela_config(sela_id: str, identity_id: str, private_key: str):
    """
    Create Sela miner configuration.
    
    Args:
        sela_id: The Sela ID
        identity_id: The identity ID
        private_key: The private key for signing
    """
    logger.info(f"Creating Sela miner configuration for: {sela_id}")
    
    # Create private key file
    keys_dir = os.path.join(os.path.dirname(__file__), '..', 'keys')
    os.makedirs(keys_dir, exist_ok=True)
    
    private_key_path = os.path.join(keys_dir, f"{sela_id}_private_key.txt")
    with open(private_key_path, 'w') as f:
        f.write(private_key)
    
    logger.info(f"Private key saved to: {private_key_path}")
    
    # Create Sela configuration
    config = {
        "sela_id": sela_id,
        "identity_id": identity_id,
        "private_key_path": private_key_path,
        "api_port": 8888,
        "role": "validator",
        "auto_mine": True,
        "mine_interval": 60,
        "activity_log_path": f"blockchain/data/activity_{sela_id}.json",
        "validator_rotation": {
            "enabled": True,
            "min_etzem_score": 30,
            "required_badges": [
                "SELA_FOUNDER",
                "VALIDATOR_ELIGIBLE_BADGE"
            ]
        },
        "mining": {
            "enabled": True,
            "difficulty": 1,
            "reward_token": "ONX",
            "reward_amount": 10
        },
        "network": {
            "host": "127.0.0.1",
            "port": 8888,
            "peers": []
        }
    }
    
    # Save configuration
    config_path = os.path.join(os.path.dirname(__file__), '..', 'blockchain', 'data', 'sela_config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info(f"✅ Sela configuration saved to: {config_path}")
    
    return config

def create_activity_ledger(sela_id: str):
    """Create initial activity ledger for the Sela."""
    activity_ledger = {
        "sela_id": sela_id,
        "activities": [],
        "last_updated": 0,
        "total_activities": 0
    }
    
    activity_path = os.path.join(os.path.dirname(__file__), '..', 'blockchain', 'data', f'activity_{sela_id}.json')
    with open(activity_path, 'w') as f:
        json.dump(activity_ledger, f, indent=2)
    
    logger.info(f"✅ Activity ledger created: {activity_path}")

def main():
    """Main configuration function."""
    print("🔧 ONNYX SELA MINER CONFIGURATION")
    print("=" * 50)
    
    # Get available Selas
    selas = get_available_selas()
    
    if not selas:
        print("❌ No active Selas found in the database.")
        print("   Please register a Sela first using register_real_identity.py")
        return
    
    print(f"Found {len(selas)} active Sela(s):")
    for i, sela in enumerate(selas):
        identity = get_identity_by_id(sela['identity_id'])
        print(f"  {i+1}. {sela['name']} (ID: {sela['sela_id']})")
        print(f"     Owner: {identity.name if identity else 'Unknown'}")
        print(f"     Category: {sela['category']}")
        print()
    
    # Select Sela
    try:
        choice = int(input("Select Sela number to configure: ")) - 1
        if choice < 0 or choice >= len(selas):
            print("❌ Invalid selection!")
            return
    except ValueError:
        print("❌ Invalid input!")
        return
    
    selected_sela = selas[choice]
    sela_id = selected_sela['sela_id']
    identity_id = selected_sela['identity_id']
    
    print(f"\nConfiguring Sela: {selected_sela['name']} ({sela_id})")
    
    # Get private key
    private_key = input("Enter the private key for this Sela's identity: ").strip()
    
    if not private_key:
        print("❌ Private key is required!")
        return
    
    try:
        # Create configuration
        config = create_sela_config(sela_id, identity_id, private_key)
        
        # Create activity ledger
        create_activity_ledger(sela_id)
        
        print("\n" + "=" * 50)
        print("🎉 SELA MINER CONFIGURATION COMPLETE!")
        print(f"   Sela ID: {sela_id}")
        print(f"   Identity ID: {identity_id}")
        print(f"   Mining enabled: {config['mining']['enabled']}")
        print(f"   Auto-mine: {config['auto_mine']}")
        print("\n🚀 You can now start mining with:")
        print(f"   python dashboard/cli/onnyx-sela-miner.py mine")
        
    except Exception as e:
        logger.error(f"Failed to configure Sela miner: {e}")
        print(f"❌ Configuration failed: {e}")

if __name__ == "__main__":
    main()
