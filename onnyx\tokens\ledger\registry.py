"""
Onnyx Token Registry Module

This module provides the TokenRegistry class for managing tokens.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional

class TokenRegistry:
    """
    TokenRegistry manages tokens in the Onnyx ecosystem.
    
    A token is a digital asset that can be minted, transferred, and burned.
    """
    
    def __init__(self, path: str = "data/tokens.json"):
        """
        Initialize the TokenRegistry.
        
        Args:
            path: Path to the token registry JSON file
        """
        self.path = path
        self.tokens = self._load()
        
        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.path), exist_ok=True)
    
    def _load(self) -> Dict[str, Any]:
        """
        Load the token registry from the JSON file.
        
        Returns:
            The token registry as a dictionary
        """
        if os.path.exists(self.path):
            try:
                with open(self.path, "r") as f:
                    data = json.load(f)
                    # Ensure the data is a dictionary
                    if isinstance(data, dict):
                        return data
                    else:
                        return {}
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def _save(self) -> None:
        """Save the token registry to the JSON file."""
        with open(self.path, "w") as f:
            json.dump(self.tokens, f, indent=2)
    
    def register_token(self, token_id: str, name: str, symbol: str, creator_id: str, 
                       supply: int, category: str = "general", decimals: int = 2, 
                       metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Register a new token.
        
        Args:
            token_id: The token ID
            name: The token name
            symbol: The token symbol
            creator_id: The creator's identity ID
            supply: The initial supply
            category: The token category (e.g., "loyalty", "equity", "community")
            decimals: The number of decimal places
            metadata: Additional metadata for the token
        
        Returns:
            The newly created token
        
        Raises:
            Exception: If the token already exists
        """
        if token_id in self.tokens:
            raise Exception(f"Token with ID '{token_id}' already exists")
        
        if symbol in [token["symbol"] for token in self.tokens.values()]:
            raise Exception(f"Token with symbol '{symbol}' already exists")
        
        token = {
            "token_id": token_id,
            "name": name,
            "symbol": symbol,
            "creator_id": creator_id,
            "supply": supply,
            "category": category,
            "decimals": decimals,
            "created_at": int(time.time()),
            "metadata": metadata or {}
        }
        
        self.tokens[token_id] = token
        self._save()
        
        return token
    
    def get_token(self, token_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a token by ID.
        
        Args:
            token_id: The token ID
        
        Returns:
            The token or None if not found
        """
        return self.tokens.get(token_id)
    
    def get_token_by_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get a token by symbol.
        
        Args:
            symbol: The token symbol
        
        Returns:
            The token or None if not found
        """
        for token in self.tokens.values():
            if token["symbol"] == symbol:
                return token
        return None
    
    def update_token_supply(self, token_id: str, supply_change: int) -> Optional[Dict[str, Any]]:
        """
        Update the supply of a token.
        
        Args:
            token_id: The token ID
            supply_change: The change in supply (positive for mint, negative for burn)
        
        Returns:
            The updated token or None if not found
        
        Raises:
            Exception: If the token does not exist or the supply would become negative
        """
        if token_id not in self.tokens:
            raise Exception(f"Token with ID '{token_id}' not found")
        
        new_supply = self.tokens[token_id]["supply"] + supply_change
        
        if new_supply < 0:
            raise Exception(f"Cannot reduce supply below 0 (current: {self.tokens[token_id]['supply']}, change: {supply_change})")
        
        self.tokens[token_id]["supply"] = new_supply
        self._save()
        
        return self.tokens[token_id]
    
    def get_tokens_by_creator(self, creator_id: str) -> List[Dict[str, Any]]:
        """
        Get all tokens created by an identity.
        
        Args:
            creator_id: The creator's identity ID
        
        Returns:
            A list of tokens created by the identity
        """
        return [token for token in self.tokens.values() if token["creator_id"] == creator_id]
    
    def get_tokens_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Get all tokens of a specific category.
        
        Args:
            category: The token category
        
        Returns:
            A list of tokens of the specified category
        """
        return [token for token in self.tokens.values() if token["category"] == category]
    
    def get_tokens_by_sela(self, sela_id: str) -> List[Dict[str, Any]]:
        """
        Get all tokens associated with a Sela.
        
        Args:
            sela_id: The Sela ID
        
        Returns:
            A list of tokens associated with the Sela
        """
        return [token for token in self.tokens.values() if token.get("metadata", {}).get("sela_id") == sela_id]
    
    def get_all_tokens(self) -> Dict[str, Any]:
        """
        Get all tokens.
        
        Returns:
            All tokens in the registry
        """
        return self.tokens
