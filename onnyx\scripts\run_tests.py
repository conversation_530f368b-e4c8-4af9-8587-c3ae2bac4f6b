#!/usr/bin/env python3
"""
Onnyx Test Runner

This script runs the Onnyx tests.
"""

import os
import sys
import unittest
import argparse
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("onnyx.scripts.run_tests")

def run_tests(test_pattern=None, verbose=False):
    """
    Run the Onnyx tests.
    
    Args:
        test_pattern: Pattern to match test names
        verbose: Whether to run tests in verbose mode
    """
    # Get the test directory
    test_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'tests'))
    
    # Discover tests
    if test_pattern:
        test_suite = unittest.defaultTestLoader.discover(test_dir, pattern=test_pattern)
    else:
        test_suite = unittest.defaultTestLoader.discover(test_dir)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(test_suite)
    
    # Return the result
    return result.wasSuccessful()

def main():
    """
    Main function to run the Onnyx tests.
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run Onnyx tests")
    parser.add_argument("--pattern", help="Pattern to match test names")
    parser.add_argument("--verbose", action="store_true", help="Run tests in verbose mode")
    args = parser.parse_args()
    
    # Run tests
    success = run_tests(args.pattern, args.verbose)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
