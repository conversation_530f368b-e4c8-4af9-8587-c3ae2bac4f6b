"""
Onnyx Database Module

This module provides database functionality for the Onnyx blockchain.
"""

import os
import sqlite3
import logging
import threading
from typing import Dict, Any, List, Optional, Tuple

# Set up logging
logger = logging.getLogger("onnyx.data.db")

class Database:
    """
    Database provides SQLite database functionality for the Onnyx blockchain.
    """

    def __init__(self, db_path: str = None):
        """
        Initialize the Database.

        Args:
            db_path: Path to the SQLite database file
        """
        # Use production database in data/ directory
        if db_path is None:
            # Get the project root directory (go up from shared/db/ to project root)
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            db_path = os.path.join(project_root, "data", "onnyx.db")
        self.db_path = db_path
        self.lock = threading.RLock()

        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # Initialize the database
        self._init_db()

    def _init_db(self):
        """Initialize the database."""
        with self.lock:
            try:
                # Create the database if it doesn't exist
                conn = self.get_connection()

                # Close the connection
                conn.close()

                logger.info(f"Initialized database at {self.db_path}")
            except Exception as e:
                logger.error(f"Error initializing database: {str(e)}")

    def get_connection(self) -> sqlite3.Connection:
        """
        Get a database connection.

        Returns:
            A database connection
        """
        with self.lock:
            try:
                conn = sqlite3.connect(self.db_path, timeout=30.0)  # Increase timeout to 30 seconds
                conn.row_factory = sqlite3.Row

                # Enable foreign keys
                conn.execute("PRAGMA foreign_keys = ON")

                # Set journal mode to WAL for better concurrency
                conn.execute("PRAGMA journal_mode = WAL")

                # Set busy timeout
                conn.execute("PRAGMA busy_timeout = 30000")  # 30 seconds

                return conn
            except Exception as e:
                logger.error(f"Error getting database connection: {str(e)}")
                raise

    def execute_schema(self, schema_path: str):
        """
        Execute a schema file.

        Args:
            schema_path: Path to the schema file
        """
        with self.lock:
            try:
                # Read the schema file
                with open(schema_path, "r") as f:
                    schema = f.read()

                # Execute the schema
                conn = self.get_connection()
                conn.executescript(schema)
                conn.commit()
                conn.close()

                logger.info(f"Executed schema from {schema_path}")
            except Exception as e:
                logger.error(f"Error executing schema: {str(e)}")

    def execute(self, query: str, params: tuple = ()):
        """
        Execute a query.

        Args:
            query: The query to execute
            params: The query parameters
        """
        with self.lock:
            try:
                conn = self.get_connection()
                conn.execute(query, params)
                conn.commit()
                conn.close()
            except Exception as e:
                logger.error(f"Error executing query: {query} with params {params}: {str(e)}")
                raise

    def execute_many(self, query: str, params_list: List[tuple]):
        """
        Execute a query with multiple parameter sets.

        Args:
            query: The query to execute
            params_list: The list of parameter sets
        """
        with self.lock:
            conn = None
            try:
                conn = self.get_connection()
                conn.executemany(query, params_list)
                conn.commit()
            except Exception as e:
                logger.error(f"Error executing query with multiple parameter sets: {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except Exception as rollback_error:
                        logger.error(f"Error rolling back transaction: {str(rollback_error)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute a query and return the results.

        Args:
            query: The query to execute
            params: The query parameters

        Returns:
            The query results
        """
        with self.lock:
            conn = None
            try:
                conn = self.get_connection()
                cursor = conn.execute(query, params)
                results = [dict(row) for row in cursor.fetchall()]
                return results
            except Exception as e:
                logger.error(f"Error executing query: {query} with params {params}: {str(e)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def query_one(self, query: str, params: tuple = ()) -> Optional[Dict[str, Any]]:
        """
        Execute a query and return the first result.

        Args:
            query: The query to execute
            params: The query parameters

        Returns:
            The first query result or None if no results
        """
        with self.lock:
            conn = None
            try:
                conn = self.get_connection()
                cursor = conn.execute(query, params)
                row = cursor.fetchone()
                return dict(row) if row else None
            except Exception as e:
                logger.error(f"Error executing query: {query} with params {params}: {str(e)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def insert(self, table: str, data: Dict[str, Any]) -> int:
        """
        Insert data into a table.

        Args:
            table: The table to insert into
            data: The data to insert

        Returns:
            The ID of the inserted row
        """
        with self.lock:
            conn = None
            try:
                # Prepare the query
                columns = ", ".join(data.keys())
                placeholders = ", ".join(["?"] * len(data))
                query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"

                # Execute the query
                conn = self.get_connection()
                cursor = conn.execute(query, tuple(data.values()))
                conn.commit()

                # Get the ID of the inserted row
                row_id = cursor.lastrowid

                return row_id
            except Exception as e:
                logger.error(f"Error inserting data into {table}: {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except Exception as rollback_error:
                        logger.error(f"Error rolling back transaction: {str(rollback_error)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def update(self, table: str, data: Dict[str, Any], where: str, params: tuple = ()):
        """
        Update data in a table.

        Args:
            table: The table to update
            data: The data to update
            where: The WHERE clause
            params: The WHERE clause parameters
        """
        with self.lock:
            conn = None
            try:
                # Prepare the query
                set_clause = ", ".join([f"{key} = ?" for key in data.keys()])
                query = f"UPDATE {table} SET {set_clause} WHERE {where}"

                # Execute the query
                conn = self.get_connection()
                conn.execute(query, tuple(data.values()) + params)
                conn.commit()
            except Exception as e:
                logger.error(f"Error updating data in {table}: {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except Exception as rollback_error:
                        logger.error(f"Error rolling back transaction: {str(rollback_error)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def delete(self, table: str, where: str, params: tuple = ()):
        """
        Delete data from a table.

        Args:
            table: The table to delete from
            where: The WHERE clause
            params: The WHERE clause parameters
        """
        with self.lock:
            conn = None
            try:
                # Prepare the query
                query = f"DELETE FROM {table} WHERE {where}"

                # Execute the query
                conn = self.get_connection()
                conn.execute(query, params)
                conn.commit()
            except Exception as e:
                logger.error(f"Error deleting data from {table}: {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except Exception as rollback_error:
                        logger.error(f"Error rolling back transaction: {str(rollback_error)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def table_exists(self, table: str) -> bool:
        """
        Check if a table exists.

        Args:
            table: The table to check

        Returns:
            True if the table exists, False otherwise
        """
        with self.lock:
            conn = None
            try:
                conn = self.get_connection()
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    (table,)
                )
                exists = cursor.fetchone() is not None
                return exists
            except Exception as e:
                logger.error(f"Error checking if table exists: {str(e)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def get_tables(self) -> List[str]:
        """
        Get a list of tables.

        Returns:
            A list of table names
        """
        with self.lock:
            conn = None
            try:
                conn = self.get_connection()
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row["name"] for row in cursor.fetchall()]
                return tables
            except Exception as e:
                logger.error(f"Error getting tables: {str(e)}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing connection: {str(close_error)}")

    def backup(self, backup_path: str):
        """
        Backup the database.

        Args:
            backup_path: Path to the backup file
        """
        with self.lock:
            source_conn = None
            backup_conn = None
            try:
                # Ensure the directory exists
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)

                # Create a connection to the source database
                source_conn = self.get_connection()

                # Create a connection to the backup database
                backup_conn = sqlite3.connect(backup_path)

                # Backup the database
                source_conn.backup(backup_conn)

                logger.info(f"Backed up database to {backup_path}")
            except Exception as e:
                logger.error(f"Error backing up database: {str(e)}")
                raise
            finally:
                # Close the connections
                if source_conn:
                    try:
                        source_conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing source connection: {str(close_error)}")
                if backup_conn:
                    try:
                        backup_conn.close()
                    except Exception as close_error:
                        logger.error(f"Error closing backup connection: {str(close_error)}")

# Create a global instance of the Database
db = Database()
