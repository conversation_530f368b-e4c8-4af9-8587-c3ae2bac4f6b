/**
 * Onnyx Typewriter Effect
 */

document.addEventListener('DOMContentLoaded', function() {
  // Typing effect logic
  const lines = [
    "Hello World, without end.",
    "Come and let us reason together.",
    "Let us build a world where trust is the currency.",
    "Where every action is a vote for righteousness.",
    "Where every token is a reflection of your worth.",
    "Where every transaction is a step towards a better future.",
    "Where every identity is a unique snowflake.",
    "Where every Sela is a beacon of hope.",
    "Where every Onnyx is a shining star.",
    "Where every block is a testament to your integrity."
  ];

  const typeEl = document.getElementById('typewriter');
  if (!typeEl) return;
  
  let lineIndex = 0;
  let charIndex = 0;
  let currentLine = '';
  let typingSpeed = 40;

  function typeLine() {
    if (lineIndex < lines.length) {
      if (charIndex < lines[lineIndex].length) {
        currentLine += lines[lineIndex][charIndex];
        typeEl.innerText = currentLine;
        charIndex++;
        setTimeout(typeLine, typingSpeed);
      } else {
        currentLine += '\n';
        charIndex = 0;
        lineIndex++;
        setTimeout(typeLine, 600);
      }
    }
    // No border when complete
  }

  typeLine();
});