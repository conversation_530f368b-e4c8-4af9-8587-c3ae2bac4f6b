# Onnyx Database Layer

The Onnyx database layer provides a persistent storage solution for the Onnyx blockchain. It uses SQLite to store blockchain data, identities, tokens, and other information.

## Overview

The database layer is organized into the following components:

- **Blockchain**: Stores blocks and transactions.
- **Identities**: Stores identity information, reputation, and badges.
- **Tokens**: Stores token information, balances, and transactions.
- **Governance**: Stores voice scrolls (governance proposals) and votes.
- **Economic**: Stores Sela businesses, Zeman credits, and Etzem scores.
- **Node**: Stores node-specific information, such as the mempool and activity ledger.

## Schema

The database schema is defined in `schemas/schema.sql`. It includes the following tables:

### Blockchain Tables

- **blocks**: Stores block information, such as block hash, height, previous hash, timestamp, difficulty, nonce, miner, transactions, merkle root, size, and version.
- **transactions**: Stores transaction information, such as transaction ID, block hash, timestamp, operation, data, sender, signature, status, and creation time.
- **mempool**: Stores unconfirmed transactions, with similar fields as the transactions table.

### Identity Tables

- **identities**: Stores identity information, such as identity ID, name, public key, metadata, creation time, and update time.
- **reputation**: Stores reputation information, such as identity ID, reputation type, value, issuer ID, timestamp, and transaction ID.
- **badges**: Stores badge information, such as identity ID, badge type, issuer ID, timestamp, and transaction ID.

### Token Tables

- **tokens**: Stores token information, such as token ID, name, symbol, creator ID, supply, category, decimals, creation time, and metadata.
- **token_balances**: Stores token balances, such as token ID, identity ID, balance, and update time.
- **token_transactions**: Stores token transactions, such as transaction ID, token ID, from ID, to ID, amount, operation, and timestamp.

### Governance Tables

- **voice_scrolls**: Stores governance proposals, such as scroll ID, title, description, proposer ID, status, proposal type, parameters, voting start time, voting end time, implementation time, creation time, and update time.
- **scroll_votes**: Stores votes on governance proposals, such as scroll ID, voter ID, vote, weight, timestamp, and transaction ID.

### Economic Tables

- **selas**: Stores Sela business information, such as Sela ID, identity ID, name, category, stake amount, stake token ID, creation time, and metadata.
- **zeman_credits**: Stores Zeman time/work credits, such as identity ID, hours, description, issuer ID, timestamp, and transaction ID.
- **etzem_scores**: Stores Etzem trust scores, such as identity ID, composite score, Zeman score, reputation score, Sela score, token activity score, governance score, longevity score, and update time.

### Node Tables

- **event_logs**: Stores event logs, such as event type, entity ID, data, and timestamp.
- **activity_ledger**: Stores activity information, such as identity ID, activity type, data, and timestamp.
- **rotation_registry**: Stores validator rotation information, such as Sela ID, identity ID, Etzem score, eligibility, last block mined, next rotation time, and update time.

## Usage

### Initialization

To initialize the database, use the `init_db.py` script:

```bash
python scripts/init_db.py
```

This script creates the database file, creates the tables, and migrates data from JSON files to the database.

### Database Access

To access the database, use the SQLite3 library:

```python
import sqlite3

# Connect to the database
conn = sqlite3.connect("runtime/onnyx.db")
cursor = conn.cursor()

# Execute a query
cursor.execute("SELECT * FROM blocks")
blocks = cursor.fetchall()

# Close the connection
conn.close()
```

### Database Models

The database layer includes model classes for each table. These models provide a convenient way to interact with the database:

```python
from models.block import Block
from models.transaction import Transaction
from models.identity import Identity
from models.token import Token

# Get a block by hash
block = Block.get_by_hash("0000000000000000000000000000000000000000000000000000000000000000")

# Get a transaction by ID
transaction = Transaction.get_by_id("tx_1")

# Get an identity by ID
identity = Identity.get_by_id("test_identity")

# Get a token by ID
token = Token.get_by_id("TEST_ONX")
```

### Database Migrations

The database layer includes a migration system to handle schema changes. Migrations are defined in the `migrations` directory and are applied automatically when the database is initialized.

To create a new migration, create a new file in the `migrations` directory with a name like `001_initial_schema.sql`. The file should contain SQL statements to modify the schema.

To apply migrations, use the `migrate.py` script:

```bash
python scripts/migrate.py
```

## Testing

The database layer includes tests to ensure that it works correctly. These tests are located in the `tests` directory and can be run with the following command:

```bash
python -m unittest discover tests
```

The tests use an in-memory SQLite database to avoid modifying the production database.

## Performance Considerations

SQLite is a file-based database that is suitable for small to medium-sized deployments. For larger deployments, you may want to consider using a more scalable database system, such as PostgreSQL or MySQL.

The database layer includes indexes on frequently queried columns to improve performance. However, you may need to add additional indexes based on your specific query patterns.

The database layer also includes a connection pool to avoid the overhead of creating and closing connections for each query. The connection pool is managed by the `db.py` module.
