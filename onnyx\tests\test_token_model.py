"""
Onnyx Token Model Tests

This module provides tests for the Onnyx token model.
"""

import os
import sys
import unittest
import time
import hashlib
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.token import Token
from models.identity import Identity
from data.db import db

class TestTokenModel(unittest.TestCase):
    """
    Test the Token model.
    """
    
    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_token_model.db")
        
        # Set the database path
        db.db_path = self.db_path
        
        # Create the tables
        conn = sqlite3.connect(self.db_path)
        
        # Create the identities table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER,
            updated_at INTEGER
        )
        """)
        
        # Create the tokens table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            token_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            creator_id TEXT NOT NULL,
            supply INTEGER NOT NULL,
            category TEXT,
            decimals INTEGER,
            created_at INTEGER,
            metadata TEXT,
            FOREIGN KEY (creator_id) REFERENCES identities (identity_id)
        )
        """)
        
        # Create the token_balances table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS token_balances (
            identity_id TEXT NOT NULL,
            token_id TEXT NOT NULL,
            balance INTEGER NOT NULL,
            updated_at INTEGER,
            PRIMARY KEY (identity_id, token_id),
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id),
            FOREIGN KEY (token_id) REFERENCES tokens (token_id)
        )
        """)
        
        conn.commit()
        conn.close()
        
        # Create a test identity
        self.identity_id = "test_identity"
        
        # Insert the identity into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.identity_id,
                "Test Identity",
                "0x1234567890abcdef",
                json.dumps({"type": "individual", "description": "Test identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")
        
        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")
    
    def test_create_token(self):
        """Test creating a token."""
        # Create a token directly in the database
        token_id = "test_token"
        
        # Insert the token into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO tokens (token_id, name, symbol, creator_id, supply, category, decimals, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                token_id,
                "Test Token",
                "TEST",
                self.identity_id,
                1000,
                "test",
                2,
                int(time.time()),
                json.dumps({"description": "Test token"})
            )
        )
        conn.commit()
        conn.close()
        
        # Check if the token was saved to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM tokens WHERE token_id = ?", (token_id,))
        row = cursor.fetchone()
        conn.close()
        
        print(f"Database row: {row}")
        
        # Get the token by ID
        retrieved_token = Token.get_by_id(token_id)
        
        # Check that the token was retrieved
        self.assertIsNotNone(retrieved_token)
        self.assertEqual(retrieved_token.token_id, token_id)
        self.assertEqual(retrieved_token.name, "Test Token")
        self.assertEqual(retrieved_token.symbol, "TEST")
        self.assertEqual(retrieved_token.creator_id, self.identity_id)
        self.assertEqual(retrieved_token.supply, 1000)
        self.assertEqual(retrieved_token.category, "test")
        self.assertEqual(retrieved_token.decimals, 2)
        self.assertEqual(retrieved_token.metadata["description"], "Test token")
    
    def test_token_balance(self):
        """Test token balance operations."""
        # Create a token directly in the database
        token_id = "test_token"
        
        # Insert the token into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO tokens (token_id, name, symbol, creator_id, supply, category, decimals, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                token_id,
                "Test Token",
                "TEST",
                self.identity_id,
                1000,
                "test",
                2,
                int(time.time()),
                json.dumps({"description": "Test token"})
            )
        )
        
        # Insert a token balance
        conn.execute(
            "INSERT INTO token_balances (identity_id, token_id, balance, updated_at) VALUES (?, ?, ?, ?)",
            (
                self.identity_id,
                token_id,
                1000,
                int(time.time())
            )
        )
        conn.commit()
        conn.close()
        
        # Get the token by ID
        token = Token.get_by_id(token_id)
        
        # Check the token balance
        balance = token.get_balance(self.identity_id)
        self.assertEqual(balance, 1000)

if __name__ == "__main__":
    unittest.main()
