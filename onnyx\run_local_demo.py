#!/usr/bin/env python3
"""
Onnyx Miner Local Demo

This script sets up and runs a local demo of the Onnyx Miner.
"""

import os
import sys
import time
import subprocess
import threading
import json
import yaml
import shutil
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from shared.config.config import OnnyxConfig

# Configuration
DEMO_DIR = "demo"
CONFIG_DIR = os.path.join(DEMO_DIR, "config")
RUNTIME_DIR = os.path.join(DEMO_DIR, "runtime")
DATA_DIR = os.path.join(DEMO_DIR, "data")
KEYS_DIR = os.path.join(RUNTIME_DIR, "keys")
LOG_DIR = os.path.join(RUNTIME_DIR, "logs")
LOG_FILE = os.path.join(LOG_DIR, "onnyx_miner.log")

# Initialize the configuration
config = OnnyxConfig(config_dir=CONFIG_DIR, data_dir=RUNTIME_DIR)

# Demo identities
IDENTITIES = [
    {"id": "alice_salon", "name": "Alice's Beauty Salon", "type": "BEAUTY"},
    {"id": "bob_barber", "name": "Bob's Barbershop", "type": "BARBER"},
    {"id": "charlie_cafe", "name": "Charlie's Cafe", "type": "CAFE"}
]

def setup_demo():
    """Set up the demo environment."""
    print("Setting up demo environment...")

    # Create demo directories
    os.makedirs(DEMO_DIR, exist_ok=True)
    os.makedirs(CONFIG_DIR, exist_ok=True)
    os.makedirs(RUNTIME_DIR, exist_ok=True)
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(KEYS_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "sela"), exist_ok=True)

    # Create mock blockchain data
    create_mock_data()

    # Create configuration files for each identity
    for identity in IDENTITIES:
        create_config(identity)

    print("Demo environment set up successfully!")

def create_mock_data():
    """Create mock blockchain data."""
    # Create mock blocks
    blocks_file = os.path.join(DATA_DIR, "blocks.json")
    blocks = {
        "blocks": [
            {
                "index": 0,
                "timestamp": int(time.time()) - 3600,
                "proposer": "genesis",
                "previous_hash": "0",
                "hash": "000000000000000000000000000000000000000000000000000000000000000",
                "nonce": 0,
                "signature": "",
                "signed_by": "genesis",
                "transactions": []
            }
        ]
    }

    with open(blocks_file, "w") as f:
        json.dump(blocks, f, indent=2)

    # Create mock identities
    identities_file = os.path.join(DATA_DIR, "identities.json")
    identities = {}

    for identity in IDENTITIES:
        identities[identity["id"]] = {
            "identity_id": identity["id"],
            "name": identity["name"],
            "public_key": f"0x{identity['id']}",
            "created_at": int(time.time()) - 3600,
            "reputation": 100,
            "etzem_score": 50,
            "badges": ["VALIDATOR_ELIGIBLE_BADGE"],
            "joined_selas": [],
            "founded_selas": [identity["id"]]
        }

    with open(identities_file, "w") as f:
        json.dump(identities, f, indent=2)

    # Create mock selas
    selas_file = os.path.join(DATA_DIR, "selas.json")
    selas = {}

    for identity in IDENTITIES:
        selas[identity["id"]] = {
            "sela_id": identity["id"],
            "name": identity["name"],
            "founder": identity["id"],
            "type": identity["type"],
            "token_id": f"{identity['id']}_TOKEN",
            "created_at": int(time.time()) - 3600,
            "members": [identity["id"]],
            "roles": {identity["id"]: "FOUNDER"},
            "services_offered": ["Service 1", "Service 2", "Service 3"],
            "is_validator": True,
            "validator_since": int(time.time()) - 3600,
            "last_block_proposed": 0
        }

    with open(selas_file, "w") as f:
        json.dump(selas, f, indent=2)

    # Create mock tokens
    tokens_file = os.path.join(DATA_DIR, "tokens.json")
    tokens = {}

    for identity in IDENTITIES:
        tokens[f"{identity['id']}_TOKEN"] = {
            "token_id": f"{identity['id']}_TOKEN",
            "name": f"{identity['name']} Token",
            "symbol": f"{identity['id'][:3].upper()}",
            "creator_id": identity["id"],
            "supply": 1000,
            "category": "loyalty",
            "decimals": 2,
            "created_at": int(time.time()) - 3600,
            "metadata": {
                "sela_id": identity["id"]
            }
        }

    with open(tokens_file, "w") as f:
        json.dump(tokens, f, indent=2)

    # Create mock balances
    balances_file = os.path.join(DATA_DIR, "balances.json")
    balances = {}

    for identity in IDENTITIES:
        balances[identity["id"]] = {
            f"{identity['id']}_TOKEN": 1000,
            "ONX": 100
        }

    with open(balances_file, "w") as f:
        json.dump(balances, f, indent=2)

def create_config(identity):
    """Create a configuration file for an identity."""
    config = {
        "identity": {
            "id": identity["id"],
            "name": identity["name"],
            "keys_dir": KEYS_DIR
        },
        "sela": {
            "id": identity["id"],
            "name": identity["name"],
            "type": identity["type"]
        },
        "node": {
            "api_url": "http://localhost:8000",
            "p2p_port": 9000 + IDENTITIES.index(identity),
            "data_dir": DATA_DIR,
            "auto_mine": False,
            "mine_interval": 60
        },
        "gui": {
            "enabled": True,
            "port": 5005 + IDENTITIES.index(identity),
            "host": "127.0.0.1"
        }
    }

    config_file = os.path.join(DEMO_DIR, f"config_{identity['id']}.yaml")

    with open(config_file, "w") as f:
        yaml.dump(config, f, default_flow_style=False)

    # Create keys for the identity
    identity_dir = os.path.join(KEYS_DIR, identity["id"])
    os.makedirs(identity_dir, exist_ok=True)

    # Create a mock private key
    with open(os.path.join(identity_dir, "private.pem"), "w") as f:
        f.write(f"-----BEGIN PRIVATE KEY-----\n{identity['id']}\n-----END PRIVATE KEY-----")

    # Create a mock public key
    with open(os.path.join(identity_dir, "public.pem"), "w") as f:
        f.write(f"-----BEGIN PUBLIC KEY-----\n{identity['id']}\n-----END PUBLIC KEY-----")

    # Create a sela config file for the onnyx-sela-miner.py script
    sela_config = {
        "sela_id": identity["id"],
        "identity_id": identity["id"],
        "name": identity["name"],
        "type": identity["type"],
        "private_key_path": os.path.join(KEYS_DIR, identity["id"], "private.pem"),
        "api_port": 8000 + IDENTITIES.index(identity),
        "auto_mine": False,
        "mine_interval": 60
    }

    # Create the data directory if it doesn't exist
    os.makedirs(os.path.join(DATA_DIR, "sela"), exist_ok=True)

    # Save the sela config
    sela_config_file = os.path.join(DATA_DIR, "sela", f"{identity['id']}_config.json")
    with open(sela_config_file, "w") as f:
        json.dump(sela_config, f, indent=2)

def run_miner(identity):
    """Run the Onnyx Miner for an identity."""
    config_file = os.path.join(DEMO_DIR, f"config_{identity['id']}.yaml")
    sela_config_file = os.path.join(DATA_DIR, "sela", f"{identity['id']}_config.json")

    # Set the environment variable for the configuration file
    env = os.environ.copy()
    env["ONNYX_CONFIG"] = config_file

    # Run the miner
    print(f"Starting Onnyx Miner for {identity['name']}...")

    # Use subprocess to run the miner
    try:
        # Run the sela miner script from the new location
        print(f"Running: python dashboard/cli/onnyx-sela-miner.py status --config {sela_config_file}")
        miner_process = subprocess.Popen(
            [sys.executable, "dashboard/cli/onnyx-sela-miner.py", "status", "--config", sela_config_file],
            env=env
        )

        # Wait for the miner to start
        miner_process.wait()

        # Run the explorer script from the new location
        port = 5005 + IDENTITIES.index(identity)
        print(f"Running: python dashboard/cli/onnyx-explorer.py --port {port}")
        explorer_process = subprocess.Popen(
            [sys.executable, "dashboard/cli/onnyx-explorer.py", "--port", str(port)],
            env=env
        )

        return miner_process, explorer_process
    except Exception as e:
        print(f"Error starting miner for {identity['name']}: {str(e)}")
        return None, None

def run_demo():
    """Run the demo."""
    # Set up the demo environment
    setup_demo()

    # Store all processes
    processes = []

    # Run the miners
    for identity in IDENTITIES:
        miner_process, explorer_process = run_miner(identity)

        if miner_process:
            processes.append(miner_process)
        if explorer_process:
            processes.append(explorer_process)

        # Wait a bit between starting miners
        time.sleep(1)

    print("\nDemo is running!")
    print("You can access the miners at:")

    for identity in IDENTITIES:
        port = 5005 + IDENTITIES.index(identity)
        print(f"  {identity['name']}: http://localhost:{port}")

    print("\nPress Ctrl+C to stop the demo.")

    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping demo...")

        # Terminate all processes
        for process in processes:
            try:
                process.terminate()
            except:
                pass

if __name__ == "__main__":
    run_demo()
