{% extends "base.html" %}

{% block content %}
<div class="row">
  <div class="col-md-6">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Propose Voice Scroll</h5>
      </div>
      <div class="card-body">
        <form action="{{ url_for('propose') }}" method="post">
          <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" required>
          </div>
          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
          </div>
          <div class="mb-3">
            <label for="category" class="form-label">Category</label>
            <select class="form-select" id="category" name="category" required>
              <option value="economic">Economic</option>
              <option value="technical">Technical</option>
              <option value="social">Social</option>
              <option value="governance">Governance</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="param" class="form-label">Parameter (optional)</label>
            <input type="text" class="form-control" id="param" name="param">
            <div class="form-text">For parameter changes, specify the parameter name</div>
          </div>
          <div class="mb-3">
            <label for="value" class="form-label">Value (optional)</label>
            <input type="text" class="form-control" id="value" name="value">
            <div class="form-text">For parameter changes, specify the new value</div>
          </div>
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">Propose</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Active Voice Scrolls</h5>
      </div>
      <div class="card-body">
        {% if scrolls %}
          <div class="list-group">
            {% for scroll in scrolls if scroll.status == 'active' %}
              <div class="list-group-item">
                <h6>{{ scroll.title }}</h6>
                <p>{{ scroll.description }}</p>
                <div class="d-flex justify-content-between align-items-center">
                  <small>Category: {{ scroll.category }}</small>
                  <div>
                    <form action="{{ url_for('vote') }}" method="post" class="d-inline">
                      <input type="hidden" name="scroll_id" value="{{ scroll.id }}">
                      <input type="hidden" name="decision" value="yes">
                      <button type="submit" class="btn btn-sm btn-success">Yes</button>
                    </form>
                    <form action="{{ url_for('vote') }}" method="post" class="d-inline">
                      <input type="hidden" name="scroll_id" value="{{ scroll.id }}">
                      <input type="hidden" name="decision" value="no">
                      <button type="submit" class="btn btn-sm btn-danger">No</button>
                    </form>
                    <form action="{{ url_for('vote') }}" method="post" class="d-inline">
                      <input type="hidden" name="scroll_id" value="{{ scroll.id }}">
                      <input type="hidden" name="decision" value="abstain">
                      <button type="submit" class="btn btn-sm btn-secondary">Abstain</button>
                    </form>
                  </div>
                </div>
              </div>
            {% else %}
              <p>No active scrolls found.</p>
            {% endfor %}
          </div>
        {% else %}
          <p>No scrolls found.</p>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<div class="card">
  <div class="card-header">
    <h5 class="card-title">Past Voice Scrolls</h5>
  </div>
  <div class="card-body">
    {% if scrolls %}
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>ID</th>
              <th>Title</th>
              <th>Category</th>
              <th>Status</th>
              <th>Outcome</th>
            </tr>
          </thead>
          <tbody>
            {% for scroll in scrolls if scroll.status != 'active' %}
              <tr>
                <td>{{ scroll.id }}</td>
                <td>{{ scroll.title }}</td>
                <td>{{ scroll.category }}</td>
                <td>
                  <span class="badge bg-{{ 'success' if scroll.status == 'passed' else 'danger' if scroll.status == 'rejected' else 'secondary' }}">
                    {{ scroll.status }}
                  </span>
                </td>
                <td>{{ scroll.outcome }}</td>
              </tr>
            {% else %}
              <tr>
                <td colspan="5">No past scrolls found.</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <p>No scrolls found.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
