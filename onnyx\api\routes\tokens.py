"""
Onnyx Token Routes

This module provides API routes for token operations.
"""

from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from tokens.ledger.ledger.ledger.registry import TokenRegistry
from tokens.ledger.ledger.ledger.ledger import TokenLedger
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry
from yovel.limits import calculate_yovel_cap
from identity.trust.etzem_engine import EtzemEngine

# Create router
router = APIRouter()

# Create instances
token_registry = TokenRegistry()
token_ledger = TokenLedger()
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()
etzem_engine = EtzemEngine()

@router.post("/tokens/mint")
def mint_token(
    token_id: str,
    name: str,
    symbol: str,
    creator_id: str,
    supply: int,
    category: str = "general",
    decimals: int = 2,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Mint a new token.

    Args:
        token_id: The token ID
        name: The token name
        symbol: The token symbol
        creator_id: The creator's identity ID
        supply: The initial supply
        category: The token category (e.g., "loyalty", "equity", "community")
        decimals: The number of decimal places
        metadata: Additional metadata for the token

    Returns:
        Information about the minted token
    """
    try:
        # Check if the creator identity exists
        creator = identity_registry.get_identity(creator_id)
        if not creator:
            raise Exception(f"Identity with ID '{creator_id}' not found")

        # Check if the creator is a Selaholder
        is_selaholder = False
        if "founded_selas" in creator and creator["founded_selas"]:
            is_selaholder = True
        elif "joined_selas" in creator and creator["joined_selas"]:
            is_selaholder = True

        if not is_selaholder:
            raise Exception("Only Selaholders can mint Mikvah tokens")

        # Calculate the Etzem score
        etzem_data = etzem_engine.compute_etzem(creator_id)
        etzem_score = etzem_data["etzem"]

        # Calculate the Yovel mint cap
        mint_cap = calculate_yovel_cap(etzem_score, category)

        # Check if the supply is within the mint cap
        if supply > mint_cap:
            raise Exception(f"Supply ({supply}) exceeds mint cap of {mint_cap} for category '{category}'")

        # Register the token
        token = token_registry.register_token(
            token_id,
            name,
            symbol,
            creator_id,
            supply,
            category,
            decimals,
            metadata
        )

        # Credit the initial supply to the creator
        token_ledger.credit(creator_id, token_id, supply)

        return {
            "status": "token_minted",
            "token": token
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tokens/{token_id}")
def get_token(token_id: str) -> Dict[str, Any]:
    """
    Get a token by ID.

    Args:
        token_id: The token ID

    Returns:
        The token
    """
    token = token_registry.get_token(token_id)
    if not token:
        raise HTTPException(status_code=404, detail=f"Token with ID '{token_id}' not found")

    return token

@router.get("/tokens/symbol/{symbol}")
def get_token_by_symbol(symbol: str) -> Dict[str, Any]:
    """
    Get a token by symbol.

    Args:
        symbol: The token symbol

    Returns:
        The token
    """
    token = token_registry.get_token_by_symbol(symbol)
    if not token:
        raise HTTPException(status_code=404, detail=f"Token with symbol '{symbol}' not found")

    return token

@router.get("/tokens")
def list_tokens(category: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all tokens.

    Args:
        category: Filter by token category

    Returns:
        All tokens in the registry
    """
    if category:
        tokens = token_registry.get_tokens_by_category(category)
    else:
        tokens = list(token_registry.get_all_tokens().values())

    return {
        "tokens": tokens
    }

@router.get("/tokens/creator/{creator_id}")
def get_tokens_by_creator(creator_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all tokens created by an identity.

    Args:
        creator_id: The creator's identity ID

    Returns:
        A list of tokens created by the identity
    """
    tokens = token_registry.get_tokens_by_creator(creator_id)

    return {
        "tokens": tokens
    }

@router.get("/sela/{sela_id}/tokens")
def get_tokens_by_sela(sela_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all tokens associated with a Sela.

    Args:
        sela_id: The Sela ID

    Returns:
        A list of tokens associated with the Sela
    """
    # Check if the Sela exists
    sela = sela_registry.get_sela(sela_id)
    if not sela:
        raise HTTPException(status_code=404, detail=f"Sela with ID '{sela_id}' not found")

    # Get tokens created by the Sela founder
    founder_id = sela["founder"]
    tokens = token_registry.get_tokens_by_creator(founder_id)

    # Filter tokens that have the Sela ID in their metadata
    sela_tokens = []
    for token in tokens:
        metadata = token.get("metadata", {})
        if isinstance(metadata, dict) and metadata.get("sela_id") == sela_id:
            sela_tokens.append(token)

    return {
        "sela_id": sela_id,
        "sela_name": sela["name"],
        "tokens": sela_tokens
    }

@router.get("/tokenregistry/categories")
def get_token_categories() -> Dict[str, List[str]]:
    """
    Get all token categories.

    Returns:
        All token categories
    """
    # Get all tokens
    tokens = token_registry.get_all_tokens().values()

    # Extract unique categories
    categories = set(token["category"] for token in tokens)

    return {
        "categories": list(categories)
    }
