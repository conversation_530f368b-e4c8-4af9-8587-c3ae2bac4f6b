#!/usr/bin/env python3
"""
Onnyx Sela Miner CLI

This script provides a command-line interface for Sela miners.
"""

import os
import sys
import time
import json
import argparse
import logging
import asyncio
from typing import Dict, Any, List, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("sela_miner.log")
    ]
)
logger = logging.getLogger("onnyx.sela_miner")

# Add the root directory to the Python path
root_dir = os.path.join(os.path.dirname(__file__), "..", "..")
sys.path.insert(0, os.path.abspath(root_dir))

# Import Onnyx modules
from blockchain.node.sela_config import SelaConfig
from blockchain.node.activity_ledger import ActivityLedger
from blockchain.node.rotation_registry import RotationRegistry
from blockchain.node.block_signer import BlockSigner
from blockchain.consensus.miner import BlockMiner
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry

def setup_sela_miner(args):
    """
    Set up a new Sela miner.

    Args:
        args: Command-line arguments
    """
    logger.info("Setting up Sela miner...")

    # Create a new Sela configuration
    config = SelaConfig(args.config)

    # Set up the Sela miner
    config.setup_new_sela(
        sela_id=args.sela_id,
        identity_id=args.identity_id,
        private_key_path=args.private_key,
        api_port=args.port
    )

    logger.info(f"Sela miner set up for Sela {args.sela_id}")
    logger.info(f"Configuration saved to {config.config_path}")

def mine_block(args):
    """
    Mine a new block.

    Args:
        args: Command-line arguments
    """
    logger.info("Mining a new block...")

    # Load configuration
    config = SelaConfig(args.config)

    # Check if the configuration is valid
    if not config.validate():
        logger.error("Invalid Sela miner configuration")
        return

    # Check if this Sela is the current validator
    rotation_registry = RotationRegistry()
    current_validator = rotation_registry.get_current_validator()

    if current_validator != config.get("sela_id") and not args.force:
        logger.warning(f"This Sela ({config.get('sela_id')}) is not the current validator ({current_validator})")
        logger.warning("Use --force to mine anyway")
        return

    # Load the private key
    try:
        block_signer = BlockSigner()
        private_key_pem = block_signer.load_private_key(config.get("private_key_path"))
    except Exception as e:
        logger.error(f"Error loading private key: {str(e)}")
        return

    # Create a block miner
    miner = BlockMiner()

    # Mine a new block
    try:
        block = miner.create_block(
            proposer_id=config.get("identity_id"),
            sela_id=config.get("sela_id"),
            private_key_pem=private_key_pem
        )

        logger.info(f"Block {block['index']} mined successfully")
        logger.info(f"Block hash: {block['hash']}")
        logger.info(f"Transactions: {len(block['transactions'])}")
    except Exception as e:
        logger.error(f"Error mining block: {str(e)}")

def record_service(args):
    """
    Record a service provided by the Sela.

    Args:
        args: Command-line arguments
    """
    logger.info("Recording a service...")

    # Load configuration
    config = SelaConfig(args.config)

    # Check if the configuration is valid
    if not config.validate():
        logger.error("Invalid Sela miner configuration")
        return

    # Create an activity ledger
    ledger = ActivityLedger(sela_id=config.get("sela_id"))

    # Record the service
    try:
        service = ledger.record_service(
            service_type=args.type,
            description=args.description,
            recipient_id=args.recipient,
            duration=args.duration,
            metadata=json.loads(args.metadata) if args.metadata else None
        )

        logger.info(f"Service recorded: {service['id']}")
        logger.info(f"Type: {service['type']}")
        logger.info(f"Description: {service['description']}")
    except Exception as e:
        logger.error(f"Error recording service: {str(e)}")

def vote(args):
    """
    Record a governance vote.

    Args:
        args: Command-line arguments
    """
    logger.info("Recording a governance vote...")

    # Load configuration
    config = SelaConfig(args.config)

    # Check if the configuration is valid
    if not config.validate():
        logger.error("Invalid Sela miner configuration")
        return

    # Create an activity ledger
    ledger = ActivityLedger(sela_id=config.get("sela_id"))

    # Record the vote
    try:
        vote = ledger.record_vote(
            scroll_id=args.scroll_id,
            vote=args.vote,
            reason=args.reason
        )

        logger.info(f"Vote recorded: {vote['id']}")
        logger.info(f"Scroll: {vote['scroll_id']}")
        logger.info(f"Vote: {vote['vote']}")
    except Exception as e:
        logger.error(f"Error recording vote: {str(e)}")

def status(args):
    """
    Get the status of the Sela miner.

    Args:
        args: Command-line arguments
    """
    logger.info("Getting Sela miner status...")

    # Load configuration
    config = SelaConfig(args.config)

    # Check if the configuration is valid
    if not config.validate():
        logger.error("Invalid Sela miner configuration")
        return

    # Get the validator status
    rotation_registry = RotationRegistry()
    validator_status = rotation_registry.get_validator_status(config.get("sela_id"))

    # Get the activity ledger
    ledger = ActivityLedger(sela_id=config.get("sela_id"))

    # Get the miner stats
    miner = BlockMiner()
    mining_stats = miner.get_mining_stats()

    # Print the status
    print("\n=== Sela Miner Status ===\n")
    print(f"Sela ID: {config.get('sela_id')}")
    print(f"Identity ID: {config.get('identity_id')}")
    print(f"Role: {config.get('role')}")
    print(f"API Port: {config.get('api_port')}")
    print(f"Auto Mine: {config.get('auto_mine')}")
    print(f"Mine Interval: {config.get('mine_interval')} seconds")
    print()
    print("=== Validator Status ===\n")
    print(f"Current Validator: {validator_status['current_validator']}")
    print(f"Is Current Validator: {validator_status['is_current_validator']}")
    print(f"Is Eligible: {validator_status['is_eligible']}")
    print(f"Is Active: {validator_status['is_active']}")
    print(f"Next Rotation: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(validator_status['next_rotation']))}")
    print(f"Time Until Rotation: {validator_status['time_until_rotation']} seconds")
    print()
    print("=== Mining Stats ===\n")
    print(f"Chain Height: {mining_stats['chain_height']}")
    print(f"Latest Block Hash: {mining_stats['latest_block_hash']}")
    print(f"Mempool Size: {mining_stats['mempool_size']}")
    print(f"Block Reward: {mining_stats['block_reward']} {mining_stats['reward_token']}")
    print()
    print("=== Activity Stats ===\n")
    print(f"Activities: {len(ledger.get_activities())}")
    print(f"Services: {len(ledger.get_services())}")
    print(f"Pending Transactions: {len(ledger.get_pending_transactions())}")
    print(f"Governance Votes: {len(ledger.get_votes())}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Onnyx Sela Miner CLI")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Setup command
    setup_parser = subparsers.add_parser("setup", help="Set up a new Sela miner")
    setup_parser.add_argument("--sela-id", required=True, help="Sela ID")
    setup_parser.add_argument("--identity-id", required=True, help="Identity ID")
    setup_parser.add_argument("--private-key", required=True, help="Path to private key file")
    setup_parser.add_argument("--port", type=int, default=8888, help="API port")
    setup_parser.add_argument("--config", default=None, help="Path to configuration file")

    # Mine command
    mine_parser = subparsers.add_parser("mine", help="Mine a new block")
    mine_parser.add_argument("--force", action="store_true", help="Force mining even if not the current validator")
    mine_parser.add_argument("--config", default=None, help="Path to configuration file")

    # Service command
    service_parser = subparsers.add_parser("service", help="Record a service")
    service_parser.add_argument("--type", required=True, help="Service type")
    service_parser.add_argument("--description", required=True, help="Service description")
    service_parser.add_argument("--recipient", help="Recipient identity ID")
    service_parser.add_argument("--duration", type=int, help="Service duration in minutes")
    service_parser.add_argument("--metadata", help="Service metadata (JSON string)")
    service_parser.add_argument("--config", default=None, help="Path to configuration file")

    # Vote command
    vote_parser = subparsers.add_parser("vote", help="Record a governance vote")
    vote_parser.add_argument("--scroll-id", required=True, help="Voice Scroll ID")
    vote_parser.add_argument("--vote", required=True, choices=["yes", "no", "abstain"], help="Vote")
    vote_parser.add_argument("--reason", help="Reason for the vote")
    vote_parser.add_argument("--config", default=None, help="Path to configuration file")

    # Status command
    status_parser = subparsers.add_parser("status", help="Get Sela miner status")
    status_parser.add_argument("--config", default=None, help="Path to configuration file")

    args = parser.parse_args()

    if args.command == "setup":
        setup_sela_miner(args)
    elif args.command == "mine":
        mine_block(args)
    elif args.command == "service":
        record_service(args)
    elif args.command == "vote":
        vote(args)
    elif args.command == "status":
        status(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
