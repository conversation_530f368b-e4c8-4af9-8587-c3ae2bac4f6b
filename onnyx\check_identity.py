#!/usr/bin/env python3

"""
Script to check identity storage and database issues.
"""

import os
import sys
import json
import sqlite3
import logging
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.check_identity")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def check_json_identities():
    """Check identities in the JSON file."""
    try:
        json_path = os.path.join("data", "identities.json")
        if os.path.exists(json_path):
            with open(json_path, "r") as f:
                identities = json.load(f)
                logger.info(f"JSON identities count: {len(identities)}")
                logger.info(f"JSON identities: {list(identities.keys())}")
        else:
            logger.warning(f"JSON file not found: {json_path}")
    except Exception as e:
        logger.error(f"Error checking JSON identities: {str(e)}")

def check_db_identities():
    """Check identities in the SQLite database."""
    try:
        db_path = os.path.join("data", "onnyx.db")
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Check if the identities table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='identities'")
            if cursor.fetchone() is None:
                logger.warning("Identities table does not exist in the database")
                return
            
            # Get all identities
            cursor.execute("SELECT * FROM identities")
            identities = cursor.fetchall()
            
            logger.info(f"DB identities count: {len(identities)}")
            for identity in identities:
                logger.info(f"DB identity: {dict(identity)}")
            
            # Check for the specific identity
            cursor.execute("SELECT * FROM identities WHERE name = 'Jedidiah'")
            jedidiah = cursor.fetchone()
            if jedidiah:
                logger.info(f"Found Jedidiah in DB: {dict(jedidiah)}")
            else:
                logger.warning("Jedidiah not found in DB")
            
            conn.close()
        else:
            logger.warning(f"Database file not found: {db_path}")
    except Exception as e:
        logger.error(f"Error checking DB identities: {str(e)}")

def check_identity_registry():
    """Check identities using the IdentityRegistry class."""
    try:
        from src.identity.registry import IdentityRegistry
        registry = IdentityRegistry()
        identities = registry.get_all_identities()
        
        logger.info(f"Registry identities count: {len(identities)}")
        for identity in identities:
            logger.info(f"Registry identity: {identity.identity_id} - {identity.name}")
        
        # Check for the specific identity
        jedidiah = [identity for identity in identities if identity.name == "Jedidiah"]
        if jedidiah:
            logger.info(f"Found Jedidiah in registry: {jedidiah[0].identity_id}")
        else:
            logger.warning("Jedidiah not found in registry")
    except Exception as e:
        logger.error(f"Error checking identity registry: {str(e)}")

def check_db_tables():
    """Check all tables in the database."""
    try:
        db_path = os.path.join("data", "onnyx.db")
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            logger.info(f"DB tables: {[table[0] for table in tables]}")
            
            conn.close()
        else:
            logger.warning(f"Database file not found: {db_path}")
    except Exception as e:
        logger.error(f"Error checking DB tables: {str(e)}")

def check_identity_model():
    """Check the Identity model."""
    try:
        from models.identity import Identity
        
        # Get all identities
        identities = Identity.get_all()
        
        logger.info(f"Model identities count: {len(identities)}")
        for identity in identities:
            logger.info(f"Model identity: {identity.identity_id} - {identity.name}")
        
        # Check for the specific identity
        jedidiah = [identity for identity in identities if identity.name == "Jedidiah"]
        if jedidiah:
            logger.info(f"Found Jedidiah in model: {jedidiah[0].identity_id}")
        else:
            logger.warning("Jedidiah not found in model")
    except Exception as e:
        logger.error(f"Error checking identity model: {str(e)}")

def main():
    """Main entry point."""
    logger.info("Checking identity storage...")
    
    # Check JSON identities
    check_json_identities()
    
    # Check DB identities
    check_db_identities()
    
    # Check identity registry
    check_identity_registry()
    
    # Check DB tables
    check_db_tables()
    
    # Check identity model
    check_identity_model()
    
    logger.info("Identity check complete")

if __name__ == "__main__":
    main()
