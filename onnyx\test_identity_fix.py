#!/usr/bin/env python3

"""
<PERSON>rip<PERSON> to test the identity fix.
"""

import os
import sys
import logging
import time
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.test_identity_fix")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_identity_creation():
    """Test identity creation."""
    try:
        from src.identity.registry import IdentityRegistry
        from src.node.crypto import generate_keys, public_key_to_base64

        # Create the identity registry
        registry = IdentityRegistry()

        # Generate a unique ID for the keys directory
        identity_uuid = str(uuid.uuid4())
        keys_dir = os.path.join("data", "keys", identity_uuid)
        os.makedirs(keys_dir, exist_ok=True)

        # Generate keys
        _, public_key = generate_keys(keys_dir)

        # Convert public key to base64 for storage
        public_key_base64 = public_key_to_base64(public_key)

        # Create metadata
        metadata = {
            'nation': 'Israel',
            'tribe': 'Judah',
            'dob': '01/01/2000',
            'region': 'North America',
            'purpose': 'Testing',
            'soul_seal': 'test_seal',
            'description': 'Test identity created manually',
            'created_at': int(time.time())
        }

        # Register the identity
        identity = registry.register_identity(f"Test Identity {int(time.time())}", public_key_base64, nation='Israel', metadata=metadata)

        logger.info(f"Created identity: {identity.identity_id} - {identity.name}")

        # Verify the identity was created
        all_identities = registry.get_all_identities()
        logger.info(f"Total identities: {len(all_identities)}")

        # Check if the identity is in the list
        found = False
        for id_obj in all_identities:
            if id_obj.identity_id == identity.identity_id:
                found = True
                break

        if found:
            logger.info("Identity found in registry")
        else:
            logger.warning("Identity not found in registry")

        # Check the database directly
        from data.db import db
        db_identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (identity.identity_id,))

        if db_identity:
            logger.info("Identity found in database")
            logger.info(f"Database identity: {db_identity}")
        else:
            logger.warning("Identity not found in database")

        return identity
    except Exception as e:
        logger.error(f"Error creating identity: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def main():
    """Main entry point."""
    logger.info("Testing identity fix...")

    identity = test_identity_creation()

    if identity:
        logger.info(f"Successfully created identity: {identity.identity_id}")
    else:
        logger.error("Failed to create identity")

if __name__ == "__main__":
    main()
