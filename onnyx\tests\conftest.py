"""
Pytest Configuration

This module provides pytest fixtures for the Onnyx tests.
"""

import os
import sys
import pytest
import tempfile
import sqlite3
import logging
import shutil
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data.db import Database, db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger("onnyx.tests.conftest")

@pytest.fixture(scope="session")
def test_db_dir():
    """Create a temporary directory for test databases."""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Return the directory path
    yield temp_dir

    # Clean up
    try:
        shutil.rmtree(temp_dir)
    except Exception as e:
        logger.warning(f"Failed to remove temporary directory: {e}")

@pytest.fixture(scope="function")
def test_db_path(test_db_dir):
    """Create a unique database path for each test."""
    # Create a unique database path
    db_path = os.path.join(test_db_dir, f"test_{os.getpid()}_{id(test_db_path)}.db")

    # Return the path
    yield db_path

    # Clean up
    try:
        if os.path.exists(db_path):
            os.unlink(db_path)
    except Exception as e:
        logger.warning(f"Failed to remove test database: {e}")

@pytest.fixture(scope="function")
def test_db(test_db_path):
    """Create a test database for each test."""
    # Create a test database
    test_db = Database(test_db_path)

    # Create the tables
    conn = test_db.get_connection()

    # Execute the schema file
    schema_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "schemas", "schema.sql")
    with open(schema_path, "r") as f:
        schema = f.read()
        conn.executescript(schema)

    # Commit the changes
    conn.commit()
    conn.close()

    # Set the global database to the test database
    global db
    db.db_path = test_db_path

    # Return the test database
    yield test_db

    # Close any open connections
    try:
        conn = sqlite3.connect(test_db_path)
        conn.close()
    except Exception as e:
        logger.warning(f"Failed to close database connection: {e}")

@pytest.fixture(autouse=True)
def setup_test_db(test_db):
    """Set up the test database for each test."""
    # Return the test database
    return test_db
