#!/usr/bin/env python
# generate_sample_data.py

import json
import os
import random
import time
import uuid
import hashlib

def generate_identity(identity_id=None):
    """Generate a sample identity."""
    if identity_id is None:
        identity_id = f"id_{uuid.uuid4().hex[:8]}"
    
    return {
        "id": identity_id,
        "name": f"User {identity_id[-4:]}",
        "public_key": hashlib.sha256(identity_id.encode()).hexdigest(),
        "reputation": random.randint(1, 100),
        "created_at": int(time.time()) - random.randint(0, 10000000),
        "tokens_created": random.randint(0, 5),
        "metadata": {
            "bio": f"This is a sample identity for {identity_id}",
            "website": f"https://example.com/{identity_id}",
            "twitter": f"@{identity_id}"
        }
    }

def generate_token(token_id=None, creator=None):
    """Generate a sample token."""
    if token_id is None:
        token_id = f"token_{uuid.uuid4().hex[:8]}"
    
    if creator is None:
        creator = f"id_{uuid.uuid4().hex[:8]}"
    
    token_types = ["fungible", "non-fungible", "soulbound", "governance"]
    
    return {
        "id": token_id,
        "name": f"Token {token_id[-4:]}",
        "symbol": f"TKN{token_id[-4:]}",
        "creator": creator,
        "type": random.choice(token_types),
        "supply": random.randint(1, 1000000),
        "max_supply": random.randint(1000000, 10000000),
        "mintable": random.choice([True, False]),
        "burnable": random.choice([True, False]),
        "transferable": random.choice([True, False]),
        "created_at": int(time.time()) - random.randint(0, 10000000),
        "metadata": {
            "description": f"This is a sample token {token_id}",
            "website": f"https://example.com/{token_id}",
            "logo": f"https://example.com/{token_id}.png"
        }
    }

def generate_transaction(tx_id=None, block_index=None):
    """Generate a sample transaction."""
    if tx_id is None:
        tx_id = f"tx_{uuid.uuid4().hex[:8]}"
    
    tx_types = ["transfer", "mint", "burn", "spawn", "identity"]
    tx_type = random.choice(tx_types)
    
    from_id = f"id_{uuid.uuid4().hex[:8]}"
    to_id = f"id_{uuid.uuid4().hex[:8]}"
    token_id = f"token_{uuid.uuid4().hex[:8]}"
    
    tx = {
        "txid": tx_id,
        "type": tx_type,
        "timestamp": int(time.time()) - random.randint(0, 10000000)
    }
    
    if tx_type == "transfer":
        tx.update({
            "from": from_id,
            "to": to_id,
            "token": token_id,
            "amount": random.randint(1, 1000)
        })
    elif tx_type == "mint":
        tx.update({
            "from": from_id,
            "token": token_id,
            "amount": random.randint(1, 1000)
        })
    elif tx_type == "burn":
        tx.update({
            "from": from_id,
            "token": token_id,
            "amount": random.randint(1, 1000)
        })
    elif tx_type == "spawn":
        tx.update({
            "from": from_id,
            "token": token_id,
            "name": f"Token {token_id[-4:]}",
            "symbol": f"TKN{token_id[-4:]}",
            "supply": random.randint(1, 1000000)
        })
    elif tx_type == "identity":
        tx.update({
            "identity": from_id,
            "name": f"User {from_id[-4:]}",
            "public_key": hashlib.sha256(from_id.encode()).hexdigest()
        })
    
    return tx

def generate_block(index=None, previous_hash=None):
    """Generate a sample block."""
    if index is None:
        index = 0
    
    if previous_hash is None:
        previous_hash = "0" * 64
    
    # Generate 1-10 transactions
    transactions = []
    for _ in range(random.randint(1, 10)):
        transactions.append(generate_transaction())
    
    # Create the block
    block = {
        "index": index,
        "previous_hash": previous_hash,
        "timestamp": int(time.time()) - random.randint(0, 10000000),
        "transactions": transactions,
        "nonce": random.randint(0, 1000000),
    }
    
    # Calculate the hash
    block_string = json.dumps(block, sort_keys=True)
    block["hash"] = hashlib.sha256(block_string.encode()).hexdigest()
    
    return block

def generate_blockchain(num_blocks=10):
    """Generate a sample blockchain."""
    blockchain = []
    
    # Generate the genesis block
    genesis_block = generate_block(0, "0" * 64)
    blockchain.append(genesis_block)
    
    # Generate the rest of the blocks
    for i in range(1, num_blocks):
        block = generate_block(i, blockchain[i-1]["hash"])
        blockchain.append(block)
    
    return blockchain

def generate_mempool(num_transactions=5):
    """Generate a sample mempool."""
    mempool = []
    
    # Generate transactions
    for _ in range(num_transactions):
        mempool.append(generate_transaction())
    
    return mempool

def generate_identities(num_identities=10):
    """Generate sample identities."""
    identities = []
    
    # Generate identities
    for _ in range(num_identities):
        identities.append(generate_identity())
    
    return identities

def generate_tokens(num_tokens=10, identities=None):
    """Generate sample tokens."""
    tokens = []
    
    # Use provided identities or generate random ones
    if identities:
        creators = [identity["id"] for identity in identities]
    else:
        creators = [f"id_{uuid.uuid4().hex[:8]}" for _ in range(num_tokens)]
    
    # Generate tokens
    for i in range(num_tokens):
        tokens.append(generate_token(creator=random.choice(creators)))
    
    return tokens

def main():
    """Generate sample data and save it to files."""
    # Create the data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)
    
    # Generate identities
    identities = generate_identities(10)
    with open("data/identities.json", "w") as f:
        json.dump(identities, f, indent=2)
    print(f"Generated {len(identities)} identities")
    
    # Generate tokens
    tokens = generate_tokens(10, identities)
    with open("data/tokens.json", "w") as f:
        json.dump(tokens, f, indent=2)
    print(f"Generated {len(tokens)} tokens")
    
    # Generate blockchain
    blockchain = generate_blockchain(10)
    with open("data/blockchain.json", "w") as f:
        json.dump(blockchain, f, indent=2)
    print(f"Generated blockchain with {len(blockchain)} blocks")
    
    # Generate mempool
    mempool = generate_mempool(5)
    with open("data/mempool.json", "w") as f:
        json.dump(mempool, f, indent=2)
    print(f"Generated mempool with {len(mempool)} transactions")

if __name__ == "__main__":
    main()
