{% extends 'base.html' %}

{% block title %}Onnyx Explorer - Search Results{% endblock %}

{% block content %}
    {% with active_tab='search' %}
    {% include 'explorer_hero.html' %}
    {% include 'explorer_nav.html' %}
    {% endwith %}

    {% if not query %}
        <div class="search-form-large">
            <form action="{{ url_for('search') }}" method="get">
                <input type="text" name="q" placeholder="Search for tokens, identities, or transactions...">
                <button type="submit">Search</button>
            </form>
        </div>
    {% elif results %}
        <div class="search-results">
            {% if results.tokens %}
                <div class="result-section">
                    <h2>Tokens ({{ results.tokens | length }})</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Symbol</th>
                                <th>Creator</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for token in results.tokens %}
                                <tr>
                                    <td>{{ token.name }}</td>
                                    <td>{{ token.symbol }}</td>
                                    <td>
                                        <a href="{{ url_for('identity_detail', identity_id=token.creator_identity) }}">
                                            {{ token.creator_identity }}
                                        </a>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('token_detail', token_id=token.token_id) }}">View Details</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}

            {% if results.identities %}
                <div class="result-section">
                    <h2>Identities ({{ results.identities | length }})</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Identity ID</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for identity in results.identities %}
                                <tr>
                                    <td>{{ identity.name }}</td>
                                    <td>{{ identity.identity_id }}</td>
                                    <td>
                                        <a href="{{ url_for('identity_detail', identity_id=identity.identity_id) }}">View Details</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}

            {% if results.transactions %}
                <div class="result-section">
                    <h2>Transactions ({{ results.transactions | length }})</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Time</th>
                                <th>Transaction ID</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tx in results.transactions %}
                                <tr>
                                    <td>{{ tx.type }}</td>
                                    <td>{{ tx.timestamp | format_timestamp }}</td>
                                    <td>{{ tx.txid }}</td>
                                    <td>
                                        <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">View Details</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        </div>
    {% else %}
        <p>No results found for "{{ query }}".</p>
        <div class="search-form-large">
            <form action="{{ url_for('search') }}" method="get">
                <input type="text" name="q" placeholder="Try another search...">
                <button type="submit">Search</button>
            </form>
        </div>
    {% endif %}
{% endblock %}

{% block head %}
<style>
    .search-form-large {
        margin: 40px 0;
        text-align: center;
    }

    .search-form-large input {
        width: 70%;
        padding: 15px;
        border: none;
        border-radius: 4px;
        background-color: #333;
        color: #eee;
        font-size: 18px;
    }

    .search-form-large button {
        padding: 15px 30px;
        border: none;
        border-radius: 4px;
        background-color: #0f0;
        color: #111;
        font-size: 18px;
        cursor: pointer;
        margin-left: 10px;
    }

    .search-results {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .result-section {
        background-color: #222;
        padding: 20px;
        border-radius: 8px;
    }

    .result-section h2 {
        margin-top: 0;
        border-bottom: 1px solid #444;
        padding-bottom: 10px;
    }
</style>
{% endblock %}
