# Onnyx Persistent DB Layer

## Overview

The Onnyx blockchain uses SQLite to persist all chain data:

- Indexed blocks & transactions
- Full identity and Sela registries
- Token supply & balance tracking
- Reputation & badge systems
- Smart logs for historical analysis

This enables high-performance queries, trusted backups, and sync integrity.

## Database Schema

The Onnyx database schema includes the following tables:

### Blockchain Tables

- **blocks**: Stores block data
- **transactions**: Stores transaction data
- **mempool**: Stores pending transactions

### Identity Tables

- **identities**: Stores identity data
- **identity_badges**: Stores identity badges
- **selas**: Stores Sela data
- **sela_members**: Stores Sela members

### Token Tables

- **tokens**: Stores token data
- **balances**: Stores token balances

### Trust Tables

- **etzem_scores**: Stores Etzem scores
- **zeman_credits**: Stores Zeman credits
- **stakes**: Stores token stakes

### Governance Tables

- **voice_scrolls**: Stores Voice Scrolls
- **voice_scroll_votes**: Stores Voice Scroll votes
- **chain_parameters**: Stores chain parameters

### Analytics Tables

- **event_logs**: Stores event logs
- **validator_rotation**: Stores validator rotation data
- **validator_queue**: Stores validator queue data

## Components

### Database Module

The `Database` class provides a wrapper around SQLite for the Onnyx blockchain. It includes methods for:

- Executing queries
- Inserting, updating, and deleting data
- Backing up the database

### Blockchain Modules

- **DBBlockchain**: Manages the blockchain data
- **DBMempool**: Manages the mempool data

### Identity Modules

- **DBIdentityRegistry**: Manages identities in the Onnyx ecosystem

### Token Modules

- **DBTokenRegistry**: Manages tokens in the Onnyx ecosystem
- **DBTokenLedger**: Manages token balances in the Onnyx ecosystem

### Analytics Modules

- **DBEventLogger**: Logs blockchain events for analytics purposes

### Configuration Modules

- **DBChainParameters**: Manages the chain parameters for the Onnyx blockchain

## Benefits

### Performance

- **Indexed Queries**: Fast lookups by block, transaction, identity, or token
- **Efficient Joins**: Relate data across different tables
- **Pagination**: Easily paginate large result sets

### Data Integrity

- **ACID Transactions**: Ensure data consistency
- **Constraints**: Enforce data relationships
- **Backups**: Easily backup and restore the database

### Scalability

- **Reduced Memory Usage**: Only load the data you need
- **Efficient Storage**: Compact storage format
- **Concurrent Access**: Multiple processes can access the database

## Usage Examples

### Querying Blocks

```python
# Get the latest block
latest_block = db_blockchain.get_latest()

# Get a block by index
block = db_blockchain.get_block(123)

# Get blocks in a range
blocks = db_blockchain.get_blocks(100, 10)  # 10 blocks starting from index 100
```

### Managing Identities

```python
# Register a new identity
identity = db_identity_registry.register_identity(
    "alice",
    "Alice",
    "0x123456789abcdef",
    {"description": "Alice's identity"}
)

# Get an identity
identity = db_identity_registry.get_identity("alice")

# Add a badge to an identity
db_identity_registry.add_badge("alice", "VALIDATOR_ELIGIBLE_BADGE")
```

### Managing Tokens

```python
# Register a new token
token = db_token_registry.register_token(
    "ALICE_TOKEN",
    "Alice Token",
    "ALICE",
    "alice",
    1000,
    "community",
    2,
    {"description": "Alice's community token"}
)

# Get a token
token = db_token_registry.get_token("ALICE_TOKEN")

# Update token supply
db_token_registry.update_token_supply("ALICE_TOKEN", 100)  # Mint 100 tokens
```

### Managing Token Balances

```python
# Credit tokens to an identity
db_token_ledger.credit("alice", "ALICE_TOKEN", 100)

# Transfer tokens
db_token_ledger.transfer("alice", "bob", "ALICE_TOKEN", 50)

# Get a balance
balance = db_token_ledger.get_balance("bob", "ALICE_TOKEN")
```

### Logging Events

```python
# Log a block
db_event_logger.log_block(block, block["transactions"], "alice")

# Get event logs
logs = db_event_logger.get_logs(10)  # Get the 10 most recent logs

# Generate a summary
summary = db_event_logger.generate_summary(7)  # 7-day summary
```

### Managing Chain Parameters

```python
# Get a chain parameter
block_reward = db_chain_parameters.get("block_reward")

# Set a chain parameter
db_chain_parameters.set("block_reward", 8)

# Reset a chain parameter
db_chain_parameters.reset("block_reward")
```

## Migration

The migration process from JSON files to SQLite involves:

1. Creating the database schema
2. Migrating identities from JSON to SQLite
3. Migrating Selas from JSON to SQLite
4. Migrating tokens from JSON to SQLite
5. Migrating balances from JSON to SQLite
6. Migrating blocks from JSON to SQLite
7. Migrating event logs from JSON to SQLite
8. Migrating chain parameters from JSON to SQLite

The `init_db.py` script automates this process.

## Backup and Restore

The `Database` class provides a `backup` method for backing up the database:

```python
from data.db import db

# Backup the database
db.backup("data/onnyx_backup.db")
```

To restore from a backup, simply copy the backup file to the original location:

```bash
cp data/onnyx_backup.db data/onnyx.db
```

## Conclusion

The Persistent DB Layer provides a solid foundation for the Onnyx blockchain, enabling fast queries, data integrity, and scalability. It replaces flat JSON files with a structured database, making it easier to manage and query the blockchain data.
