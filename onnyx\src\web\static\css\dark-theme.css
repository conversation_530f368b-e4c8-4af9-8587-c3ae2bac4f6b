/* Onnyx Dark Theme - Modern, Mobile-First Design */

:root {
    /* Main Colors */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-tertiary: rgba(255, 255, 255, 0.5);

    /* Accent Colors */
    --accent-primary: #3498db;
    --accent-secondary: #2980b9;
    --accent-success: #27ae60;
    --accent-warning: #f39c12;
    --accent-danger: #e74c3c;

    /* UI Elements */
    --card-bg: #1e1e1e;
    --card-border: #333333;
    --input-bg: #2d2d2d;
    --input-border: #444444;
    --button-bg: #3498db;
    --button-hover: #2980b9;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 20px;
    --radius-circle: 50%;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-md: 1rem;
    --text-lg: 1.25rem;
    --text-xl: 1.5rem;
    --text-2xl: 2rem;
    --text-3xl: 2.5rem;
    --text-4xl: 3rem;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

a {
    color: var(--accent-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-secondary);
}

img {
    max-width: 100%;
    height: auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--space-md);
    font-weight: 700;
    line-height: 1.2;
}

h1 {
    font-size: var(--text-3xl);
}

h2 {
    font-size: var(--text-2xl);
}

h3 {
    font-size: var(--text-xl);
}

h4 {
    font-size: var(--text-lg);
}

p {
    margin-bottom: var(--space-md);
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

.section {
    padding: var(--space-xl) 0;
}

/* Header */
.site-header {
    background-color: var(--bg-secondary);
    padding: var(--space-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-md);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 40px;
    width: auto;
    margin-right: var(--space-md);
}

.site-title {
    font-size: var(--text-xl);
    font-weight: 700;
    margin: 0;
}

.nav-list {
    display: flex;
    list-style: none;
}

.nav-link {
    color: var(--text-primary);
    padding: var(--space-sm) var(--space-md);
    margin-left: var(--space-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.nav-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Mobile Navigation */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--text-xl);
    cursor: pointer;
}

@media (max-width: 768px) {
    .mobile-nav-toggle {
        display: block;
    }

    .nav-list {
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        background-color: var(--bg-secondary);
        flex-direction: column;
        padding: var(--space-md);
        transform: translateY(-100%);
        transition: transform var(--transition-normal);
        z-index: 99;
    }

    .nav-list.active {
        transform: translateY(0);
    }

    .nav-link {
        display: block;
        padding: var(--space-md);
        margin: var(--space-xs) 0;
    }
}

/* Hero Section */
.hero {
    background-color: var(--bg-secondary);
    padding: var(--space-xxl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-lg);
    background: linear-gradient(90deg, var(--accent-primary), #9b59b6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
}

.hero-cta {
    display: inline-block;
    background-color: var(--button-bg);
    color: white;
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.hero-cta:hover {
    background-color: var(--button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: 1;
    background: radial-gradient(circle at 50% 50%, var(--accent-primary), transparent 70%);
}

/* Particles Container */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
    border: 1px solid var(--card-border);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    margin-bottom: var(--space-md);
    border-bottom: 1px solid var(--card-border);
    padding-bottom: var(--space-md);
}

.card-title {
    margin-bottom: var(--space-xs);
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.card-body {
    margin-bottom: var(--space-md);
}

.card-footer {
    border-top: 1px solid var(--card-border);
    padding-top: var(--space-md);
    display: flex;
    justify-content: flex-end;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--button-bg);
    color: white;
}

.btn-primary:hover {
    background-color: var(--button-hover);
    color: white;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--card-border);
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-lg);
}

.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-sm);
}

/* Footer */
.site-footer {
    background-color: var(--bg-secondary);
    padding: var(--space-xl) 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 200px;
    margin-bottom: var(--space-lg);
}

.footer-title {
    margin-bottom: var(--space-md);
    font-size: var(--text-lg);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--space-sm);
}

.footer-links a {
    color: var(--text-secondary);
}

.footer-links a:hover {
    color: var(--accent-primary);
}

.footer-bottom {
    border-top: 1px solid var(--card-border);
    padding-top: var(--space-lg);
    text-align: center;
    color: var(--text-tertiary);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes floating {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.fade-in {
    animation: fadeIn var(--transition-normal) forwards;
}

.slide-in-up {
    animation: slideInUp var(--transition-normal) forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

.float-element {
    animation: floating 3s ease-in-out infinite;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Glowing Effect */
.glowing {
    box-shadow: 0 0 10px var(--accent-primary), 0 0 20px var(--accent-primary);
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Protection Grid */
.protection-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
}

@media (min-width: 768px) {
    .protection-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1200px) {
    .protection-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Steps Container */
.steps-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
}

@media (min-width: 768px) {
    .steps-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .steps-container {
        grid-template-columns: repeat(4, 1fr);
    }
}

.step-number {
    position: absolute;
    top: -15px;
    left: 20px;
    width: 30px;
    height: 30px;
    background-color: var(--accent-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    z-index: 1;
}

/* Examples Container */
.examples-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
}

@media (min-width: 768px) {
    .examples-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Nations Grid */
.nations-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    margin-top: var(--space-xl);
}

@media (min-width: 768px) {
    .nations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1200px) {
    .nations-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.nation-card {
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.nation-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.nation-symbol {
    font-size: 2rem;
    margin-left: auto;
}

.nation-father {
    font-weight: 500;
    color: var(--accent-primary);
    margin-bottom: var(--space-sm);
}

.nation-description {
    margin-bottom: var(--space-md);
}

.nation-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

@media (min-width: 992px) {
    .nation-details {
        grid-template-columns: repeat(3, 1fr);
    }
}

.nation-covenant h4,
.nation-economy h4,
.nation-jurisdiction h4,
.nation-tribes h4 {
    color: var(--accent-primary);
    margin-bottom: var(--space-sm);
}

.tribes-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xs);
    list-style: none;
    margin: 0;
    padding: 0;
}

@media (min-width: 576px) {
    .tribes-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

.tribes-list li {
    padding: var(--space-xs) 0;
}

.select-nation-btn {
    width: 100%;
    margin-top: var(--space-md);
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
}

@media (min-width: 768px) {
    .info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .info-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Section Styles */
.section-title {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-md);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: var(--space-xl);
}

.bg-accent {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Responsive */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }
}

@media (max-width: 480px) {
    html {
        font-size: 12px;
    }

    .hero-title {
        font-size: var(--text-2xl);
    }

    .hero-subtitle {
        font-size: var(--text-md);
    }

    .card {
        padding: var(--space-md);
    }
}
