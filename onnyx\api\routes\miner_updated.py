"""
Onnyx Miner Routes

This module provides API routes for mining operations.
"""

import logging
import time
import uuid
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from blockchain.consensus.miner import BlockMiner
from shared.models.block import Block
from shared.models.transaction import Transaction
from shared.models.identity import Identity

# Set up logging
logger = logging.getLogger("onnyx.routes.miner")

# Create router
miner_router = APIRouter()

# Create miner instance
miner = BlockMiner()

@miner_router.post("/mine")
def mine_block(proposer_id: str = Query(..., description="The identity ID of the block proposer")) -> Dict[str, Any]:
    """
    Mine a new block.
    
    Args:
        proposer_id: The identity ID of the block proposer
    
    Returns:
        Information about the mined block
    """
    try:
        logger.info(f"Mining block with proposer: {proposer_id}")
        
        # Check if the proposer exists
        proposer = Identity.get_by_id(proposer_id)
        if not proposer:
            logger.warning(f"Proposer identity {proposer_id} not found")
            raise Exception(f"Proposer identity {proposer_id} not found")
        
        # Mine the block
        block_data = miner.create_block(proposer_id)
        
        # Create the block in the database
        block = Block.create(
            block_hash=block_data["hash"],
            previous_hash=block_data["previous_hash"],
            index=block_data["index"],
            timestamp=block_data["timestamp"],
            transactions=block_data["transactions"],
            nonce=block_data["nonce"],
            difficulty=block_data.get("difficulty", 4),
            miner=proposer_id
        )
        
        # Update transaction statuses
        for tx_data in block_data["transactions"]:
            if isinstance(tx_data, dict) and "tx_id" in tx_data:
                tx = Transaction.get_by_id(tx_data["tx_id"])
                if tx:
                    tx.status = "CONFIRMED"
                    tx.block_hash = block.block_hash
                    tx.block_index = block.index
                    tx.save()
                    logger.info(f"Transaction {tx.tx_id} confirmed in block #{block.index}")
        
        logger.info(f"Block #{block.index} mined with hash {block.block_hash}")
        return {
            "status": "block created",
            "index": block.index,
            "hash": block.block_hash,
            "tx_count": len(block.transactions),
            "timestamp": block.timestamp,
            "miner": block.miner
        }
    except Exception as e:
        logger.error(f"Error mining block: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@miner_router.get("/stats")
def get_mining_stats() -> Dict[str, Any]:
    """
    Get mining statistics.
    
    Returns:
        Mining statistics
    """
    logger.info("Getting mining statistics")
    
    # Get statistics from the database
    total_blocks = len(Block.get_all())
    latest_block = Block.get_latest()
    
    # Get pending transactions
    pending_txs = Transaction.find_by_status("PENDING")
    
    stats = {
        "total_blocks": total_blocks,
        "latest_block": {
            "index": latest_block.index if latest_block else 0,
            "hash": latest_block.block_hash if latest_block else "",
            "timestamp": latest_block.timestamp if latest_block else 0,
            "tx_count": len(latest_block.transactions) if latest_block else 0,
            "miner": latest_block.miner if latest_block else ""
        },
        "pending_transactions": len(pending_txs),
        "mining_difficulty": latest_block.difficulty if latest_block else 4
    }
    
    logger.info(f"Mining statistics: {stats}")
    return stats

@miner_router.get("/latest-blocks/{count}")
def get_latest_blocks(count: int = 10) -> Dict[str, Any]:
    """
    Get the latest blocks.
    
    Args:
        count: The number of blocks to get
    
    Returns:
        The latest blocks
    """
    if count <= 0 or count > 100:
        logger.warning(f"Invalid count: {count}")
        raise HTTPException(status_code=400, detail="Count must be between 1 and 100")
    
    logger.info(f"Getting latest {count} blocks")
    
    # Get the latest blocks from the database
    blocks = Block.get_latest_n(count)
    
    # Format the blocks for the response
    formatted_blocks = []
    for block in blocks:
        formatted_blocks.append({
            "index": block.index,
            "hash": block.block_hash,
            "previous_hash": block.previous_hash,
            "timestamp": block.timestamp,
            "tx_count": len(block.transactions),
            "miner": block.miner,
            "difficulty": block.difficulty,
            "nonce": block.nonce
        })
    
    logger.info(f"Retrieved {len(formatted_blocks)} blocks")
    return {
        "count": len(formatted_blocks),
        "blocks": formatted_blocks
    }

@miner_router.get("/block/{block_id}")
def get_block(block_id: str) -> Dict[str, Any]:
    """
    Get a block by hash or index.
    
    Args:
        block_id: The block hash or index
    
    Returns:
        The block
    """
    logger.info(f"Getting block: {block_id}")
    
    # Try to get the block by hash
    block = Block.get_by_hash(block_id)
    
    # If not found, try to get by index
    if not block:
        try:
            index = int(block_id)
            block = Block.get_by_index(index)
        except ValueError:
            pass
    
    if not block:
        logger.warning(f"Block {block_id} not found")
        raise HTTPException(status_code=404, detail=f"Block {block_id} not found")
    
    # Get transactions for this block
    block_txs = []
    for tx_data in block.transactions:
        if isinstance(tx_data, dict) and "tx_id" in tx_data:
            tx = Transaction.get_by_id(tx_data["tx_id"])
            if tx:
                block_txs.append({
                    "tx_id": tx.tx_id,
                    "tx_type": tx.tx_type,
                    "sender": tx.sender,
                    "recipient": tx.recipient,
                    "token_id": tx.token_id,
                    "amount": tx.amount,
                    "status": tx.status,
                    "timestamp": tx.timestamp,
                    "data": tx.data
                })
            else:
                block_txs.append(tx_data)
        else:
            block_txs.append(tx_data)
    
    logger.info(f"Retrieved block #{block.index} with hash {block.block_hash}")
    return {
        "index": block.index,
        "hash": block.block_hash,
        "previous_hash": block.previous_hash,
        "timestamp": block.timestamp,
        "transactions": block_txs,
        "miner": block.miner,
        "difficulty": block.difficulty,
        "nonce": block.nonce
    }

@miner_router.post("/transactions")
def add_transaction(
    tx_type: str = Body(...),
    sender: str = Body(...),
    recipient: str = Body(...),
    token_id: Optional[str] = Body(None),
    amount: Optional[int] = Body(None),
    data: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Add a transaction to the mempool.
    
    Args:
        tx_type: The transaction type
        sender: The sender's identity ID
        recipient: The recipient's identity ID
        token_id: The token ID (for token transactions)
        amount: The amount (for token transactions)
        data: Additional transaction data
    
    Returns:
        Information about the added transaction
    """
    try:
        logger.info(f"Adding transaction: {tx_type} from {sender} to {recipient}")
        
        # Check if the sender exists
        sender_identity = Identity.get_by_id(sender)
        if not sender_identity:
            logger.warning(f"Sender identity {sender} not found")
            raise Exception(f"Sender identity {sender} not found")
        
        # Check if the recipient exists
        recipient_identity = Identity.get_by_id(recipient)
        if not recipient_identity:
            logger.warning(f"Recipient identity {recipient} not found")
            raise Exception(f"Recipient identity {recipient} not found")
        
        # Generate a transaction ID
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        
        # Create the transaction in the database
        tx = Transaction.create(
            tx_id=tx_id,
            tx_type=tx_type,
            sender=sender,
            recipient=recipient,
            token_id=token_id,
            amount=amount,
            status="PENDING",
            data=data or {}
        )
        
        # Add the transaction to the mempool
        miner.add_transaction({
            "tx_id": tx_id,
            "type": tx_type,
            "sender": sender,
            "recipient": recipient,
            "token_id": token_id,
            "amount": amount,
            "timestamp": tx.timestamp,
            "data": data or {}
        })
        
        logger.info(f"Transaction {tx_id} added to mempool")
        return {
            "status": "transaction added",
            "tx_id": tx_id,
            "tx_type": tx_type,
            "sender": sender,
            "recipient": recipient,
            "token_id": token_id,
            "amount": amount,
            "timestamp": tx.timestamp
        }
    except Exception as e:
        logger.error(f"Error adding transaction: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
