# Onnyx API Routes

This directory contains the API routes for the Onnyx blockchain.

## Overview

The Onnyx API is organized into three main components:

1. **Token Routes**: Endpoints for creating, minting, transferring, and burning tokens
2. **Identity Routes**: Endpoints for creating and managing identities, as well as reputation
3. **Explorer Routes**: Endpoints for exploring the blockchain, searching for tokens, identities, and transactions

All transactions are logged in a transaction ledger, which can be queried through the API.

## Token Routes

### POST /token/forktoken

Create a new token (fork) linked to an identity.

**Request Body**:
```json
{
  "creator_identity": "identity_id",
  "name": "Token Name",
  "symbol": "TKN",
  "token_type": "FORKED",
  "supply": 1000,
  "decimals": 2,
  "mintable": true,
  "transferable": true,
  "metadata": {
    "description": "Token description"
  },
  "message": "forktoken|identity_id|token_name",
  "signature": "signature"
}
```

**Response**:
```json
{
  "status": "success",
  "token_id": "token_id",
  "name": "Token Name",
  "symbol": "TKN",
  "creator_identity": "identity_id",
  "txid": "fork_token_id"
}
```

### POST /token/minttoken

Mint additional tokens.

**Request Body**:
```json
{
  "token_id": "token_id",
  "amount": 500,
  "to_address": "address",
  "caller_identity": "identity_id",
  "message": "minttoken|token_id|500|address",
  "signature": "signature"
}
```

**Response**:
```json
{
  "status": "success",
  "txid": "mint_token_id_500"
}
```

### POST /token/sendtoken

Transfer tokens from one address to another.

**Request Body**:
```json
{
  "token_id": "token_id",
  "from_address": "from_address",
  "to_address": "to_address",
  "amount": 100,
  "memo": "Payment for services",
  "message": "sendtoken|token_id|from_address|to_address|100",
  "signature": "signature"
}
```

**Response**:
```json
{
  "status": "success",
  "txid": "send_token_id_100"
}
```

### POST /token/burntoken

Burn tokens.

**Request Body**:
```json
{
  "token_id": "token_id",
  "from_address": "address",
  "amount": 100,
  "burner_identity": "identity_id",
  "message": "burntoken|token_id|address|100",
  "signature": "signature"
}
```

**Response**:
```json
{
  "status": "success",
  "txid": "burn_token_id_100"
}
```

### GET /token/gettokenbalance

Get the balance of a token for an address.

**Query Parameters**:
- `token_id`: The token ID
- `address`: The address

**Response**:
```json
{
  "balance": 1000,
  "token_metadata": {
    "description": "Token description"
  }
}
```

### GET /token/tokenregistry

Get a list of tokens, optionally filtered by creator or type.

**Query Parameters**:
- `creator_identity`: (optional) Filter by creator identity ID
- `token_type`: (optional) Filter by token type
- `limit`: (optional) Maximum number of results to return
- `offset`: (optional) Number of results to skip

**Response**:
```json
{
  "total": 100,
  "tokens": [
    {
      "token_id": "token_id",
      "name": "Token Name",
      "symbol": "TKN",
      "creator_identity": "identity_id",
      "creation_block": 0,
      "creation_time": 0,
      "type": "FORKED",
      "total_supply": 1000,
      "holders_count": 10,
      "transaction_count": 50,
      "ancestry": {
        "forked_from": null,
        "forks": []
      }
    }
  ]
}
```

### GET /token/transactions

Get a list of transactions, optionally filtered by token ID, identity ID, or transaction ID.

**Query Parameters**:
- `token_id`: (optional) Filter transactions by token ID
- `identity_id`: (optional) Filter transactions by identity ID
- `txid`: (optional) Get a specific transaction by ID
- `limit`: (optional) Maximum number of transactions to return
- `offset`: (optional) Number of transactions to skip

**Response**:
```json
{
  "transactions": [
    {
      "txid": "tx_1234567890abcdef_1631234567",
      "timestamp": 1631234567,
      "type": "forktoken",
      "payload": {
        "token_id": "token_id",
        "name": "Token Name",
        "symbol": "TKN",
        "creator_identity": "identity_id",
        "token_type": "FORKED",
        "supply": 1000,
        "metadata": {
          "description": "Token description"
        },
        "mintable": true,
        "transferable": true
      }
    },
    {
      "txid": "tx_abcdef1234567890_1631234568",
      "timestamp": 1631234568,
      "type": "minttoken",
      "payload": {
        "token_id": "token_id",
        "amount": 500,
        "to_address": "identity_id",
        "caller_identity": "identity_id"
      }
    }
  ]
}
```

## Identity Routes

### POST /identity/createidentity

Create a new identity.

**Request Body**:
```json
{
  "name": "Identity Name",
  "public_key": "public_key",
  "metadata": {
    "type": "individual",
    "description": "Identity description"
  },
  "message": "createidentity|identity_name",
  "signature": "signature"
}
```

**Response**:
```json
{
  "identity_id": "identity_id",
  "name": "Identity Name",
  "public_key": "public_key",
  "txid": "create_identity_id"
}
```

### GET /identity/getidentity

Get information about an identity.

**Query Parameters**:
- `identity_id`: The identity ID

**Response**:
```json
{
  "identity_id": "identity_id",
  "name": "Identity Name",
  "public_key": "public_key",
  "reputation": {
    "score": 100,
    "transactions": 50,
    "minted_tokens": 5,
    "followers": 10
  },
  "metadata": {
    "type": "individual",
    "description": "Identity description"
  },
  "tokens": [
    {
      "token_id": "token_id",
      "name": "Token Name",
      "symbol": "TKN",
      "balance": 1000
    }
  ]
}
```

### POST /identity/updateidentity

Update an identity's metadata.

**Request Body**:
```json
{
  "identity_id": "identity_id",
  "field": "description",
  "value": "New description",
  "message": "updateidentity|identity_id|description|new_description",
  "signature": "signature"
}
```

**Response**:
```json
{
  "identity_id": "identity_id",
  "txid": "update_identity_id"
}
```

### POST /identity/grantreputation

Grant reputation to an identity.

**Request Body**:
```json
{
  "to_identity": "identity_id",
  "reputation_type": "CONTRIBUTOR",
  "value": 10,
  "issuer_identity": "issuer_identity_id",
  "message": "grantreputation|identity_id|CONTRIBUTOR|10",
  "signature": "signature"
}
```

**Response**:
```json
{
  "status": "success",
  "to_identity": "identity_id",
  "reputation_type": "CONTRIBUTOR",
  "value": 10,
  "issuer_identity": "issuer_identity_id",
  "txid": "reputation_identity_id_CONTRIBUTOR"
}
```

### GET /identity/identityscore

Get the composite score and detailed reputation metrics for an identity.

**Query Parameters**:
- `identity_id`: The identity ID

**Response**:
```json
{
  "identity_id": "identity_id",
  "composite_score": 100,
  "rank_percentile": 90,
  "metrics": {
    "transaction_volume": 1000,
    "token_activity": 50,
    "network_trust": 80,
    "longevity_days": 100,
    "badges": [
      {
        "type": "CONTRIBUTOR",
        "value": 10,
        "issuer": "issuer_identity_id",
        "timestamp": 1631234567
      }
    ]
  }
}
```
