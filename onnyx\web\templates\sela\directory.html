{% extends "base.html" %}

{% block title %}Validator Network Directory - ONNYX Platform{% endblock %}

{% block content %}
<div class="directory-content hero-gradient cyber-grid relative" style="padding-top: 5rem; padding-bottom: 3rem;">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="container relative z-10">
        <!-- Enhanced Header Section -->
        <div class="text-center mb-16">
            <!-- ONNYX Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Validator Network</span>
            </h1>
            <p class="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed mb-8">
                Discover verified business validators securing the ONNYX blockchain network
            </p>

            <!-- Enhanced Network Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                <div class="card text-center">
                    <div class="text-3xl font-orbitron font-bold text-cyber-cyan-glow mb-2">{{ network_stats.active_validators or selas|length }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Active Validators</div>
                </div>
                <div class="card text-center">
                    <div class="text-3xl font-orbitron font-bold text-cyber-purple-glow mb-2">{{ network_stats.total_blocks or total_blocks or 0 }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Blocks Validated</div>
                </div>
                <div class="card text-center">
                    <div class="text-3xl font-orbitron font-bold text-cyber-blue-glow mb-2">{{ network_stats.total_transactions or total_transactions or 0 }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Transactions</div>
                </div>
                <div class="card text-center">
                    <div class="text-3xl font-orbitron font-bold text-green-400 mb-2" style="text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);">{{ "%.1f"|format(network_stats.network_uptime or 99.9) }}%</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Network Uptime</div>
                </div>
            </div>

            <!-- Additional Mining Network Stats -->
            {% if network_stats %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div class="card text-center">
                    <div class="text-2xl font-orbitron font-bold text-cyber-cyan-glow mb-2">{{ "%.1f"|format(network_stats.total_mining_power or 0) }}x</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Total Mining Power</div>
                </div>
                <div class="card text-center">
                    <div class="text-2xl font-orbitron font-bold text-cyber-purple-glow mb-2">{{ "%.1f"|format(network_stats.total_rewards or 0) }} ONX</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Rewards Distributed</div>
                </div>
                <div class="card text-center">
                    <div class="text-2xl font-orbitron font-bold text-cyber-blue-glow mb-2">{{ network_stats.mempool_count or 0 }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Pending Transactions</div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Enhanced Search and Filters -->
        <div class="card mb-12">
            <div class="card-header">
                <h3 class="card-title">Search & Filter Validators</h3>
                <p class="card-subtitle">Find validators by name, category, or status</p>
            </div>
            <div class="card-body">
                <div class="flex flex-col lg:flex-row gap-6 items-end">
                    <div class="flex-1">
                        <div class="form-group">
                            <label for="validator-search" class="form-label">Search Validators</label>
                            <div class="relative">
                                <input type="text"
                                       id="validator-search"
                                       placeholder="Search by name, category, or ID..."
                                       class="form-control pl-12">
                                <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-4">
                        <div class="form-group">
                            <label for="category-filter" class="form-label">Category</label>
                            <select class="form-control form-select" id="category-filter">
                                <option value="">All Categories</option>
                                <option value="technology">Technology</option>
                                <option value="finance">Finance</option>
                                <option value="retail">Retail</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="services">Services</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="status-filter" class="form-label">Status</label>
                            <select class="form-control form-select" id="status-filter">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Validators Grid -->
        <div id="validators-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" style="align-items: start !important; grid-auto-rows: min-content !important;">
            {% if selas %}
                {% for sela in selas %}
                <div class="card group data-stream validator-card"
                     data-category="{{ sela.category|lower }}"
                     data-status="{{ sela.status|lower }}"
                     data-name="{{ sela.name|lower }}"
                     style="height: auto !important; min-height: auto !important; max-height: none !important; align-self: start !important;">

                    <!-- Enhanced Validator Header -->
                    <div class="card-header">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="card-title group-hover:text-cyber-cyan-glow transition-colors duration-300">
                                    <a href="{{ url_for('sela.profile', sela_id=sela.sela_id) }}">
                                        {{ sela.name }}
                                    </a>
                                </h3>
                                <p class="card-subtitle uppercase tracking-wider">{{ sela.category }}</p>
                                {% if sela.mining_tier %}
                                <div class="flex items-center gap-2 mt-2">
                                    <span class="badge-info text-xs">{{ sela.mining_tier_display }}</span>
                                    <span class="text-xs text-cyber-cyan-glow font-orbitron font-bold">{{ sela.mining_power_display }}</span>
                                </div>
                                {% endif %}
                            </div>
                            <div class="ml-4 flex flex-col items-end gap-2">
                                {% if sela.status == 'active' %}
                                <span class="badge-success">ACTIVE</span>
                                {% else %}
                                <span class="badge-error">INACTIVE</span>
                                {% endif %}
                                {% if sela.mining_power %}
                                <span class="badge-info text-xs">MINING</span>
                                {% endif %}
                            </div>
                        </div>
                        <p class="text-text-secondary text-sm mt-2">{{ sela.description or "Verified blockchain validator" }}</p>
                    </div>

                    <!-- Enhanced Validator Metrics -->
                    <div class="card-body">
                        <div class="space-y-4">
                            <!-- ONX Balance -->
                            {% if sela.onx_balance is defined %}
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-text-muted">ONX Balance</span>
                                <span class="text-sm font-orbitron font-bold text-cyber-cyan-glow">{{ "%.1f"|format(sela.onx_balance or 0) }} ONX</span>
                            </div>
                            {% endif %}

                            <!-- Mining Power -->
                            {% if sela.mining_power %}
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-text-muted">Mining Power</span>
                                <span class="text-sm font-orbitron font-bold text-cyber-purple-glow">{{ sela.mining_power_display }}</span>
                            </div>
                            {% endif %}

                            <div class="flex items-center justify-between">
                                <span class="text-sm text-text-muted">Owner</span>
                                <span class="text-sm text-text-secondary">{{ sela.owner_name }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-text-muted">Trust Score</span>
                                <span class="text-sm font-orbitron font-bold text-cyber-purple-glow">{{ sela.trust_score or 'N/A' }}</span>
                            </div>

                            <!-- Mining Stats Grid -->
                            {% if sela.blocks_mined is defined or sela.total_rewards is defined %}
                            <div class="grid grid-cols-2 gap-4 pt-2 border-t border-surface-secondary">
                                <div class="text-center">
                                    <div class="text-lg font-orbitron font-bold text-cyber-cyan-glow">{{ sela.blocks_mined or 0 }}</div>
                                    <div class="text-xs text-text-tertiary uppercase tracking-wider">Blocks Mined</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-orbitron font-bold text-cyber-purple-glow">{{ "%.1f"|format(sela.total_rewards or 0) }}</div>
                                    <div class="text-xs text-text-tertiary uppercase tracking-wider">ONX Earned</div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Enhanced Action Buttons -->
                    <div class="card-footer">
                        <a href="{{ url_for('sela.profile', sela_id=sela.sela_id) }}" class="btn btn-primary flex-1">
                            View Profile
                        </a>
                        <button class="btn btn-icon btn-secondary" onclick="copyValidatorId('{{ sela.sela_id }}')">
                            📋
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- Enhanced Empty State -->
                <div class="col-span-full text-center py-20">
                    <div class="card max-w-md mx-auto">
                        <div class="card-header text-center">
                            <div class="w-24 h-24 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex items-center justify-center mx-auto mb-6">
                                <svg class="w-12 h-12 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                </svg>
                            </div>
                            <h3 class="card-title">No Validators Found</h3>
                            <p class="card-subtitle">Be the first to register your business as a verified validator</p>
                        </div>
                        <div class="card-body text-center">
                            <p class="text-text-muted mb-6">
                                Join the ONNYX network and help secure the future of trustworthy commerce.
                            </p>
                        </div>
                        <div class="card-footer justify-center">
                            <a href="{{ url_for('auth.register_sela') }}" class="btn btn-primary">
                                <span class="text-xl">🏢</span>
                                <span>Register Validator</span>
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Enhanced Load More Button -->
        {% if selas and selas|length >= 12 %}
        <div class="text-center mt-16">
            <button id="load-more" class="btn btn-secondary btn-lg">
                <span>Load More Validators</span>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('validator-search');
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const validatorCards = document.querySelectorAll('.validator-card');

    function filterValidators() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value.toLowerCase();
        const selectedStatus = statusFilter.value.toLowerCase();

        validatorCards.forEach(card => {
            const name = card.dataset.name;
            const category = card.dataset.category;
            const status = card.dataset.status;

            const matchesSearch = !searchTerm || name.includes(searchTerm);
            const matchesCategory = !selectedCategory || category === selectedCategory;
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesCategory && matchesStatus) {
                card.style.display = 'block';
                card.classList.add('fade-in');
            } else {
                card.style.display = 'none';
            }
        });
    }

    searchInput.addEventListener('input', filterValidators);
    categoryFilter.addEventListener('change', filterValidators);
    statusFilter.addEventListener('change', filterValidators);
});

// Copy validator ID function
function copyValidatorId(validatorId) {
    navigator.clipboard.writeText(validatorId).then(() => {
        Onnyx.utils.showNotification('Validator ID copied to clipboard!', 'success');
    }).catch(() => {
        Onnyx.utils.showNotification('Failed to copy validator ID', 'error');
    });
}
</script>
{% endblock %}
