#!/usr/bin/env python3
"""
ONNYX Genesis Identity Creation

Creates the Genesis Identity with Platform Founder role for ONNYX production launch.
This is the foundational identity that will bootstrap the real-world platform.
"""

import os
import sys
import uuid
import hashlib
import secrets
from datetime import datetime
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

class GenesisIdentityCreator:
    """Creates the Genesis Identity for ONNYX platform launch."""

    def __init__(self):
        self.genesis_role = "Platform Founder"
        self.genesis_permissions = [
            "create_validators",
            "approve_validators",
            "manage_mining_tiers",
            "platform_administration",
            "genesis_operations",
            "validator_oversight",
            "network_governance"
        ]

    def generate_secure_credentials(self):
        """Generate secure credentials for the Genesis Identity."""
        # Generate a cryptographically secure private key
        private_key = secrets.token_hex(32)  # 256-bit key

        # Generate public key from private key (simplified for demo)
        public_key = hashlib.sha256(private_key.encode()).hexdigest()

        # Generate identity ID
        identity_id = str(uuid.uuid4())

        return {
            'identity_id': identity_id,
            'private_key': private_key,
            'public_key': public_key
        }

    def create_genesis_identity(self, name, email, organization=None):
        """Create the Genesis Identity with Platform Founder role."""
        print("🌟 CREATING ONNYX GENESIS IDENTITY")
        print("=" * 50)

        try:
            # Check if Genesis Identity already exists
            existing_genesis = db.query_one("""
                SELECT * FROM identities
                WHERE role = 'Platform Founder' OR email = ?
            """, (email,))

            if existing_genesis:
                print("⚠️  Genesis Identity already exists!")
                print(f"   Name: {existing_genesis['name']}")
                print(f"   Email: {existing_genesis['email']}")
                print(f"   Role: {existing_genesis['role']}")
                print(f"   Identity ID: {existing_genesis['identity_id']}")
                return existing_genesis['identity_id']

            # Generate secure credentials
            credentials = self.generate_secure_credentials()

            # Create Genesis Identity data
            current_time = datetime.now().isoformat()
            genesis_data = {
                'identity_id': credentials['identity_id'],
                'name': name,
                'email': email,
                'role': self.genesis_role,
                'status': 'active',
                'created_at': current_time,
                'updated_at': current_time,
                'public_key': credentials['public_key'],
                'nation_id': 'ONNYX',  # Platform nation
                'metadata': f'{{"organization": "{organization or "ONNYX Platform"}", "genesis": true, "permissions": {self.genesis_permissions}}}'
            }

            # Insert Genesis Identity
            db.insert('identities', genesis_data)

            # Save credentials securely
            self.save_genesis_credentials(credentials, name)

            print("✅ Genesis Identity created successfully!")
            print(f"   Name: {name}")
            print(f"   Email: {email}")
            print(f"   Role: {self.genesis_role}")
            print(f"   Organization: {organization or 'ONNYX Platform'}")
            print(f"   Identity ID: {credentials['identity_id']}")
            print(f"   Public Key: {credentials['public_key'][:16]}...")

            # Create initial transaction for Genesis Identity
            self.create_genesis_transaction(credentials['identity_id'])

            # Log the Genesis creation event
            self.log_genesis_event(credentials['identity_id'], name)

            return credentials['identity_id']

        except Exception as e:
            print(f"❌ Error creating Genesis Identity: {e}")
            raise

    def save_genesis_credentials(self, credentials, name):
        """Save Genesis credentials securely."""
        try:
            # Create credentials directory
            creds_dir = Path("credentials")
            creds_dir.mkdir(exist_ok=True)

            # Save credentials to secure file
            cred_file = creds_dir / f"genesis_{name.lower().replace(' ', '_')}_credentials.txt"

            with open(cred_file, 'w') as f:
                f.write("ONNYX GENESIS IDENTITY CREDENTIALS\n")
                f.write("=" * 40 + "\n")
                f.write(f"Name: {name}\n")
                f.write(f"Role: {self.genesis_role}\n")
                f.write(f"Identity ID: {credentials['identity_id']}\n")
                f.write(f"Public Key: {credentials['public_key']}\n")
                f.write(f"Private Key: {credentials['private_key']}\n")
                f.write(f"Created: {datetime.now().isoformat()}\n")
                f.write("\n")
                f.write("SECURITY WARNING:\n")
                f.write("- Keep this file secure and private\n")
                f.write("- Do not share the private key\n")
                f.write("- Back up these credentials safely\n")
                f.write("- This identity has platform administration privileges\n")

            print(f"🔐 Credentials saved to: {cred_file}")

        except Exception as e:
            print(f"⚠️  Warning: Could not save credentials file: {e}")

    def create_genesis_transaction(self, identity_id):
        """Create the Genesis transaction to bootstrap the blockchain."""
        try:
            genesis_tx_data = {
                'tx_id': str(uuid.uuid4()),
                'op': 'GENESIS_IDENTITY_CREATION',
                'sender': identity_id,
                'data': f'{{"action": "create_genesis_identity", "role": "{self.genesis_role}", "timestamp": "{datetime.now().isoformat()}"}}',
                'status': 'confirmed',
                'created_at': datetime.now().isoformat(),
                'block_hash': None  # Will be assigned when mined
            }

            db.insert('transactions', genesis_tx_data)
            print(f"✅ Genesis transaction created: {genesis_tx_data['tx_id']}")

        except Exception as e:
            print(f"⚠️  Warning: Could not create Genesis transaction: {e}")

    def log_genesis_event(self, identity_id, name):
        """Log the Genesis Identity creation event."""
        try:
            # Check if event_log table exists
            if db.table_exists('event_log'):
                event_data = {
                    'event_id': str(uuid.uuid4()),
                    'event_type': 'GENESIS_IDENTITY_CREATED',
                    'identity_id': identity_id,
                    'data': f'{{"name": "{name}", "role": "{self.genesis_role}", "genesis": true}}',
                    'timestamp': datetime.now().isoformat()
                }

                db.insert('event_log', event_data)
                print("✅ Genesis event logged")

        except Exception as e:
            print(f"⚠️  Warning: Could not log Genesis event: {e}")

    def verify_genesis_identity(self, identity_id):
        """Verify the Genesis Identity was created correctly."""
        try:
            genesis = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))

            if not genesis:
                return False, "Genesis Identity not found"

            if genesis['role'] != self.genesis_role:
                return False, f"Invalid role: {genesis['role']}"

            if genesis['status'] != 'active':
                return False, f"Invalid status: {genesis['status']}"

            return True, "Genesis Identity verified successfully"

        except Exception as e:
            return False, f"Verification error: {e}"

    def show_genesis_status(self):
        """Show the current Genesis Identity status."""
        try:
            genesis = db.query_one("SELECT * FROM identities WHERE role = ?", (self.genesis_role,))

            if not genesis:
                print("❌ No Genesis Identity found")
                return False

            print("\n🌟 GENESIS IDENTITY STATUS")
            print("=" * 30)
            print(f"Name: {genesis['name']}")
            print(f"Email: {genesis['email']}")
            print(f"Role: {genesis['role']}")
            print(f"Status: {genesis['status']}")
            print(f"Identity ID: {genesis['identity_id']}")
            print(f"Created: {genesis['created_at']}")

            # Check for associated transactions
            tx_count = db.query_one("""
                SELECT COUNT(*) as count FROM transactions
                WHERE sender = ?
            """, (genesis['identity_id'],))['count']

            print(f"Transactions: {tx_count}")

            return True

        except Exception as e:
            print(f"❌ Error checking Genesis status: {e}")
            return False

def main():
    """Main entry point for Genesis Identity creation."""
    import argparse

    parser = argparse.ArgumentParser(description="Create ONNYX Genesis Identity")
    parser.add_argument('--name', required=True, help='Full name of the Platform Founder')
    parser.add_argument('--email', required=True, help='Email address')
    parser.add_argument('--organization', help='Organization name (optional)')
    parser.add_argument('--status', action='store_true', help='Show Genesis Identity status')

    args = parser.parse_args()

    creator = GenesisIdentityCreator()

    if args.status:
        creator.show_genesis_status()
        return 0

    try:
        # Create Genesis Identity
        identity_id = creator.create_genesis_identity(
            name=args.name,
            email=args.email,
            organization=args.organization
        )

        # Verify creation
        success, message = creator.verify_genesis_identity(identity_id)
        if success:
            print(f"\n✅ {message}")
            print("\n🚀 GENESIS IDENTITY READY FOR PRODUCTION LAUNCH")
            print("   The Platform Founder identity is now active and ready to:")
            print("   - Register the first Sela validators")
            print("   - Approve validator mining tiers")
            print("   - Oversee platform operations")
            print("   - Manage network governance")
        else:
            print(f"\n❌ Verification failed: {message}")
            return 1

        return 0

    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
