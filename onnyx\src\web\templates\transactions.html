{% extends 'base.html' %}

{% block title %}Onnyx Explorer - {{ title }}{% endblock %}

{% block content %}
    {% with active_tab='transactions' %}
    {% include 'explorer_hero.html' %}
    {% include 'explorer_nav.html' %}
    {% endwith %}

    {% if transactions %}
        <table>
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Time</th>
                    <th>ID</th>
                    <th>Details</th>
                </tr>
            </thead>
            <tbody>
                {% for tx in transactions %}
                    <tr>
                        <td>
                            {% if tx.op is defined %}
                                {{ tx.op }}
                            {% else %}
                                {{ tx.type }}
                            {% endif %}
                        </td>
                        <td>
                            {% if tx.timestamp is defined %}
                                {{ tx.timestamp | format_timestamp }}
                            {% else %}
                                {{ tx.created_at | format_timestamp }}
                            {% endif %}
                        </td>
                        <td>
                            {% if mempool %}
                                <a href="{{ url_for('transaction_detail', txid=tx.id) }}">{{ tx.id[:10] }}...</a>
                            {% elif tx.tx_id is defined %}
                                <a href="{{ url_for('transaction_detail', txid=tx.tx_id) }}">{{ tx.tx_id[:10] }}...</a>
                            {% else %}
                                <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a>
                            {% endif %}
                        </td>
                        <td>
                            {% if tx.op is defined and tx.op == 'CREATE_IDENTITY' %}
                                {% if tx.data is defined and tx.data is mapping %}
                                    Created identity <strong>{{ tx.data.name }}</strong> from nation {{ tx.data.nation }}
                                {% elif tx.data is defined and tx.data is string %}
                                    {% set data = tx.data|tojson|fromjson %}
                                    Created identity <strong>{{ data.name }}</strong> from nation {{ data.nation }}
                                {% else %}
                                    Created identity
                                {% endif %}
                            {% elif tx.type == 'forktoken' %}
                                Created token <strong>{{ tx.payload.name }}</strong> ({{ tx.payload.symbol }})
                            {% elif tx.type == 'minttoken' %}
                                Minted {{ tx.payload.amount }} tokens to
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">{{ tx.payload.to_address[:10] }}...</a>
                            {% elif tx.type == 'sendtoken' %}
                                Sent {{ tx.payload.amount }} tokens from
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">{{ tx.payload.from_address[:10] }}...</a> to
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">{{ tx.payload.to_address[:10] }}...</a>
                            {% elif tx.type == 'burntoken' %}
                                Burned {{ tx.payload.amount }} tokens from
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">{{ tx.payload.from_address[:10] }}...</a>
                            {% elif tx.type == 'createidentity' %}
                                Created identity <strong>{{ tx.payload.name }}</strong>
                            {% elif tx.type == 'updateidentity' %}
                                Updated identity
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.identity_id) }}">{{ tx.payload.identity_id[:10] }}...</a>
                            {% elif tx.type == 'grantreputation' %}
                                Granted {{ tx.payload.value }} {{ tx.payload.reputation_type }} reputation to
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_identity) }}">{{ tx.payload.to_identity[:10] }}...</a>
                            {% else %}
                                {% if tx.tx_id is defined %}
                                    <a href="{{ url_for('transaction_detail', txid=tx.tx_id) }}">View details</a>
                                {% elif mempool %}
                                    <a href="{{ url_for('transaction_detail', txid=tx.id) }}">View details</a>
                                {% else %}
                                    <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">View details</a>
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="pagination">
            {% if offset > 0 %}
                <a href="{{ url_for(request.endpoint, limit=limit, offset=max(0, offset - limit)) }}">⬅ Previous</a>
            {% endif %}

            <span>Showing {{ offset + 1 }} to {{ min(offset + limit, total) }} of {{ total }}</span>

            {% if offset + limit < total %}
                <a href="{{ url_for(request.endpoint, limit=limit, offset=offset + limit) }}">Next ➡</a>
            {% endif %}
        </div>
    {% else %}
        <p>No transactions found.</p>
    {% endif %}
{% endblock %}
