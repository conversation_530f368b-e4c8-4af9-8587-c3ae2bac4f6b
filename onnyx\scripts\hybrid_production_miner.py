#!/usr/bin/env python3
"""
ONNYX Hybrid Production Miner

Enhanced blockchain miner implementing Proof-of-Trust + Performance Boost Hybrid Mining.
Supports tiered mining with validator quality rewards and official miner authentication.
"""

import os
import sys
import json
import time
import hashlib
import logging
import toml
from datetime import datetime
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.hybrid_miner")

class HybridMiner:
    """Proof-of-Trust + Performance Boost Hybrid Mining System."""

    def __init__(self, sela_id=None):
        self.sela_id = sela_id or "ONNYX_GENESIS_MINER"
        self.mining_interval = 10  # seconds
        self.running = False
        
        # Load miner configuration
        self.config = self.load_miner_config()
        
        # Initialize mining parameters
        self.is_official_miner = self.config.get('miner', {}).get('is_official_miner', False)
        self.mining_tier = self.determine_mining_tier()
        self.mining_power = self.get_mining_power()
        self.base_reward = 1.0
        
        logger.info(f"🚀 Hybrid Miner Initialized")
        logger.info(f"   Sela ID: {self.sela_id}")
        logger.info(f"   Official Miner: {self.is_official_miner}")
        logger.info(f"   Mining Tier: {self.mining_tier}")
        logger.info(f"   Mining Power: {self.mining_power}x")

    def load_miner_config(self):
        """Load ONNYX miner configuration."""
        config_path = Path("onnyx_miner_config.toml")
        
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = toml.load(f)
                logger.info("✅ Official ONNYX miner configuration loaded")
                return config
            except Exception as e:
                logger.warning(f"⚠️  Failed to load miner config: {e}")
        else:
            logger.info("📋 Using basic miner configuration (no official config found)")
        
        # Default configuration for basic miners
        return {
            'miner': {
                'is_official_miner': False,
                'miner_tier': 'basic',
                'base_mining_power': 1
            }
        }

    def determine_mining_tier(self):
        """Determine the mining tier based on configuration and validation."""
        if not self.is_official_miner:
            return "basic"
        
        # Verify official miner signature
        config_tier = self.config.get('miner', {}).get('miner_tier', 'basic')
        signature = self.config.get('miner', {}).get('miner_signature', '')
        
        if self.verify_miner_signature(signature):
            return config_tier
        else:
            logger.warning("⚠️  Invalid miner signature, defaulting to basic tier")
            return "basic"

    def verify_miner_signature(self, signature):
        """Verify the official miner signature."""
        expected_signature = "ONNYX_OFFICIAL_MINER_v1.0.0"
        return signature == expected_signature

    def get_mining_power(self):
        """Get mining power multiplier based on tier and validator status."""
        # Check if this is a registered validator
        validator = self.get_validator_info()
        
        if validator:
            # Use validator's mining power from database
            return validator.get('mining_power', 1)
        
        # Default mining power based on tier
        tier_power = {
            'basic': 1,
            'optimized': 2,
            'pro': 5
        }
        
        return tier_power.get(self.mining_tier, 1)

    def get_validator_info(self):
        """Get validator information from database."""
        try:
            validator = db.query_one("""
                SELECT * FROM selas 
                WHERE sela_id = ? AND status = 'active'
            """, (self.sela_id,))
            return validator
        except Exception as e:
            logger.debug(f"No validator found for {self.sela_id}: {e}")
            return None

    def update_mining_activity(self):
        """Update last mining activity for the validator."""
        try:
            if self.get_validator_info():
                db.update(
                    'selas',
                    {'last_mining_activity': datetime.now().isoformat()},
                    'sela_id = ?',
                    (self.sela_id,)
                )
        except Exception as e:
            logger.debug(f"Failed to update mining activity: {e}")

    def calculate_reward(self, base_reward=None):
        """Calculate mining reward based on tier and performance."""
        if base_reward is None:
            base_reward = self.base_reward
        
        # Apply mining power multiplier
        final_reward = base_reward * self.mining_power
        
        # Performance bonus for official miners
        if self.is_official_miner and self.mining_tier in ['optimized', 'pro']:
            performance_bonus = 0.1  # 10% bonus
            final_reward *= (1 + performance_bonus)
        
        return round(final_reward, 6)

    def record_mining_reward(self, block_height, reward):
        """Record mining reward in the database."""
        try:
            reward_data = {
                'sela_id': self.sela_id,
                'block_height': block_height,
                'base_reward': self.base_reward,
                'mining_power': self.mining_power,
                'final_reward': reward,
                'mining_tier': self.mining_tier,
                'timestamp': datetime.now().isoformat()
            }
            
            db.insert('mining_rewards', reward_data)
            
            # Update validator's total rewards and blocks mined
            validator = self.get_validator_info()
            if validator:
                new_total_rewards = validator.get('mining_rewards_earned', 0) + reward
                new_blocks_mined = validator.get('blocks_mined', 0) + 1
                
                db.update(
                    'selas',
                    {
                        'mining_rewards_earned': new_total_rewards,
                        'blocks_mined': new_blocks_mined
                    },
                    'sela_id = ?',
                    (self.sela_id,)
                )
                
                logger.info(f"💰 Reward: {reward} (Total: {new_total_rewards}, Blocks: {new_blocks_mined})")
            
        except Exception as e:
            logger.error(f"Failed to record mining reward: {e}")

    def get_latest_block(self):
        """Get the latest block from the database."""
        try:
            latest = db.query_one("""
                SELECT * FROM blocks
                ORDER BY block_height DESC
                LIMIT 1
            """)
            return latest
        except Exception as e:
            logger.debug(f"No blocks found or error: {e}")
            return None

    def get_pending_transactions(self):
        """Get pending transactions from the database."""
        try:
            pending = db.query("""
                SELECT * FROM transactions
                WHERE status = 'pending'
                ORDER BY created_at ASC
                LIMIT 10
            """)
            return pending
        except Exception as e:
            logger.debug(f"No pending transactions or error: {e}")
            return []

    def calculate_hash(self, height, timestamp, previous_hash, transactions, miner, nonce=0):
        """Calculate block hash."""
        data = f"{height}{timestamp}{previous_hash}{json.dumps(transactions, sort_keys=True)}{miner}{nonce}"
        return hashlib.sha256(data.encode()).hexdigest()

    def create_genesis_block(self):
        """Create the genesis block if none exists."""
        try:
            timestamp = int(time.time())
            transactions_json = json.dumps([])
            merkle_root = hashlib.sha256(transactions_json.encode()).hexdigest()
            genesis_hash = self.calculate_hash(0, timestamp, "0", [], "GENESIS")

            block_data = {
                'block_hash': genesis_hash,
                'block_height': 0,
                'previous_hash': '0',
                'timestamp': timestamp,
                'miner': 'GENESIS',
                'transactions': transactions_json,
                'merkle_root': merkle_root,
                'nonce': 0,
                'difficulty': 1,
                'created_at': timestamp
            }

            db.insert('blocks', block_data)
            logger.info(f"✅ Genesis block created: {genesis_hash}")
            return block_data

        except Exception as e:
            logger.error(f"❌ Failed to create genesis block: {e}")
            raise

    def mine_block(self):
        """Mine a new block with hybrid mining rewards."""
        try:
            # Update mining activity
            self.update_mining_activity()
            
            # Get latest block
            latest_block = self.get_latest_block()

            # Create genesis if no blocks exist
            if not latest_block:
                return self.create_genesis_block()

            # Get pending transactions
            pending_txs = self.get_pending_transactions()

            # Prepare new block
            new_height = latest_block['block_height'] + 1
            timestamp = int(time.time())
            previous_hash = latest_block['block_hash']

            # Create transaction list for block
            tx_list = []
            for tx in pending_txs:
                tx_list.append({
                    'tx_id': tx['tx_id'],
                    'op': tx['op'],
                    'sender': tx['sender']
                })

            # Create transactions JSON and merkle root
            transactions_json = json.dumps(tx_list)
            merkle_root = hashlib.sha256(transactions_json.encode()).hexdigest()

            # Calculate block hash
            block_hash = self.calculate_hash(
                new_height,
                timestamp,
                previous_hash,
                tx_list,
                self.sela_id
            )

            # Create block data
            block_data = {
                'block_hash': block_hash,
                'block_height': new_height,
                'previous_hash': previous_hash,
                'timestamp': timestamp,
                'miner': self.sela_id,
                'transactions': transactions_json,
                'merkle_root': merkle_root,
                'nonce': 0,
                'difficulty': 1,
                'created_at': timestamp
            }

            # Insert block into database
            db.insert('blocks', block_data)

            # Update transaction statuses
            for tx in pending_txs:
                db.update(
                    'transactions',
                    {'status': 'confirmed', 'block_hash': block_hash},
                    'tx_id = ?',
                    (tx['tx_id'],)
                )

            # Calculate and record mining reward
            reward = self.calculate_reward()
            self.record_mining_reward(new_height, reward)

            logger.info(f"⛏️  Block #{new_height} mined: {block_hash[:16]}... ({len(pending_txs)} txs) [Tier: {self.mining_tier}, Power: {self.mining_power}x]")
            return block_data

        except Exception as e:
            logger.error(f"❌ Failed to mine block: {e}")
            raise

    def start_mining(self):
        """Start the hybrid mining loop."""
        logger.info("🚀 ONNYX HYBRID PRODUCTION MINER STARTING")
        logger.info(f"⚙️  Mining interval: {self.mining_interval} seconds")
        logger.info(f"🏷️  Miner ID: {self.sela_id}")
        logger.info(f"🎯 Mining Tier: {self.mining_tier}")
        logger.info(f"⚡ Mining Power: {self.mining_power}x multiplier")
        logger.info(f"🏆 Official Miner: {'Yes' if self.is_official_miner else 'No'}")
        logger.info("=" * 60)

        self.running = True

        try:
            while self.running:
                try:
                    # Mine a new block
                    block = self.mine_block()

                    # Update blockchain.json cache
                    self.update_blockchain_cache()

                    # Wait for next mining cycle
                    time.sleep(self.mining_interval)

                except KeyboardInterrupt:
                    logger.info("⏹️  Mining stopped by user")
                    break
                except Exception as e:
                    logger.error(f"❌ Mining error: {e}")
                    time.sleep(5)  # Wait before retrying

        finally:
            self.running = False
            logger.info("🛑 Hybrid mining stopped")

    def update_blockchain_cache(self):
        """Update the blockchain.json cache file."""
        try:
            # Get all blocks
            blocks = db.query("""
                SELECT * FROM blocks
                ORDER BY block_height ASC
            """)

            # Write to cache file
            with open('blockchain.json', 'w') as f:
                json.dump(blocks, f, indent=2)

        except Exception as e:
            logger.debug(f"Failed to update blockchain cache: {e}")

    def get_stats(self):
        """Get hybrid mining statistics."""
        try:
            stats = {
                'total_blocks': db.query_one("SELECT COUNT(*) as count FROM blocks")['count'],
                'total_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions")['count'],
                'pending_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'pending'")['count'],
                'confirmed_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'confirmed'")['count'],
                'mining_tier': self.mining_tier,
                'mining_power': self.mining_power,
                'is_official_miner': self.is_official_miner
            }
            
            # Add validator-specific stats
            validator = self.get_validator_info()
            if validator:
                stats.update({
                    'total_rewards_earned': validator.get('mining_rewards_earned', 0),
                    'blocks_mined': validator.get('blocks_mined', 0),
                    'last_mining_activity': validator.get('last_mining_activity', 'Never')
                })
            
            return stats
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}

def main():
    """Main entry point."""
    try:
        # Allow specifying sela_id as command line argument
        sela_id = sys.argv[1] if len(sys.argv) > 1 else None
        
        miner = HybridMiner(sela_id=sela_id)

        # Show initial stats
        stats = miner.get_stats()
        logger.info(f"📊 Initial Stats:")
        logger.info(f"   Blocks: {stats['total_blocks']}")
        logger.info(f"   Transactions: {stats['total_transactions']}")
        logger.info(f"   Mining Tier: {stats['mining_tier']}")
        logger.info(f"   Mining Power: {stats['mining_power']}x")
        if 'total_rewards_earned' in stats:
            logger.info(f"   Total Rewards: {stats['total_rewards_earned']}")
            logger.info(f"   Blocks Mined: {stats['blocks_mined']}")

        # Start mining
        miner.start_mining()

    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
