# src/harashim/registry.py

import time
from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.identity.registry import IdentityRegistry

class HarashimRegistry:
    """
    Registry for managing Harashim roles.
    """
    
    def __init__(self, identity_registry: Optional[IdentityRegistry] = None):
        """
        Initialize the HarashimRegistry.
        
        Args:
            identity_registry: The identity registry
        """
        self.identities = identity_registry or IdentityRegistry()
        self.db = db_manager.get_connection()
    
    def register_harash(self, identity_id: str, affiliated_sela: Optional[str] = None) -> Dict[str, Any]:
        """
        Register an identity as a Harash.
        
        Args:
            identity_id: The identity ID
            affiliated_sela: The affiliated Sela ID (optional)
        
        Returns:
            The newly created Harash role
        
        Raises:
            Exception: If the identity does not exist or is already a Harash
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the identity is already a Harash
        existing_role = self.get_harash_role(identity_id)
        if existing_role:
            raise Exception(f"Identity with ID '{identity_id}' is already a Harash")
        
        # Create the Harash role
        harash_role = {
            "identity_id": identity_id,
            "role": "Harash",
            "affiliated_sela": affiliated_sela,
            "active_contracts": 0,
            "completed_contracts": 0,
            "created_at": int(time.time())
        }
        
        # Save to database
        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT INTO harashim_roles
            (identity_id, role, affiliated_sela, active_contracts, completed_contracts, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            (
                harash_role["identity_id"],
                harash_role["role"],
                harash_role["affiliated_sela"],
                harash_role["active_contracts"],
                harash_role["completed_contracts"],
                harash_role["created_at"]
            )
        )
        self.db.commit()
        
        # Add the Harash badge to the identity
        try:
            self.identities.add_badge(identity_id, "HARASH")
        except Exception:
            # Badge already exists
            pass
        
        return harash_role
    
    def get_harash_role(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the Harash role for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The Harash role or None if the identity is not a Harash
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM harashim_roles WHERE identity_id = ?",
            (identity_id,)
        )
        row = cursor.fetchone()
        
        if not row:
            return None
        
        return {
            "identity_id": row["identity_id"],
            "role": row["role"],
            "affiliated_sela": row["affiliated_sela"],
            "active_contracts": row["active_contracts"],
            "completed_contracts": row["completed_contracts"],
            "created_at": row["created_at"]
        }
    
    def update_harash_role(self, identity_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update the Harash role for an identity.
        
        Args:
            identity_id: The identity ID
            updates: The updates to apply
        
        Returns:
            The updated Harash role or None if the identity is not a Harash
        
        Raises:
            Exception: If the identity does not exist
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the identity is a Harash
        existing_role = self.get_harash_role(identity_id)
        if not existing_role:
            return None
        
        # Update the Harash role
        cursor = self.db.cursor()
        
        # Build the SET clause for the SQL query
        set_clause = ", ".join([f"{key} = ?" for key in updates.keys()])
        values = list(updates.values())
        values.append(identity_id)
        
        cursor.execute(
            f"UPDATE harashim_roles SET {set_clause} WHERE identity_id = ?",
            tuple(values)
        )
        self.db.commit()
        
        # Get the updated role
        return self.get_harash_role(identity_id)
    
    def get_harashim_by_sela(self, sela_id: str) -> List[Dict[str, Any]]:
        """
        Get all Harashim affiliated with a Sela.
        
        Args:
            sela_id: The Sela ID
        
        Returns:
            A list of Harash roles
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM harashim_roles WHERE affiliated_sela = ?",
            (sela_id,)
        )
        rows = cursor.fetchall()
        
        return [{
            "identity_id": row["identity_id"],
            "role": row["role"],
            "affiliated_sela": row["affiliated_sela"],
            "active_contracts": row["active_contracts"],
            "completed_contracts": row["completed_contracts"],
            "created_at": row["created_at"]
        } for row in rows]
