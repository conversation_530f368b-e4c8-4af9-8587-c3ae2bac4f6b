#!/usr/bin/env python
# setup_viewer.py

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("onnyx.setup_viewer")

def main():
    """Set up the Onnyx Transaction Viewer."""
    # Create the necessary directories
    viewer_dir = os.path.join(os.path.dirname(__file__), "onnyx_viewer")
    static_dir = os.path.join(viewer_dir, "static")
    templates_dir = os.path.join(viewer_dir, "templates")
    data_dir = os.path.join(viewer_dir, "data")
    
    os.makedirs(static_dir, exist_ok=True)
    logger.info(f"Created directory: {static_dir}")
    
    os.makedirs(templates_dir, exist_ok=True)
    logger.info(f"Created directory: {templates_dir}")
    
    os.makedirs(data_dir, exist_ok=True)
    logger.info(f"Created directory: {data_dir}")
    
    logger.info("Onnyx Transaction Viewer setup complete")
    logger.info("You can now run the viewer with: python onnyx-explorer.py")
    logger.info("Or generate sample data with: python generate_sample_data.py")

if __name__ == "__main__":
    main()
