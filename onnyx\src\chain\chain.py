# src/chain/chain.py

from src.chain.block import Block

class Blockchain:
    def __init__(self):
        self.chain = [self.create_genesis_block()]
        self.mempool = []

    def create_genesis_block(self):
        return Block(index=0, previous_hash="0", transactions=["Genesis Block"])

    def get_latest_block(self):
        return self.chain[-1]

    def add_block(self, block):
        if block.previous_hash != self.get_latest_block().hash:
            raise Exception("Invalid block: previous hash mismatch")
        self.chain.append(block)

    def add_transaction(self, tx):
        self.mempool.append(tx)

    def mine_pending_transactions(self):
        new_block = Block(
            index=len(self.chain),
            previous_hash=self.get_latest_block().hash,
            transactions=self.mempool
        )
        self.chain.append(new_block)
        self.mempool = []
        return new_block

    def to_list(self):
        return [block.to_dict() for block in self.chain]
