"""
Onnyx Block Model

This module provides the Block model for the Onnyx blockchain.
"""

import time
import logging
from typing import Dict, Any, List, Optional, ClassVar

from shared.models.base import BaseModel
from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.block")

class Block(BaseModel):
    """
    Block model for the Onnyx blockchain.
    """

    # Table name
    table_name: ClassVar[str] = "blocks"

    # Primary key column
    primary_key: ClassVar[str] = "block_id"

    # JSON fields
    json_fields: ClassVar[List[str]] = ["transactions", "metadata"]

    def __init__(
        self,
        block_id: str,
        block_height: int,
        previous_block_id: str,
        timestamp: int,
        difficulty: int,
        nonce: str,
        miner_identity_id: str,
        transactions: List[str],
        hash: str,
        metadata: Dict[str, Any] = None,
        **kwargs
    ):
        """
        Initialize the Block model.

        Args:
            block_id: The block ID
            block_height: The block height
            previous_block_id: The previous block ID
            timestamp: The block timestamp
            difficulty: The block difficulty
            nonce: The block nonce
            miner_identity_id: The miner identity ID
            transactions: The transaction IDs
            hash: The block hash
            metadata: The block metadata
            **kwargs: Additional attributes
        """
        self.block_id = block_id
        self.block_height = block_height
        self.previous_block_id = previous_block_id
        self.timestamp = timestamp
        self.difficulty = difficulty
        self.nonce = nonce
        self.miner_identity_id = miner_identity_id
        self.transactions = transactions
        self.hash = hash
        self.metadata = metadata or {}

        super().__init__(**kwargs)

    @classmethod
    def get_by_height(cls, height: int) -> Optional['Block']:
        """
        Get a block by height.

        Args:
            height: The block height

        Returns:
            The block or None if not found
        """
        query = f"SELECT * FROM {cls.table_name} WHERE block_height = ?"
        row = db.query_one(query, (height,))

        if row:
            return cls.from_dict(row)

        return None

    @classmethod
    def get_latest(cls) -> Optional['Block']:
        """
        Get the latest block.

        Returns:
            The latest block or None if no blocks exist
        """
        query = f"SELECT * FROM {cls.table_name} ORDER BY timestamp DESC LIMIT 1"
        row = db.query_one(query)

        if row:
            return cls.from_dict(row)

        return None

    @classmethod
    def get_blocks(cls, limit: int = 10, offset: int = 0) -> List['Block']:
        """
        Get blocks with pagination.

        Args:
            limit: The maximum number of blocks to return
            offset: The offset for pagination

        Returns:
            A list of blocks
        """
        query = f"SELECT * FROM {cls.table_name} ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        rows = db.query(query, (limit, offset))

        return [cls.from_dict(row) for row in rows]

    def get_transactions(self) -> List[Dict[str, Any]]:
        """
        Get the block's transactions.

        Returns:
            A list of transaction entries
        """
        if not self.transactions:
            return []

        placeholders = ", ".join(["?"] * len(self.transactions))
        query = f"SELECT * FROM transactions WHERE tx_id IN ({placeholders}) ORDER BY timestamp ASC"

        return db.query(query, tuple(self.transactions))

    @classmethod
    def create(
        cls,
        block_height: int,
        previous_block_id: str,
        timestamp: int,
        difficulty: int,
        nonce: str,
        miner_identity_id: str,
        transactions: List[str],
        metadata: Dict[str, Any] = None
    ) -> 'Block':
        """
        Create a new block.

        Args:
            block_height: The block height
            previous_block_id: The previous block ID
            timestamp: The block timestamp
            difficulty: The block difficulty
            nonce: The block nonce
            miner_identity_id: The miner identity ID
            transactions: The transaction IDs
            metadata: The block metadata

        Returns:
            The created block
        """
        # Generate the block ID and hash
        import hashlib
        import uuid

        # Generate a unique block ID
        block_id = str(uuid.uuid4())

        # Generate the block hash
        data = f"{block_height}{previous_block_id}{timestamp}{difficulty}{nonce}{miner_identity_id}{transactions}"
        hash = hashlib.sha256(data.encode()).hexdigest()

        # Create the block
        block = cls(
            block_id=block_id,
            block_height=block_height,
            previous_block_id=previous_block_id,
            timestamp=timestamp,
            difficulty=difficulty,
            nonce=nonce,
            miner_identity_id=miner_identity_id,
            transactions=transactions,
            hash=hash,
            metadata=metadata
        )

        # Save the block
        block.save()

        # Update the transactions with the block ID
        if transactions:
            placeholders = ", ".join(["?"] * len(transactions))
            query = f"UPDATE transactions SET block_id = ?, status = 'confirmed' WHERE tx_id IN ({placeholders})"
            db.execute(query, (block_id,) + tuple(transactions))

        return block
