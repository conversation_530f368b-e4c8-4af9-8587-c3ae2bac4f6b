# src/node/message.py

import json
import time
import logging
from typing import Dict, Any

from src.node.config import NODE_ID
from src.node.crypto import key_manager

# Configure logging
logger = logging.getLogger("onnyx.node.message")

class Message:
    """
    Represents a message in the Onnyx network.
    """

    def __init__(self, message_type: str, payload: Dict[str, Any] = None, from_node: str = None):
        """
        Initialize a message.

        Args:
            message_type: The type of the message.
            payload: The payload of the message.
            from_node: The ID of the node that sent the message.
        """
        self.type = message_type
        self.payload = payload or {}
        self.from_node = from_node or NODE_ID
        self.timestamp = time.time()
        self.signature = None

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the message to a dictionary.

        Returns:
            A dictionary representation of the message.
        """
        message_dict = {
            "type": self.type,
            "from": self.from_node,
            "timestamp": self.timestamp,
            **self.payload
        }

        if self.signature:
            message_dict["signature"] = self.signature

        return message_dict

    def to_json(self) -> str:
        """
        Convert the message to a JSON string.

        Returns:
            A JSON string representation of the message.
        """
        return json.dumps(self.to_dict())

    def sign(self) -> str:
        """
        Sign the message using the node's key manager.

        Returns:
            The signature.

        Raises:
            ValueError: If no private key is available.
        """
        # Create a canonical representation of the message for signing
        message_dict = self.to_dict()
        if "signature" in message_dict:
            del message_dict["signature"]

        # Sign the message using the key manager
        self.signature = key_manager.sign_message(message_dict)

        return self.signature

    def verify(self) -> bool:
        """
        Verify the message signature using the node's key manager.

        Returns:
            True if the signature is valid, False otherwise.

        Raises:
            ValueError: If no signature is available.
        """
        if not self.signature:
            raise ValueError("No signature to verify")

        # Create a canonical representation of the message for verification
        message_dict = self.to_dict()
        del message_dict["signature"]

        # Verify the signature using the key manager
        try:
            return key_manager.verify_message(message_dict, self.signature, self.from_node)
        except ValueError as e:
            logger.warning(f"Error verifying message: {str(e)}")
            return False

    @classmethod
    def from_dict(cls, message_dict: Dict[str, Any]) -> 'Message':
        """
        Create a message from a dictionary.

        Args:
            message_dict: The dictionary to create the message from.

        Returns:
            A new Message instance.

        Raises:
            ValueError: If the dictionary is missing required fields.
        """
        if "type" not in message_dict:
            raise ValueError("Message dictionary missing 'type' field")

        message_type = message_dict["type"]
        from_node = message_dict.get("from")

        # Extract the payload (all fields except type, from, timestamp, and signature)
        payload = {k: v for k, v in message_dict.items() if k not in ["type", "from", "timestamp", "signature"]}

        # Create the message
        message = cls(message_type, payload, from_node)

        # Set the timestamp and signature
        message.timestamp = message_dict.get("timestamp", time.time())
        message.signature = message_dict.get("signature")

        return message

    @classmethod
    def from_json(cls, message_json: str) -> 'Message':
        """
        Create a message from a JSON string.

        Args:
            message_json: The JSON string to create the message from.

        Returns:
            A new Message instance.

        Raises:
            ValueError: If the JSON string is invalid or missing required fields.
        """
        try:
            message_dict = json.loads(message_json)
            return cls.from_dict(message_dict)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON: {str(e)}")


def create_message(message_type: str, payload: Dict[str, Any] = None, sign: bool = True) -> Message:
    """
    Create a new message.

    Args:
        message_type: The type of the message.
        payload: The payload of the message.
        sign: Whether to sign the message.

    Returns:
        A new Message instance.
    """
    message = Message(message_type, payload)

    if sign:
        try:
            message.sign()
        except Exception as e:
            logger.error(f"Error signing message: {str(e)}")

    return message


def verify_message(message: Message) -> bool:
    """
    Verify a message signature.

    Args:
        message: The message to verify.

    Returns:
        True if the signature is valid, False otherwise.
    """
    try:
        return message.verify()
    except Exception as e:
        logger.error(f"Error verifying message: {str(e)}")
        return False
