"""
Onnyx Database-Backed Token Registry Module

This module provides a SQLite-backed token registry implementation.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.tokens.db_registry")

class DBTokenRegistry:
    """
    DBTokenRegistry manages tokens in the Onnyx ecosystem using a SQLite database.
    
    A token is a digital asset that can be minted, transferred, and burned.
    """
    
    def __init__(self):
        """Initialize the DBTokenRegistry."""
        # Check if the tokens table exists
        if not db.table_exists("tokens"):
            logger.warning("Tokens table does not exist.")
    
    def register_token(self, token_id: str, name: str, symbol: str, creator_id: str, 
                       supply: float, category: str = "general", decimals: int = 2, 
                       metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Register a new token.
        
        Args:
            token_id: The token ID
            name: The token name
            symbol: The token symbol
            creator_id: The creator's identity ID
            supply: The initial supply
            category: The token category (e.g., "loyalty", "equity", "community")
            decimals: The number of decimal places
            metadata: Additional metadata for the token
        
        Returns:
            The newly created token
        
        Raises:
            Exception: If the token already exists
        """
        try:
            # Check if the token already exists
            existing_token = self.get_token(token_id)
            if existing_token:
                raise Exception(f"Token with ID '{token_id}' already exists")
            
            # Check if the symbol already exists
            existing_symbol = db.query_one("SELECT * FROM tokens WHERE symbol = ?", (symbol,))
            if existing_symbol:
                raise Exception(f"Token with symbol '{symbol}' already exists")
            
            # Create the token
            now = int(time.time())
            token = {
                "id": token_id,
                "name": name,
                "symbol": symbol,
                "creator": creator_id,
                "supply": supply,
                "category": category,
                "decimals": decimals,
                "metadata": json.dumps(metadata or {}),
                "created_at": now,
                "updated_at": now
            }
            
            # Insert the token into the database
            db.insert("tokens", token)
            
            # Return the token
            return self.get_token(token_id)
        except Exception as e:
            logger.error(f"Error registering token: {str(e)}")
            raise
    
    def get_token(self, token_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a token by ID.
        
        Args:
            token_id: The token ID
        
        Returns:
            The token or None if not found
        """
        try:
            # Query the database for the token
            token = db.query_one("SELECT * FROM tokens WHERE id = ?", (token_id,))
            
            if token:
                # Parse the metadata field
                token["metadata"] = json.loads(token["metadata"]) if token["metadata"] else {}
                
                # Rename fields to match the expected format
                token["token_id"] = token["id"]
                token["creator_id"] = token["creator"]
                
                # Remove the renamed fields
                del token["id"]
                del token["creator"]
            
            return token
        except Exception as e:
            logger.error(f"Error getting token: {str(e)}")
            return None
    
    def get_token_by_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get a token by symbol.
        
        Args:
            symbol: The token symbol
        
        Returns:
            The token or None if not found
        """
        try:
            # Query the database for the token
            token = db.query_one("SELECT * FROM tokens WHERE symbol = ?", (symbol,))
            
            if token:
                # Parse the metadata field
                token["metadata"] = json.loads(token["metadata"]) if token["metadata"] else {}
                
                # Rename fields to match the expected format
                token["token_id"] = token["id"]
                token["creator_id"] = token["creator"]
                
                # Remove the renamed fields
                del token["id"]
                del token["creator"]
            
            return token
        except Exception as e:
            logger.error(f"Error getting token by symbol: {str(e)}")
            return None
    
    def get_all_tokens(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all tokens.
        
        Args:
            limit: The maximum number of tokens to return
            offset: The number of tokens to skip
        
        Returns:
            A list of tokens
        """
        try:
            # Query the database for the tokens
            tokens = db.query(
                "SELECT * FROM tokens ORDER BY created_at DESC LIMIT ? OFFSET ?",
                (limit, offset)
            )
            
            # Process the tokens
            for token in tokens:
                # Parse the metadata field
                token["metadata"] = json.loads(token["metadata"]) if token["metadata"] else {}
                
                # Rename fields to match the expected format
                token["token_id"] = token["id"]
                token["creator_id"] = token["creator"]
                
                # Remove the renamed fields
                del token["id"]
                del token["creator"]
            
            return tokens
        except Exception as e:
            logger.error(f"Error getting tokens: {str(e)}")
            return []
    
    def get_tokens_by_creator(self, creator_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get tokens by creator.
        
        Args:
            creator_id: The creator's identity ID
            limit: The maximum number of tokens to return
            offset: The number of tokens to skip
        
        Returns:
            A list of tokens
        """
        try:
            # Query the database for the tokens
            tokens = db.query(
                "SELECT * FROM tokens WHERE creator = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
                (creator_id, limit, offset)
            )
            
            # Process the tokens
            for token in tokens:
                # Parse the metadata field
                token["metadata"] = json.loads(token["metadata"]) if token["metadata"] else {}
                
                # Rename fields to match the expected format
                token["token_id"] = token["id"]
                token["creator_id"] = token["creator"]
                
                # Remove the renamed fields
                del token["id"]
                del token["creator"]
            
            return tokens
        except Exception as e:
            logger.error(f"Error getting tokens by creator: {str(e)}")
            return []
    
    def get_tokens_by_category(self, category: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get tokens by category.
        
        Args:
            category: The token category
            limit: The maximum number of tokens to return
            offset: The number of tokens to skip
        
        Returns:
            A list of tokens
        """
        try:
            # Query the database for the tokens
            tokens = db.query(
                "SELECT * FROM tokens WHERE category = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
                (category, limit, offset)
            )
            
            # Process the tokens
            for token in tokens:
                # Parse the metadata field
                token["metadata"] = json.loads(token["metadata"]) if token["metadata"] else {}
                
                # Rename fields to match the expected format
                token["token_id"] = token["id"]
                token["creator_id"] = token["creator"]
                
                # Remove the renamed fields
                del token["id"]
                del token["creator"]
            
            return tokens
        except Exception as e:
            logger.error(f"Error getting tokens by category: {str(e)}")
            return []
    
    def get_tokens_by_sela(self, sela_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get tokens by Sela.
        
        Args:
            sela_id: The Sela ID
            limit: The maximum number of tokens to return
            offset: The number of tokens to skip
        
        Returns:
            A list of tokens
        """
        try:
            # Query the database for the tokens
            tokens = db.query(
                """
                SELECT * FROM tokens 
                WHERE JSON_EXTRACT(metadata, '$.sela_id') = ? 
                ORDER BY created_at DESC LIMIT ? OFFSET ?
                """,
                (sela_id, limit, offset)
            )
            
            # Process the tokens
            for token in tokens:
                # Parse the metadata field
                token["metadata"] = json.loads(token["metadata"]) if token["metadata"] else {}
                
                # Rename fields to match the expected format
                token["token_id"] = token["id"]
                token["creator_id"] = token["creator"]
                
                # Remove the renamed fields
                del token["id"]
                del token["creator"]
            
            return tokens
        except Exception as e:
            logger.error(f"Error getting tokens by Sela: {str(e)}")
            return []
    
    def update_token_supply(self, token_id: str, supply_change: float) -> Optional[Dict[str, Any]]:
        """
        Update the supply of a token.
        
        Args:
            token_id: The token ID
            supply_change: The change in supply (positive for mint, negative for burn)
        
        Returns:
            The updated token or None if not found
        
        Raises:
            Exception: If the token does not exist or the supply would become negative
        """
        try:
            # Check if the token exists
            token = self.get_token(token_id)
            if not token:
                raise Exception(f"Token with ID '{token_id}' not found")
            
            # Calculate the new supply
            new_supply = token["supply"] + supply_change
            
            # Check if the new supply is valid
            if new_supply < 0:
                raise Exception(f"Cannot reduce supply below 0 (current: {token['supply']}, change: {supply_change})")
            
            # Update the token
            db.update(
                "tokens",
                {
                    "supply": new_supply,
                    "updated_at": int(time.time())
                },
                "id = ?",
                (token_id,)
            )
            
            # Return the updated token
            return self.get_token(token_id)
        except Exception as e:
            logger.error(f"Error updating token supply: {str(e)}")
            raise
    
    def update_token_metadata(self, token_id: str, metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update the metadata of a token.
        
        Args:
            token_id: The token ID
            metadata: The new metadata
        
        Returns:
            The updated token or None if not found
        
        Raises:
            Exception: If the token does not exist
        """
        try:
            # Check if the token exists
            token = self.get_token(token_id)
            if not token:
                raise Exception(f"Token with ID '{token_id}' not found")
            
            # Update the token
            db.update(
                "tokens",
                {
                    "metadata": json.dumps(metadata),
                    "updated_at": int(time.time())
                },
                "id = ?",
                (token_id,)
            )
            
            # Return the updated token
            return self.get_token(token_id)
        except Exception as e:
            logger.error(f"Error updating token metadata: {str(e)}")
            raise

# Create a global instance of the DBTokenRegistry
db_token_registry = DBTokenRegistry()
