#!/usr/bin/env python3
"""
Onnyx CLI Entry Point

This is the main command-line interface for the Onnyx platform.
It provides access to all CLI tools and utilities.
"""

import sys
import os
import argparse

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='Onnyx Platform CLI')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Miner command
    miner_parser = subparsers.add_parser('miner', help='Run Onnyx miner')
    miner_parser.add_argument('--config', help='Configuration file path')
    miner_parser.add_argument('--sela-id', help='Sela ID for mining')
    
    # Explorer command
    explorer_parser = subparsers.add_parser('explorer', help='Run Onnyx explorer')
    explorer_parser.add_argument('--port', type=int, default=5000, help='Port to run on')
    
    # Sela miner command
    sela_parser = subparsers.add_parser('sela-miner', help='Run Sela miner')
    sela_parser.add_argument('action', choices=['start', 'stop', 'status'], help='Action to perform')
    sela_parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    if args.command == 'miner':
        from dashboard.cli.onnyx_miner import main as miner_main
        sys.argv = ['onnyx-miner']
        if args.config:
            sys.argv.extend(['--config', args.config])
        if args.sela_id:
            sys.argv.extend(['--sela-id', args.sela_id])
        miner_main()
    
    elif args.command == 'explorer':
        from dashboard.cli.onnyx_explorer import main as explorer_main
        sys.argv = ['onnyx-explorer', '--port', str(args.port)]
        explorer_main()
    
    elif args.command == 'sela-miner':
        from dashboard.cli.onnyx_sela_miner import main as sela_main
        sys.argv = ['onnyx-sela-miner', args.action]
        if args.config:
            sys.argv.extend(['--config', args.config])
        sela_main()
    
    else:
        parser.print_help()

if __name__ == '__main__':
    main()
