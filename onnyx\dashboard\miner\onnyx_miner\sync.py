"""
Onnyx Miner Sync Module

This module provides functions for syncing with the blockchain.
"""

import os
import json
import logging
import requests
import time
from typing import Dict, Any, List, Optional

from .config import get_config

# Set up logging
logger = logging.getLogger("onnyx_miner.sync")

def get_chain_info() -> Dict[str, Any]:
    """
    Get information about the blockchain.
    
    Returns:
        Information about the blockchain
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the chain info
        chain_url = f"{api_url}/api/chain/info"
        chain_response = requests.get(chain_url)
        chain_response.raise_for_status()
        
        chain_info = chain_response.json()
        
        # Add sync status
        chain_info["sync_status"] = "synced"
        
        return chain_info
    except Exception as e:
        logger.error(f"Error getting chain info: {str(e)}")
        
        # Return default info if there's an error
        return {
            "chain_height": 0,
            "last_block_hash": "",
            "last_proposer": "",
            "sync_status": "error"
        }

def get_latest_block() -> Dict[str, Any]:
    """
    Get the latest block.
    
    Returns:
        The latest block
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the latest block
        block_url = f"{api_url}/api/blocks/latest"
        block_response = requests.get(block_url)
        block_response.raise_for_status()
        
        return block_response.json().get("block", {})
    except Exception as e:
        logger.error(f"Error getting latest block: {str(e)}")
        
        # Return an empty block if there's an error
        return {}

def get_block(block_index: int) -> Dict[str, Any]:
    """
    Get a block by index.
    
    Args:
        block_index: The block index
    
    Returns:
        The block
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the block
        block_url = f"{api_url}/api/blocks/{block_index}"
        block_response = requests.get(block_url)
        block_response.raise_for_status()
        
        return block_response.json().get("block", {})
    except Exception as e:
        logger.error(f"Error getting block: {str(e)}")
        
        # Return an empty block if there's an error
        return {}

def get_transactions(block_index: int) -> List[Dict[str, Any]]:
    """
    Get transactions for a block.
    
    Args:
        block_index: The block index
    
    Returns:
        The transactions
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the transactions
        tx_url = f"{api_url}/api/blocks/{block_index}/transactions"
        tx_response = requests.get(tx_url)
        tx_response.raise_for_status()
        
        return tx_response.json().get("transactions", [])
    except Exception as e:
        logger.error(f"Error getting transactions: {str(e)}")
        
        # Return an empty list if there's an error
        return []

def sync_chain() -> bool:
    """
    Sync the local chain with the network.
    
    Returns:
        True if the sync was successful, False otherwise
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the data directory
        data_dir = config.get("node", {}).get("data_dir", "data")
        
        # Create the data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Get the chain info
        chain_info = get_chain_info()
        
        # Get the local chain height
        local_height = 0
        local_chain_path = os.path.join(data_dir, "chain.json")
        if os.path.exists(local_chain_path):
            try:
                with open(local_chain_path, "r") as f:
                    local_chain = json.load(f)
                local_height = local_chain.get("height", 0)
            except Exception:
                pass
        
        # Check if we need to sync
        network_height = chain_info.get("chain_height", 0)
        if local_height >= network_height:
            logger.info("Chain is already synced")
            return True
        
        # Sync the chain
        logger.info(f"Syncing chain from height {local_height} to {network_height}")
        
        # Get the blocks
        blocks = []
        for i in range(local_height + 1, network_height + 1):
            block = get_block(i)
            if not block:
                logger.error(f"Failed to get block {i}")
                return False
            
            blocks.append(block)
        
        # Save the blocks
        with open(local_chain_path, "w") as f:
            json.dump({"height": network_height, "blocks": blocks}, f, indent=2)
        
        logger.info(f"Synced {len(blocks)} blocks")
        
        return True
    except Exception as e:
        logger.error(f"Error syncing chain: {str(e)}")
        return False

def get_peers() -> List[Dict[str, Any]]:
    """
    Get the list of peers.
    
    Returns:
        The list of peers
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the peers
        peers_url = f"{api_url}/api/peers"
        peers_response = requests.get(peers_url)
        peers_response.raise_for_status()
        
        return peers_response.json().get("peers", [])
    except Exception as e:
        logger.error(f"Error getting peers: {str(e)}")
        
        # Return an empty list if there's an error
        return []

def add_peer(peer_url: str) -> bool:
    """
    Add a peer.
    
    Args:
        peer_url: The peer URL
    
    Returns:
        True if the peer was added, False otherwise
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Add the peer
        peer_url = f"{api_url}/api/peers"
        peer_response = requests.post(peer_url, json={"url": peer_url})
        peer_response.raise_for_status()
        
        return True
    except Exception as e:
        logger.error(f"Error adding peer: {str(e)}")
        return False
