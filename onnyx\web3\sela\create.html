<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Your Sela - Onnyx Onboarding</title>
    <link rel="stylesheet" href="/static/css/onboarding.css">
    <link rel="stylesheet" href="/static/css/sela.css">
    <link rel="icon" href="/static/images/onnyx_logo.svg">
</head>
<body>
    <header class="onboarding-header">
        <div class="logo-container">
            <img src="/static/images/onnyx_logo.svg" alt="Onnyx Logo" class="logo">
        </div>
        <h1>Establish Your Sela</h1>
        <p class="tagline">Your Sovereign Business Entity Protected from Caesar</p>
    </header>

    <main class="onboarding-container">
        <section class="declaration">
            <h2>Sela Protection Framework</h2>
            <p class="mission-statement">
                A Sela is your sovereign business entity in the Onnyx economy - protected by immutable records and operating under covenant principles.
            </p>
            <p class="value-proposition">
                Create a business that operates outside of <PERSON>'s control, with transparent records that can't be manipulated by outside authorities.
            </p>
        </section>

        <section class="sela-benefits">
            <h2>How Your Sela Protects You from Caesar</h2>
            
            <div class="benefit-card">
                <h3>🔐 Immutable Proof of Labor & Income</h3>
                <p>Every transaction, service, and receipt logged on-chain is timestamped, cryptographically verifiable, and outside Caesar's edit control.</p>
                <blockquote>"You say I didn't work? The ledger says otherwise. Here's my labor log — verified by my tribe and my Sela."</blockquote>
            </div>
            
            <div class="benefit-card">
                <h3>🧾 Alternative Taxation Model</h3>
                <p>In Onnyx, taxation becomes voluntary offerings or tithes based on covenant laws, not national debt schemes.</p>
                <blockquote>"My work has already been accounted for — and my loyalty isn't to Babylon."</blockquote>
            </div>
            
            <div class="benefit-card">
                <h3>📊 Transparent Business Records</h3>
                <p>ONX Selas keep real-time business health on-chain, verifiable by partners and customers — not manipulable by Caesar.</p>
                <blockquote>Avoid double-taxation, defend against fabricated claims, prove labor in court or international tribunals if necessary.</blockquote>
            </div>
            
            <div class="benefit-card">
                <h3>🧱 Sovereign Parallel Economy</h3>
                <p>By conducting commerce in ONX + Mikvah tokens, you operate outside the fiat system with no IRS-style system defining what you "owe".</p>
                <blockquote>"Render unto Caesar what is Caesar's..." — but ONX ain't Caesar's.</blockquote>
            </div>
            
            <div class="benefit-card">
                <h3>🧬 Identity = Jurisdiction = Legal Cover</h3>
                <p>Your business identity tied to a biblically declared nation gives you a basis to refuse unjust jurisdiction.</p>
                <blockquote>"We resolve disputes and record labor within our own nation."</blockquote>
            </div>
        </section>

        <section class="sela-examples">
            <h2>Real-World Sela Examples</h2>
            
            <div class="example-card">
                <div class="example-header">
                    <h3>Sarah's Kitchen</h3>
                    <div class="example-tribe">Tribe of Zebulun</div>
                </div>
                <div class="example-content">
                    <p class="example-description">Sarah runs a catering business and joined Onnyx to protect her livelihood.</p>
                    <div class="example-details">
                        <h4>How Sarah's Sela Works:</h4>
                        <ul>
                            <li>All catering jobs are logged on-chain with immutable timestamps</li>
                            <li>Customer payments in ONX are outside the banking system</li>
                            <li>Her "Sarah's Kitchen Tokens" (SKT) create a parallel loyalty economy</li>
                            <li>Customers earn SKT with each purchase (5% back)</li>
                            <li>SKT can be redeemed for discounts or special menu items</li>
                            <li>Customers can stake ONX to her Sela for VIP status</li>
                            <li>When faced with an audit, she presents her complete, tamper-proof records</li>
                            <li>Her business operates under Zebulun tribal jurisdiction</li>
                        </ul>
                    </div>
                    <div class="example-benefits">
                        <h4>Benefits for Sarah:</h4>
                        <ul>
                            <li>Loyal customer base through token incentives</li>
                            <li>Reduced marketing costs through community reputation</li>
                            <li>Predictable income through staking programs</li>
                            <li>Higher Etzem score from verified transactions and reviews</li>
                            <li>Protection from unjust seizure and taxation</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="example-card">
                <div class="example-header">
                    <h3>David's Woodworks</h3>
                    <div class="example-tribe">Tribe of Judah</div>
                </div>
                <div class="example-content">
                    <p class="example-description">David creates handmade furniture and uses Onnyx to protect his craft.</p>
                    <div class="example-details">
                        <h4>How David's Sela Works:</h4>
                        <ul>
                            <li>Each piece is authenticated on-chain with provenance</li>
                            <li>Materials, labor, and sales are recorded immutably</li>
                            <li>"Craftsman Tokens" (CRT) create a sovereign customer community</li>
                            <li>CRT holders get early access to new pieces</li>
                            <li>Stakers receive custom pieces annually</li>
                            <li>His workshop operates under Judah's creative jurisdiction</li>
                            <li>Disputes are resolved within the tribal community</li>
                        </ul>
                    </div>
                    <div class="example-benefits">
                        <h4>Benefits for David:</h4>
                        <ul>
                            <li>Authentication prevents counterfeits</li>
                            <li>Built-in loyalty program</li>
                            <li>Community of collectors</li>
                            <li>Reputation-based pricing power</li>
                            <li>Protection from unjust seizure and taxation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="sela-creation-form">
            <h2>Create Your Sela</h2>
            <p class="form-intro">Fill out the form below to establish your sovereign business entity in the Onnyx economy.</p>
            
            <form id="sela-form" class="sela-form">
                <div class="form-group">
                    <label for="sela-name">Sela Name</label>
                    <input type="text" id="sela-name" name="sela-name" placeholder="Your business name" required>
                </div>
                
                <div class="form-group">
                    <label for="sela-description">Description</label>
                    <textarea id="sela-description" name="sela-description" placeholder="Describe your business and its purpose" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="sela-category">Category</label>
                    <select id="sela-category" name="sela-category" required>
                        <option value="">Select a category</option>
                        <option value="food">Food & Catering</option>
                        <option value="crafts">Crafts & Artisanal</option>
                        <option value="education">Education & Training</option>
                        <option value="services">Professional Services</option>
                        <option value="retail">Retail & Merchandise</option>
                        <option value="tech">Technology & Digital</option>
                        <option value="health">Health & Wellness</option>
                        <option value="agriculture">Agriculture & Farming</option>
                        <option value="construction">Construction & Trades</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="token-name">Token Name (Optional)</label>
                    <input type="text" id="token-name" name="token-name" placeholder="Your loyalty token name">
                </div>
                
                <div class="form-group">
                    <label for="token-symbol">Token Symbol (Optional)</label>
                    <input type="text" id="token-symbol" name="token-symbol" placeholder="3-5 letter symbol" maxlength="5">
                </div>
                
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="covenant-agreement" name="covenant-agreement" required>
                        I agree to operate my Sela according to covenant principles and the laws of my tribal nation
                    </label>
                </div>
                
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="sovereignty-declaration" name="sovereignty-declaration" required>
                        I declare my business sovereign and protected from unjust seizure, taxation, and manipulation by Caesar
                    </label>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="submit-btn">Create My Sela</button>
                </div>
            </form>
        </section>

        <div class="navigation-buttons">
            <a href="/onboarding/nations/select.html" class="nav-button back">Back to Nation Selection</a>
            <a href="/onboarding/sovereignty/protection.html" class="nav-button next">Continue to Protection Framework</a>
        </div>
    </main>

    <footer class="onboarding-footer">
        <div class="footer-content">
            <div class="footer-logo">
                <img src="/static/images/onnyx_logo.svg" alt="Onnyx Logo" class="footer-logo-img">
            </div>
            <div class="footer-links">
                <h4>Learn More</h4>
                <ul>
                    <li><a href="/docs/covenant_principles.html">Covenant Principles</a></li>
                    <li><a href="/docs/sovereignty.html">Tribal Sovereignty</a></li>
                    <li><a href="/docs/economic_system.html">Righteous Economy</a></li>
                    <li><a href="/docs/protection.html">Protection from Caesar</a></li>
                </ul>
            </div>
            <div class="footer-quote">
                <blockquote>"Render unto Caesar what is Caesar's, and unto God what is God's."</blockquote>
            </div>
        </div>
        <div class="copyright">
            &copy; <span id="current-year">2023</span> Onnyx - A Covenant-Based Economic System
        </div>
    </footer>

    <script src="/static/js/onboarding.js"></script>
    <script src="/static/js/sela.js"></script>
    <script>
        // Set current year in copyright
        document.getElementById('current-year').textContent = new Date().getFullYear();
    </script>
</body>
</html>
