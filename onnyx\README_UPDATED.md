# ONNYX: Identity-Centric Blockchain

ONNYX is a new blockchain protocol that puts identity at the center of the token ecosystem.

## 🔑 Key Features

- **Identity-Centric**: Every token is linked to an identity
- **Reputation System**: Build reputation through on-chain activity
- **Token Forking**: Create tokens based on existing ones
- **Soulbound Badges**: Non-transferable tokens that represent achievements
- **OnnyxScript**: Stack-based scripting language for token operations

## 🏗️ Project Structure

```
onnyx/
├── src/
│   ├── chain/        # Blockchain core (blocks, chain, txlog, mempool)
│   ├── identity/     # Identity management
│   ├── tokens/       # Token management
│   ├── vm/           # Virtual machine for OnnyxScript
│   ├── node/         # Node implementation
│   ├── wallet/       # Wallet implementation
│   ├── routes/       # API routes
│   └── web/          # Web interface
├── tests/            # Unit tests
├── data/             # Data storage
├── onnyx-cli.py      # Command-line interface
├── onnyx-api.py      # API server
└── onnyx-explorer.py # Web interface
```

## 🚀 Getting Started

### Running the API Server

```bash
# Install dependencies
pip install fastapi uvicorn pydantic ecdsa flask

# Run the API server
python onnyx-api.py
```

The API server will be available at http://127.0.0.1:8000. You can access the API documentation at http://127.0.0.1:8000/docs.

### Running the Web Interface

```bash
# Run the web interface
python onnyx-explorer.py
```

The web interface will be available at http://127.0.0.1:8080. You can use it to browse transactions, tokens, identities, and the mempool.

### Running the CLI

```bash
# Run the CLI in interactive mode
python onnyx-cli.py

# Run a specific command
python onnyx-cli.py identity  # Create a new identity
python onnyx-cli.py spawn     # Spawn a new token
python onnyx-cli.py mine      # Mine a new block
```

## 📡 API Endpoints

### Token Endpoints

- `POST /token/forktoken` - Create a new token linked to an identity
- `POST /token/minttoken` - Mint additional tokens
- `POST /token/sendtoken` - Transfer tokens between addresses
- `POST /token/burntoken` - Burn tokens
- `GET /token/gettokenbalance` - Get the balance of a token for an address
- `GET /token/tokenregistry` - Get a list of tokens, optionally filtered by creator or type
- `GET /token/transactions` - Get a list of transactions, optionally filtered by token ID, identity ID, or transaction ID

### Identity Endpoints

- `POST /identity/createidentity` - Create a new identity
- `GET /identity/getidentity` - Get information about an identity
- `POST /identity/updateidentity` - Update an identity's metadata
- `POST /identity/grantreputation` - Grant reputation to an identity
- `GET /identity/identityscore` - Get the composite score and detailed reputation metrics for an identity
- `GET /identity/transactions` - Get transactions for a specific identity

### Explorer Endpoints

- `GET /explorer/stats` - Get overall statistics for the Onnyx blockchain
- `GET /explorer/search` - Search for tokens, identities, or transactions by ID or name
- `GET /explorer/recent` - Get recent activity on the Onnyx blockchain

For detailed API documentation, see the [API Documentation](http://127.0.0.1:8000/docs) or the [Routes README](src/routes/README.md).

## 🧪 Coming Soon

| Feature                   | Description                                                  |
|---------------------------|--------------------------------------------------------------|
| 🔍 Token Explorer         | Search identities, token forks, and their traffic/reputation |
| 🧑‍💼 Identity Dashboard   | Visual Onnyx ID profiles with token holdings, history, rank  |
| 📲 Onnyx Wallet App       | Full desktop/web wallet with token creation, perks, staking  |
| 📘 OnnyxScript Playground | Web IDE to prototype token behavior without coding           |
| 📤 DAO/Business Launcher  | Templates to bootstrap identity-backed micro-economies       |

---

## 🧠 Philosophy

> "Don't just own crypto — become it."

ONNYX redefines ownership by tying it to **identity, activity, and outcome**.

- Token creation is not permissioned — it is **earned by action** and **anchored in identity**.
- Every transaction adds to your **reputation score**.
- Every token holder validates your **worth**.
- Every forked token tells the **story of your journey**.

ONNYX is not just a blockchain — it's a canvas for expressing trust, value, and progress in public.

---

## 🔐 License

ONNYX is currently licensed under a **restricted-use, non-commercial license**.

- 🚫 You may **not** fork, redistribute, or commercially use this software.
- 🧪 You may **view and contribute** under explicit approval from the ONNYX Core Team.
- 📩 For collaboration, licensing, or commercial interest — contact: <EMAIL>

A more permissive license may be released after the protocol reaches public beta.

---

## 🧿 Powered by ONNYX

> **Tokenize Yourself. Tokenize Your World.**

---
