#!/usr/bin/env python3
"""
Visual Test for Modern Navigation

This script creates a simple test to verify the modern navigation is working correctly.
"""

import requests
from bs4 import BeautifulSoup

def test_modern_nav_visual():
    """Test if modern navigation elements are present in the HTML."""
    base_url = "http://127.0.0.1:5000"
    
    print("🎨 Visual Test: Modern Navigation Elements")
    print("=" * 50)
    
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code != 200:
            print(f"❌ Server error: {response.status_code}")
            return
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for modern navigation structure
        modern_nav = soup.find('nav', class_='modern-nav')
        if modern_nav:
            print("✅ Found <nav class='modern-nav'>")
            
            # Check nav container
            nav_container = modern_nav.find('div', class_='nav-container')
            if nav_container:
                print("✅ Found nav-container")
                
                # Check left section
                nav_left = nav_container.find('div', class_='nav-left')
                if nav_left:
                    print("✅ Found nav-left section")
                    
                    logo_link = nav_left.find('a', class_='nav-logo-link')
                    if logo_link:
                        print("✅ Found nav-logo-link")
                        
                        logo_container = logo_link.find('div', class_='nav-logo-container')
                        logo_text = logo_link.find('span', class_='nav-logo-text')
                        
                        if logo_container:
                            print("✅ Found nav-logo-container")
                        if logo_text and 'ONNYX' in logo_text.get_text():
                            print("✅ Found nav-logo-text with 'ONNYX'")
                
                # Check center section
                nav_center = nav_container.find('div', class_='nav-center')
                if nav_center:
                    print("✅ Found nav-center section")
                    
                    links_container = nav_center.find('div', class_='nav-links-container')
                    if links_container:
                        print("✅ Found nav-links-container")
                        
                        modern_links = links_container.find_all('a', class_='nav-link-modern')
                        print(f"✅ Found {len(modern_links)} nav-link-modern elements")
                        
                        for i, link in enumerate(modern_links[:3]):
                            icon = link.find('span', class_='nav-link-icon')
                            text = link.find('span', class_='nav-link-text')
                            if icon and text:
                                print(f"  ✅ Link {i+1}: {icon.get_text()} {text.get_text()}")
                
                # Check right section
                nav_right = nav_container.find('div', class_='nav-right')
                if nav_right:
                    print("✅ Found nav-right section")
                    
                    # Check for guest buttons or user profile
                    guest_buttons = nav_right.find('div', class_='guest-buttons')
                    user_profile = nav_right.find('div', class_='user-profile-section')
                    
                    if guest_buttons:
                        print("✅ Found guest-buttons (logged out state)")
                        buttons = guest_buttons.find_all('a', class_='guest-button')
                        print(f"  ✅ Found {len(buttons)} guest buttons")
                    
                    if user_profile:
                        print("✅ Found user-profile-section (logged in state)")
        else:
            print("❌ Modern navigation not found!")
            
            # Check if old navigation is still there
            old_nav = soup.find('nav', class_='glass-nav')
            if old_nav:
                print("⚠️ Old glass-nav navigation still present")
        
        # Check CSS link
        css_link = soup.find('link', href=lambda x: x and 'main.css' in x)
        if css_link:
            href = css_link.get('href')
            print(f"✅ Found CSS link: {href}")
            
            if 'modern-nav-2024' in href:
                print("✅ Cache-busting parameter present")
        
        print("\n📊 Summary:")
        if modern_nav:
            print("🎉 Modern navigation structure is present in HTML!")
            print("💡 If you're not seeing the visual changes, try:")
            print("   1. Hard refresh (Ctrl+F5 or Cmd+Shift+R)")
            print("   2. Clear browser cache")
            print("   3. Open in incognito/private mode")
        else:
            print("❌ Modern navigation structure not found in HTML")
    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_modern_nav_visual()
