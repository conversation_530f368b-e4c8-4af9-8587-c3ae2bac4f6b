#!/usr/bin/env python3

import sys
import os
import json
import shutil
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("onnyx.explorer")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def ensure_data_directory():
    """Ensure the data directory exists and contains the necessary files."""
    data_dir = os.path.join("onnyx_viewer", "data")
    os.makedirs(data_dir, exist_ok=True)

    # Create empty JSON files if they don't exist
    for filename in ["blockchain.json", "mempool.json", "identities.json", "tokens.json"]:
        filepath = os.path.join(data_dir, filename)
        if not os.path.exists(filepath):
            with open(filepath, "w") as f:
                json.dump([], f)
            logger.info(f"Created empty {filename}")

def copy_blockchain_data():
    """Copy blockchain data from the Onnyx data directory to the viewer data directory."""
    source_dir = "data"
    target_dir = os.path.join("onnyx_viewer", "data")

    # Check if source directory exists
    if not os.path.exists(source_dir):
        logger.warning(f"Source directory {source_dir} does not exist. Using empty data files.")
        return

    # Copy blockchain.json
    blockchain_path = os.path.join(source_dir, "blockchain.json")
    if os.path.exists(blockchain_path):
        shutil.copy(blockchain_path, os.path.join(target_dir, "blockchain.json"))
        logger.info(f"Copied {blockchain_path} to {target_dir}")
    else:
        logger.warning(f"{blockchain_path} does not exist. Using empty blockchain.json.")

    # Copy mempool.json
    mempool_path = os.path.join(source_dir, "mempool.json")
    if os.path.exists(mempool_path):
        shutil.copy(mempool_path, os.path.join(target_dir, "mempool.json"))
        logger.info(f"Copied {mempool_path} to {target_dir}")
    else:
        logger.warning(f"{mempool_path} does not exist. Using empty mempool.json.")

    # Copy identities.json
    identities_path = os.path.join(source_dir, "identities.json")
    if os.path.exists(identities_path):
        shutil.copy(identities_path, os.path.join(target_dir, "identities.json"))
        logger.info(f"Copied {identities_path} to {target_dir}")
    else:
        logger.warning(f"{identities_path} does not exist. Using empty identities.json.")

    # Copy tokens.json
    tokens_path = os.path.join(source_dir, "tokens.json")
    if os.path.exists(tokens_path):
        shutil.copy(tokens_path, os.path.join(target_dir, "tokens.json"))
        logger.info(f"Copied {tokens_path} to {target_dir}")
    else:
        logger.warning(f"{tokens_path} does not exist. Using empty tokens.json.")

def main():
    """Main entry point for the Onnyx Explorer."""
    parser = argparse.ArgumentParser(description="Run the Onnyx Transaction Viewer")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode")
    parser.add_argument("--no-copy", action="store_true", help="Don't copy blockchain data")
    parser.add_argument("--use-web", action="store_true", help="Use the web module instead of the viewer")

    args = parser.parse_args()

    if args.use_web:
        # Use the original web module
        from src.web.app import app
    else:
        # Ensure the data directory exists
        ensure_data_directory()

        # Copy blockchain data if not disabled
        if not args.no_copy:
            copy_blockchain_data()

        # Add the root directory to the Python path
        root_dir = os.path.join(os.path.dirname(__file__), "..", "..")
        sys.path.insert(0, root_dir)

        # Add the dashboard viewer directory to the Python path
        viewer_dir = os.path.join(os.path.dirname(__file__), "..", "viewer")
        sys.path.insert(0, viewer_dir)

        # Import the Flask app
        from dashboard.viewer.app_explorer import app

    # Run the app
    logger.info(f"Starting Onnyx Transaction Viewer on {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == "__main__":
    main()
