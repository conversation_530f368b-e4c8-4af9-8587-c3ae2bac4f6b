"""
Onnyx Test Helpers

This module provides helper functions for testing the Onnyx blockchain.
"""

import os
import sys
import json
import shutil
import sqlite3
import tempfile
import unittest
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config.config import OnnyxConfig

class OnnyxTestCase(unittest.TestCase):
    """
    Base test case for Onnyx tests.

    This class provides common functionality for Onnyx tests, such as
    setting up a test environment with a temporary database and configuration.
    """

    def setUp(self):
        """
        Set up the test environment.
        """
        # Create a temporary directory for test data
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = self.temp_dir.name

        # Create subdirectories
        os.makedirs(os.path.join(self.test_dir, 'config'), exist_ok=True)
        os.makedirs(os.path.join(self.test_dir, 'data'), exist_ok=True)
        os.makedirs(os.path.join(self.test_dir, 'keys'), exist_ok=True)

        # Copy test fixtures to the temporary directory
        self.copy_fixtures()

        # Initialize the test configuration
        self.config = self.init_config()

    def tearDown(self):
        """
        Clean up the test environment.
        """
        # Close the temporary directory
        try:
            self.temp_dir.cleanup()
        except Exception as e:
            print(f"Warning: Failed to clean up temporary directory: {e}")

    def copy_fixtures(self):
        """
        Copy test fixtures to the temporary directory.
        """
        fixtures_dir = os.path.join(os.path.dirname(__file__), 'fixtures')

        # Copy configuration
        config_path = os.path.join(os.path.dirname(__file__), 'data', 'test_config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)

            # Save chain parameters
            with open(os.path.join(self.test_dir, 'config', 'chain_params.json'), 'w') as f:
                json.dump(config['chain_params'], f, indent=2)

            # Save network parameters
            with open(os.path.join(self.test_dir, 'config', 'network_params.json'), 'w') as f:
                json.dump(config['network_params'], f, indent=2)

            # Save node parameters
            with open(os.path.join(self.test_dir, 'config', 'node_params.json'), 'w') as f:
                json.dump(config['node_params'], f, indent=2)

            # Save Sela parameters
            with open(os.path.join(self.test_dir, 'config', 'sela_params.json'), 'w') as f:
                json.dump(config['sela_params'], f, indent=2)

        # Copy identities
        identities_path = os.path.join(fixtures_dir, 'identities.json')
        if os.path.exists(identities_path):
            shutil.copy2(identities_path, os.path.join(self.test_dir, 'data', 'identities.json'))

        # Copy tokens
        tokens_path = os.path.join(fixtures_dir, 'tokens.json')
        if os.path.exists(tokens_path):
            shutil.copy2(tokens_path, os.path.join(self.test_dir, 'data', 'tokens.json'))

        # Copy blockchain
        blockchain_path = os.path.join(fixtures_dir, 'blockchain.json')
        if os.path.exists(blockchain_path):
            shutil.copy2(blockchain_path, os.path.join(self.test_dir, 'data', 'blockchain.json'))

        # Copy mempool
        mempool_path = os.path.join(fixtures_dir, 'mempool.json')
        if os.path.exists(mempool_path):
            shutil.copy2(mempool_path, os.path.join(self.test_dir, 'data', 'mempool.json'))

    def init_config(self):
        """
        Initialize the test configuration.

        Returns:
            The test configuration
        """
        return OnnyxConfig(
            config_dir=os.path.join(self.test_dir, 'config'),
            data_dir=os.path.join(self.test_dir, 'data')
        )

    def init_db(self):
        """
        Initialize the test database.
        """
        # Read the schema file
        schema_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'schemas', 'schema.sql')
        if not os.path.exists(schema_path):
            return

        with open(schema_path, 'r') as f:
            schema = f.read()

        # Connect to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Execute the schema
        cursor.executescript(schema)

        # Load test data
        self.load_test_data(conn)

        # Commit the changes
        conn.commit()

        # Close the connection
        conn.close()

    def load_test_data(self, conn):
        """
        Load test data into the database.

        Args:
            conn: SQLite connection
        """
        cursor = conn.cursor()

        # Load identities
        identities_path = os.path.join(self.test_dir, 'data', 'identities.json')
        if os.path.exists(identities_path):
            with open(identities_path, 'r') as f:
                identities = json.load(f)

            for identity_id, identity in identities.items():
                cursor.execute(
                    "INSERT OR REPLACE INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
                    (
                        identity_id,
                        identity.get('name', ''),
                        identity.get('public_key', ''),
                        json.dumps(identity.get('metadata', {})),
                        identity.get('created_at', 0),
                        identity.get('updated_at', 0)
                    )
                )

        # Load tokens
        tokens_path = os.path.join(self.test_dir, 'data', 'tokens.json')
        if os.path.exists(tokens_path):
            with open(tokens_path, 'r') as f:
                tokens = json.load(f)

            for token_id, token in tokens.items():
                cursor.execute(
                    "INSERT OR REPLACE INTO tokens (token_id, name, symbol, creator_id, supply, category, decimals, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (
                        token_id,
                        token.get('name', ''),
                        token.get('symbol', ''),
                        token.get('creator_id', ''),
                        token.get('supply', 0),
                        token.get('category', ''),
                        token.get('decimals', 0),
                        token.get('created_at', 0),
                        json.dumps(token.get('metadata', {}))
                    )
                )

        # Load blockchain
        blockchain_path = os.path.join(self.test_dir, 'data', 'blockchain.json')
        if os.path.exists(blockchain_path):
            with open(blockchain_path, 'r') as f:
                blockchain = json.load(f)

            for block in blockchain.get('blocks', []):
                cursor.execute(
                    "INSERT OR REPLACE INTO blocks (block_hash, block_height, previous_hash, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (
                        block.get('hash', ''),
                        block.get('height', 0),
                        block.get('previous_hash', ''),
                        block.get('timestamp', 0),
                        block.get('difficulty', 0),
                        block.get('nonce', 0),
                        block.get('miner', ''),
                        json.dumps(block.get('transactions', [])),
                        block.get('merkle_root', ''),
                        block.get('size', 0),
                        block.get('version', '1.0'),
                        block.get('timestamp', 0)
                    )
                )

            for tx_id, tx in blockchain.get('transactions', {}).items():
                cursor.execute(
                    "INSERT OR REPLACE INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (
                        tx_id,
                        tx.get('block_hash', ''),
                        tx.get('timestamp', 0),
                        tx.get('op', ''),
                        json.dumps(tx.get('data', {})),
                        tx.get('sender', ''),
                        tx.get('signature', ''),
                        tx.get('status', ''),
                        tx.get('created_at', 0)
                    )
                )

        # Load mempool
        mempool_path = os.path.join(self.test_dir, 'data', 'mempool.json')
        if os.path.exists(mempool_path):
            with open(mempool_path, 'r') as f:
                mempool = json.load(f)

            for tx_id, tx in mempool.items():
                cursor.execute(
                    "INSERT OR REPLACE INTO mempool (tx_id, timestamp, op, data, sender, signature, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                    (
                        tx_id,
                        tx.get('timestamp', 0),
                        tx.get('op', ''),
                        json.dumps(tx.get('data', {})),
                        tx.get('sender', ''),
                        tx.get('signature', ''),
                        tx.get('created_at', 0)
                    )
                )

def get_test_config():
    """
    Get the test configuration.

    Returns:
        The test configuration
    """
    config_path = os.path.join(os.path.dirname(__file__), 'data', 'test_config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            return json.load(f)
    return {}
