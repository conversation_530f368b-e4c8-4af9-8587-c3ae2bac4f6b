# Onnyx Scroll-Controlled Chain Parameters

## Overview

Onnyx's Scroll-Controlled Chain Parameters give the community and Council the ability to dynamically update the blockchain's core economic rules through governance. This enables adaptive policy without hard forks, with all changes transparently recorded on-chain.

## Key Parameters

| Parameter | Description | Example Change Source |
|-----------|-------------|------------------------|
| 🪙 block_reward | ONX earned per mined block | Governance scroll vote |
| 🔼 mint_cap_factor | Base multiplier for Yovel minting cap | Reputation-based tuning |
| 🗳 quorum_percent | % of total trust needed for proposal | Council override |
| 🎯 vote_pass_ratio | % of "yes" votes required to pass | Scroll-controlled |

## Components

### ChainParameters

The ChainParameters class manages the chain parameters for the Onnyx blockchain. It provides methods for:

- Getting parameter values
- Setting parameter values
- Updating multiple parameters
- Resetting parameters to default values

### VoiceScrolls

The VoiceScrolls class manages governance proposals (scrolls) for the Onnyx blockchain. It provides methods for:

- Creating proposals
- Voting on proposals
- Tallying votes
- Resolving proposals
- Applying proposal effects

## Default Parameters

```json
{
  "block_reward": 10,
  "reward_token": "ONX",
  "quorum_percent": 50,
  "vote_pass_ratio": 0.6,
  "mint_cap_factor": 1.0,
  "min_etzem_score": 30,
  "validator_badges": ["VALIDATOR_ELIGIBLE_BADGE"],
  "council_badges": ["COUNCIL_ELIGIBLE_BADGE"],
  "guardian_badges": ["GUARDIAN_ELIGIBLE_BADGE"],
  "proposal_badges": ["PROPOSAL_ELIGIBLE_BADGE"],
  "validator_rotation_interval": 3600,
  "scroll_voting_period": 604800,
  "scroll_implementation_delay": 86400,
  "max_token_supply": 1000000000,
  "min_stake_amount": 100,
  "stake_lock_period": 2592000,
  "max_mempool_size": 1000,
  "max_block_size": 1000000,
  "target_block_time": 60,
  "difficulty_adjustment_period": 100,
  "max_transaction_size": 100000,
  "max_transactions_per_block": 1000
}
```

## API Endpoints

### Chain Parameters Endpoints

- `GET /api/chain-parameters` - Get all chain parameters
- `GET /api/chain-parameters/{key}` - Get a specific chain parameter
- `POST /api/chain-parameters/{key}` - Set a chain parameter
- `POST /api/chain-parameters/reset/{key}` - Reset a chain parameter to its default value
- `POST /api/chain-parameters/reset-all` - Reset all chain parameters to their default values
- `POST /api/chain-parameters/propose` - Propose a chain parameter change

### Voice Scroll Endpoints

- `POST /api/governance/propose` - Propose a new Voice Scroll
- `POST /api/governance/vote` - Vote on a Voice Scroll
- `GET /api/governance/scrolls` - Get Voice Scrolls
- `GET /api/governance/scrolls/{scroll_id}` - Get a Voice Scroll by ID
- `GET /api/governance/scrolls/{scroll_id}/tally` - Tally the votes for a Voice Scroll
- `POST /api/governance/resolve/{scroll_id}` - Resolve a Voice Scroll

## Example Usage

### Proposing a Chain Parameter Change

```json
POST /api/chain-parameters/propose
{
  "creator_id": "alice",
  "title": "Reduce block reward",
  "description": "Cut block reward to 8 ONX to reduce inflation",
  "param": "block_reward",
  "value": 8,
  "expiry_days": 7
}
```

### Voting on a Proposal

```json
POST /api/governance/vote
{
  "identity_id": "bob",
  "scroll_id": "scroll_1a2b3c_1712341234",
  "decision": "yes"
}
```

### Resolving a Proposal

```json
POST /api/governance/resolve/scroll_1a2b3c_1712341234
```

## Integration with Core Logic

### Mining

```python
from config.chain_parameters import chain_parameters

# Get the current block reward
block_reward = chain_parameters.get("block_reward")
reward_token = chain_parameters.get("reward_token")

# Create a coinbase transaction
coinbase = {
    "type": "reward",
    "op": "OP_REWARD",
    "txid": f"coinbase-{int(time.time())}-{proposer_id}",
    "to": proposer_id,
    "amount": block_reward,
    "token_id": reward_token,
    "timestamp": int(time.time())
}
```

### Validator Rotation

```python
from config.chain_parameters import chain_parameters

# Get validator parameters
rotation_interval = chain_parameters.get("validator_rotation_interval")
min_etzem_score = chain_parameters.get("min_etzem_score")
required_badges = chain_parameters.get("validator_badges")
```

### Voice Scroll Tallying

```python
from config.chain_parameters import chain_parameters

# Get governance parameters
quorum_percent = chain_parameters.get("quorum_percent")
pass_ratio = chain_parameters.get("vote_pass_ratio")
```

## Governance Process

1. **Proposal**: An identity with sufficient Etzem score creates a proposal to change a chain parameter
2. **Voting**: Identities vote on the proposal, with voting power determined by their Etzem score
3. **Tallying**: Votes are tallied and the outcome is determined based on the votes
4. **Resolution**: After the voting period ends, the proposal is resolved and the parameter is updated if the proposal passed

## Benefits

- **Adaptive Policy**: Chain parameters can be adjusted without hard forks
- **Community Governance**: The community can participate in decision-making
- **Transparency**: All changes are transparently recorded on-chain
- **Protection**: Parameters are protected by Council quorum and reputation thresholds
