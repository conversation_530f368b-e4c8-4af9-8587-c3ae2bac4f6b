"""
Onnyx Yovel Limiter Module

This module provides the YovelLimiter class for calculating token mint limits.
"""

import os
import json
from typing import Dict, List, Any, Optional

from identity.registry import IdentityRegistry
from governance.etzem_engine import EtzemEngine
from identity.trust.etzem_engine import <PERSON>tzemEngine as TrustEtzemEngine

def calculate_yovel_cap(etzem_score: float, category: str) -> int:
    """
    Calculate the Yovel mint cap based on Etzem score and token category.

    Args:
        etzem_score: The Etzem score of the identity
        category: The token category (e.g., "loyalty", "equity", "community")

    Returns:
        The Yovel mint cap
    """
    base = 1000

    # Adjust base cap based on token category
    if category == "loyalty":
        base *= 1.5
    elif category == "equity":
        base *= 1.2
    elif category == "community":
        base *= 1.0
    else:
        base *= 0.8

    # Apply Etzem score multiplier
    multiplier = max(0.1, etzem_score / 100)  # Minimum multiplier of 0.1

    return int(base * multiplier)

class YovelLimiter:
    """
    YovelLimiter calculates token mint limits for identities.

    The Yovel Limiter ensures that identities can only mint a limited number of tokens.
    """

    def __init__(self, identity_registry: IdentityRegistry, etzem_engine: EtzemEngine):
        """
        Initialize the YovelLimiter.

        Args:
            identity_registry: The identity registry
            etzem_engine: The Etzem engine
        """
        self.identity_registry = identity_registry
        self.etzem_engine = etzem_engine

        # Define base mint cap
        self.base_mint_cap = 1000

    def calculate_mint_cap(self, identity_id: str, category: str = "general") -> int:
        """
        Calculate the mint cap for an identity.

        Args:
            identity_id: The identity ID
            category: The token category (e.g., "loyalty", "equity", "community")

        Returns:
            The mint cap

        Raises:
            Exception: If the identity does not exist
        """
        identity = self.identity_registry.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the identity is a Selaholder
        is_selaholder = False
        if "founded_selas" in identity and identity["founded_selas"]:
            is_selaholder = True
        elif "joined_selas" in identity and identity["joined_selas"]:
            is_selaholder = True

        if not is_selaholder:
            return 0  # Only Selaholders can mint tokens

        # Calculate the Etzem score
        etzem_data = self.etzem_engine.calculate_etzem_score(identity_id)
        etzem_score = etzem_data["final_etzem"]

        # Calculate the mint cap based on Etzem score and category
        base_cap = calculate_yovel_cap(etzem_score, category)

        # Calculate the mint cap based on badges
        badge_multiplier = 1
        badges = identity.get("badges", [])

        if "STAKER" in badges:
            badge_multiplier += 0.5

        if "VALIDATOR_ELIGIBLE_BADGE" in badges:
            badge_multiplier += 0.5

        if "GUARDIAN_ELIGIBLE_BADGE" in badges:
            badge_multiplier += 0.3

        if "SELA_FOUNDER" in badges:
            badge_multiplier += 0.5

        # Apply Etzem badges multipliers
        if "ETZEM_TRUSTED" in badges:
            badge_multiplier += 0.2

        if "ETZEM_REPUTABLE" in badges:
            badge_multiplier += 0.3

        if "ETZEM_ESTEEMED" in badges:
            badge_multiplier += 0.5

        if "ETZEM_VENERABLE" in badges:
            badge_multiplier += 1.0

        # Calculate the final mint cap
        mint_cap = int(base_cap * badge_multiplier)

        return mint_cap
