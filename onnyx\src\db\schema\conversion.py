# src/db/schema/conversion.py

from src.db.manager import db_manager

def create_conversion_tables():
    """
    Create the tables for the fiat-to-ONX conversion system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Create conversion_logs table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS conversion_logs (
        tx_id TEXT PRIMARY KEY,
        identity_id TEXT NOT NULL,
        currency TEXT NOT NULL,
        amount_in REAL NOT NULL,
        onx_out REAL NOT NULL,
        source TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversion_logs_identity_id ON conversion_logs (identity_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversion_logs_currency ON conversion_logs (currency)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversion_logs_source ON conversion_logs (source)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversion_logs_created_at ON conversion_logs (created_at)')
    
    conn.commit()
