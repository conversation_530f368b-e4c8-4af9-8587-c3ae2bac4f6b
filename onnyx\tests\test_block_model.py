"""
Onnyx Block Model Tests

This module provides tests for the Onnyx block model.
"""

import os
import sys
import unittest
import time
import hashlib
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.block import Block
from models.identity import Identity
from data.db import db

class TestBlockModel(unittest.TestCase):
    """
    Test the Block model.
    """
    
    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_block_model.db")
        
        # Set the database path
        db.db_path = self.db_path
        
        # Create the tables
        conn = sqlite3.connect(self.db_path)
        
        # Create the identities table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER,
            updated_at INTEGER
        )
        """)
        
        # Create the blocks table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS blocks (
            block_hash TEXT PRIMARY KEY,
            previous_hash TEXT NOT NULL,
            block_height INTEGER NOT NULL,
            timestamp INTEGER NOT NULL,
            difficulty INTEGER NOT NULL,
            nonce INTEGER NOT NULL,
            miner TEXT NOT NULL,
            transactions TEXT,
            merkle_root TEXT,
            size INTEGER,
            version TEXT,
            created_at INTEGER,
            FOREIGN KEY (miner) REFERENCES identities (identity_id)
        )
        """)
        
        conn.commit()
        conn.close()
        
        # Create a test identity
        self.identity_id = "test_identity"
        
        # Insert the identity into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.identity_id,
                "Test Identity",
                "0x1234567890abcdef",
                json.dumps({"type": "individual", "description": "Test identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")
        
        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")
    
    def test_create_block(self):
        """Test creating a block."""
        # Create a block directly in the database
        block_hash = "test_block_hash"
        previous_hash = "0000000000000000000000000000000000000000000000000000000000000000"
        timestamp = int(time.time())
        
        # Insert the block into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO blocks (block_hash, previous_hash, block_height, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                block_hash,
                previous_hash,
                0,
                timestamp,
                1,
                0,
                self.identity_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                timestamp
            )
        )
        conn.commit()
        conn.close()
        
        # Check if the block was saved to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM blocks WHERE block_hash = ?", (block_hash,))
        row = cursor.fetchone()
        conn.close()
        
        print(f"Database row: {row}")
        
        # Get the block by ID
        retrieved_block = Block.get_by_id(block_hash)
        
        # Check that the block was retrieved
        self.assertIsNotNone(retrieved_block)
        self.assertEqual(retrieved_block.block_hash, block_hash)
        self.assertEqual(retrieved_block.previous_hash, previous_hash)
        self.assertEqual(retrieved_block.block_height, 0)
        self.assertEqual(retrieved_block.timestamp, timestamp)
        self.assertEqual(retrieved_block.difficulty, 1)
        self.assertEqual(retrieved_block.nonce, 0)
        self.assertEqual(retrieved_block.miner, self.identity_id)
        self.assertEqual(retrieved_block.transactions, [])
        self.assertEqual(retrieved_block.merkle_root, "0000000000000000000000000000000000000000000000000000000000000000")
        self.assertEqual(retrieved_block.size, 0)
        self.assertEqual(retrieved_block.version, "1.0")
        self.assertEqual(retrieved_block.created_at, timestamp)
    
    def test_get_latest_block(self):
        """Test getting the latest block."""
        # Create multiple blocks directly in the database
        conn = sqlite3.connect(self.db_path)
        
        # Insert the genesis block
        conn.execute(
            "INSERT INTO blocks (block_hash, previous_hash, block_height, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "genesis_block_hash",
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                int(time.time()) - 3600,
                1,
                0,
                self.identity_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                int(time.time()) - 3600
            )
        )
        
        # Insert a second block
        conn.execute(
            "INSERT INTO blocks (block_hash, previous_hash, block_height, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "second_block_hash",
                "genesis_block_hash",
                1,
                int(time.time()) - 1800,
                1,
                0,
                self.identity_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                int(time.time()) - 1800
            )
        )
        
        # Insert the latest block
        latest_timestamp = int(time.time())
        conn.execute(
            "INSERT INTO blocks (block_hash, previous_hash, block_height, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "latest_block_hash",
                "second_block_hash",
                2,
                latest_timestamp,
                1,
                0,
                self.identity_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                latest_timestamp
            )
        )
        
        conn.commit()
        conn.close()
        
        # Get the latest block
        latest_block = Block.get_latest()
        
        # Check that the latest block was retrieved
        self.assertIsNotNone(latest_block)
        self.assertEqual(latest_block.block_hash, "latest_block_hash")
        self.assertEqual(latest_block.block_height, 2)

if __name__ == "__main__":
    unittest.main()
