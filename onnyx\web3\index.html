<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Onnyx | A World Built on Trust</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="/web3/static/favicon.png" type="image/png">
  <style>
    html, body {
      margin: 0;
      padding: 0;
      overflow-x: hidden;
      font-family: 'Segoe UI', sans-serif;
      background-color: #0a0a0a;
      color: #f2f2f2;
      height: 100%;
      scroll-behavior: smooth;
    }

    .sela-tiers-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 2rem;
      max-width: 800px;
      margin: 0 auto;
    }

    .coming-soon-text {
      margin-top: 2rem;
      font-style: italic;
      color: #a64dff;
      text-align: center;
    }

    #particles-js {
      position: fixed;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
    }

    /* Navigation Styles */
    .navbar {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      background-color: rgba(10, 10, 10, 0.95);
      backdrop-filter: blur(8px);
      z-index: 1000;
      padding: 0.6rem 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.7);
      box-sizing: border-box;
    }

    .navbar-logo {
      display: flex;
      align-items: center;
    }

    .logo-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      transition: transform 0.3s ease;
      position: relative;
    }

    .logo-link:hover {
      transform: scale(1.05);
    }

    .logo-link:hover::after {
      content: '→ Roadmap';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: #a64dff; /* Purple */
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 0.8rem;
      white-space: nowrap;
      opacity: 0;
      animation: fadeIn 0.3s forwards;
    }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    /* Animation Classes */
    @keyframes slide-in-up {
      from {
        transform: translateY(30px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @keyframes fade-in {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }

    @keyframes floating {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    .slide-in-up {
      animation: slide-in-up 0.8s forwards;
    }

    .fade-in {
      animation: fade-in 0.8s forwards;
    }

    .pulse {
      animation: pulse 2s infinite;
    }

    .float-element {
      animation: floating 3s ease-in-out infinite;
    }

    .glowing {
      box-shadow: 0 0 20px rgba(166, 77, 255, 0.8), 0 0 30px rgba(166, 77, 255, 0.6), 0 0 40px rgba(166, 77, 255, 0.4);
      transition: box-shadow 0.3s ease;
    }

    .navbar-logo img {
      height: 55px;
      width: auto;
      filter: drop-shadow(0 0 8px rgba(128, 0, 255, 0.6));
    }

    .navbar-links {
      display: flex;
      gap: 1.2rem;
      align-items: center;
      margin-left: auto;
    }

    .navbar-links a {
      color: #f2f2f2;
      text-decoration: none;
      font-size: 1rem;
      transition: all 0.3s ease;
      position: relative;
      padding: 0.5rem 0;
    }

    .navbar-links a:hover {
      color: #a64dff; /* Purple */
    }

    .navbar-links a::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: #a64dff; /* Purple */
      transition: width 0.3s ease;
    }

    .navbar-links a:hover::after {
      width: 100%;
    }

    .nav-cta {
      background: #a64dff; /* Solid purple */
      color: #fff !important;
      padding: 0.5rem 1rem !important;
      border-radius: 5px;
      font-weight: bold;
      margin-left: 0.5rem;
      white-space: nowrap;
    }

    .nav-cta:hover {
      background: #8a2be2; /* Darker purple on hover */
      color: #fff !important;
    }

    .nav-cta::after {
      display: none;
    }

    .mobile-menu-btn {
      display: none;
      background: rgba(20, 20, 20, 0.8);
      border: 1px solid rgba(166, 77, 255, 0.7);
      color: #f2f2f2;
      cursor: pointer;
      margin-left: auto;
      border-radius: 5px;
      padding: 10px;
      width: 45px;
      height: 45px;
      position: relative;
      transition: all 0.3s ease;
      z-index: 1100;
      box-shadow: 0 0 10px rgba(166, 77, 255, 0.3);
      margin-right: 10px;
    }

    .mobile-menu-btn:hover, .mobile-menu-btn:focus {
      background: rgba(166, 77, 255, 0.3);
      box-shadow: 0 0 15px rgba(166, 77, 255, 0.7);
      outline: none;
    }

    .menu-icon {
      position: relative;
      display: block;
      width: 25px;
      height: 2px;
      background-color: #ffd700;
      margin: 0 auto;
      transition: background-color 0.3s ease;
    }

    .menu-icon::before,
    .menu-icon::after {
      content: '';
      position: absolute;
      width: 25px;
      height: 2px;
      background-color: #ffd700;
      transition: transform 0.3s ease;
    }

    .menu-icon::before {
      top: -8px;
    }

    .menu-icon::after {
      top: 8px;
    }

    .mobile-menu-btn.active .menu-icon {
      background-color: transparent;
    }

    .mobile-menu-btn.active .menu-icon::before {
      transform: translateY(8px) rotate(45deg);
    }

    .mobile-menu-btn.active .menu-icon::after {
      transform: translateY(-8px) rotate(-45deg);
    }

    .mobile-menu-btn.active {
      background: rgba(166, 77, 255, 0.4);
      border-color: #ffd700;
    }

    .section {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 2rem;
      box-sizing: border-box;
      padding-top: 80px; /* Account for navbar */
      scroll-margin-top: 80px; /* This is crucial for scroll-to-anchor functionality */
    }

    .typewriter {
      font-size: 1.4rem;
      white-space: pre-line;
      overflow: hidden;
      width: 100%;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
      height: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }

    h2 {
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: #a64dff; /* Purple */
      text-align: center;
      position: relative;
      display: inline-block;
    }

    h2::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 2px;
      background: linear-gradient(90deg, rgba(166,77,255,0), rgba(166,77,255,0.8), rgba(166,77,255,0)); /* Purple gradient */
    }

    .section-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .section-subtitle {
      color: #aaa;
      font-size: 1.1rem;
      max-width: 700px;
      margin: 0 auto;
      margin-top: 1rem;
    }

    .comparison {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 2rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .box {
      flex: 1;
      min-width: 300px;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .fiat {
      background-color: rgba(80, 0, 0, 0.2);
      border: 1px solid #800000;
    }

    .onnyx {
      background-color: rgba(0, 80, 60, 0.2);
      border: 1px solid #00ffcc;
    }

    h3 {
      font-size: 1.5rem;
      margin-top: 0;
      margin-bottom: 1rem;
    }

    ul {
      list-style: none;
      padding-left: 1rem;
    }

    ul li {
      margin-bottom: 0.8rem;
      position: relative;
    }

    .fiat ul li::before {
      content: "✗ ";
      color: #ff6666;
    }

    .onnyx ul li::before {
      content: "✦ ";
      color: #00ffcc;
    }

    .path {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      max-width: 600px;
      margin: 0 auto;
    }

    .path div {
      padding: 1.2rem;
      background-color: rgba(0, 255, 204, 0.1);
      border-radius: 8px;
      text-align: center;
      font-size: 1.2rem;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      transition: transform 0.3s ease, background-color 0.3s ease;
    }

    .path div:hover {
      transform: translateY(-5px);
      background-color: rgba(0, 255, 204, 0.2);
    }

    .path-detail {
      font-size: 0.9rem;
      color: #aaa;
      font-style: italic;
    }

    .path div:not(:last-child)::after {
      content: "↓";
      position: absolute;
      bottom: -1.5rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 1.5rem;
      color: #00ffcc;
    }

    .scrolls-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 2rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .scroll {
      background-color: rgba(255, 255, 255, 0.05);
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      max-width: 400px;
      position: relative;
      border: 1px solid rgba(255, 215, 0, 0.2);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .scroll:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(255, 215, 0, 0.1);
    }

    .scroll::before {
      content: '"';
      position: absolute;
      top: 10px;
      left: 15px;
      font-size: 4rem;
      color: rgba(255, 215, 0, 0.2);
      font-family: Georgia, serif;
      line-height: 1;
    }

    .scroll h3 {
      color: #ffd700;
      margin-bottom: 1rem;
      padding-left: 1rem;
      border-left: 3px solid #ffd700;
    }

    .scroll p {
      font-style: italic;
      line-height: 1.6;
    }

    .scroll-author {
      margin-top: 1.5rem;
      text-align: right;
      font-size: 0.9rem;
      color: #aaa;
    }

    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #a64dff 0%, #6600cc 100%); /* Purple gradient */
      color: #fff;
      padding: 15px 30px;
      text-align: center;
      border: none;
      font-size: 1.2rem;
      font-weight: bold;
      cursor: pointer;
      border-radius: 5px;
      text-decoration: none;
      transition: all 0.3s ease;
      margin-top: 2rem;
    }

    .cta-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(166, 77, 255, 0.5); /* Purple */
    }

    footer {
      text-align: center;
      padding: 2rem;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .footer-branding {
      font-size: 0.9rem;
      color: #888;
      max-width: 600px;
      margin: 0 auto;
    }

    .identity-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 40px;
      justify-content: center;
      margin-top: 40px;
      max-width: 1000px;
      margin-left: auto;
      margin-right: auto;
    }

    .identity-form, .identity-preview {
      flex: 1 1 300px;
      max-width: 400px;
      background: rgba(0, 0, 0, 0.7);
      padding: 25px;
      border: 1px solid #a64dff; /* Purple */
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(166, 77, 255, 0.3);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .identity-form:hover, .identity-preview:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(166, 77, 255, 0.4);
    }

    .identity-form label {
      display: block;
      margin-top: 15px;
      font-weight: bold;
      color: #ffd700; /* Gold */
      font-size: 1.1rem;
    }

    .identity-form input, .identity-form select {
      width: 100%;
      padding: 12px;
      margin-top: 8px;
      background: rgba(10, 10, 10, 0.8);
      border: 1px solid #444;
      color: #eee;
      border-radius: 5px;
      font-size: 1rem;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .identity-form input:focus, .identity-form select:focus {
      outline: none;
      border-color: #a64dff; /* Purple */
      box-shadow: 0 0 8px rgba(166, 77, 255, 0.5);
    }

    .identity-button {
      margin-top: 25px;
      padding: 12px 24px;
      background: linear-gradient(135deg, #a64dff 0%, #6600cc 100%); /* Purple gradient */
      color: #fff;
      font-weight: bold;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1.1rem;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      display: block;
      width: 100%;
    }

    .identity-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(166, 77, 255, 0.5);
    }

    .identity-preview h3 {
      text-align: center;
      margin-bottom: 20px;
      color: #ffd700; /* Gold */
      font-size: 1.3rem;
    }

    #identityCanvas {
      display: block;
      margin: 0 auto;
      background: #000;
      border: 1px solid #444;
      border-radius: 6px;
      box-shadow: 0 0 20px rgba(166, 77, 255, 0.5); /* Purple glow */
      transition: box-shadow 0.3s ease;
    }

    #identityCanvas:hover {
      box-shadow: 0 0 30px rgba(166, 77, 255, 0.7); /* Stronger purple glow on hover */
    }

    .sig-caption {
      text-align: center;
      font-size: 0.9rem;
      margin-top: 15px;
      color: #bbb;
      font-style: italic;
    }

    .identity-tagline {
      text-align: center;
      font-size: 1.2rem;
      color: #aaa;
      margin: 1rem 0;
      letter-spacing: 2px;
      font-style: italic;
    }

    #networkCanvas {
      width: 100%;
      max-width: 900px;
      height: 400px;
      display: block;
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid #a64dff; /* Purple */
      border-radius: 10px;
      margin: 0 auto;
    }

    .network-legend {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 1rem;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .legend-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
    }

    .sela-dot {
      background-color: #FFD700;
    }

    .identity-dot {
      background-color: #a64dff; /* Purple */
    }

    .block-dot {
      background-color: #8a2be2; /* BlueViolet */
    }

    .network-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 900px;
      margin: 0 auto 1rem;
      padding: 0 1rem;
    }

    .theme-selector {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .theme-btn {
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(166, 77, 255, 0.3); /* Purple */
      color: #f2f2f2;
      padding: 0.3rem 0.8rem;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .theme-btn:hover {
      background-color: rgba(166, 77, 255, 0.1); /* Purple */
    }

    .theme-btn.active {
      background-color: rgba(166, 77, 255, 0.2); /* Purple */
      border-color: #a64dff; /* Purple */
    }

    .network-stats {
      display: flex;
      gap: 1.5rem;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .stat-label {
      color: #aaa;
    }

    .stat-value {
      color: #a64dff; /* Purple */
      font-weight: bold;
    }

    .network-info {
      max-width: 800px;
      margin: 1.5rem auto 0;
      text-align: center;
      color: #aaa;
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
      max-width: 1200px;
      margin: 2rem auto;
    }

    .feature-card {
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(166, 77, 255, 0.3); /* Purple */
      border-radius: 8px;
      padding: 1.5rem;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(166, 77, 255, 0.2); /* Purple */
    }

    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #a64dff; /* Purple */
    }

    .wallet-tagline {
      text-align: center;
      font-size: 1.3rem;
      color: #aaa;
      margin: 0 0 2rem;
      font-style: italic;
    }

    .wallet-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      margin-top: 30px;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
      padding-bottom: 30px; /* Add padding at bottom to prevent cut-off */
    }

    .wallet-card {
      background: rgba(0, 0, 0, 0.75);
      padding: 25px;
      border: 1px solid #a64dff; /* Purple */
      border-radius: 10px;
      color: #e0e0e0;
      box-shadow: 0 0 10px rgba(166, 77, 255, 0.3);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      display: flex;
      flex-direction: column;
      height: 100%; /* Ensure consistent height */
      min-height: 380px; /* Increased minimum height to prevent overlap */
      justify-content: space-between; /* Distribute content evenly */
    }

    .wallet-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 0 20px rgba(166, 77, 255, 0.5);
    }

    .wallet-card h3 {
      margin-bottom: 15px;
      color: #ffd700; /* Gold */
      font-size: 1.4rem;
    }

    .wallet-card p {
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .wallet-card-content {
      flex-grow: 1;
      margin-bottom: 20px; /* Space between content and value/reward sections */
    }

    .wallet-card-footer {
      margin-top: auto; /* Push to bottom of card */
    }

    .wallet-value, .wallet-reward {
      background-color: rgba(0, 0, 0, 0.4);
      padding: 10px 15px;
      border-radius: 6px;
      margin-bottom: 10px;
    }

    .wallet-value {
      border-left: 3px solid #a64dff; /* Purple */
    }

    .wallet-reward {
      border-left: 3px solid #ffd700; /* Gold */
    }

    .value-label, .reward-label {
      font-weight: bold;
      display: block;
      margin-bottom: 5px;
      font-size: 0.9rem;
    }

    .value-label {
      color: #a64dff; /* Purple */
    }

    .reward-label {
      color: #ffd700; /* Gold */
    }

    .value-text, .reward-text {
      font-style: italic;
    }

    .faq-item {
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(166, 77, 255, 0.3); /* Purple */
      border-radius: 8px;
      margin-bottom: 1rem;
      overflow: hidden;
      max-width: 800px;
      width: 100%;
    }

    .faq-question {
      padding: 1.2rem;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      color: #a64dff; /* Purple */
    }

    .faq-answer {
      padding: 0 1.2rem;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease, padding 0.3s ease;
    }

    .faq-item.active .faq-answer {
      padding: 0 1.2rem 1.2rem;
      max-height: 500px;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .logo-pulse {
      width: 150px;
      height: auto;
      margin-bottom: 15px;
      animation: pulse 2s infinite;
      filter: drop-shadow(0 0 8px rgba(128, 0, 255, 0.6));
    }

    /* Logo Hero Section Styles */
    .logo-hero {
      min-height: 80vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.8);
      position: relative;
      overflow: hidden;
    }

    .logo-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(166, 77, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
      z-index: 0;
    }

    .logo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1;
      position: relative;
    }

    .hero-logo {
      width: 80%;
      max-width: 600px;
      height: auto;
      filter: drop-shadow(0 0 30px rgba(166, 77, 255, 0.8));
      animation: float-logo 6s ease-in-out infinite;
    }

    @keyframes float-logo {
      0% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-20px);
      }
      100% {
        transform: translateY(0);
      }
    }

    .logo-tagline {
      margin-top: 2rem;
      font-size: 2.5rem;
      color: #ffd700; /* Gold */
      text-align: center;
      text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
      animation: glow 3s ease-in-out infinite alternate;
    }

    @keyframes glow {
      from {
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
      }
      to {
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.9), 0 0 30px rgba(255, 215, 0, 0.7);
      }
    }

    /* Responsive styles */
    @media (max-width: 768px) {
      /* Add padding to body for fixed navbar */
      body {
        padding-top: 70px;
      }

      /* Navigation */
      .navbar {
        padding: 0.5rem 1rem;
        justify-content: flex-start;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1050;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        border-bottom: 1px solid rgba(166, 77, 255, 0.3);
      }

      .navbar-links {
        display: none;
        position: fixed;
        top: 70px; /* Height of navbar */
        left: 0;
        width: 100%;
        height: calc(100vh - 70px);
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.95);
        padding: 0;
        z-index: 1000;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.8), 0 0 30px rgba(166, 77, 255, 0.3);
        border-bottom: 2px solid #a64dff;
        border-top: 1px solid rgba(166, 77, 255, 0.3);
        overflow: hidden;
        transform: translateY(-10px);
        opacity: 0;
        transition: transform 0.3s ease, opacity 0.3s ease;
        background-image:
          linear-gradient(to bottom, rgba(166, 77, 255, 0.05) 1px, transparent 1px),
          linear-gradient(to right, rgba(166, 77, 255, 0.05) 1px, transparent 1px);
        background-size: 20px 20px;
        justify-content: flex-start;
        align-items: stretch;
        padding-top: 20px;
      }

      .navbar-links.active {
        display: flex;
        transform: translateY(0);
        opacity: 1;
      }

      .navbar-links a {
        padding: 1.2rem 2rem;
        width: 100%;
        border-bottom: 1px solid rgba(166, 77, 255, 0.2);
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        transform: translateX(-20px);
        opacity: 0;
        font-size: 1.1rem;
        letter-spacing: 1px;
        display: flex;
        align-items: center;
      }

      .navbar-links.active a {
        transform: translateX(0);
        opacity: 1;
        transition-delay: calc(0.05s * var(--item-index, 0));
      }

      .navbar-links a:last-child {
        margin-top: auto;
        margin-bottom: 30px;
        border-top: 1px solid rgba(166, 77, 255, 0.2);
      }

      .navbar-links a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background-color: #a64dff;
        transform: scaleY(0);
        transition: transform 0.3s ease;
        box-shadow: 0 0 10px rgba(166, 77, 255, 0.8);
      }

      .navbar-links a::after {
        content: '→';
        position: absolute;
        right: 20px;
        opacity: 0;
        transform: translateX(-10px);
        transition: all 0.3s ease;
        color: #ffd700;
      }

      .navbar-links a:hover {
        background-color: rgba(166, 77, 255, 0.15);
        color: #ffd700;
        padding-left: 2.5rem;
      }

      .navbar-links a:hover::before {
        transform: scaleY(1);
      }

      .navbar-links a:hover::after {
        opacity: 1;
        transform: translateX(0);
      }

      .nav-cta {
        margin: 1.5rem 2rem;
        text-align: center;
        display: block;
        background: linear-gradient(135deg, #a64dff 0%, #6600cc 100%);
        border-radius: 5px;
        box-shadow: 0 4px 15px rgba(166, 77, 255, 0.5);
        padding: 1.2rem !important;
        font-weight: bold !important;
        border: none !important;
        position: relative;
        overflow: hidden;
      }

      .nav-cta::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          to bottom right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.1) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        transform: rotate(45deg);
        animation: shimmer 3s infinite;
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%) rotate(45deg);
        }
        100% {
          transform: translateX(100%) rotate(45deg);
        }
      }

      .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 12px;
        right: 15px;
      }

      /* Typography */
      .typewriter {
        font-size: 1.2rem;
        height: 400px;
        padding: 10px;
      }

      h2 {
        font-size: 1.8rem;
      }

      h3 {
        font-size: 1.3rem;
      }

      .section-subtitle {
        font-size: 1rem;
        padding: 0 10px;
      }

      /* Layout */
      .section {
        padding: 1.5rem 1rem;
        padding-top: 70px;
      }

      /* Comparison boxes */
      .comparison {
        flex-direction: column;
        gap: 1.5rem;
      }

      .box {
        min-width: auto;
        width: 100%;
      }

      /* Feature grid and wallet grid */
      .feature-grid, .wallet-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .wallet-card {
        padding: 20px;
        min-height: 350px; /* Adjusted for mobile to prevent overlap */
      }

      .wallet-card h3 {
        font-size: 1.3rem;
      }

      .wallet-grid {
        padding-bottom: 50px; /* More padding on mobile */
      }

      /* Network visualization */
      #networkCanvas {
        height: 300px;
      }

      .network-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }

      /* Logo Hero Section */
      .logo-hero {
        min-height: 60vh;
      }

      .hero-logo {
        width: 90%;
      }

      .logo-tagline {
        font-size: 1.8rem;
      }

      .network-legend {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
      }

      /* Scrolls */
      .scrolls-container {
        flex-direction: column;
        align-items: center;
      }

      .scroll {
        width: 100%;
        max-width: 100%;
      }

      /* FAQ */
      .faq-question {
        font-size: 1rem;
        padding: 1rem;
      }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
      .navbar-logo img {
        height: 40px;
      }

      h2 {
        font-size: 1.6rem;
      }

      .typewriter {
        font-size: 1rem;
        height: 350px;
      }

      .section-header {
        margin-bottom: 2rem;
      }

      /* Logo Hero Section */
      .logo-hero {
        min-height: 50vh;
      }

      .hero-logo {
        width: 95%;
      }

      .logo-tagline {
        font-size: 1.5rem;
        margin-top: 1.5rem;
      }

      .identity-grid {
        gap: 20px;
      }

      .identity-form, .identity-preview {
        padding: 20px;
      }

      #identityCanvas {
        width: 220px;
        height: 220px;
      }

      .theme-selector {
        flex-wrap: wrap;
        justify-content: center;
      }

      .theme-btn {
        margin-top: 0.5rem;
      }

      .network-stats {
        flex-direction: column;
        gap: 0.5rem;
      }

      .cta-button {
        padding: 12px 24px;
        font-size: 1.1rem;
      }

      .wallet-card {
        padding: 15px;
        min-height: 320px; /* Adjusted for small mobile to prevent overlap */
      }

      .wallet-grid {
        padding-bottom: 60px; /* Even more padding on small mobile */
        gap: 20px; /* Smaller gap on small mobile */
      }

      .wallet-value, .wallet-reward {
        padding: 8px 12px;
      }

      .value-label, .reward-label {
        font-size: 0.8rem;
      }

      .value-text, .reward-text {
        font-size: 0.9rem;
      }

      .wallet-tagline {
        font-size: 1.1rem; /* Smaller tagline on small mobile */
        margin-bottom: 1.5rem;
      }
    }
  </style>
</head>
<body>

<!-- Particles Background -->
<div id="particles-js"></div>

<!-- Navigation -->
<nav class="navbar">
  <div class="navbar-logo">
    <a href="#roadmap" class="logo-link" id="logo-roadmap-link">
      <img src="/web3/static/onnyx_logo.png" alt="Onnyx Logo">
    </a>
  </div>
  <div class="navbar-links" id="navLinks">
    <a href="#hero">Home</a>
    <a href="#why-onnyx">Why Onnyx</a>
    <a href="#how-it-works">How It Works</a>
    <a href="#journey">Your Journey</a>
    <a href="#sela">Sela</a>
    <a href="#faq">FAQ</a>
    <a href="#cta" class="nav-cta">Get Started</a>
  </div>
  <button class="mobile-menu-btn" id="mobileMenuBtn">
    <span class="menu-icon"></span>
  </button>
</nav>

<!-- Section 1: Typing Intro -->
<section id="hero" class="section hero">
  <div class="typewriter" id="typewriter"></div>
</section>

<!-- Logo Hero Section -->
<section id="logo-hero" class="section logo-hero">
  <div class="logo-container">
    <img src="/web3/static/onnyx_logo.png" alt="Onnyx Logo" class="hero-logo">
    <!-- <h2 class="logo-tagline">A World Built on Trust</h2> -->
  </div>
</section>

<!-- Section 2: Why Onnyx (Animated Explanation) -->
<section id="why-onnyx" class="section">
  <div class="section-header">
    <h2>Why Onnyx?</h2>
    <p class="section-subtitle">A new paradigm for economic sovereignty and trust-based commerce</p>
  </div>
  <div class="comparison">
    <div class="box fiat">
      <h3>Fiat Systems</h3>
      <ul>
        <li>Inflation steals your labor</li>
        <li>Central banks control value</li>
        <li>Taxation without consent</li>
        <li>Records are editable or lost</li>
      </ul>
    </div>
    <div class="box onnyx">
      <h3>Onnyx Covenant</h3>
      <ul>
        <li>Labor logged immutably</li>
        <li>Value tied to trust and service</li>
        <li>Offerings, not extraction</li>
        <li>Proof lives across all nodes</li>
      </ul>
    </div>
  </div>
</section>

<!-- Section 3: How It Works -->
<section id="how-it-works" class="section">
  <div class="section-header">
    <h2>How Onnyx Works</h2>
    <p class="section-subtitle">The core mechanisms that power our trust-based blockchain ecosystem</p>
  </div>
  <div class="feature-grid">
    <div class="feature-card">
      <div class="feature-icon">🔐</div>
      <h3>Identity First</h3>
      <p>Unlike other blockchains that prioritize anonymous wallets, Onnyx puts your identity at the center. Your reputation, labor, and trust are all tied to your unique identity.</p>
    </div>
    <div class="feature-card">
      <div class="feature-icon">⚖️</div>
      <h3>Etzem Trust Score</h3>
      <p>Your on-chain integrity is calculated through the Etzem system, combining your Zeman hours, reputation badges, Sela registration, and governance participation.</p>
    </div>
    <div class="feature-card">
      <div class="feature-icon">⏱️</div>
      <h3>Zeman Time Credits</h3>
      <p>Track hours of service that convert into minting power, fee discounts, reputation boosts, and redeemable value in the Onnyx ecosystem.</p>
    </div>
    <div class="feature-card">
      <div class="feature-icon">🏛️</div>
      <h3>Governance by Voice</h3>
      <p>Participate in Voice Scrolls for proposals with voting weighted by Etzem score rather than token holdings, ensuring fair representation.</p>
    </div>
    <div class="feature-card">
      <div class="feature-icon">🔄</div>
      <h3>Validator Rotation</h3>
      <p>Fair selection of which Sela miner can propose the next block, using round-robin scheduling among eligible Selas with sufficient Etzem score.</p>
    </div>
    <div class="feature-card">
      <div class="feature-icon">📜</div>
      <h3>Smart Event Logging</h3>
      <p>Track block activities, transactions, token mints, proposals, and votes with weekly summaries for transparent reporting.</p>
    </div>
  </div>
</section>

<!-- Section 4: Live Network Visual -->
<section id="network" class="section">
  <div class="section-header">
    <h2>Onnyx in Motion</h2>
    <p class="section-subtitle">Watch the blockchain network come alive with real-time block creation and propagation</p>
  </div>
  <div class="network-controls">
    <div class="theme-selector">
      <span>Theme:</span>
      <button id="theme-matrix" class="theme-btn active">Matrix</button>
      <button id="theme-tribal" class="theme-btn">Tribal</button>
      <button id="theme-light" class="theme-btn">Light</button>
    </div>
    <div class="network-stats">
      <div class="stat-item">
        <span class="stat-label">Blocks:</span>
        <span id="block-count" class="stat-value">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Last Block:</span>
        <span id="last-block-time" class="stat-value">--:--</span>
      </div>
    </div>
  </div>
  <canvas id="networkCanvas"></canvas>
  <div class="network-legend">
    <div class="legend-item">
      <span class="legend-dot sela-dot"></span>
      <span>Sela Businesses</span>
    </div>
    <div class="legend-item">
      <span class="legend-dot identity-dot"></span>
      <span>Identities</span>
    </div>
    <div class="legend-item">
      <span class="legend-dot block-dot"></span>
      <span>Blocks</span>
    </div>
  </div>
  <div class="network-info">
    <p>This visualization represents the Onnyx blockchain network in real-time. Watch as new blocks are created by Sela validators and propagate through the network of identities.</p>
    <p>Hover over nodes to see their details. Each pulse represents a new block being added to the chain.</p>
  </div>
</section>

<!-- Section 5: Covenant Path -->
<section id="journey" class="section">
  <div class="section-header">
    <h2>Your Covenant Journey</h2>
    <p class="section-subtitle">The path to becoming a full participant in the Onnyx ecosystem</p>
  </div>
  <div class="path">
    <div>1️⃣ Identity Creation <span class="path-detail">Create your unique, soulbound identity</span></div>
    <div>2️⃣ Nation Selection <span class="path-detail">Join one of the twelve tribes</span></div>
    <div>3️⃣ Sela Establishment <span class="path-detail">Register your business on the blockchain</span></div>
    <div>4️⃣ Token Interaction <span class="path-detail">Mint, transfer, and utilize tokens</span></div>
    <div>5️⃣ Voice & Governance <span class="path-detail">Participate in ecosystem decisions</span></div>
  </div>
</section>

<!-- Section 6: Sela Business Registry -->
<section id="sela" class="section">
  <div class="section-header">
    <h2>Sela Business Registry</h2>
    <p class="section-subtitle">Register your business on the Onnyx blockchain and gain access to powerful token creation and community building tools</p>
  </div>

  <div class="comparison">
    <div class="box fiat">
      <h3>Traditional Business</h3>
      <ul>
        <li>Expensive registration fees</li>
        <li>Complex regulatory compliance</li>
        <li>Limited control over customer relationships</li>
        <li>Centralized payment processors</li>
        <li>No immutable proof of labor</li>
      </ul>
    </div>
    <div class="box onnyx">
      <h3>Onnyx Sela</h3>
      <ul>
        <li>Simple ONX token stake to register</li>
        <li>Transparent reputation system</li>
        <li>Direct community engagement</li>
        <li>Mint your own tokens for loyalty, equity, or community</li>
        <li>Permanent record of all work and transactions</li>
      </ul>
    </div>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <h3>Sela Tiers Based on Etzem Score</h3>
    <div class="sela-tiers-container">
      <div class="feature-card">
        <h4>Bronze Sela</h4>
        <p>Entry level for new businesses</p>
        <p>Requires: 100 ONX stake</p>
        <p>Benefits: Basic token minting</p>
      </div>
      <div class="feature-card">
        <h4>Silver Sela</h4>
        <p>Established businesses</p>
        <p>Requires: 500 ONX stake + 50 Etzem</p>
        <p>Benefits: Enhanced minting limits</p>
      </div>
      <div class="feature-card">
        <h4>Gold Sela</h4>
        <p>Trusted community pillars</p>
        <p>Requires: 1000 ONX stake + 200 Etzem</p>
        <p>Benefits: Validator eligibility</p>
      </div>
    </div>
    <p class="coming-soon-text">More information about Sela tiers and benefits coming soon</p>
  </div>
</section>



<!-- Section 8: Scroll Testimonies -->
<section id="testimonies" class="section">
  <div class="section-header">
    <h2>Scrolls of the People</h2>
    <p class="section-subtitle">Hear from those who have already joined the Onnyx covenant</p>
  </div>
  <div class="scrolls-container">
    <article class="scroll">
      <h3>Sarah of Zebulun</h3>
      <p>"Onnyx let me run my business in peace. My records speak louder than Caesar's threats."</p>
      <div class="scroll-author">Bakery Owner • Member since 2024</div>
    </article>
    <article class="scroll">
      <h3>David of Judah</h3>
      <p>"I build furniture — Onnyx builds trust. My work now lives forever on-chain."</p>
      <div class="scroll-author">Craftsman • Member since 2023</div>
    </article>
    <article class="scroll">
      <h3>Rachel of Asher</h3>
      <p>"My customers love the loyalty tokens I created. It's brought my community closer together."</p>
      <div class="scroll-author">Café Owner • Member since 2024</div>
    </article>
  </div>
</section>

<!-- Section 9: Wallet & Token System -->
<section id="wallet" class="section">
  <div class="section-header">
    <h2>💼 Onnyx Wallet</h2>
    <p class="section-subtitle">Your command deck in the covenant economy</p>
  </div>
  <p class="wallet-tagline">More than just storage — your wallet reflects your identity, tracks your labor, and earns you influence</p>
  <div style="margin-bottom: 20px;"></div> <!-- Extra spacing before wallet grid -->

  <div class="wallet-grid">
    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>🧬 Identity Hub</h3>
        <p>Manage your soulbound identity, nation affiliation, tribal role, and Etzem (trust score). Everything begins with who you are — not what you have.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Anchors you in the covenant system</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Etzem reputation begins</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>🔁 Token Exchange</h3>
        <p>Send and receive ONX and Mikvah tokens. Interact with tokens created by Selas. Every transaction is a vote of trust and contribution.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Powers local/tribal commerce</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Transaction history = trust</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>⏳ Zeman Tracker</h3>
        <p>Log hours of service (labor, teaching, building, caregiving) and accumulate Zeman credits — the fuel for access, reputation, and reward.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Non-monetary value tracking</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Unlocks roles, rewards, access</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>🌱 Value Contribution Logs</h3>
        <p>Submit proof of value brought to the ecosystem: jobs completed, services rendered, communities supported. These logs build your standing and unlock new opportunities.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Grows network utility</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Badges, tokens, reputation boosts</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>🪙 Staking + Role Activation</h3>
        <p>Stake ONX to become a VALIDATOR, CREATOR, or MENTOR. These roles earn you influence in governance and a share of ecosystem growth.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Shows commitment to the ecosystem</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Voting power, block rewards</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>🏅 Reward Claim Center</h3>
        <p>Claim tokens, badges, and unlocked perks for verified labor, referrals, teaching others, or helping scale the covenant economy.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Encourages contribution</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Airdrops, token mint shares</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>📦 Service Contracts</h3>
        <p>Issue, accept, and complete labor/service contracts on-chain. Prove your contribution and get paid in ONX, Zeman, or Sela tokens.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Trustless peer commerce</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Escrowed ONX/Zeman payout</span>
        </div>
      </div>
    </div>

    <div class="wallet-card">
      <div class="wallet-card-content">
        <h3>📊 Impact Dashboard</h3>
        <p>View your total value contributed, impact zones, and tribe-based leaderboard. Track your covenant legacy in real-time.</p>
      </div>
      <div class="wallet-card-footer">
        <div class="wallet-value">
          <span class="value-label">Value Added:</span>
          <span class="value-text">Creates accountability + pride</span>
        </div>
        <div class="wallet-reward">
          <span class="reward-label">Reward:</span>
          <span class="reward-text">Visible to community/governance</span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Section 10: FAQ -->
<section id="faq" class="section">
  <div class="section-header">
    <h2>Frequently Asked Questions</h2>
    <p class="section-subtitle">Answers to common questions about the Onnyx ecosystem</p>
  </div>

  <div class="faq-item">
    <div class="faq-question">
      What makes Onnyx different from other blockchains?
      <span class="toggle">+</span>
    </div>
    <div class="faq-answer">
      <p>Onnyx is identity-centric rather than wallet-centric. Your reputation, labor, and trust are all tied to your unique identity. We focus on building a parallel economy based on trust and service rather than speculation and anonymity.</p>
    </div>
  </div>

  <div class="faq-item">
    <div class="faq-question">
      What is a Sela and why should I create one?
      <span class="toggle">+</span>
    </div>
    <div class="faq-answer">
      <p>A Sela is a business registry on the Onnyx blockchain. By creating a Sela, you can mint your own tokens for loyalty programs, equity, or community building. Selas also gain the ability to participate in validation and governance as they build reputation.</p>
    </div>
  </div>

  <div class="faq-item">
    <div class="faq-question">
      How do I earn Etzem trust score?
      <span class="toggle">+</span>
    </div>
    <div class="faq-answer">
      <p>Etzem is calculated by combining your Zeman hours (time/labor), reputation badges, Sela registration, token activity, governance participation, and account longevity. The more you contribute positively to the ecosystem, the higher your Etzem score will be.</p>
    </div>
  </div>

  <div class="faq-item">
    <div class="faq-question">
      What are Voice Scrolls?
      <span class="toggle">+</span>
    </div>
    <div class="faq-answer">
      <p>Voice Scrolls are governance proposals in the Onnyx ecosystem. Unlike other systems where voting power is determined by token holdings, Onnyx weights votes by Etzem score, ensuring that those who have demonstrated trust and service have more influence.</p>
    </div>
  </div>

  <div class="faq-item">
    <div class="faq-question">
      How does the Council of Twelve Tribes work?
      <span class="toggle">+</span>
    </div>
    <div class="faq-answer">
      <p>The Council of Twelve Tribes assigns seats to specific tribes with councilors having soulbound roles and special voting powers in governance. Council members are elected based on their Etzem score and contributions to the ecosystem.</p>
    </div>
  </div>
</section>

<!-- Section 11: Goals & Roadmap -->
<section id="roadmap" class="section">
  <div class="section-header">
    <h2>🛤️ The Road Ahead</h2>
    <p class="section-subtitle">Onnyx Covenant Timeline</p>
  </div>
  <div class="timeline">

    <div class="milestone">
      <div class="marker">✅</div>
      <div class="details">
        <h3>Phase 0 — Covenant Drafted</h3>
        <p>Onnyx core principles established: soulbound identity, labor-based tokens, and tribal jurisdiction.</p>
        <div class="milestone-date">Completed</div>
      </div>
    </div>

    <div class="milestone">
      <div class="marker">🧱</div>
      <div class="details">
        <h3>Phase 1 — Identity Engine + SQL Chain</h3>
        <p>Users can create identities linked to heritage. All identities logged via SQL and propagated across the Onnyx blockchain.</p>
        <div class="milestone-date">In Progress</div>
      </div>
    </div>

    <div class="milestone">
      <div class="marker">🏗️</div>
      <div class="details">
        <h3>Phase 2 — Sela Business Registry</h3>
        <p>Verified users launch businesses on-chain as Selas, operating under covenant law and tribal protection.</p>
        <div class="milestone-date">Q3 2025</div>
      </div>
    </div>

    <div class="milestone">
      <div class="marker">💰</div>
      <div class="details">
        <h3>Phase 3 — Wallet + Token Engine</h3>
        <p>Users receive Onnyx Wallets to store ONX and Mikvah tokens. All tokens reflect verified labor or covenant contributions.</p>
        <div class="milestone-date">Q4 2025</div>
      </div>
    </div>

    <div class="milestone">
      <div class="marker">📱</div>
      <div class="details">
        <h3>Phase 4 — Mobile Node Deployment</h3>
        <p>Users connect from mobile devices, operate personal Onnyx nodes, and verify labor logs without centralized infrastructure.</p>
        <div class="milestone-date">Q1 2026</div>
      </div>
    </div>

    <div class="milestone">
      <div class="marker">🗳️</div>
      <div class="details">
        <h3>Phase 5 — Voice Scrolls + Tribal Governance</h3>
        <p>Identities gain voting weight via Etzem (reputation), and proposals are passed through tribe-based consensus.</p>
        <div class="milestone-date">Q2 2026</div>
      </div>
    </div>

    <div class="milestone">
      <div class="marker">🛰️</div>
      <div class="details">
        <h3>Phase 6 — Cross-Nation Economic Channels</h3>
        <p>Value flows between Selas across nations. Convert wages to ONX, operate sovereignly from anywhere.</p>
        <div class="milestone-date">Q3 2026</div>
      </div>
    </div>

  </div>
</section>

<!-- Section 12: Call to Action -->
<section id="cta" class="section">
  <div class="section-header">
    <h2>🧬 Your Soulbound Identity</h2>
    <p class="section-subtitle">Before value can flow, before tokens can transfer — there must be an identity</p>
  </div>
  <p class="identity-tagline">Yours is not random. It is soulbound. Immutable. Unclonable. Yours alone.</p>

  <a class="cta-button" href="/web3/identity/create.html">Create Your Identity</a>
  <p class="cta-note">No personal information required. Just a name and a commitment to the covenant.</p>
</section>

<!-- Footer -->
<footer>
  <div class="footer-links">
    <div class="footer-section">
      <h4>Explore</h4>
      <ul>
        <li><a href="#why-onnyx">Why Onnyx</a></li>
        <li><a href="#how-it-works">How It Works</a></li>
        <li><a href="#journey">Your Journey</a></li>
        <li><a href="#sela">Sela Registry</a></li>
      </ul>
    </div>
    <div class="footer-section">
      <h4>Resources</h4>
      <ul>
        <li><a href="/docs">Documentation</a></li>
        <li><a href="/api">API Reference</a></li>
        <li><a href="/tutorials">Tutorials</a></li>
        <li><a href="/faq">FAQ</a></li>
      </ul>
    </div>
    <div class="footer-section">
      <h4>Community</h4>
      <ul>
        <li><a href="/forum">Forum</a></li>
        <li><a href="/discord">Discord</a></li>
        <li><a href="/github">GitHub</a></li>
        <li><a href="/events">Events</a></li>
      </ul>
    </div>
  </div>

  <div class="footer-logo">
    <img src="/web3/static/onnyx_logo.png" alt="Onnyx Logo" class="logo-pulse">
    <div class="footer-branding">© 2025 Onnyx — "Render unto Caesar what is Caesar's, and unto God what is God's."</div>
    <div class="footer-tagline">Building a world of trust, one block at a time.</div>
  </div>
</footer>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script src="/web3/static/scripts/network-visual.js" defer></script>
<script>
  // Function to scroll to any section
  function scrollToSection(event, sectionId) {
    event.preventDefault();
    console.log('Attempting to scroll to section: ' + sectionId);

    const section = document.getElementById(sectionId);
    if (section) {
      console.log('Section found with ID: ' + sectionId);

      // Use element.scrollIntoView instead of window.scrollTo
      section.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Apply offset for fixed header after scrolling
      setTimeout(function() {
        window.scrollBy(0, -80);
      }, 500);
    } else {
      console.error('Section not found: ' + sectionId);
    }
  }

  // Function to scroll to roadmap section
  function scrollToRoadmap(event) {
    scrollToSection(event, 'roadmap');
  }
</script>
<script>
  // Typing effect logic
  document.addEventListener('DOMContentLoaded', function() {
    const lines = [
      "Hello World, without end.",
      "This is not a system for the powerful — but for the faithful.",
      "Let us rebuild each nation where trust is the only currency.",
      "Where every act is recorded in heaven and hashed on earth.",
      "Where every token honors labor, not leverage.",
      "Where Selas rise as sanctuaries of service.",
      "Where identity is soulbound, not for sale.",
      "Where the world ends in Babylon, but begins again in Onnyx."
    ];

    const typeEl = document.getElementById('typewriter');
    if (!typeEl) return;

    let lineIndex = 0;
    let charIndex = 0;
    let currentLine = '';
    let typingSpeed = 40;

    function typeLine() {
      if (lineIndex < lines.length) {
        if (charIndex < lines[lineIndex].length) {
          currentLine += lines[lineIndex][charIndex];
          typeEl.innerText = currentLine;
          charIndex++;
          setTimeout(typeLine, typingSpeed);
        } else {
          currentLine += '\n';
          charIndex = 0;
          lineIndex++;
          setTimeout(typeLine, 600);
        }
      }
    }

    typeLine();

    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navLinks = document.getElementById('navLinks');

    if (mobileMenuBtn && navLinks) {
      // Set item index for staggered animation
      const navLinksItems = navLinks.querySelectorAll('a');
      navLinksItems.forEach((link, index) => {
        link.style.setProperty('--item-index', index);
      });

      // Toggle menu when hamburger icon is clicked
      mobileMenuBtn.addEventListener('click', function(event) {
        event.stopPropagation(); // Prevent document click from immediately closing menu
        navLinks.classList.toggle('active');
        mobileMenuBtn.classList.toggle('active'); // Toggle active class for animation
      });

      // Close menu when a link is clicked
      navLinksItems.forEach(link => {
        link.addEventListener('click', function() {
          navLinks.classList.remove('active');
          mobileMenuBtn.classList.remove('active');
        });
      });

      // Close menu when clicking outside
      document.addEventListener('click', function(event) {
        if (!event.target.closest('.navbar') ||
            (!event.target.closest('.navbar-links') && !event.target.closest('.mobile-menu-btn'))) {
          navLinks.classList.remove('active');
          mobileMenuBtn.classList.remove('active');
        }
      });
    }

    // We're now using the inline onclick handler for the logo link

    // FAQ toggle
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');

      question.addEventListener('click', () => {
        // Close all other items
        faqItems.forEach(otherItem => {
          if (otherItem !== item) {
            otherItem.classList.remove('active');
            const toggle = otherItem.querySelector('.toggle');
            if (toggle) toggle.textContent = '+';
          }
        });

        // Toggle current item
        item.classList.toggle('active');
        const toggle = item.querySelector('.toggle');
        if (toggle) {
          toggle.textContent = item.classList.contains('active') ? '−' : '+';
        }
      });
    });
  });

  // Initialize particles
  particlesJS("particles-js", {
    particles: {
      number: { value: 60, density: { enable: true, value_area: 800 } },
      color: { value: "#a64dff" }, // Purple
      shape: { type: "circle" },
      opacity: { value: 0.4 },
      size: { value: 3, random: true },
      line_linked: {
        enable: true,
        distance: 120,
        color: "#a64dff", // Purple
        opacity: 0.5,
        width: 1
      },
      move: { enable: true, speed: 1.2 }
    },
    interactivity: {
      events: {
        onhover: { enable: true, mode: "grab" }
      },
      modes: {
        grab: { distance: 150, line_linked: { opacity: 0.6 } }
      }
    },
    retina_detect: true
  });

  // Add scroll animations
  document.addEventListener('DOMContentLoaded', function() {
    // We're now using inline onclick handlers for smooth scrolling

    // Get all sections
    const sections = document.querySelectorAll('.section');

    // Create an intersection observer for sections
    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe each section
    sections.forEach(section => {
      if (section.id !== 'hero') { // Don't animate the hero section
        section.style.opacity = 0;
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        sectionObserver.observe(section);
      }
    });

    // Timeline animation
    const milestones = document.querySelectorAll('.milestone');

    // Create an intersection observer for timeline milestones
    const timelineObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          // Add a delay between each milestone animation
          const index = Array.from(milestones).indexOf(entry.target);
          setTimeout(() => {
            entry.target.style.opacity = 1;
            entry.target.style.transform = 'translateY(0)';
          }, index * 200); // 200ms delay between each milestone
        }
      });
    }, {
      threshold: 0.2,
      rootMargin: '0px 0px -100px 0px' // Trigger a bit earlier
    });

    // Observe each milestone
    milestones.forEach(milestone => {
      timelineObserver.observe(milestone);
    });
  });
</script>

<style>
  /* Additional styles for the enhanced footer */
  .footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 2rem;
  }

  .footer-section {
    min-width: 150px;
  }

  .footer-section h4 {
    color: #a64dff; /* Purple */
    margin-bottom: 1rem;
  }

  .footer-section ul {
    list-style: none;
    padding: 0;
  }

  .footer-section ul li {
    margin-bottom: 0.5rem;
  }

  .footer-section ul li a {
    color: #f2f2f2;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .footer-section ul li a:hover {
    color: #00ffcc;
  }

  .footer-logo {
    text-align: center;
  }

  .footer-tagline {
    font-size: 0.9rem;
    color: #aaa;
    margin-top: 0.5rem;
  }

  /* Timeline Roadmap styles */
  .timeline {
    position: relative;
    margin: 40px auto;
    max-width: 800px;
    padding-left: 40px;
    border-left: 2px solid #a64dff; /* Purple */
  }

  .milestone {
    margin-bottom: 60px;
    position: relative;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  .milestone.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .marker {
    position: absolute;
    left: -60px;
    top: 0;
    background: #a64dff; /* Purple */
    color: #fff;
    font-size: 1.2rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 15px rgba(166, 77, 255, 0.6);
    z-index: 2;
  }

  .details {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(166, 77, 255, 0.2); /* Purple */
    border-radius: 8px;
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .details:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 15px rgba(166, 77, 255, 0.3); /* Purple */
  }

  .details h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #ffd700;
    font-size: 1.4rem;
  }

  .details p {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 15px;
  }

  .milestone-date {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.5);
    color: #a64dff; /* Purple */
    font-weight: bold;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(166, 77, 255, 0.3); /* Purple */
  }

  /* CTA styles */
  .identity-tagline {
    text-align: center;
    font-size: 1.2rem;
    color: #aaa;
    margin: 1rem 0 2rem;
    letter-spacing: 2px;
    font-style: italic;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-note {
    text-align: center;
    color: #aaa;
    margin-top: 1.5rem;
    font-style: italic;
    font-size: 0.9rem;
  }

  @media (max-width: 768px) {
    .footer-links {
      flex-direction: column;
      gap: 1.5rem;
      text-align: center;
      padding: 0 1rem;
    }

    .footer-section {
      margin-bottom: 1.5rem;
    }

    .footer-logo {
      margin-top: 1rem;
    }

    .timeline {
      padding-left: 30px;
    }

    .marker {
      left: -45px;
      width: 30px;
      height: 30px;
      font-size: 1rem;
    }

    .details {
      padding: 1.2rem;
    }

    .details h3 {
      font-size: 1.2rem;
    }
  }
</style>



<!-- Dark Animations Script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script src="/web3/static/dark-animations.js"></script>

<!-- Add animation classes to elements -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to hero elements
    const heroElements = document.querySelectorAll('h2, .section-subtitle');
    heroElements.forEach((element, index) => {
      element.classList.add('hero-animate');
    });

    // Add card animation classes
    const cards = document.querySelectorAll('.box, .scroll, .identity-form, .identity-preview');
    cards.forEach((card) => {
      card.classList.add('card-animate', 'tilt-card');
    });

    // Add floating effect to the logo
    const logo = document.querySelector('.navbar-logo img');
    if (logo) {
      logo.classList.add('float-element');
    }

    // Add scroll animation to sections
    const sections = document.querySelectorAll('.section > div');
    sections.forEach((section) => {
      section.classList.add('scroll-animate');
    });

    // Add glowing effect to buttons
    const buttons = document.querySelectorAll('.cta-button, .identity-button');
    buttons.forEach((button) => {
      button.classList.add('glow-element');
    });

    // Add typing effect to taglines
    const taglines = document.querySelectorAll('.section-subtitle');
    taglines.forEach((tagline) => {
      tagline.classList.add('typing-effect');
    });
  });
</script>

</body>
</html>