"""
Onnyx Updated Models Tests

This module provides tests for the updated Onnyx models.
"""

import sys
import os
import unittest
import json
import time
import uuid

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.identity import Identity
from models.token import Token
from models.transaction import Transaction
from models.sela import Sela
from models.etzem import Etzem
from models.activity import Activity
from models.service import Service
from models.vote import Vote
from models.rotation import Rotation
from models.chain_parameter import ChainParameter
from models.voice_scroll import VoiceScroll
from tests.test_helpers import OnnyxTestCase

class TestUpdatedModels(OnnyxTestCase):
    """
    Test case for the updated Onnyx models.
    """

    def setUp(self):
        """
        Set up the test environment.
        """
        # Call the parent setUp method to set up the test environment
        super().setUp()

    def test_identity_model(self):
        """Test the Identity model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key",
            metadata={
                "type": "individual",
                "description": "Test identity"
            }
        )

        # Get the identity
        retrieved_identity = Identity.get_by_id("test_identity_id")

        # Check the identity
        self.assertIsNotNone(retrieved_identity)
        self.assertEqual(retrieved_identity.identity_id, "test_identity_id")
        self.assertEqual(retrieved_identity.name, "Test Identity")
        self.assertEqual(retrieved_identity.public_key, "test_public_key")
        self.assertEqual(retrieved_identity.metadata["type"], "individual")
        self.assertEqual(retrieved_identity.metadata["description"], "Test identity")

        # Update the identity
        identity.name = "Updated Identity"
        identity.save()

        # Get the updated identity
        updated_identity = Identity.get_by_id("test_identity_id")

        # Check the updated identity
        self.assertEqual(updated_identity.name, "Updated Identity")

        # Get all identities
        identities = Identity.get_all()

        # Check that there's at least one identity
        self.assertGreaterEqual(len(identities), 1)

        # Convert to dictionary
        identity_dict = identity.to_dict()

        # Check the dictionary
        self.assertEqual(identity_dict["identity_id"], "test_identity_id")
        self.assertEqual(identity_dict["name"], "Updated Identity")

    def test_token_model(self):
        """Test the Token model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a token
        token = Token.create(
            token_id="test_token_id",
            name="Test Token",
            symbol="TST",
            creator_id=identity.identity_id,
            supply=1000,
            category="test",
            decimals=2,
            metadata={
                "description": "Test token"
            }
        )

        # Get the token
        retrieved_token = Token.get_by_id("test_token_id")

        # Check the token
        self.assertIsNotNone(retrieved_token)
        self.assertEqual(retrieved_token.token_id, "test_token_id")
        self.assertEqual(retrieved_token.name, "Test Token")
        self.assertEqual(retrieved_token.symbol, "TST")
        self.assertEqual(retrieved_token.creator_id, identity.identity_id)
        self.assertEqual(retrieved_token.supply, 1000)
        self.assertEqual(retrieved_token.category, "test")
        self.assertEqual(retrieved_token.decimals, 2)
        self.assertEqual(retrieved_token.metadata["description"], "Test token")

        # Update the token
        token.name = "Updated Token"
        token.save()

        # Get the updated token
        updated_token = Token.get_by_id("test_token_id")

        # Check the updated token
        self.assertEqual(updated_token.name, "Updated Token")

        # Get all tokens
        tokens = Token.get_all()

        # Check that there's at least one token
        self.assertGreaterEqual(len(tokens), 1)

        # Get tokens by creator
        creator_tokens = Token.find_by_creator(identity.identity_id)

        # Check that there's at least one token by the creator
        self.assertGreaterEqual(len(creator_tokens), 1)

        # Convert to dictionary
        token_dict = token.to_dict()

        # Check the dictionary
        self.assertEqual(token_dict["token_id"], "test_token_id")
        self.assertEqual(token_dict["name"], "Updated Token")

    def test_transaction_model(self):
        """Test the Transaction model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a token
        token = Token.create(
            token_id="test_token_id",
            name="Test Token",
            symbol="TST",
            creator_id=identity.identity_id,
            supply=1000
        )

        # Create a transaction
        transaction = Transaction.create(
            tx_id="test_tx_id",
            tx_type="TEST",
            sender=identity.identity_id,
            recipient=identity.identity_id,
            token_id=token.token_id,
            amount=100,
            status="PENDING",
            data={
                "op": "OP_TEST",
                "description": "Test transaction"
            }
        )

        # Get the transaction
        retrieved_tx = Transaction.get_by_id("test_tx_id")

        # Check the transaction
        self.assertIsNotNone(retrieved_tx)
        self.assertEqual(retrieved_tx.tx_id, "test_tx_id")
        self.assertEqual(retrieved_tx.tx_type, "TEST")
        self.assertEqual(retrieved_tx.sender, identity.identity_id)
        self.assertEqual(retrieved_tx.recipient, identity.identity_id)
        self.assertEqual(retrieved_tx.token_id, token.token_id)
        self.assertEqual(retrieved_tx.amount, 100)
        self.assertEqual(retrieved_tx.status, "PENDING")
        self.assertEqual(retrieved_tx.data["op"], "OP_TEST")
        self.assertEqual(retrieved_tx.data["description"], "Test transaction")

        # Update the transaction
        transaction.status = "CONFIRMED"
        transaction.save()

        # Get the updated transaction
        updated_tx = Transaction.get_by_id("test_tx_id")

        # Check the updated transaction
        self.assertEqual(updated_tx.status, "CONFIRMED")

        # Get all transactions
        transactions = Transaction.get_all()

        # Check that there's at least one transaction
        self.assertGreaterEqual(len(transactions), 1)

        # Get transactions by sender
        sender_txs = Transaction.find_by_sender(identity.identity_id)

        # Check that there's at least one transaction by the sender
        self.assertGreaterEqual(len(sender_txs), 1)

        # Get transactions by status
        status_txs = Transaction.find_by_status("CONFIRMED")

        # Check that there's at least one transaction with the status
        self.assertGreaterEqual(len(status_txs), 1)

        # Convert to dictionary
        tx_dict = transaction.to_dict()

        # Check the dictionary
        self.assertEqual(tx_dict["tx_id"], "test_tx_id")
        self.assertEqual(tx_dict["status"], "CONFIRMED")

    def test_sela_model(self):
        """Test the Sela model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a Sela
        sela = Sela.create(
            sela_id="test_sela_id",
            name="Test Sela",
            founder_id=identity.identity_id,
            sela_type="BUSINESS",
            metadata={
                "description": "Test Sela"
            }
        )

        # Get the Sela
        retrieved_sela = Sela.get_by_id("test_sela_id")

        # Check the Sela
        self.assertIsNotNone(retrieved_sela)
        self.assertEqual(retrieved_sela.sela_id, "test_sela_id")
        self.assertEqual(retrieved_sela.name, "Test Sela")
        self.assertEqual(retrieved_sela.founder_id, identity.identity_id)
        self.assertEqual(retrieved_sela.sela_type, "BUSINESS")
        self.assertEqual(retrieved_sela.metadata["description"], "Test Sela")

        # Update the Sela
        sela.name = "Updated Sela"
        sela.save()

        # Get the updated Sela
        updated_sela = Sela.get_by_id("test_sela_id")

        # Check the updated Sela
        self.assertEqual(updated_sela.name, "Updated Sela")

        # Get all Selas
        selas = Sela.get_all()

        # Check that there's at least one Sela
        self.assertGreaterEqual(len(selas), 1)

        # Get Selas by founder
        founder_selas = Sela.find_by_founder(identity.identity_id)

        # Check that there's at least one Sela by the founder
        self.assertGreaterEqual(len(founder_selas), 1)

        # Add a service
        sela.add_service("Test Service")

        # Check that the service was added
        self.assertIn("Test Service", sela.services)

        # Remove the service
        sela.remove_service("Test Service")

        # Check that the service was removed
        self.assertNotIn("Test Service", sela.services)

        # Add a member
        sela.add_member(identity.identity_id, "MEMBER")

        # Check that the member was added
        self.assertIn(identity.identity_id, sela.members)
        self.assertEqual(sela.roles[identity.identity_id], "MEMBER")

        # Update the member's role
        sela.update_role(identity.identity_id, "ADMIN")

        # Check that the role was updated
        self.assertEqual(sela.roles[identity.identity_id], "ADMIN")

        # Remove the member
        sela.remove_member(identity.identity_id)

        # Check that the member was removed
        self.assertNotIn(identity.identity_id, sela.members)
        self.assertNotIn(identity.identity_id, sela.roles)

        # Convert to dictionary
        sela_dict = sela.to_dict()

        # Check the dictionary
        self.assertEqual(sela_dict["sela_id"], "test_sela_id")
        self.assertEqual(sela_dict["name"], "Updated Sela")

    def test_etzem_model(self):
        """Test the Etzem model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create an Etzem score
        etzem = Etzem.create(
            identity_id=identity.identity_id,
            etzem_score=100,
            components={
                "reputation": 50,
                "activity": 50
            },
            badges=["CONTRIBUTOR"]
        )

        # Get the Etzem score
        retrieved_etzem = Etzem.get_by_id(identity.identity_id)

        # Check the Etzem score
        self.assertIsNotNone(retrieved_etzem)
        self.assertEqual(retrieved_etzem.identity_id, identity.identity_id)
        self.assertEqual(retrieved_etzem.etzem_score, 100)
        self.assertEqual(retrieved_etzem.components["reputation"], 50)
        self.assertEqual(retrieved_etzem.components["activity"], 50)
        self.assertIn("CONTRIBUTOR", retrieved_etzem.badges)

        # Update the Etzem score
        etzem.update_score(150, {"reputation": 75, "activity": 75})

        # Get the updated Etzem score
        updated_etzem = Etzem.get_by_id(identity.identity_id)

        # Check the updated Etzem score
        self.assertEqual(updated_etzem.etzem_score, 150)
        self.assertEqual(updated_etzem.components["reputation"], 75)
        self.assertEqual(updated_etzem.components["activity"], 75)

        # Add a badge
        etzem.add_badge("MENTOR")

        # Check that the badge was added
        self.assertIn("MENTOR", etzem.badges)

        # Remove a badge
        etzem.remove_badge("CONTRIBUTOR")

        # Check that the badge was removed
        self.assertNotIn("CONTRIBUTOR", etzem.badges)

        # Get all Etzem scores
        etzem_scores = Etzem.get_all()

        # Check that there's at least one Etzem score
        self.assertGreaterEqual(len(etzem_scores), 1)

        # Get the Etzem leaderboard
        leaderboard = Etzem.get_leaderboard()

        # Check that there's at least one Etzem score in the leaderboard
        self.assertGreaterEqual(len(leaderboard), 1)

        # Convert to dictionary
        etzem_dict = etzem.to_dict()

        # Check the dictionary
        self.assertEqual(etzem_dict["identity_id"], identity.identity_id)
        self.assertEqual(etzem_dict["etzem_score"], 150)

    def test_activity_model(self):
        """Test the Activity model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a Sela
        sela = Sela.create(
            sela_id="test_sela_id",
            name="Test Sela",
            founder_id=identity.identity_id,
            sela_type="BUSINESS"
        )

        # Create an Activity
        activity = Activity.create(
            activity_id="test_activity_id",
            sela_id=sela.sela_id,
            activity_type="TEST",
            description="Test activity",
            metadata={
                "key": "value"
            }
        )

        # Get the Activity
        retrieved_activity = Activity.get_by_id("test_activity_id")

        # Check the Activity
        self.assertIsNotNone(retrieved_activity)
        self.assertEqual(retrieved_activity.activity_id, "test_activity_id")
        self.assertEqual(retrieved_activity.sela_id, sela.sela_id)
        self.assertEqual(retrieved_activity.activity_type, "TEST")
        self.assertEqual(retrieved_activity.description, "Test activity")
        self.assertEqual(retrieved_activity.metadata["key"], "value")

        # Get all Activities
        activities = Activity.get_all()

        # Check that there's at least one Activity
        self.assertGreaterEqual(len(activities), 1)

        # Get Activities by Sela
        sela_activities = Activity.find_by_sela(sela.sela_id)

        # Check that there's at least one Activity by the Sela
        self.assertGreaterEqual(len(sela_activities), 1)

        # Get Activities by type
        type_activities = Activity.find_by_type("TEST")

        # Check that there's at least one Activity of the type
        self.assertGreaterEqual(len(type_activities), 1)

        # Convert to dictionary
        activity_dict = activity.to_dict()

        # Check the dictionary
        self.assertEqual(activity_dict["activity_id"], "test_activity_id")
        self.assertEqual(activity_dict["sela_id"], sela.sela_id)
        self.assertEqual(activity_dict["activity_type"], "TEST")

    def test_service_model(self):
        """Test the Service model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a Sela
        sela = Sela.create(
            sela_id="test_sela_id",
            name="Test Sela",
            founder_id=identity.identity_id,
            sela_type="BUSINESS"
        )

        # Create a Service
        service = Service.create(
            service_id="test_service_id",
            sela_id=sela.sela_id,
            service_type="TEST",
            description="Test service",
            recipient_id=identity.identity_id,
            duration=60,
            metadata={
                "key": "value"
            }
        )

        # Get the Service
        retrieved_service = Service.get_by_id("test_service_id")

        # Check the Service
        self.assertIsNotNone(retrieved_service)
        self.assertEqual(retrieved_service.service_id, "test_service_id")
        self.assertEqual(retrieved_service.sela_id, sela.sela_id)
        self.assertEqual(retrieved_service.service_type, "TEST")
        self.assertEqual(retrieved_service.description, "Test service")
        self.assertEqual(retrieved_service.recipient_id, identity.identity_id)
        self.assertEqual(retrieved_service.duration, 60)
        self.assertEqual(retrieved_service.metadata["key"], "value")

        # Get all Services
        services = Service.get_all()

        # Check that there's at least one Service
        self.assertGreaterEqual(len(services), 1)

        # Get Services by Sela
        sela_services = Service.find_by_sela(sela.sela_id)

        # Check that there's at least one Service by the Sela
        self.assertGreaterEqual(len(sela_services), 1)

        # Get Services by type
        type_services = Service.find_by_type("TEST")

        # Check that there's at least one Service of the type
        self.assertGreaterEqual(len(type_services), 1)

        # Get Services by recipient
        recipient_services = Service.find_by_recipient(identity.identity_id)

        # Check that there's at least one Service for the recipient
        self.assertGreaterEqual(len(recipient_services), 1)

        # Convert to dictionary
        service_dict = service.to_dict()

        # Check the dictionary
        self.assertEqual(service_dict["service_id"], "test_service_id")
        self.assertEqual(service_dict["sela_id"], sela.sela_id)
        self.assertEqual(service_dict["service_type"], "TEST")
        self.assertEqual(service_dict["recipient_id"], identity.identity_id)
        self.assertEqual(service_dict["duration"], 60)

    def test_vote_model(self):
        """Test the Vote model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a Sela
        sela = Sela.create(
            sela_id="test_sela_id",
            name="Test Sela",
            founder_id=identity.identity_id,
            sela_type="BUSINESS"
        )

        # Create a Voice Scroll
        scroll = VoiceScroll.create(
            scroll_id="test_scroll_id",
            creator_id=identity.identity_id,
            title="Test Scroll",
            description="Test Voice Scroll",
            category="test",
            expiry_days=7
        )

        # Create a Vote
        vote = Vote.create(
            vote_id="test_vote_id",
            scroll_id=scroll.scroll_id,
            sela_id=sela.sela_id,
            identity_id=identity.identity_id,
            vote="yes",
            reason="Test reason"
        )

        # Get the Vote
        retrieved_vote = Vote.get_by_id("test_vote_id")

        # Check the Vote
        self.assertIsNotNone(retrieved_vote)
        self.assertEqual(retrieved_vote.vote_id, "test_vote_id")
        self.assertEqual(retrieved_vote.scroll_id, scroll.scroll_id)
        self.assertEqual(retrieved_vote.sela_id, sela.sela_id)
        self.assertEqual(retrieved_vote.identity_id, identity.identity_id)
        self.assertEqual(retrieved_vote.vote, "yes")
        self.assertEqual(retrieved_vote.reason, "Test reason")

        # Get all Votes
        votes = Vote.get_all()

        # Check that there's at least one Vote
        self.assertGreaterEqual(len(votes), 1)

        # Get Votes by Scroll
        scroll_votes = Vote.find_by_scroll(scroll.scroll_id)

        # Check that there's at least one Vote for the Scroll
        self.assertGreaterEqual(len(scroll_votes), 1)

        # Get Votes by Sela
        sela_votes = Vote.find_by_sela(sela.sela_id)

        # Check that there's at least one Vote by the Sela
        self.assertGreaterEqual(len(sela_votes), 1)

        # Get Votes by identity
        identity_votes = Vote.find_by_identity(identity.identity_id)

        # Check that there's at least one Vote by the identity
        self.assertGreaterEqual(len(identity_votes), 1)

        # Convert to dictionary
        vote_dict = vote.to_dict()

        # Check the dictionary
        self.assertEqual(vote_dict["vote_id"], "test_vote_id")
        self.assertEqual(vote_dict["scroll_id"], scroll.scroll_id)
        self.assertEqual(vote_dict["sela_id"], sela.sela_id)
        self.assertEqual(vote_dict["identity_id"], identity.identity_id)
        self.assertEqual(vote_dict["vote"], "yes")
        self.assertEqual(vote_dict["reason"], "Test reason")

    def test_rotation_model(self):
        """Test the Rotation model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a Sela
        sela = Sela.create(
            sela_id="test_sela_id",
            name="Test Sela",
            founder_id=identity.identity_id,
            sela_type="BUSINESS"
        )

        # Create a Rotation
        rotation = Rotation.create(
            id="test_rotation_id",
            current_validator=sela.sela_id,
            queue=[sela.sela_id],
            update_interval=3600,
            min_etzem_score=100,
            required_badges=["VALIDATOR"]
        )

        # Get the Rotation
        retrieved_rotation = Rotation.get_by_id("test_rotation_id")

        # Check the Rotation
        self.assertIsNotNone(retrieved_rotation)
        self.assertEqual(retrieved_rotation.id, "test_rotation_id")
        self.assertEqual(retrieved_rotation.current_validator, sela.sela_id)
        self.assertEqual(retrieved_rotation.queue, [sela.sela_id])
        self.assertEqual(retrieved_rotation.update_interval, 3600)
        self.assertEqual(retrieved_rotation.min_etzem_score, 100)
        self.assertEqual(retrieved_rotation.required_badges, ["VALIDATOR"])

        # Update the queue
        rotation.update_queue([sela.sela_id, "another_sela_id"])

        # Get the updated Rotation
        updated_rotation = Rotation.get_by_id("test_rotation_id")

        # Check the updated Rotation
        self.assertEqual(updated_rotation.queue, [sela.sela_id, "another_sela_id"])

        # Set the current validator
        rotation.set_current_validator("another_sela_id")

        # Get the updated Rotation
        updated_rotation = Rotation.get_by_id("test_rotation_id")

        # Check the updated Rotation
        self.assertEqual(updated_rotation.current_validator, "another_sela_id")

        # Set the update interval
        rotation.set_update_interval(7200)

        # Get the updated Rotation
        updated_rotation = Rotation.get_by_id("test_rotation_id")

        # Check the updated Rotation
        self.assertEqual(updated_rotation.update_interval, 7200)

        # Set the minimum Etzem score
        rotation.set_min_etzem_score(200)

        # Get the updated Rotation
        updated_rotation = Rotation.get_by_id("test_rotation_id")

        # Check the updated Rotation
        self.assertEqual(updated_rotation.min_etzem_score, 200)

        # Set the required badges
        rotation.set_required_badges(["VALIDATOR", "GUARDIAN"])

        # Get the updated Rotation
        updated_rotation = Rotation.get_by_id("test_rotation_id")

        # Check the updated Rotation
        self.assertEqual(updated_rotation.required_badges, ["VALIDATOR", "GUARDIAN"])

        # Get the next validator
        next_validator = rotation.get_next_validator(1)

        # Check the next validator
        self.assertEqual(next_validator, sela.sela_id)

        # Check if a Sela is a valid proposer
        is_valid = rotation.is_valid_proposer(sela.sela_id, 1)

        # Check the result
        self.assertTrue(is_valid)

        # Convert to dictionary
        rotation_dict = rotation.to_dict()

        # Check the dictionary
        self.assertEqual(rotation_dict["id"], "test_rotation_id")
        self.assertEqual(rotation_dict["current_validator"], "another_sela_id")
        self.assertEqual(rotation_dict["queue"], [sela.sela_id, "another_sela_id"])
        self.assertEqual(rotation_dict["update_interval"], 7200)
        self.assertEqual(rotation_dict["min_etzem_score"], 200)
        self.assertEqual(rotation_dict["required_badges"], ["VALIDATOR", "GUARDIAN"])

    def test_chain_parameter_model(self):
        """Test the ChainParameter model."""
        # Create a ChainParameter
        parameter = ChainParameter.create(
            key="test_param",
            value=100,
            default_value=50,
            description="Test parameter",
            category="test"
        )

        # Get the ChainParameter
        retrieved_param = ChainParameter.get_by_id("test_param")

        # Check the ChainParameter
        self.assertIsNotNone(retrieved_param)
        self.assertEqual(retrieved_param.key, "test_param")
        self.assertEqual(retrieved_param.value, 100)
        self.assertEqual(retrieved_param.default_value, 50)
        self.assertEqual(retrieved_param.description, "Test parameter")
        self.assertEqual(retrieved_param.category, "test")

        # Update the ChainParameter
        parameter.update(200, "test_identity_id")

        # Get the updated ChainParameter
        updated_param = ChainParameter.get_by_id("test_param")

        # Check the updated ChainParameter
        self.assertEqual(updated_param.value, 200)
        self.assertEqual(updated_param.last_updated_by, "test_identity_id")

        # Reset the ChainParameter
        parameter.reset()

        # Get the reset ChainParameter
        reset_param = ChainParameter.get_by_id("test_param")

        # Check the reset ChainParameter
        self.assertEqual(reset_param.value, 50)

        # Get all ChainParameters
        parameters = ChainParameter.get_all()

        # Check that there's at least one ChainParameter
        self.assertGreaterEqual(len(parameters), 1)

        # Get ChainParameters by category
        category_params = ChainParameter.find_by_category("test")

        # Check that there's at least one ChainParameter in the category
        self.assertGreaterEqual(len(category_params), 1)

        # Convert to dictionary
        param_dict = parameter.to_dict()

        # Check the dictionary
        self.assertEqual(param_dict["key"], "test_param")
        self.assertEqual(param_dict["value"], 50)
        self.assertEqual(param_dict["default_value"], 50)
        self.assertEqual(param_dict["description"], "Test parameter")
        self.assertEqual(param_dict["category"], "test")

    def test_voice_scroll_model(self):
        """Test the VoiceScroll model."""
        # Create an identity
        identity = Identity.create(
            identity_id="test_identity_id",
            name="Test Identity",
            public_key="test_public_key"
        )

        # Create a VoiceScroll
        scroll = VoiceScroll.create(
            scroll_id="test_scroll_id",
            creator_id=identity.identity_id,
            title="Test Scroll",
            description="Test Voice Scroll",
            category="test",
            expiry_days=7,
            effect={
                "param": "test_param",
                "value": "test_value"
            }
        )

        # Get the VoiceScroll
        retrieved_scroll = VoiceScroll.get_by_id("test_scroll_id")

        # Check the VoiceScroll
        self.assertIsNotNone(retrieved_scroll)
        self.assertEqual(retrieved_scroll.scroll_id, "test_scroll_id")
        self.assertEqual(retrieved_scroll.creator_id, identity.identity_id)
        self.assertEqual(retrieved_scroll.title, "Test Scroll")
        self.assertEqual(retrieved_scroll.description, "Test Voice Scroll")
        self.assertEqual(retrieved_scroll.category, "test")
        self.assertEqual(retrieved_scroll.status, "active")
        self.assertEqual(retrieved_scroll.effect["param"], "test_param")
        self.assertEqual(retrieved_scroll.effect["value"], "test_value")

        # Add a vote
        scroll.add_vote(identity.identity_id, "yes")

        # Get the updated VoiceScroll
        updated_scroll = VoiceScroll.get_by_id("test_scroll_id")

        # Check the updated VoiceScroll
        self.assertEqual(updated_scroll.votes[identity.identity_id], "yes")

        # Tally the votes
        tally = scroll.tally_votes()

        # Check the tally
        self.assertEqual(tally["yes"], 1)
        self.assertEqual(tally["no"], 0)
        self.assertEqual(tally["abstain"], 0)
        self.assertEqual(tally["total"], 1)

        # Resolve the scroll
        scroll.resolve("passed")

        # Get the resolved VoiceScroll
        resolved_scroll = VoiceScroll.get_by_id("test_scroll_id")

        # Check the resolved VoiceScroll
        self.assertEqual(resolved_scroll.status, "resolved")
        self.assertEqual(resolved_scroll.outcome, "passed")
        self.assertIsNotNone(resolved_scroll.resolved_at)

        # Get all VoiceScrolls
        scrolls = VoiceScroll.get_all()

        # Check that there's at least one VoiceScroll
        self.assertGreaterEqual(len(scrolls), 1)

        # Get VoiceScrolls by status
        status_scrolls = VoiceScroll.find_by_status("resolved")

        # Check that there's at least one VoiceScroll with the status
        self.assertGreaterEqual(len(status_scrolls), 1)

        # Get VoiceScrolls by category
        category_scrolls = VoiceScroll.find_by_category("test")

        # Check that there's at least one VoiceScroll in the category
        self.assertGreaterEqual(len(category_scrolls), 1)

        # Get VoiceScrolls by creator
        creator_scrolls = VoiceScroll.find_by_creator(identity.identity_id)

        # Check that there's at least one VoiceScroll by the creator
        self.assertGreaterEqual(len(creator_scrolls), 1)

        # Get VoiceScrolls by outcome
        outcome_scrolls = VoiceScroll.find_by_outcome("passed")

        # Check that there's at least one VoiceScroll with the outcome
        self.assertGreaterEqual(len(outcome_scrolls), 1)

        # Convert to dictionary
        scroll_dict = scroll.to_dict()

        # Check the dictionary
        self.assertEqual(scroll_dict["scroll_id"], "test_scroll_id")
        self.assertEqual(scroll_dict["creator_id"], identity.identity_id)
        self.assertEqual(scroll_dict["title"], "Test Scroll")
        self.assertEqual(scroll_dict["description"], "Test Voice Scroll")
        self.assertEqual(scroll_dict["category"], "test")
        self.assertEqual(scroll_dict["status"], "resolved")
        self.assertEqual(scroll_dict["outcome"], "passed")
        self.assertEqual(scroll_dict["effect"]["param"], "test_param")
        self.assertEqual(scroll_dict["effect"]["value"], "test_value")

if __name__ == "__main__":
    unittest.main()
