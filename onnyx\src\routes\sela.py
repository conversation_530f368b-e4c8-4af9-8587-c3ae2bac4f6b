# src/routes/sela.py

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from src.tokens.ledger import TokenLedger
from src.tokens.registry import TokenRegistry
from src.business.sela import SelaRegistry
from src.business.stake_policy import calculate_sela_stake, get_stake_tiers, get_tier_for_reputation, get_network_maturity
from src.identity.registry import IdentityRegistry
from src.chain.txlog import TxLogger
from src.chain.mempool import Mempool

# Initialize components
sela_router = APIRouter(prefix="/sela", tags=["sela"])
ledger = TokenLedger()
token_registry = TokenRegistry()
sela_registry = SelaRegistry()
identity_registry = IdentityRegistry()
txlog = TxLogger()
mempool = Mempool()

# Request models
class RegisterSelaRequest(BaseModel):
    identity_id: str
    name: str
    sector: str
    metadata: Optional[Dict[str, Any]] = {}
    message: str
    signature: str

class UpdateSelaRequest(BaseModel):
    identity_id: str
    field: str
    value: Any
    message: str
    signature: str

class AddMemberRequest(BaseModel):
    sela_id: str
    identity_id: str
    added_by: str
    message: str
    signature: str

class RemoveMemberRequest(BaseModel):
    sela_id: str
    identity_id: str
    removed_by: str
    message: str
    signature: str

# Routes
@sela_router.post("/register")
def register_sela(req: RegisterSelaRequest):
    """
    Register a new Sela (business entity).
    
    Requires staking ONX tokens, with the amount determined by the identity's reputation.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # Calculate reputation score
    rep_score = sum(identity.reputation.values()) if identity.reputation else 0
    
    # Calculate stake required
    network_maturity = get_network_maturity()
    stake_required = calculate_sela_stake(rep_score, network_maturity)
    
    # Get stake tier
    tier_id, tier = get_tier_for_reputation(rep_score)
    
    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "name": req.name,
        "sector": req.sector,
        "metadata": req.metadata,
        "stake_required": stake_required,
        "tier": tier_id
    }
    mempool_tx = mempool.add("register_sela", payload)
    
    try:
        # Register the Sela
        sela = sela_registry.register_sela(
            identity_id=req.identity_id,
            name=req.name,
            sector=req.sector,
            stake_required=stake_required,
            ledger=ledger,
            identity_registry=identity_registry
        )
        
        # Add metadata
        if req.metadata:
            sela_registry.update_sela(req.identity_id, "metadata", req.metadata)
            sela = sela_registry.get_by_owner(req.identity_id)
        
        # Record the transaction
        txid = txlog.record("register_sela", {
            "sela_id": sela["id"],
            "identity_id": req.identity_id,
            "name": req.name,
            "sector": req.sector,
            "stake": stake_required,
            "tier": tier_id
        })
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        # Add tier information to the response
        sela["tier"] = {
            "id": tier_id,
            "name": tier["name"],
            "benefits": tier["benefits"]
        }
        
        return {
            "status": "success",
            "txid": txid,
            "sela": sela
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@sela_router.get("/list")
def list_selas(
    sector: Optional[str] = None,
    owner: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
):
    """
    List all registered Selas, optionally filtered by sector or owner.
    """
    selas = sela_registry.get_all()
    
    # Apply filters
    if sector:
        selas = [s for s in selas if s["sector"] == sector]
    
    if owner:
        selas = [s for s in selas if s["owner"] == owner]
    
    # Sort by creation time (newest first)
    selas = sorted(selas, key=lambda s: s["created_at"], reverse=True)
    
    # Apply pagination
    total = len(selas)
    selas = selas[offset:offset+limit]
    
    # Add tier information to each Sela
    for sela in selas:
        identity = identity_registry.get_identity(sela["owner"])
        if identity:
            rep_score = sum(identity.reputation.values()) if identity.reputation else 0
            tier_id, tier = get_tier_for_reputation(rep_score)
            sela["tier"] = {
                "id": tier_id,
                "name": tier["name"]
            }
    
    return {
        "total": total,
        "selas": selas
    }

@sela_router.get("/get/{sela_id}")
def get_sela(sela_id: str):
    """
    Get a Sela by its ID.
    """
    sela = sela_registry.get_by_id(sela_id)
    if not sela:
        raise HTTPException(status_code=404, detail="Sela not found")
    
    # Add tier information
    identity = identity_registry.get_identity(sela["owner"])
    if identity:
        rep_score = sum(identity.reputation.values()) if identity.reputation else 0
        tier_id, tier = get_tier_for_reputation(rep_score)
        sela["tier"] = {
            "id": tier_id,
            "name": tier["name"],
            "benefits": tier["benefits"]
        }
    
    return sela

@sela_router.post("/update")
def update_sela(req: UpdateSelaRequest):
    """
    Update a Sela's information.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # Check if the identity has a Sela
    sela = sela_registry.get_by_owner(req.identity_id)
    if not sela:
        raise HTTPException(status_code=404, detail="Sela not found for this identity")
    
    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "field": req.field,
        "value": req.value
    }
    mempool_tx = mempool.add("update_sela", payload)
    
    try:
        # Update the Sela
        sela = sela_registry.update_sela(req.identity_id, req.field, req.value)
        
        # Record the transaction
        txid = txlog.record("update_sela", {
            "sela_id": sela["id"],
            "identity_id": req.identity_id,
            "field": req.field,
            "value": req.value
        })
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "txid": txid,
            "sela": sela
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@sela_router.post("/add_member")
def add_member(req: AddMemberRequest):
    """
    Add a member to a Sela.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # Verify Sela
    sela = sela_registry.get_by_id(req.sela_id)
    if not sela:
        raise HTTPException(status_code=404, detail="Sela not found")
    
    # Verify that the adder is the owner or a member
    if req.added_by != sela["owner"] and req.added_by not in sela["members"]:
        raise HTTPException(status_code=403, detail="Only the owner or members can add new members")
    
    # Add to mempool
    payload = {
        "sela_id": req.sela_id,
        "identity_id": req.identity_id,
        "added_by": req.added_by
    }
    mempool_tx = mempool.add("add_member", payload)
    
    try:
        # Add the member
        sela = sela_registry.add_member(req.sela_id, req.identity_id, identity_registry)
        
        # Record the transaction
        txid = txlog.record("add_member", {
            "sela_id": req.sela_id,
            "identity_id": req.identity_id,
            "added_by": req.added_by
        })
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "txid": txid,
            "sela": sela
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@sela_router.post("/remove_member")
def remove_member(req: RemoveMemberRequest):
    """
    Remove a member from a Sela.
    """
    # Verify Sela
    sela = sela_registry.get_by_id(req.sela_id)
    if not sela:
        raise HTTPException(status_code=404, detail="Sela not found")
    
    # Verify that the remover is the owner
    if req.removed_by != sela["owner"]:
        raise HTTPException(status_code=403, detail="Only the owner can remove members")
    
    # Add to mempool
    payload = {
        "sela_id": req.sela_id,
        "identity_id": req.identity_id,
        "removed_by": req.removed_by
    }
    mempool_tx = mempool.add("remove_member", payload)
    
    try:
        # Remove the member
        sela = sela_registry.remove_member(req.sela_id, req.identity_id)
        
        # Record the transaction
        txid = txlog.record("remove_member", {
            "sela_id": req.sela_id,
            "identity_id": req.identity_id,
            "removed_by": req.removed_by
        })
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "txid": txid,
            "sela": sela
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@sela_router.get("/tiers")
def get_tiers():
    """
    Get the stake tiers for Sela registration.
    """
    return get_stake_tiers()
