#!/usr/bin/env python
# mine_block.py

import asyncio
import argparse
import logging
import sys
import time
import random
import uuid
from src.node.blockchain import local_blockchain
from src.node.server import node_server
from src.chain.mempool import Mempool
from models.transaction import Transaction

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("onnyx.mine_block")

async def mine_block():
    """
    Mine a new block and broadcast it to peers.
    """
    # Initialize the mempool
    mempool = Mempool()

    # Get transactions from the mempool
    mempool_txs = mempool.get_all()

    # Get transaction IDs
    tx_ids = []
    for mempool_tx in mempool_txs:
        # Create a transaction from the mempool transaction
        tx = Transaction.create(
            tx_id=f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}",
            timestamp=int(time.time()),
            op=mempool_tx.op,
            data=mempool_tx.data,
            sender=mempool_tx.sender,
            signature=mempool_tx.signature
        )
        tx_ids.append(tx.tx_id)

    # Get the miner identity
    # In a real implementation, this would be the node's identity
    miner_id = "MINER_1"

    # Create a new block
    block = local_blockchain.create_block(tx_ids, miner_id)

    # In a real implementation, we would add a proof of work here
    # For now, we'll just simulate it
    logger.info(f"Mining block {block.block_height}...")
    time.sleep(2)  # Simulate mining

    logger.info(f"Mined block {block.block_height} with hash {block.block_hash}")

    # Broadcast the block to peers
    success = await node_server.broadcast_new_block(block)

    if success:
        logger.info(f"Block {block.block_height} successfully broadcast to peers")

        # Clear the mempool
        for mempool_tx in mempool_txs:
            mempool.remove(mempool_tx.tx_id)

        logger.info(f"Cleared {len(mempool_txs)} transactions from mempool")
    else:
        logger.error(f"Failed to broadcast block {block.block_height}")

    return block

async def main():
    """
    Main entry point for the mining script.
    """
    parser = argparse.ArgumentParser(description="Mine a new block")
    parser.add_argument("--transactions", type=int, default=5, help="Number of dummy transactions to include")

    args = parser.parse_args()

    # Start the node server
    server_task = asyncio.create_task(node_server.start())

    # Wait for the server to start
    await asyncio.sleep(2)

    # Create dummy transactions if needed
    mempool = Mempool()
    if args.transactions > 0 and len(mempool.get_all()) < args.transactions:
        logger.info(f"Creating {args.transactions} dummy transactions")

        for i in range(args.transactions):
            op = random.choice(["OP_SEND", "OP_MINT", "OP_BURN"])
            sender = f"user_{random.randint(1, 100)}"
            data = {
                "from_id": sender,
                "to_id": f"user_{random.randint(1, 100)}",
                "amount": random.randint(1, 1000),
                "token_id": f"token_{random.randint(1, 10)}"
            }
            signature = f"sig_{uuid.uuid4().hex[:16]}"

            mempool.add(op, data, sender, signature)

    # Mine a block
    try:
        block = await mine_block()
        logger.info(f"Mined block: {block.block_height} with hash {block.block_hash}")
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, stopping mining")
    except Exception as e:
        logger.error(f"Error mining block: {str(e)}")
    finally:
        # Stop the node server
        await node_server.stop()

        # Cancel the server task
        server_task.cancel()
        try:
            await server_task
        except asyncio.CancelledError:
            pass

if __name__ == "__main__":
    asyncio.run(main())
