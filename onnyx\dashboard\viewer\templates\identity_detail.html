{% extends "base.html" %}

{% block title %}Identity Details - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="detail-header">
    <div>
        <h1 class="detail-title">{{ identity.name }}</h1>
        <div class="detail-subtitle">{{ identity.id }}</div>
    </div>
</div>

<div class="detail-meta">
    {% if identity.created_at %}
    <div class="meta-item">
        <i class="fas fa-calendar"></i>
        <span>Created: {{ format_timestamp(identity.created_at) }}</span>
    </div>
    {% endif %}
    
    {% if identity.reputation is defined %}
    <div class="meta-item">
        <i class="fas fa-star"></i>
        <span>Reputation: {{ identity.reputation }}</span>
    </div>
    {% endif %}
    
    {% if identity.tokens_created is defined %}
    <div class="meta-item">
        <i class="fas fa-coins"></i>
        <span>Tokens Created: {{ identity.tokens_created }}</span>
    </div>
    {% endif %}
</div>

<div class="detail-section">
    <h2 class="detail-section-title">Identity Information</h2>
    
    <div class="property-list">
        <div class="property-item">
            <div class="property-label">ID</div>
            <div class="property-value">{{ identity.id }}</div>
        </div>
        
        <div class="property-item">
            <div class="property-label">Name</div>
            <div class="property-value">{{ identity.name }}</div>
        </div>
        
        {% if identity.public_key %}
        <div class="property-item">
            <div class="property-label">Public Key</div>
            <div class="property-value">{{ identity.public_key }}</div>
        </div>
        {% endif %}
        
        {% if identity.address %}
        <div class="property-item">
            <div class="property-label">Address</div>
            <div class="property-value">{{ identity.address }}</div>
        </div>
        {% endif %}
        
        {% if identity.reputation is defined %}
        <div class="property-item">
            <div class="property-label">Reputation</div>
            <div class="property-value">{{ identity.reputation }}</div>
        </div>
        {% endif %}
        
        {% if identity.badges %}
        <div class="property-item">
            <div class="property-label">Badges</div>
            <div class="property-value">
                {% for badge in identity.badges %}
                <span class="badge">{{ badge }}</span>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% if identity.metadata %}
<div class="detail-section">
    <h2 class="detail-section-title">Metadata</h2>
    
    <div class="property-list">
        {% for key, value in identity.metadata.items() %}
        <div class="property-item">
            <div class="property-label">{{ key }}</div>
            <div class="property-value">{{ value }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<div class="detail-section">
    <h2 class="detail-section-title">Related Transactions</h2>
    
    <table>
        <thead>
            <tr>
                <th>Transaction ID</th>
                <th>Block</th>
                <th>Type</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            {% for tx in related_txs %}
            <tr>
                <td><a href="{{ url_for('tx_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a></td>
                <td>{{ tx.block }}</td>
                <td>
                    <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
                </td>
                <td>{{ format_timestamp(tx.timestamp) }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="4" style="text-align: center;">No related transactions found</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<div class="detail-section">
    <h2 class="detail-section-title">Raw Identity Data</h2>
    
    <div class="json-viewer">{{ identity|tojson(indent=2) }}</div>
</div>
{% endblock %}
