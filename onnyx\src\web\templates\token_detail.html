{% extends 'base.html' %}

{% block title %}Onnyx Explorer - Token Details{% endblock %}

{% block content %}
    <h1>Token Details: {{ token.name }} ({{ token.symbol }})</h1>

    <div class="token-detail">
        <div class="detail-section">
            <h2>Basic Information</h2>
            <table>
                <tr>
                    <th>Token ID</th>
                    <td>{{ token.token_id }}</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>{{ token.name }}</td>
                </tr>
                <tr>
                    <th>Symbol</th>
                    <td>{{ token.symbol }}</td>
                </tr>
                <tr>
                    <th>Type</th>
                    <td>{{ token.metadata.category if token.metadata and token.metadata.category is defined else (token.token_type.value if token.token_type and token.token_type.value is defined else token.token_type) }}</td>
                </tr>
                <tr>
                    <th>Creator</th>
                    <td>
                        <a href="{{ url_for('identity_detail', identity_id=token.creator_id) }}">
                            {{ token.creator_id }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <th>Total Supply</th>
                    <td>{{ token.supply }}</td>
                </tr>
                <tr>
                    <th>Mintable</th>
                    <td>{{ 'Yes' if token.metadata and token.metadata.mintable else 'No' }}</td>
                </tr>
                <tr>
                    <th>Transferable</th>
                    <td>{{ 'Yes' if token.metadata and token.metadata.transferable else 'No' }}</td>
                </tr>
            </table>
        </div>

        <div class="detail-section">
            <h2>Metadata</h2>
            <pre>{{ token.metadata | tojson(indent=2) }}</pre>
        </div>
    </div>

    <div class="detail-section">
        <h2>Token Transactions</h2>
        {% if transactions %}
            <table>
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Time</th>
                        <th>Details</th>
                        <th>Transaction ID</th>
                    </tr>
                </thead>
                <tbody>
                    {% for tx in transactions %}
                        <tr>
                            <td>{{ tx.type }}</td>
                            <td>{{ tx.timestamp | format_timestamp }}</td>
                            <td>
                                {% if tx.type == 'forktoken' %}
                                    Created by
                                    <a href="{{ url_for('identity_detail', identity_id=tx.payload.creator_identity) }}">
                                        {{ tx.payload.creator_identity }}
                                    </a>
                                {% elif tx.type == 'minttoken' %}
                                    Minted {{ tx.payload.amount }} tokens to
                                    <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">
                                        {{ tx.payload.to_address }}
                                    </a>
                                {% elif tx.type == 'sendtoken' %}
                                    Sent {{ tx.payload.amount }} tokens from
                                    <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">
                                        {{ tx.payload.from_address }}
                                    </a> to
                                    <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">
                                        {{ tx.payload.to_address }}
                                    </a>
                                {% elif tx.type == 'burntoken' %}
                                    Burned {{ tx.payload.amount }} tokens from
                                    <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">
                                        {{ tx.payload.from_address }}
                                    </a>
                                {% else %}
                                    <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">View details</a>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">{{ tx.txid }}</a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>No transactions found for this token.</p>
        {% endif %}
    </div>

    <div class="actions">
        <a href="{{ url_for('tokens') }}" class="button">Back to Tokens</a>
    </div>
{% endblock %}

{% block head %}
<style>
    .token-detail {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 20px;
    }

    .detail-section {
        background-color: #222;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .detail-section h2 {
        margin-top: 0;
        border-bottom: 1px solid #444;
        padding-bottom: 10px;
    }

    .actions {
        margin-top: 20px;
        display: flex;
        gap: 10px;
    }

    .button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #0f0;
        color: #111;
        border-radius: 4px;
        font-weight: bold;
        text-decoration: none;
    }

    .button:hover {
        background-color: #00cc00;
        text-decoration: none;
    }

    @media (min-width: 768px) {
        .token-detail {
            grid-template-columns: 1fr 1fr;
        }
    }
</style>
{% endblock %}
