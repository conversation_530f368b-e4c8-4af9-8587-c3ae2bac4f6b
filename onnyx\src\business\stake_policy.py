# src/business/stake_policy.py

def calculate_sela_stake(identity_reputation_score, network_maturity=1.0):
    """
    Calculate the stake required for Sela registration based on identity reputation score and network maturity.
    
    Stake required increases as network matures and reputation decreases.
    
    Args:
        identity_reputation_score: The reputation score of the identity.
        network_maturity: A factor representing the maturity of the network (1.0 = new, 2.0 = mature).
        
    Returns:
        The amount of ONX tokens required for staking.
    """
    base = 100  # base ONX stake
    
    # Apply reputation-based adjustments
    if identity_reputation_score >= 100:
        reputation_factor = 0.5  # 50% discount for high reputation
    elif identity_reputation_score >= 50:
        reputation_factor = 1.0  # standard rate for medium reputation
    else:
        reputation_factor = 2.0  # 2x penalty for low reputation
    
    # Apply network maturity factor
    # As the network matures, the base stake increases
    maturity_factor = network_maturity
    
    # Calculate final stake amount
    stake_amount = base * reputation_factor * maturity_factor
    
    return int(stake_amount)  # Return as integer

def get_stake_tiers():
    """
    Get the stake tiers for Sela registration.
    
    Returns:
        A dictionary of stake tiers with reputation score ranges and stake amounts.
    """
    return {
        "tier1": {
            "name": "Platinum",
            "reputation_range": (100, float('inf')),
            "stake_amount": calculate_sela_stake(100),
            "benefits": [
                "Priority transaction processing",
                "Reduced fees",
                "Governance voting rights",
                "Access to exclusive network features"
            ]
        },
        "tier2": {
            "name": "Gold",
            "reputation_range": (50, 99),
            "stake_amount": calculate_sela_stake(50),
            "benefits": [
                "Standard transaction processing",
                "Standard fees",
                "Governance voting rights"
            ]
        },
        "tier3": {
            "name": "Silver",
            "reputation_range": (0, 49),
            "stake_amount": calculate_sela_stake(0),
            "benefits": [
                "Standard transaction processing",
                "Standard fees"
            ]
        }
    }

def get_tier_for_reputation(reputation_score):
    """
    Get the stake tier for a given reputation score.
    
    Args:
        reputation_score: The reputation score of the identity.
        
    Returns:
        The stake tier for the given reputation score.
    """
    tiers = get_stake_tiers()
    
    for tier_id, tier in tiers.items():
        min_rep, max_rep = tier["reputation_range"]
        if min_rep <= reputation_score <= max_rep:
            return tier_id, tier
    
    # Default to the lowest tier if no match is found
    return "tier3", tiers["tier3"]

def get_network_maturity():
    """
    Calculate the current network maturity factor.
    
    In a real implementation, this would be based on network metrics like:
    - Total number of identities
    - Total number of tokens
    - Total transaction volume
    - Network age
    
    For now, we'll return a fixed value.
    
    Returns:
        The network maturity factor.
    """
    # For now, return a fixed value
    # In the future, this could be calculated based on network metrics
    return 1.0
