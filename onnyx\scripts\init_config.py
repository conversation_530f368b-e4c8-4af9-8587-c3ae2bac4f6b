#!/usr/bin/env python3
"""
Onnyx Configuration Initialization Script

This script initializes the Onnyx configuration system by migrating existing
configuration files to the new format.
"""

import os
import sys
import json
import shutil
import logging
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config.config import OnnyxConfig

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("onnyx.scripts.init_config")

def ensure_directory(path):
    """
    Ensure a directory exists.
    
    Args:
        path: Path to the directory
    """
    os.makedirs(path, exist_ok=True)
    logger.info(f"Ensured directory exists: {path}")

def migrate_config_file(src_path, dest_path):
    """
    Migrate a configuration file from the old location to the new location.
    
    Args:
        src_path: Path to the source file
        dest_path: Path to the destination file
    """
    if os.path.exists(src_path) and os.path.getsize(src_path) > 0:
        # Ensure the destination directory exists
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        # Copy the file
        shutil.copy2(src_path, dest_path)
        logger.info(f"Migrated configuration file: {src_path} -> {dest_path}")
    else:
        logger.warning(f"Source file does not exist or is empty: {src_path}")

def main():
    """
    Main function to initialize the Onnyx configuration system.
    """
    # Define paths
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    config_dir = os.path.join(root_dir, 'config')
    data_dir = os.path.join(root_dir, 'data')
    runtime_dir = os.path.join(root_dir, 'runtime')
    
    # Ensure directories exist
    ensure_directory(config_dir)
    ensure_directory(data_dir)
    ensure_directory(runtime_dir)
    ensure_directory(os.path.join(runtime_dir, 'blockchain'))
    ensure_directory(os.path.join(runtime_dir, 'mempool'))
    ensure_directory(os.path.join(runtime_dir, 'identities'))
    ensure_directory(os.path.join(runtime_dir, 'tokens'))
    ensure_directory(os.path.join(runtime_dir, 'logs'))
    
    # Migrate configuration files
    migrate_config_file(os.path.join(data_dir, 'chain_params.json'), os.path.join(config_dir, 'chain_params.json'))
    migrate_config_file(os.path.join(config_dir, 'network_params.json'), os.path.join(config_dir, 'network_params.json'))
    migrate_config_file(os.path.join(data_dir, 'node_config.json'), os.path.join(config_dir, 'node_params.json'))
    migrate_config_file(os.path.join(data_dir, 'sela_config.json'), os.path.join(config_dir, 'sela_params.json'))
    
    # Initialize the OnnyxConfig
    config = OnnyxConfig(config_dir=config_dir, data_dir=runtime_dir)
    
    # Save the configurations to ensure they're in the correct format
    config.save_chain_params()
    config.save_network_params()
    config.save_node_params()
    config.save_sela_params()
    
    logger.info("Onnyx configuration system initialized successfully.")

if __name__ == "__main__":
    main()
