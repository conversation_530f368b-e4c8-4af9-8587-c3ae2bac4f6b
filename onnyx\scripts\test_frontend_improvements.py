#!/usr/bin/env python3
"""
ONNYX Frontend Visual Consistency & Mobile Responsiveness Test Suite

Tests the implementation of:
- Core Technologies section visual hierarchy consistency
- Mobile navigation improvements
- Responsive footer enhancements
- Onyx Stone theme consistency
"""

import os
import sys
import requests
import re
from pathlib import Path

def test_css_improvements():
    """Test CSS improvements for visual consistency and mobile responsiveness."""
    print("🎨 TESTING FRONTEND VISUAL CONSISTENCY & MOBILE RESPONSIVENESS")
    print("=" * 70)
    
    results = []
    
    # Test CSS file exists and has improvements
    css_path = Path("web/static/css/main.css")
    if css_path.exists():
        with open(css_path, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # Test Part 1: Visual Consistency - Core Technologies Section
        print("\n📋 PART 1: VISUAL CONSISTENCY - CORE TECHNOLOGIES SECTION")
        print("-" * 60)
        
        # Test section title styling
        if '.section-title' in css_content:
            print("✅ Section title class implemented")
            results.append(True)
        else:
            print("❌ Section title class missing")
            results.append(False)
        
        # Test CSS custom properties for theming
        if '--section-header-color: var(--cyber-cyan)' in css_content:
            print("✅ Section header color theming implemented")
            results.append(True)
        else:
            print("❌ Section header color theming missing")
            results.append(False)
        
        # Test standardized section spacing
        if '--section-spacing-top: 5rem' in css_content and '--section-spacing-bottom: 3rem' in css_content:
            print("✅ Standardized section spacing implemented")
            results.append(True)
        else:
            print("❌ Standardized section spacing missing")
            results.append(False)
        
        # Test Orbitron font styling
        if "font-family: 'Orbitron', monospace" in css_content:
            print("✅ Orbitron font styling applied")
            results.append(True)
        else:
            print("❌ Orbitron font styling missing")
            results.append(False)
        
        # Test cyber-cyan glow effects
        if 'text-shadow: 0 0 15px rgba(0, 212, 255, 0.6)' in css_content:
            print("✅ Cyber-cyan glow effects implemented")
            results.append(True)
        else:
            print("❌ Cyber-cyan glow effects missing")
            results.append(False)
        
        # Test Part 2A: Mobile Navigation Improvements
        print("\n📱 PART 2A: MOBILE NAVIGATION IMPROVEMENTS")
        print("-" * 50)
        
        # Test mobile menu toggle styling
        if '.mobile-menu-toggle' in css_content:
            print("✅ Mobile menu toggle styling implemented")
            results.append(True)
        else:
            print("❌ Mobile menu toggle styling missing")
            results.append(False)
        
        # Test full-screen slide-out menu
        if '.mobile-menu-overlay' in css_content:
            print("✅ Full-screen slide-out menu implemented")
            results.append(True)
        else:
            print("❌ Full-screen slide-out menu missing")
            results.append(False)
        
        # Test glassmorphism styling
        if 'backdrop-filter: blur(20px)' in css_content:
            print("✅ Glassmorphism styling implemented")
            results.append(True)
        else:
            print("❌ Glassmorphism styling missing")
            results.append(False)
        
        # Test hamburger animation
        if '.hamburger' in css_content and 'transition: 0.3s ease-in-out' in css_content:
            print("✅ Hamburger animation implemented")
            results.append(True)
        else:
            print("❌ Hamburger animation missing")
            results.append(False)
        
        # Test menu positioning
        if 'position: fixed' in css_content and 'top: 1rem' in css_content and 'right: 1rem' in css_content:
            print("✅ Menu positioning (top-right) implemented")
            results.append(True)
        else:
            print("❌ Menu positioning missing")
            results.append(False)
        
        # Test Part 2B: Responsive Footer Improvements
        print("\n🦶 PART 2B: RESPONSIVE FOOTER IMPROVEMENTS")
        print("-" * 50)
        
        # Test footer responsive classes
        if '.footer-responsive' in css_content:
            print("✅ Footer responsive classes implemented")
            results.append(True)
        else:
            print("❌ Footer responsive classes missing")
            results.append(False)
        
        # Test footer grid system
        if '.footer-grid' in css_content and 'grid-template-columns' in css_content:
            print("✅ Footer grid system implemented")
            results.append(True)
        else:
            print("❌ Footer grid system missing")
            results.append(False)
        
        # Test glass-card styling preservation
        if '.footer-section' in css_content and 'backdrop-filter: blur(10px)' in css_content:
            print("✅ Glass-card styling preserved")
            results.append(True)
        else:
            print("❌ Glass-card styling missing")
            results.append(False)
        
        # Test minimum touch target sizes
        if 'min-height: 44px' in css_content:
            print("✅ Minimum touch target sizes implemented")
            results.append(True)
        else:
            print("❌ Minimum touch target sizes missing")
            results.append(False)
        
        # Test responsive breakpoints
        breakpoints = ['@media (max-width: 768px)', '@media (min-width: 768px)', '@media (min-width: 1024px)']
        breakpoint_count = sum(1 for bp in breakpoints if bp in css_content)
        if breakpoint_count >= 2:
            print(f"✅ Responsive breakpoints implemented ({breakpoint_count}/3)")
            results.append(True)
        else:
            print(f"❌ Insufficient responsive breakpoints ({breakpoint_count}/3)")
            results.append(False)
        
        # Test color consistency
        if '--cyber-cyan: #00d4ff' in css_content and '--cyber-purple: #8b5cf6' in css_content:
            print("✅ Color consistency (cyber-cyan/purple) maintained")
            results.append(True)
        else:
            print("❌ Color consistency issues")
            results.append(False)
        
    else:
        print("❌ CSS file not found")
        results.extend([False] * 16)
    
    return results

def test_template_improvements():
    """Test template improvements for visual consistency."""
    print("\n🌐 TESTING TEMPLATE IMPROVEMENTS")
    print("-" * 40)
    
    results = []
    
    # Test landing page improvements
    index_path = Path("web/templates/index.html")
    if index_path.exists():
        with open(index_path, 'r', encoding='utf-8') as f:
            index_content = f.read()
        
        # Test Core Technologies section updates
        if 'section-title' in index_content:
            print("✅ Core Technologies section uses section-title class")
            results.append(True)
        else:
            print("❌ Core Technologies section not updated")
            results.append(False)
        
        # Test section container usage
        if 'section-container' in index_content:
            print("✅ Section container class applied")
            results.append(True)
        else:
            print("❌ Section container class missing")
            results.append(False)
        
        # Test content buffer usage
        if 'section-content-buffer' in index_content:
            print("✅ Section content buffer applied")
            results.append(True)
        else:
            print("❌ Section content buffer missing")
            results.append(False)
        
    else:
        print("❌ Landing page template not found")
        results.extend([False] * 3)
    
    # Test base template improvements
    base_path = Path("web/templates/base.html")
    if base_path.exists():
        with open(base_path, 'r', encoding='utf-8') as f:
            base_content = f.read()
        
        # Test mobile navigation improvements
        if 'mobile-menu-toggle' in base_content:
            print("✅ Mobile menu toggle implemented in template")
            results.append(True)
        else:
            print("❌ Mobile menu toggle missing from template")
            results.append(False)
        
        # Test hamburger animation structure
        if 'hamburger' in base_content and ':class="{ \'active\': open }"' in base_content:
            print("✅ Hamburger animation structure implemented")
            results.append(True)
        else:
            print("❌ Hamburger animation structure missing")
            results.append(False)
        
        # Test mobile menu overlay
        if 'mobile-menu-overlay' in base_content:
            print("✅ Mobile menu overlay implemented")
            results.append(True)
        else:
            print("❌ Mobile menu overlay missing")
            results.append(False)
        
        # Test footer responsive classes
        if 'footer-responsive' in base_content and 'footer-grid' in base_content:
            print("✅ Footer responsive classes applied")
            results.append(True)
        else:
            print("❌ Footer responsive classes missing")
            results.append(False)
        
        # Test footer section classes
        if 'footer-section' in base_content:
            print("✅ Footer section classes applied")
            results.append(True)
        else:
            print("❌ Footer section classes missing")
            results.append(False)
        
        # Test footer link improvements
        if 'footer-link' in base_content:
            print("✅ Footer link improvements applied")
            results.append(True)
        else:
            print("❌ Footer link improvements missing")
            results.append(False)
        
    else:
        print("❌ Base template not found")
        results.extend([False] * 6)
    
    return results

def test_web_server_response():
    """Test web server response with improvements."""
    print("\n🌐 TESTING WEB SERVER RESPONSE")
    print("-" * 35)
    
    results = []
    
    try:
        # Test landing page
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code == 200:
            print("✅ Landing page accessible")
            results.append(True)
            
            # Test for improved styling in response
            content = response.text
            if 'section-title' in content:
                print("✅ Section title styling present in response")
                results.append(True)
            else:
                print("❌ Section title styling missing from response")
                results.append(False)
            
            # Test for mobile navigation
            if 'mobile-menu-toggle' in content:
                print("✅ Mobile navigation present in response")
                results.append(True)
            else:
                print("❌ Mobile navigation missing from response")
                results.append(False)
            
        else:
            print(f"❌ Landing page HTTP {response.status_code}")
            results.extend([False] * 3)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Web server not accessible: {e}")
        results.extend([False] * 3)
    
    return results

def main():
    """Main test runner."""
    print("🚀 ONNYX FRONTEND IMPROVEMENTS TEST SUITE")
    print("=" * 50)
    
    # Run all tests
    css_results = test_css_improvements()
    template_results = test_template_improvements()
    server_results = test_web_server_response()
    
    # Calculate overall results
    all_results = css_results + template_results + server_results
    total_tests = len(all_results)
    passed_tests = sum(all_results)
    failed_tests = total_tests - passed_tests
    
    # Generate summary
    print("\n📊 FRONTEND IMPROVEMENTS TEST SUMMARY")
    print("=" * 45)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed breakdown
    print(f"\nCSS Improvements: {sum(css_results)}/{len(css_results)} passed")
    print(f"Template Updates: {sum(template_results)}/{len(template_results)} passed")
    print(f"Server Response: {sum(server_results)}/{len(server_results)} passed")
    
    # Overall assessment
    if passed_tests >= total_tests * 0.85:  # 85% pass rate
        print("\n🎉 FRONTEND IMPROVEMENTS SUCCESSFUL!")
        print("✅ Visual consistency implemented")
        print("✅ Mobile responsiveness enhanced")
        print("✅ Onyx Stone theme maintained")
        print("✅ Core Technologies section improved")
        print("✅ Navigation and footer optimized")
        return True
    else:
        print("\n⚠️  FRONTEND IMPROVEMENTS NEED ATTENTION")
        print("Some critical improvements may be missing.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
