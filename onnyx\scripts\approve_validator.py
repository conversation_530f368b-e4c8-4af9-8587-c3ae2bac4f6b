#!/usr/bin/env python3
"""
ONNYX Validator Approval and Mining Tier Management

Administrative CLI tool for managing validator status and mining tier assignments.
"""

import os
import sys
import argparse
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

class ValidatorManager:
    """Administrative tool for validator and mining tier management."""

    def __init__(self):
        self.admin_id = "ONNYX_ADMIN"

    def list_validators(self):
        """List all validators with their mining tiers."""
        try:
            validators = db.query("""
                SELECT sela_id, name, status, mining_tier, mining_power, 
                       mining_rewards_earned, blocks_mined, last_mining_activity
                FROM selas
                ORDER BY created_at ASC
            """)
            
            if not validators:
                print("📋 No validators found.")
                return
            
            print("🏢 ONNYX VALIDATOR DIRECTORY")
            print("=" * 80)
            print(f"{'Sela ID':<20} {'Name':<25} {'Status':<10} {'Tier':<12} {'Power':<6} {'Rewards':<10} {'Blocks':<8}")
            print("-" * 80)
            
            for v in validators:
                sela_id = v['sela_id'][:18] + "..." if len(v['sela_id']) > 20 else v['sela_id']
                name = v['name'][:23] + "..." if len(v['name']) > 25 else v['name']
                status = v['status']
                tier = v['mining_tier'] or 'basic'
                power = f"{v['mining_power'] or 1}x"
                rewards = f"{v['mining_rewards_earned'] or 0:.2f}"
                blocks = v['blocks_mined'] or 0
                
                print(f"{sela_id:<20} {name:<25} {status:<10} {tier:<12} {power:<6} {rewards:<10} {blocks:<8}")
            
            print("-" * 80)
            print(f"Total validators: {len(validators)}")
            
        except Exception as e:
            print(f"❌ Error listing validators: {e}")

    def approve_validator(self, sela_id, mining_tier='basic', mining_power=None):
        """Approve a validator and set their mining tier."""
        try:
            # Check if validator exists
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
            if not validator:
                print(f"❌ Validator {sela_id} not found.")
                return False
            
            # Determine mining power based on tier
            if mining_power is None:
                tier_power = {
                    'basic': 1,
                    'optimized': 2,
                    'pro': 5
                }
                mining_power = tier_power.get(mining_tier, 1)
            
            # Validate mining power limits
            max_power = {
                'basic': 1,
                'optimized': 5,
                'pro': 10
            }
            
            if mining_power > max_power.get(mining_tier, 1):
                print(f"❌ Mining power {mining_power} exceeds maximum for tier '{mining_tier}' ({max_power[mining_tier]})")
                return False
            
            # Record tier change in history
            old_tier = validator.get('mining_tier', 'basic')
            old_power = validator.get('mining_power', 1)
            
            if old_tier != mining_tier or old_power != mining_power:
                history_data = {
                    'sela_id': sela_id,
                    'old_tier': old_tier,
                    'new_tier': mining_tier,
                    'old_mining_power': old_power,
                    'new_mining_power': mining_power,
                    'changed_by': self.admin_id,
                    'reason': f"Administrative approval to {mining_tier} tier",
                    'timestamp': datetime.now().isoformat()
                }
                db.insert('mining_tier_history', history_data)
            
            # Update validator
            update_data = {
                'status': 'active',
                'mining_tier': mining_tier,
                'mining_power': mining_power,
                'last_mining_activity': datetime.now().isoformat()
            }
            
            db.update('selas', update_data, 'sela_id = ?', (sela_id,))
            
            print(f"✅ Validator {validator['name']} approved:")
            print(f"   Sela ID: {sela_id}")
            print(f"   Status: active")
            print(f"   Mining Tier: {mining_tier}")
            print(f"   Mining Power: {mining_power}x")
            
            return True
            
        except Exception as e:
            print(f"❌ Error approving validator: {e}")
            return False

    def upgrade_validator(self, sela_id, new_tier):
        """Upgrade a validator to a higher mining tier."""
        try:
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
            if not validator:
                print(f"❌ Validator {sela_id} not found.")
                return False
            
            current_tier = validator.get('mining_tier', 'basic')
            
            # Tier hierarchy
            tier_levels = {'basic': 1, 'optimized': 2, 'pro': 3}
            
            if tier_levels.get(new_tier, 0) <= tier_levels.get(current_tier, 0):
                print(f"❌ Cannot upgrade from {current_tier} to {new_tier} (not a higher tier)")
                return False
            
            return self.approve_validator(sela_id, new_tier)
            
        except Exception as e:
            print(f"❌ Error upgrading validator: {e}")
            return False

    def downgrade_validator(self, sela_id, new_tier, reason="Administrative action"):
        """Downgrade a validator to a lower mining tier."""
        try:
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
            if not validator:
                print(f"❌ Validator {sela_id} not found.")
                return False
            
            current_tier = validator.get('mining_tier', 'basic')
            
            # Record tier change with reason
            history_data = {
                'sela_id': sela_id,
                'old_tier': current_tier,
                'new_tier': new_tier,
                'old_mining_power': validator.get('mining_power', 1),
                'new_mining_power': 1 if new_tier == 'basic' else 2,
                'changed_by': self.admin_id,
                'reason': reason,
                'timestamp': datetime.now().isoformat()
            }
            db.insert('mining_tier_history', history_data)
            
            return self.approve_validator(sela_id, new_tier)
            
        except Exception as e:
            print(f"❌ Error downgrading validator: {e}")
            return False

    def deactivate_validator(self, sela_id, reason="Administrative action"):
        """Deactivate a validator."""
        try:
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
            if not validator:
                print(f"❌ Validator {sela_id} not found.")
                return False
            
            # Record deactivation
            history_data = {
                'sela_id': sela_id,
                'old_tier': validator.get('mining_tier', 'basic'),
                'new_tier': 'inactive',
                'old_mining_power': validator.get('mining_power', 1),
                'new_mining_power': 0,
                'changed_by': self.admin_id,
                'reason': reason,
                'timestamp': datetime.now().isoformat()
            }
            db.insert('mining_tier_history', history_data)
            
            # Update validator status
            db.update('selas', {'status': 'inactive', 'mining_power': 0}, 'sela_id = ?', (sela_id,))
            
            print(f"✅ Validator {validator['name']} deactivated")
            return True
            
        except Exception as e:
            print(f"❌ Error deactivating validator: {e}")
            return False

    def show_validator_details(self, sela_id):
        """Show detailed information about a validator."""
        try:
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
            if not validator:
                print(f"❌ Validator {sela_id} not found.")
                return
            
            print(f"🏢 VALIDATOR DETAILS")
            print("=" * 50)
            print(f"Sela ID: {validator['sela_id']}")
            print(f"Name: {validator['name']}")
            print(f"Category: {validator['category']}")
            print(f"Status: {validator['status']}")
            print(f"Mining Tier: {validator.get('mining_tier', 'basic')}")
            print(f"Mining Power: {validator.get('mining_power', 1)}x")
            print(f"Total Rewards: {validator.get('mining_rewards_earned', 0)}")
            print(f"Blocks Mined: {validator.get('blocks_mined', 0)}")
            print(f"Last Activity: {validator.get('last_mining_activity', 'Never')}")
            print(f"Created: {validator['created_at']}")
            
            # Show tier history
            history = db.query("""
                SELECT * FROM mining_tier_history 
                WHERE sela_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 5
            """, (sela_id,))
            
            if history:
                print("\n📋 Recent Tier Changes:")
                for h in history:
                    print(f"  {h['timestamp']}: {h['old_tier']} → {h['new_tier']} ({h['reason']})")
            
        except Exception as e:
            print(f"❌ Error showing validator details: {e}")

    def show_mining_stats(self):
        """Show overall mining statistics."""
        try:
            stats = db.query_one("""
                SELECT 
                    COUNT(*) as total_validators,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_validators,
                    COUNT(CASE WHEN mining_tier = 'basic' THEN 1 END) as basic_tier,
                    COUNT(CASE WHEN mining_tier = 'optimized' THEN 1 END) as optimized_tier,
                    COUNT(CASE WHEN mining_tier = 'pro' THEN 1 END) as pro_tier,
                    SUM(mining_rewards_earned) as total_rewards,
                    SUM(blocks_mined) as total_blocks_mined
                FROM selas
            """)
            
            print("📊 MINING SYSTEM STATISTICS")
            print("=" * 40)
            print(f"Total Validators: {stats['total_validators']}")
            print(f"Active Validators: {stats['active_validators']}")
            print(f"Basic Tier: {stats['basic_tier']}")
            print(f"Optimized Tier: {stats['optimized_tier']}")
            print(f"Pro Tier: {stats['pro_tier']}")
            print(f"Total Rewards Distributed: {stats['total_rewards'] or 0}")
            print(f"Total Blocks Mined: {stats['total_blocks_mined'] or 0}")
            
        except Exception as e:
            print(f"❌ Error showing mining stats: {e}")

def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="ONNYX Validator Management Tool")
    parser.add_argument('action', choices=[
        'list', 'approve', 'upgrade', 'downgrade', 'deactivate', 'details', 'stats'
    ], help='Action to perform')
    parser.add_argument('--sela-id', help='Validator Sela ID')
    parser.add_argument('--tier', choices=['basic', 'optimized', 'pro'], 
                       default='basic', help='Mining tier')
    parser.add_argument('--power', type=int, help='Custom mining power')
    parser.add_argument('--reason', default='Administrative action', help='Reason for change')
    
    args = parser.parse_args()
    
    manager = ValidatorManager()
    
    if args.action == 'list':
        manager.list_validators()
    elif args.action == 'approve':
        if not args.sela_id:
            print("❌ --sela-id required for approve action")
            return 1
        manager.approve_validator(args.sela_id, args.tier, args.power)
    elif args.action == 'upgrade':
        if not args.sela_id or not args.tier:
            print("❌ --sela-id and --tier required for upgrade action")
            return 1
        manager.upgrade_validator(args.sela_id, args.tier)
    elif args.action == 'downgrade':
        if not args.sela_id or not args.tier:
            print("❌ --sela-id and --tier required for downgrade action")
            return 1
        manager.downgrade_validator(args.sela_id, args.tier, args.reason)
    elif args.action == 'deactivate':
        if not args.sela_id:
            print("❌ --sela-id required for deactivate action")
            return 1
        manager.deactivate_validator(args.sela_id, args.reason)
    elif args.action == 'details':
        if not args.sela_id:
            print("❌ --sela-id required for details action")
            return 1
        manager.show_validator_details(args.sela_id)
    elif args.action == 'stats':
        manager.show_mining_stats()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
