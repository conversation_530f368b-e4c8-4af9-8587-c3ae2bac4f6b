{% extends "base.html" %}

{% block title %}Onnyx Blockchain Explorer{% endblock %}

{% block head %}
<style>
    body {
        background-color: #000000;
    }

    .container {
        max-width: 1100px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .explorer-header {
        background-color: rgba(10, 10, 10, 0.7);
        padding: 2rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .explorer-header p {
        color: #aaa;
        max-width: 800px;
        margin: 0 auto;
    }

    .explorer-nav {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }

    .explorer-nav a {
        padding: 1rem 1.5rem;
        background-color: rgba(20, 20, 20, 0.7);
        border-radius: 8px;
        text-decoration: none;
        color: var(--light-text);
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        text-align: center;
        min-width: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 10px;
    }

    .explorer-nav a:hover {
        background-color: var(--primary-color);
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(138, 43, 226, 0.3);
    }

    .icon-container {
        background-color: rgba(30, 30, 30, 0.9);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.8rem;
    }

    .explorer-nav a i {
        display: block;
        font-size: 1.8rem;
        color: var(--secondary-color);
    }

    .explorer-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background-color: rgba(20, 20, 20, 0.7);
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid var(--border-color);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(138, 43, 226, 0.3);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--secondary-color);
        margin: 0.8rem 0;
    }

    .stat-label {
        color: #aaa;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .recent-activity {
        margin-top: 3rem;
        background-color: rgba(20, 20, 20, 0.7);
        border-radius: 8px;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
    }

    .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid rgba(255, 215, 0, 0.3);
        padding-bottom: 0.8rem;
    }

    .activity-header h2 {
        margin: 0;
        color: var(--secondary-color);
    }

    .view-all {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: bold;
    }

    .view-all:hover {
        text-decoration: underline;
    }

    .back-to-main {
        margin-top: 2rem;
        text-align: center;
    }

    .back-to-main a {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background-color: rgba(20, 20, 20, 0.7);
        border-radius: 8px;
        text-decoration: none;
        color: var(--light-text);
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        font-weight: bold;
    }

    .back-to-main a:hover {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 5px 15px rgba(138, 43, 226, 0.3);
        transform: translateY(-3px);
    }

    .explorer-tabs {
        background-color: var(--darker-bg);
        border-radius: 8px;
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .explorer-tabs-inner {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .explorer-tabs-inner::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }

    .explorer-tabs a {
        padding: 1rem 1.5rem;
        color: var(--light-text);
        text-decoration: none;
        transition: all 0.3s ease;
        white-space: nowrap;
        border-bottom: 3px solid transparent;
    }

    .explorer-tabs a:hover {
        background-color: rgba(138, 43, 226, 0.2);
        border-bottom-color: rgba(138, 43, 226, 0.5);
    }

    .explorer-tabs a.active {
        background-color: rgba(138, 43, 226, 0.3);
        border-bottom-color: var(--primary-color);
        font-weight: bold;
    }

    @media (max-width: 768px) {
        .explorer-tabs a {
            padding: 0.8rem 1rem;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
{% with active_tab='dashboard' %}
{% include 'explorer_hero.html' %}
{% include 'explorer_nav.html' %}
{% endwith %}

<div class="explorer-nav">
    <a href="{{ url_for('transactions') }}">
        <div class="icon-container">
            <i class="fas fa-exchange-alt">🔄</i>
        </div>
        Transactions
    </a>
    <a href="{{ url_for('mempool_view') }}">
        <div class="icon-container">
            <i class="fas fa-clock">⏱️</i>
        </div>
        Mempool
    </a>
    <a href="{{ url_for('tokens') }}">
        <div class="icon-container">
            <i class="fas fa-coins">🪙</i>
        </div>
        Tokens
    </a>
    <a href="{{ url_for('identities') }}">
        <div class="icon-container">
            <i class="fas fa-users">👥</i>
        </div>
        Identities
    </a>
    <a href="{{ url_for('search') }}">
        <div class="icon-container">
            <i class="fas fa-search">🔍</i>
        </div>
        Search
    </a>
</div>

<div class="explorer-stats">
    <div class="stat-card">
        <div class="stat-label">Total Transactions</div>
        <div class="stat-value" id="tx-count">{{ tx_count }}</div>
        <div class="stat-label">Processed on the blockchain</div>
    </div>
    <div class="stat-card">
        <div class="stat-label">Pending Transactions</div>
        <div class="stat-value" id="mempool-count">{{ mempool_count }}</div>
        <div class="stat-label">Waiting in the mempool</div>
    </div>
    <div class="stat-card">
        <div class="stat-label">Total Tokens</div>
        <div class="stat-value" id="token-count">{{ token_count }}</div>
        <div class="stat-label">Created on the blockchain</div>
    </div>
    <div class="stat-card">
        <div class="stat-label">Total Identities</div>
        <div class="stat-value" id="identity-count">{{ identity_count }}</div>
        <div class="stat-label">Registered on the blockchain</div>
    </div>
</div>

<div class="recent-activity">
    <div class="activity-header">
        <h2>Recent Transactions</h2>
        <a href="{{ url_for('transactions') }}" class="view-all">View All →</a>
    </div>
    {% if recent_transactions %}
    <table class="explorer-table">
        <thead>
            <tr>
                <th>Transaction ID</th>
                <th>Type</th>
                <th>Sender</th>
                <th>Details</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            {% for tx in recent_transactions %}
            <tr>
                <td>
                    {% if tx.tx_id is defined %}
                        <a href="{{ url_for('transaction_detail', txid=tx.tx_id) }}">{{ tx.tx_id[:10] }}...</a>
                    {% else %}
                        <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a>
                    {% endif %}
                </td>
                <td>
                    {% if tx.op is defined %}
                        {{ tx.op }}
                    {% else %}
                        {{ tx.type }}
                    {% endif %}
                </td>
                <td>
                    {% if tx.sender is defined %}
                        {{ tx.sender[:10] }}...
                    {% else %}
                        {{ tx.sender[:10] if tx.sender else 'SYSTEM' }}...
                    {% endif %}
                </td>
                <td>
                    {% if tx.op is defined and tx.op == 'CREATE_IDENTITY' %}
                        {% if tx.data is defined and tx.data is mapping %}
                            Created identity <strong>{{ tx.data.name }}</strong>
                        {% elif tx.data is defined and tx.data is string %}
                            {% set json_parse_success = true %}
                            {% set data = {} %}
                            {% if tx.data %}
                                {% set data = tx.data|tojson|fromjson %}
                                {% if data and data.name %}
                                    Created identity <strong>{{ data.name }}</strong>
                                {% else %}
                                    Created identity
                                {% endif %}
                            {% else %}
                                Created identity
                            {% endif %}
                        {% else %}
                            Created identity
                        {% endif %}
                    {% elif tx.type == 'forktoken' %}
                        Created token <strong>{{ tx.payload.name }}</strong>
                    {% elif tx.type == 'minttoken' %}
                        Minted tokens
                    {% elif tx.type == 'sendtoken' %}
                        Sent tokens
                    {% elif tx.type == 'burntoken' %}
                        Burned tokens
                    {% elif tx.type == 'createidentity' %}
                        Created identity <strong>{{ tx.payload.name }}</strong>
                    {% elif tx.type == 'updateidentity' %}
                        Updated identity
                    {% elif tx.type == 'grantreputation' %}
                        Granted reputation
                    {% else %}
                        {% if tx.tx_id is defined %}
                            <a href="{{ url_for('transaction_detail', txid=tx.tx_id) }}">View details</a>
                        {% else %}
                            <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">View details</a>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    {% if tx.timestamp is defined %}
                        {{ tx.timestamp|format_timestamp }}
                    {% elif tx.created_at is defined %}
                        {{ tx.created_at|format_timestamp }}
                    {% else %}
                        Unknown
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="no-data">
        <p>No transactions found.</p>
    </div>
    {% endif %}
</div>

<div class="recent-activity">
    <div class="activity-header">
        <h2>Recent Tokens</h2>
        <a href="{{ url_for('tokens') }}" class="view-all">View All →</a>
    </div>
    {% if recent_tokens %}
    <table class="explorer-table">
        <thead>
            <tr>
                <th>Token ID</th>
                <th>Name</th>
                <th>Symbol</th>
                <th>Creator</th>
                <th>Supply</th>
            </tr>
        </thead>
        <tbody>
            {% for token in recent_tokens %}
            <tr>
                <td><a href="{{ url_for('token_detail', token_id=token.token_id) }}">{{ token.token_id[:10] }}...</a></td>
                <td>{{ token.name }}</td>
                <td>{{ token.symbol }}</td>
                <td>{{ token.creator_id[:10] }}...</td>
                <td>{{ token.supply }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="no-data">
        <p>No tokens found.</p>
    </div>
    {% endif %}
</div>

<style>
    .explorer-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 0.5rem;
    }

    .explorer-table th {
        background-color: rgba(30, 30, 30, 0.9);
        color: var(--secondary-color);
        font-weight: bold;
        text-align: left;
        padding: 0.8rem;
    }

    .explorer-table td {
        padding: 0.8rem;
        border-top: 1px solid rgba(80, 80, 80, 0.3);
    }

    .explorer-table tr:hover {
        background-color: rgba(138, 43, 226, 0.1);
    }

    .explorer-table a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .explorer-table a:hover {
        text-decoration: underline;
    }

    .no-data {
        text-align: center;
        padding: 2rem;
        color: #aaa;
    }
</style>

<div class="back-to-main">
    <a href="{{ url_for('home') }}">← Back to Main Site</a>
</div>

<script>
    // Function to fetch and update statistics
    function updateStatistics() {
        fetch('/api/statistics')
            .then(response => response.json())
            .then(data => {
                // Update the statistics
                document.getElementById('tx-count').textContent = data.tx_count;
                document.getElementById('mempool-count').textContent = data.mempool_count;
                document.getElementById('token-count').textContent = data.token_count;
                document.getElementById('identity-count').textContent = data.identity_count;

                // Update the last update time
                document.getElementById('last-update-time').textContent = 'Last updated: ' + data.timestamp;

                // Add a visual indicator that the stats were updated
                const statCards = document.querySelectorAll('.stat-card');
                statCards.forEach(card => {
                    card.classList.add('updated');
                    setTimeout(() => {
                        card.classList.remove('updated');
                    }, 1000);
                });
            })
            .catch(error => {
                console.error('Error fetching statistics:', error);
            });
    }

    // Set up auto-refresh
    let autoRefreshInterval = null;
    const refreshInterval = 10000; // 10 seconds

    function startAutoRefresh() {
        if (!autoRefreshInterval) {
            autoRefreshInterval = setInterval(updateStatistics, refreshInterval);
            console.log('Auto-refresh started');
        }
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
            console.log('Auto-refresh stopped');
        }
    }

    // Start auto-refresh when the page loads
    document.addEventListener('DOMContentLoaded', () => {
        // Initial update
        updateStatistics();

        // Start auto-refresh
        startAutoRefresh();

        // Stop auto-refresh when the page is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    });
</script>

<style>
    .refresh-status {
        grid-column: 1 / -1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 1rem;
        background-color: rgba(20, 20, 20, 0.7);
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    #last-update-time {
        color: #aaa;
        font-size: 0.9rem;
    }

    .refresh-btn {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .refresh-btn:hover {
        background-color: var(--secondary-color);
    }

    .stat-card.updated {
        animation: pulse 1s;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(138, 43, 226, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(138, 43, 226, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(138, 43, 226, 0);
        }
    }
</style>
{% endblock %}
