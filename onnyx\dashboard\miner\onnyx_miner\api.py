"""
Onnyx Miner API Module

This module provides functions for interacting with the Onnyx API.
"""

import os
import json
import logging
import requests
from typing import Dict, Any, List, Optional

from .config import get_config
from .keys import load_identity_keys, sign_message

# Set up logging
logger = logging.getLogger("onnyx_miner.api")

def register_identity(identity_id: str, name: str, public_key: str) -> Dict[str, Any]:
    """
    Register a new identity.
    
    Args:
        identity_id: The identity ID
        name: The identity name
        public_key: The public key
    
    Returns:
        The registered identity
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Create the identity
        identity = {
            "identity_id": identity_id,
            "name": name,
            "public_key": public_key
        }
        
        # Register the identity
        identity_url = f"{api_url}/api/identity/register"
        identity_response = requests.post(identity_url, json=identity)
        identity_response.raise_for_status()
        
        return identity_response.json().get("identity", {})
    except Exception as e:
        logger.error(f"Error registering identity: {str(e)}")
        raise

def register_sela(sela_id: str, name: str, founder_id: str, sela_type: str, token_type: str) -> Dict[str, Any]:
    """
    Register a new Sela.
    
    Args:
        sela_id: The Sela ID
        name: The Sela name
        founder_id: The founder's identity ID
        sela_type: The Sela type
        token_type: The token type
    
    Returns:
        The registered Sela
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Create the Sela
        sela = {
            "sela_id": sela_id,
            "name": name,
            "founder": founder_id,
            "type": sela_type,
            "token_type": token_type
        }
        
        # Register the Sela
        sela_url = f"{api_url}/api/sela/register"
        sela_response = requests.post(sela_url, json=sela)
        sela_response.raise_for_status()
        
        return sela_response.json().get("sela", {})
    except Exception as e:
        logger.error(f"Error registering Sela: {str(e)}")
        raise

def link_identity_to_sela(identity_id: str, sela_id: str, role: str) -> bool:
    """
    Link an identity to a Sela.
    
    Args:
        identity_id: The identity ID
        sela_id: The Sela ID
        role: The role of the identity in the Sela
    
    Returns:
        True if the link was created, False otherwise
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Create the link
        link = {
            "identity_id": identity_id,
            "sela_id": sela_id,
            "role": role
        }
        
        # Link the identity to the Sela
        link_url = f"{api_url}/api/identity/link-sela"
        link_response = requests.post(link_url, json=link)
        link_response.raise_for_status()
        
        return True
    except Exception as e:
        logger.error(f"Error linking identity to Sela: {str(e)}")
        return False

def create_token(token_id: str, name: str, symbol: str, creator_id: str, supply: float, category: str = "general") -> Dict[str, Any]:
    """
    Create a new token.
    
    Args:
        token_id: The token ID
        name: The token name
        symbol: The token symbol
        creator_id: The creator's identity ID
        supply: The initial supply
        category: The token category
    
    Returns:
        The created token
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Load the keys
        private_key_pem, _ = load_identity_keys(creator_id)
        
        # Create the token
        token = {
            "token_id": token_id,
            "name": name,
            "symbol": symbol,
            "creator_id": creator_id,
            "supply": supply,
            "category": category
        }
        
        # Register the token
        token_url = f"{api_url}/api/tokens/register"
        token_response = requests.post(token_url, json=token)
        token_response.raise_for_status()
        
        return token_response.json().get("token", {})
    except Exception as e:
        logger.error(f"Error creating token: {str(e)}")
        raise

def propose_scroll(creator_id: str, title: str, description: str, category: str, effect: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Propose a new Voice Scroll.
    
    Args:
        creator_id: The creator's identity ID
        title: The title of the scroll
        description: The description of the scroll
        category: The category of the scroll
        effect: The effect of the scroll (optional)
    
    Returns:
        The created scroll
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Load the keys
        private_key_pem, _ = load_identity_keys(creator_id)
        
        # Create the scroll
        scroll = {
            "creator_id": creator_id,
            "title": title,
            "description": description,
            "category": category
        }
        
        if effect:
            scroll["effect"] = effect
        
        # Propose the scroll
        scroll_url = f"{api_url}/api/governance/propose"
        scroll_response = requests.post(scroll_url, json=scroll)
        scroll_response.raise_for_status()
        
        return scroll_response.json().get("scroll", {})
    except Exception as e:
        logger.error(f"Error proposing scroll: {str(e)}")
        raise

def vote_on_scroll(identity_id: str, scroll_id: str, decision: str) -> Dict[str, Any]:
    """
    Vote on a Voice Scroll.
    
    Args:
        identity_id: The identity ID
        scroll_id: The scroll ID
        decision: The decision ("yes", "no", or "abstain")
    
    Returns:
        The updated scroll
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Load the keys
        private_key_pem, _ = load_identity_keys(identity_id)
        
        # Create the vote
        vote = {
            "identity_id": identity_id,
            "scroll_id": scroll_id,
            "decision": decision
        }
        
        # Vote on the scroll
        vote_url = f"{api_url}/api/governance/vote"
        vote_response = requests.post(vote_url, json=vote)
        vote_response.raise_for_status()
        
        return vote_response.json().get("scroll", {})
    except Exception as e:
        logger.error(f"Error voting on scroll: {str(e)}")
        raise

def get_scrolls(status: Optional[str] = None, category: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get Voice Scrolls.
    
    Args:
        status: Filter by status (optional)
        category: Filter by category (optional)
    
    Returns:
        A list of scrolls
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Build the URL
        scrolls_url = f"{api_url}/api/governance/scrolls"
        params = {}
        
        if status:
            params["status"] = status
        
        if category:
            params["category"] = category
        
        # Get the scrolls
        scrolls_response = requests.get(scrolls_url, params=params)
        scrolls_response.raise_for_status()
        
        return scrolls_response.json().get("scrolls", [])
    except Exception as e:
        logger.error(f"Error getting scrolls: {str(e)}")
        return []
