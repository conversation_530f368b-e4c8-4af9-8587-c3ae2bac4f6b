#!/usr/bin/env python3
"""
Force refresh frontend data to show correct production statistics.
"""

import sqlite3
import json
import os
import time

def clear_cache_files():
    """Clear any cached JSON files that might interfere."""
    print("🧹 Clearing cache files...")
    
    cache_files = [
        "data/identities.json",
        "data/tokens.json", 
        "data/mempool.json",
        "data/blockchain.json",
        "data/scrolls.json"
    ]
    
    for file_path in cache_files:
        if os.path.exists(file_path):
            try:
                # Clear the file content but keep the file
                with open(file_path, 'w') as f:
                    f.write('[]')
                print(f"   ✅ Cleared {file_path}")
            except Exception as e:
                print(f"   ⚠️ Could not clear {file_path}: {e}")
        else:
            print(f"   ℹ️ {file_path} does not exist")

def verify_database_stats():
    """Verify the actual database statistics."""
    print("\n📊 Verifying Database Statistics...")
    
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Get actual counts
        cursor.execute("SELECT COUNT(*) FROM identities")
        identity_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM selas")
        sela_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM transactions")
        transaction_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM blocks")
        block_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"   👥 Identities: {identity_count}")
        print(f"   🏢 Selas: {sela_count}")
        print(f"   💰 Transactions: {transaction_count}")
        print(f"   ⛓️ Blocks: {block_count}")
        
        return {
            'identities': identity_count,
            'selas': sela_count,
            'transactions': transaction_count,
            'blocks': block_count
        }
        
    except Exception as e:
        print(f"   ❌ Error getting database stats: {e}")
        return None

def test_web_api():
    """Test the web API to see what it returns."""
    print("\n🌐 Testing Web API...")
    
    try:
        import requests
        
        # Test the stats API endpoint
        response = requests.get("http://127.0.0.1:5000/api/stats", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ API Response:")
            print(f"      Identities: {data.get('identities', 'N/A')}")
            print(f"      Selas: {data.get('selas', 'N/A')}")
            print(f"      Transactions: {data.get('transactions', 'N/A')}")
            print(f"      Blocks: {data.get('blocks', 'N/A')}")
            return data
        else:
            print(f"   ❌ API Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error testing API: {e}")
        return None

def force_browser_refresh():
    """Instructions for forcing browser refresh."""
    print("\n🔄 Browser Refresh Instructions:")
    print("   1. Press Ctrl+F5 (or Cmd+Shift+R on Mac) to hard refresh")
    print("   2. Or press F12 -> Network tab -> Disable cache -> Refresh")
    print("   3. Or clear browser cache for localhost")

def main():
    """Execute frontend refresh operations."""
    print("🚀 ONNYX FRONTEND DATA REFRESH")
    print("=" * 50)
    print("Forcing frontend to display correct production data")
    print()
    
    # Step 1: Clear cache files
    clear_cache_files()
    
    # Step 2: Verify database
    db_stats = verify_database_stats()
    
    # Step 3: Test web API
    api_stats = test_web_api()
    
    # Step 4: Compare results
    if db_stats and api_stats:
        print("\n🔍 Comparison:")
        print("-" * 30)
        
        for key in ['identities', 'selas', 'transactions', 'blocks']:
            db_val = db_stats.get(key, 0)
            api_val = api_stats.get(key, 0)
            
            if db_val == api_val:
                print(f"   ✅ {key.title()}: {db_val} (DB) = {api_val} (API)")
            else:
                print(f"   ❌ {key.title()}: {db_val} (DB) ≠ {api_val} (API)")
    
    # Step 5: Browser refresh instructions
    force_browser_refresh()
    
    print("\n✅ Frontend refresh operations complete!")
    print("🌐 Please hard refresh your browser to see updated data")

if __name__ == "__main__":
    main()
