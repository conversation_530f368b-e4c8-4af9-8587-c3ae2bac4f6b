"""
Onnyx Wallet Tests

This module provides tests for the Onnyx wallet functionality.
"""

import sys
import os
import unittest
import hashlib

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from wallet.wallet import Wallet
from tests.test_helpers import OnnyxTestCase

# Base58 encoding (similar to Bitcoin) - copied from the old implementation for testing
def base58_encode(data):
    """Encode bytes in base58 (similar to Bitcoin)."""
    # Base58 character set
    alphabet = '**********************************************************'

    # Convert bytes to integer
    n = int.from_bytes(data, byteorder='big')

    # Encode to base58
    result = ''
    while n > 0:
        n, remainder = divmod(n, 58)
        result = alphabet[remainder] + result

    # Add leading zeros
    for byte in data:
        if byte == 0:
            result = alphabet[0] + result
        else:
            break

    return result

class TestWallet(OnnyxTestCase):
    """Test case for the Wallet class."""

    def setUp(self):
        """Set up the test environment."""
        super().setUp()
        self.wallet = Wallet()

        # Generate a keypair for testing
        self.private_key, self.public_key = self.wallet.generate_keypair()

    def test_generate_keypair(self):
        """Test generating a keypair."""
        private_key, public_key = self.wallet.generate_keypair()

        # Check that the keys are not None
        self.assertIsNotNone(private_key)
        self.assertIsNotNone(public_key)

        # Check that the keys are strings
        self.assertIsInstance(private_key, str)
        self.assertIsInstance(public_key, str)

        # Check that the keys are hex strings
        self.assertTrue(all(c in '0123456789abcdef' for c in private_key.lower()))
        self.assertTrue(all(c in '0123456789abcdef' for c in public_key.lower()))

    def test_generate_private_key(self):
        """Test generating a private key."""
        private_key_pem = self.wallet.generate_private_key()

        # Check that the key is not None
        self.assertIsNotNone(private_key_pem)

        # Check that the key is a string
        self.assertIsInstance(private_key_pem, str)

        # Check that the key is in PEM format
        self.assertTrue(private_key_pem.startswith('-----BEGIN EC PRIVATE KEY-----'))
        self.assertTrue(private_key_pem.endswith('-----END EC PRIVATE KEY-----\n'))

    def test_get_public_key(self):
        """Test getting a public key from a private key."""
        # Get the public key from the private key
        public_key = self.wallet.get_public_key(self.private_key)

        # Check that the public key is not None
        self.assertIsNotNone(public_key)

        # Check that the public key is a string
        self.assertIsInstance(public_key, str)

        # Check that the public key is a hex string
        self.assertTrue(all(c in '0123456789abcdef' for c in public_key.lower()))

        # Check that the public key matches the one from generate_keypair
        self.assertEqual(public_key, self.public_key)

    def test_sign_and_verify(self):
        """Test signing and verifying a message."""
        message = "Hello, Onnyx!"

        # Sign the message
        signature = self.wallet.sign_message(message, self.private_key)

        # Check that the signature is not None
        self.assertIsNotNone(signature)

        # Check that the signature is a string
        self.assertIsInstance(signature, str)

        # Verify the signature
        self.assertTrue(self.wallet.verify_signature(message, signature, self.public_key))

        # Verify with a different message
        self.assertFalse(self.wallet.verify_signature("Different message", signature, self.public_key))

    def test_create_address(self):
        """Test creating an address from a public key."""
        # Create an address from the public key
        address = self.wallet.create_address(self.public_key)

        # Check that the address is not None
        self.assertIsNotNone(address)

        # Check that the address is a string
        self.assertIsInstance(address, str)

        # Check that the address is a hex string
        self.assertTrue(all(c in '0123456789abcdef' for c in address.lower()))

        # Check that the address is derived from the public key
        pubkey_bytes = bytes.fromhex(self.public_key)
        sha256 = hashlib.sha256(pubkey_bytes).digest()
        ripemd160 = hashlib.new('ripemd160', sha256).digest()
        expected_address = ripemd160.hex()

        self.assertEqual(address, expected_address)

    def test_base58_encoding(self):
        """Test the base58 encoding function."""
        # Test with a known value
        data = b'\x00\x01\x02\x03'
        encoded = base58_encode(data)

        # The exact value depends on the implementation, but it should be a string
        self.assertIsInstance(encoded, str)

        # Test with a zero byte
        data = b'\x00\x01\x02\x03'
        encoded = base58_encode(data)

        # The first byte is 0, so the first character should be '1'
        self.assertTrue(encoded.startswith('1'))

if __name__ == "__main__":
    unittest.main()
