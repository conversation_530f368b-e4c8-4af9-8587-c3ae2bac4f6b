# src/node/server.py

import asyncio
import json
import time
import logging
from typing import Dict, List, Set, Any, Optional, Callable
import websockets
from websockets.server import WebSocketServerProtocol

from src.node.config import node_config, NODE_ID, NODE_HOST, NODE_PORT
from src.node.peer import peer_manager, Peer
from src.node.blockchain import local_blockchain
from src.node.crypto import key_manager
from src.chain.mempool import Mempool
from models.block import Block
from models.transaction import Transaction
from models.mempool import MempoolTransaction

# Configure logging
logger = logging.getLogger("onnyx.node.server")

# Initialize components
mempool = Mempool()

class NodeServer:
    """
    WebSocket server for the Onnyx node.
    """

    def __init__(self):
        """
        Initialize the node server.
        """
        self.host = node_config.get("node_host")
        self.port = node_config.get("node_port")
        self.server = None
        self.running = False
        self.tasks = []

    async def start(self):
        """
        Start the node server.
        """
        # Start the peer manager
        await peer_manager.start()

        # Register message handlers
        self._register_message_handlers()

        # Start the WebSocket server
        self.server = await websockets.serve(
            self._handle_connection,
            self.host,
            self.port
        )

        self.running = True

        # Start background tasks
        self.tasks = [
            asyncio.create_task(self._sync_mempool()),
            asyncio.create_task(self._sync_chain())
        ]

        logger.info(f"Node server started on {self.host}:{self.port}")

        # Keep the server running
        await self.server.wait_closed()

    async def stop(self):
        """
        Stop the node server.
        """
        self.running = False

        # Cancel all tasks
        for task in self.tasks:
            task.cancel()

        # Stop the peer manager
        await peer_manager.stop()

        # Close the server
        if self.server:
            self.server.close()
            await self.server.wait_closed()

        logger.info("Node server stopped")

    def _register_message_handlers(self):
        """
        Register message handlers for different message types.
        """
        # Register handlers for peer discovery
        peer_manager.register_message_handler("get_peers", self._handle_get_peers)
        peer_manager.register_message_handler("peers", self._handle_peers)

        # Register handlers for mempool synchronization
        peer_manager.register_message_handler("get_mempool", self._handle_get_mempool)
        peer_manager.register_message_handler("mempool_tx", self._handle_mempool_tx)
        peer_manager.register_message_handler("mempool", self._handle_mempool)

        # Register handlers for chain synchronization
        peer_manager.register_message_handler("get_block", self._handle_get_block)
        peer_manager.register_message_handler("get_chain_height", self._handle_get_chain_height)
        peer_manager.register_message_handler("block", self._handle_block)
        peer_manager.register_message_handler("chain_height", self._handle_chain_height)
        peer_manager.register_message_handler("new_block", self._handle_new_block)
        peer_manager.register_message_handler("request_chain", self._handle_request_chain)
        peer_manager.register_message_handler("chain_data", self._handle_chain_data)

    async def _handle_connection(self, websocket: WebSocketServerProtocol, path: str):
        """
        Handle a new WebSocket connection.

        Args:
            websocket: The WebSocket connection.
            path: The connection path.
        """
        # Create a peer for the connection
        peer = Peer(f"ws://{websocket.remote_address[0]}:{websocket.remote_address[1]}", websocket)

        try:
            # Wait for the hello message
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=node_config.get("connection_timeout"))
                data = json.loads(message)

                if data.get("type") == "hello":
                    peer.node_id = data.get("from")
                    peer.update_stats(message_received=True)

                    # Send hello response with public key
                    hello_response = {
                        "type": "hello",
                        "from": NODE_ID,
                        "timestamp": time.time(),
                        "public_key": key_manager.get_public_key_base64()
                    }
                    await websocket.send(json.dumps(hello_response))

                    # Request the chain from the peer
                    request_chain_message = {
                        "type": "request_chain",
                        "from": NODE_ID,
                        "timestamp": time.time()
                    }
                    await websocket.send(json.dumps(request_chain_message))

                    logger.info(f"New peer connected: {peer.node_id} at {peer.url}")
                else:
                    logger.warning(f"Expected hello message, got {data.get('type')}")
                    return
            except asyncio.TimeoutError:
                logger.warning("Timeout waiting for hello message")
                return
            except json.JSONDecodeError:
                logger.warning("Received invalid JSON")
                return

            # Process messages
            async for message in websocket:
                try:
                    data = json.loads(message)
                    peer.update_stats(message_received=True)

                    # Process the message
                    await self._process_message(peer, data)
                except json.JSONDecodeError:
                    logger.error(f"Received invalid JSON from {peer.url}")
                    peer.update_stats(error=True)
                except Exception as e:
                    logger.error(f"Error processing message from {peer.url}: {str(e)}")
                    peer.update_stats(error=True)
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Connection closed by peer: {peer.url}")
        except Exception as e:
            logger.error(f"Error handling connection from {peer.url}: {str(e)}")
            peer.update_stats(error=True)

    async def _process_message(self, peer: Peer, message: Dict[str, Any]):
        """
        Process a message from a peer.

        Args:
            peer: The peer that sent the message.
            message: The message to process.
        """
        message_type = message.get("type")
        if not message_type:
            logger.warning(f"Received message without type from {peer.url}")
            return

        # Call registered handlers
        if message_type in peer_manager.message_handlers:
            for handler in peer_manager.message_handlers[message_type]:
                try:
                    await handler(peer, message)
                except Exception as e:
                    logger.error(f"Error in message handler for {message_type}: {str(e)}")
        else:
            logger.debug(f"No handler for message type: {message_type}")

    async def _handle_get_peers(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a get_peers message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        # Get all peer URLs
        peer_urls = [p.url for p in peer_manager.get_peers() if p.url != peer.url]

        # Send peers response
        peers_response = {
            "type": "peers",
            "from": NODE_ID,
            "timestamp": time.time(),
            "peers": peer_urls
        }

        await peer.websocket.send(json.dumps(peers_response))
        logger.debug(f"Sent {len(peer_urls)} peers to {peer.url}")

    async def _handle_peers(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a peers message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        peer_urls = message.get("peers", [])

        # Add new peers
        for url in peer_urls:
            if url != node_config.get_node_url() and url not in [p.url for p in peer_manager.get_peers()]:
                peer_manager.add_peer(url)

        logger.debug(f"Received {len(peer_urls)} peers from {peer.url}")

    async def _handle_get_mempool(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a get_mempool message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        # Get all transactions in the mempool
        transactions = mempool.get_all()

        # Send mempool response
        mempool_response = {
            "type": "mempool",
            "from": NODE_ID,
            "timestamp": time.time(),
            "transactions": transactions
        }

        await peer.websocket.send(json.dumps(mempool_response))
        logger.debug(f"Sent {len(transactions)} mempool transactions to {peer.url}")

    async def _handle_mempool_tx(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a mempool_tx message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        transaction = message.get("transaction")
        if not transaction:
            logger.warning(f"Received mempool_tx without transaction from {peer.url}")
            return

        # Add the transaction to the mempool
        try:
            mempool.add(transaction["type"], transaction["payload"])
            logger.debug(f"Added transaction to mempool: {transaction['id']}")
        except Exception as e:
            logger.error(f"Error adding transaction to mempool: {str(e)}")

    async def _handle_mempool(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a mempool message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        transactions = message.get("transactions", [])

        # Add transactions to the mempool
        for transaction in transactions:
            try:
                mempool.add(transaction["type"], transaction["payload"])
            except Exception as e:
                logger.error(f"Error adding transaction to mempool: {str(e)}")

        logger.debug(f"Added {len(transactions)} transactions to mempool from {peer.url}")

    async def _handle_get_block(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a get_block message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        block_height = message.get("height")
        if block_height is None:
            logger.warning(f"Received get_block without height from {peer.url}")
            return

        # Get the block
        block = local_blockchain.get_block(block_height)
        if not block:
            logger.warning(f"Block {block_height} not found")
            return

        # Convert the block to a dictionary
        block_dict = {
            "block_hash": block.block_hash,
            "block_height": block.block_height,
            "previous_hash": block.previous_hash,
            "timestamp": block.timestamp,
            "difficulty": block.difficulty,
            "nonce": block.nonce,
            "miner": block.miner,
            "transactions": block.transactions,
            "merkle_root": block.merkle_root,
            "size": block.size,
            "version": block.version
        }

        # Send block response
        block_response = {
            "type": "block",
            "from": NODE_ID,
            "timestamp": time.time(),
            "block": block_dict
        }

        await peer.websocket.send(json.dumps(block_response))
        logger.debug(f"Sent block {block_height} to {peer.url}")

    async def _handle_get_chain_height(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a get_chain_height message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        # Get the chain height
        height = local_blockchain.get_height()

        # Send chain_height response
        height_response = {
            "type": "chain_height",
            "from": NODE_ID,
            "timestamp": time.time(),
            "height": height
        }

        await peer.websocket.send(json.dumps(height_response))
        logger.debug(f"Sent chain height {height} to {peer.url}")

    async def _handle_block(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a block message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        block_dict = message.get("block")
        if not block_dict:
            logger.warning(f"Received block without data from {peer.url}")
            return

        # Convert the dictionary to a Block object
        try:
            block = Block.from_dict(block_dict)
        except Exception as e:
            logger.error(f"Error converting block dictionary to Block object: {str(e)}")
            return

        # Add the block to the chain
        try:
            local_blockchain.add_block(block)
            logger.info(f"Added block {block.block_height} to chain")
        except Exception as e:
            logger.error(f"Error adding block to chain: {str(e)}")

    async def _handle_chain_height(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a chain_height message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        height = message.get("height")
        if height is None:
            logger.warning(f"Received chain_height without height from {peer.url}")
            return

        # Check if we need to sync
        our_height = local_blockchain.get_height()
        if height > our_height:
            logger.info(f"Peer {peer.url} has higher chain height ({height} > {our_height}), syncing")

            # Request missing blocks
            for i in range(our_height + 1, height + 1):
                get_block_message = {
                    "type": "get_block",
                    "from": NODE_ID,
                    "timestamp": time.time(),
                    "height": i
                }

                await peer.websocket.send(json.dumps(get_block_message))
                logger.debug(f"Requested block {i} from {peer.url}")

    async def _handle_new_block(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a new_block message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        block_dict = message.get("block")
        if not block_dict:
            logger.warning(f"Received new_block without block data from {peer.url}")
            return

        # Convert the dictionary to a Block object
        try:
            block = Block.from_dict(block_dict)
        except Exception as e:
            logger.error(f"Error converting block dictionary to Block object: {str(e)}")
            return

        # Add the block to the local blockchain
        accepted = local_blockchain.add_block(block)

        logger.info(f"Block accepted: {accepted} — Height {block.block_height}")

    async def _handle_request_chain(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a request_chain message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        # Get the chain
        blocks = local_blockchain.get_chain()

        # Convert blocks to dictionaries
        chain_data = []
        for block in blocks:
            chain_data.append({
                "block_hash": block.block_hash,
                "block_height": block.block_height,
                "previous_hash": block.previous_hash,
                "timestamp": block.timestamp,
                "difficulty": block.difficulty,
                "nonce": block.nonce,
                "miner": block.miner,
                "transactions": block.transactions,
                "merkle_root": block.merkle_root,
                "size": block.size,
                "version": block.version
            })

        # Send chain_data response
        chain_data_response = {
            "type": "chain_data",
            "from": NODE_ID,
            "timestamp": time.time(),
            "chain": chain_data
        }

        await peer.websocket.send(json.dumps(chain_data_response))
        logger.debug(f"Sent chain data with {len(chain_data)} blocks to {peer.url}")

    async def _handle_chain_data(self, peer: Peer, message: Dict[str, Any]):
        """
        Handle a chain_data message.

        Args:
            peer: The peer that sent the message.
            message: The message.
        """
        chain_data = message.get("chain")
        if not chain_data:
            logger.warning(f"Received chain_data without chain from {peer.url}")
            return

        # Convert dictionaries to Block objects
        blocks = []
        try:
            for block_dict in chain_data:
                block = Block.from_dict(block_dict)
                blocks.append(block)
        except Exception as e:
            logger.error(f"Error converting block dictionaries to Block objects: {str(e)}")
            return

        # Replace the chain if the new chain is longer and valid
        accepted = local_blockchain.validate_chain(blocks)

        # If the chain is valid, replace the local blockchain
        if accepted:
            try:
                # Clear the database and add all blocks
                Block.clear_all()
                for block in blocks:
                    block.save()
                logger.info(f"Replaced chain with new chain of length {len(blocks)}")
            except Exception as e:
                logger.error(f"Error replacing chain: {str(e)}")

        logger.info(f"Chain replaced: {accepted} — Length: {len(blocks)}")

    async def _sync_mempool(self):
        """
        Synchronize the mempool with peers.
        """
        while self.running:
            try:
                # Request mempool from a random peer
                peers = peer_manager.get_connected_peers()
                if peers:
                    # Choose a random peer
                    import random
                    peer = random.choice(peers)

                    # Request mempool
                    get_mempool_message = {
                        "type": "get_mempool",
                        "from": NODE_ID,
                        "timestamp": time.time()
                    }

                    await peer.websocket.send(json.dumps(get_mempool_message))
                    logger.debug(f"Requested mempool from {peer.url}")
            except Exception as e:
                logger.error(f"Error syncing mempool: {str(e)}")

            # Wait before the next sync
            await asyncio.sleep(node_config.get("mempool_sync_interval"))

    async def _sync_chain(self):
        """
        Synchronize the chain with peers.
        """
        while self.running:
            try:
                # Request chain height from all peers
                get_height_message = {
                    "type": "get_chain_height",
                    "from": NODE_ID,
                    "timestamp": time.time()
                }

                await peer_manager.broadcast(get_height_message)
                logger.debug("Requested chain height from all peers")
            except Exception as e:
                logger.error(f"Error syncing chain: {str(e)}")

            # Wait before the next sync
            await asyncio.sleep(node_config.get("block_sync_interval"))

    async def broadcast_new_block(self, block: Block):
        """
        Broadcast a new block to all peers.

        Args:
            block: The block to broadcast.
        """
        try:
            # Add the block to the local blockchain
            accepted = local_blockchain.add_block(block)

            if accepted:
                # Convert the block to a dictionary
                block_dict = {
                    "block_hash": block.block_hash,
                    "block_height": block.block_height,
                    "previous_hash": block.previous_hash,
                    "timestamp": block.timestamp,
                    "difficulty": block.difficulty,
                    "nonce": block.nonce,
                    "miner": block.miner,
                    "transactions": block.transactions,
                    "merkle_root": block.merkle_root,
                    "size": block.size,
                    "version": block.version
                }

                # Broadcast the block to all peers
                new_block_message = {
                    "type": "new_block",
                    "from": NODE_ID,
                    "timestamp": time.time(),
                    "block": block_dict
                }

                await peer_manager.broadcast(new_block_message)
                logger.info(f"Broadcast new block {block.block_height} to all peers")

                return True
            else:
                logger.warning(f"Failed to add block {block.block_height} to local blockchain")
                return False
        except Exception as e:
            logger.error(f"Error broadcasting new block: {str(e)}")
            return False


# Create a global instance of the node server
node_server = NodeServer()

async def main():
    """
    Main entry point for the node server.
    """
    try:
        await node_server.start()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, stopping server")
    except Exception as e:
        logger.error(f"Error starting server: {str(e)}")
    finally:
        await node_server.stop()

if __name__ == "__main__":
    asyncio.run(main())
