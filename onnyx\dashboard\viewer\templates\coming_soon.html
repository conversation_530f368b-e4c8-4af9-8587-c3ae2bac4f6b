{% extends "base_simple.html" %}

{% block title %}Coming Soon - {{ sela_name }}{% endblock %}

{% block content %}
<div class="coming-soon-container">
    <div class="coming-soon-card">
        <div class="coming-soon-logo">
            <i class="fas fa-store-alt"></i>
        </div>
        <h1 class="coming-soon-title">Coming Soon</h1>

        {% if sela_name %}
        <div class="sela-info">
            <h2 class="sela-name">{{ sela_name }}</h2>
            {% if sela_type %}
            <div class="sela-type">
                <span class="sela-type-label">Type:</span>
                <span class="sela-type-value">{{ sela_type }}</span>
            </div>
            {% endif %}

            {% if owner_name %}
            <div class="sela-owner">
                <span class="sela-owner-label">Owner:</span>
                <span class="sela-owner-value">{{ owner_name }}</span>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <p class="coming-soon-message">
            This Sela shop window is under construction. Soon you'll be able to explore all the services, tokens, and activities of this Sela on the Onnyx blockchain.
        </p>

        <div class="coming-soon-features">
            <div class="feature">
                <i class="fas fa-coins"></i>
                <span>Token Management</span>
            </div>
            <div class="feature">
                <i class="fas fa-exchange-alt"></i>
                <span>Transactions</span>
            </div>
            <div class="feature">
                <i class="fas fa-chart-line"></i>
                <span>Analytics</span>
            </div>
            <div class="feature">
                <i class="fas fa-users"></i>
                <span>Customer Engagement</span>
            </div>
        </div>

        <div class="coming-soon-footer">
            <p>Part of the <strong>Onnyx Blockchain</strong> ecosystem</p>
            <a href="{{ url_for('index') }}" class="btn-primary">
                <i class="fas fa-home"></i> Return to Explorer
            </a>
        </div>
    </div>
</div>
{% endblock %}
