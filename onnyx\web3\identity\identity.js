/**
 * Onnyx Identity Creation Script
 *
 * This script handles the creation of identities in the Onnyx blockchain.
 * It includes functions for:
 * - Generating cryptographic keys
 * - Creating visual signatures
 * - Submitting identity data to the blockchain
 * - Handling nation and tribe selection
 */

// Nation data structure with tribes/clans/princes/families
const NATIONS_DATA = {
  // She<PERSON>'s Lineage
  'Israel': {
    type: 'tribes',
    members: ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Manasseh']
  },
  '<PERSON><PERSON><PERSON>': {
    type: 'princes',
    members: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>mah']
  },
  'Edom': {
    type: 'dukes',
    members: ['<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>']
  },
  '<PERSON><PERSON>': {
    type: 'clans',
    members: ['Ephah', 'Epher', 'Hanoch', 'Abida', 'Eldaah']
  },
  'Moab': {
    type: 'families',
    members: ['Emim', 'Horim', 'Zuzim', 'Rephaim', 'Zamzummim']
  },
  'Ammon': {
    type: 'families',
    members: ['Zophim', 'Meunim', 'Emin', 'Anakim']
  },
  'Aram': {
    type: 'houses',
    members: ['Uz', 'Hul', 'Gether', 'Mash']
  },

  // Ham's Lineage
  'Egypt': {
    type: 'houses',
    members: ['Ludim', 'Anamim', 'Lehabim', 'Naphtuhim', 'Pathrusim', 'Casluhim', 'Caphtorim']
  },
  'Cush': {
    type: 'kingdoms',
    members: ['Seba', 'Havilah', 'Sabtah', 'Raamah', 'Sabtechah', 'Nimrod']
  },
  'Canaan': {
    type: 'peoples',
    members: ['Sidon', 'Heth', 'Jebusite', 'Amorite', 'Girgasite', 'Hivite', 'Arkite', 'Sinite', 'Arvadite', 'Zemarite', 'Hamathite']
  },
  'Put': {
    type: 'regions',
    members: ['Cyrene', 'Libya', 'Phut', 'Lubim']
  },

  // Japheth's Lineage
  'Javan': {
    type: 'isles',
    members: ['Elishah', 'Tarshish', 'Kittim', 'Dodanim']
  },
  'Gomer': {
    type: 'bands',
    members: ['Ashkenaz', 'Riphath', 'Togarmah']
  },
  'Magog': {
    type: 'hordes',
    members: ['Gog', 'Meshech', 'Tubal']
  }
};

// DOM elements
let fullnameInput, nationSelect, tribeContainer, tribeSelect, purposeInput, descriptionInput;
let birthdateInput, regionInput, soulSealInput, covenantCheckbox;
let generateBtn, generateVisualBtn, uploadVisualInput;
let identityCanvas, sigCaption, identityDetails;
let identityIdSpan, publicKeySpan, createdAtSpan;
let successMessage, errorMessage;

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Get DOM elements
  fullnameInput = document.getElementById('fullname');
  nationSelect = document.getElementById('nation');
  tribeContainer = document.getElementById('tribe-container');
  tribeSelect = document.getElementById('tribe');
  purposeInput = document.getElementById('purpose');
  descriptionInput = document.getElementById('description');
  birthdateInput = document.getElementById('birthdate');
  regionInput = document.getElementById('region');
  soulSealInput = document.getElementById('soul-seal');
  covenantCheckbox = document.getElementById('covenant-checkbox');
  generateBtn = document.getElementById('generate-identity-btn');
  generateVisualBtn = document.getElementById('generate-visual-btn');
  uploadVisualInput = document.getElementById('upload-visual');
  identityCanvas = document.getElementById('identityCanvas');
  sigCaption = document.querySelector('.sig-caption');
  identityDetails = document.getElementById('identity-details');
  identityIdSpan = document.getElementById('identity-id');
  publicKeySpan = document.getElementById('public-key');
  createdAtSpan = document.getElementById('created-at');
  successMessage = document.getElementById('success-message');
  errorMessage = document.getElementById('error-message');

  // Initialize the canvas with a default pattern
  initializeCanvas();

  // Add event listeners
  nationSelect.addEventListener('change', updateTribeOptions);
  generateBtn.addEventListener('click', createIdentity);
  generateVisualBtn.addEventListener('click', generateVisualSignaturePreview);
  uploadVisualInput.addEventListener('change', handleImageUpload);

  // Initialize tooltips
  initializeTooltips();
});

/**
 * Initialize the canvas with a default pattern
 */
function initializeCanvas() {
  const ctx = identityCanvas.getContext('2d');

  // Set background
  ctx.fillStyle = "#000";
  ctx.fillRect(0, 0, identityCanvas.width, identityCanvas.height);

  // Add placeholder text
  ctx.font = "16px Arial";
  ctx.fillStyle = "#a64dff"; // Purple
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.fillText("Enter your details and", identityCanvas.width/2, identityCanvas.height/2 - 15);
  ctx.fillText("generate your identity", identityCanvas.width/2, identityCanvas.height/2 + 15);

  // Add border
  ctx.strokeStyle = "#a64dff"; // Purple
  ctx.lineWidth = 2;
  ctx.strokeRect(10, 10, identityCanvas.width - 20, identityCanvas.height - 20);
}

/**
 * Update tribe options based on selected nation
 */
function updateTribeOptions() {
  const selectedNation = nationSelect.value;

  // Clear existing options
  tribeSelect.innerHTML = '<option value="">Select your tribe...</option>';

  if (selectedNation && NATIONS_DATA[selectedNation]) {
    const nationData = NATIONS_DATA[selectedNation];

    // Update label based on nation type
    const tribeLabel = document.querySelector('label[for="tribe"]');
    tribeLabel.textContent = `🏛️ ${capitalizeFirstLetter(nationData.type)}`;

    // Add options
    nationData.members.forEach(member => {
      const option = document.createElement('option');
      option.value = member;
      option.textContent = member;
      tribeSelect.appendChild(option);
    });

    // Show tribe container with a smooth transition
    tribeContainer.style.opacity = '0';
    tribeContainer.style.display = 'block';

    // Trigger reflow
    void tribeContainer.offsetWidth;

    // Fade in
    tribeContainer.style.transition = 'opacity 0.3s ease';
    tribeContainer.style.opacity = '1';

    // Add a highlight effect to indicate the new field
    tribeContainer.classList.add('highlight-field');
    setTimeout(() => {
      tribeContainer.classList.remove('highlight-field');
    }, 1000);
  } else {
    // Hide tribe container if no nation selected
    tribeContainer.style.display = 'none';
  }
}

/**
 * Capitalize the first letter of a string
 */
function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * Generate a cryptographic key pair
 * In a real implementation, this would use proper cryptography
 * @param {string} soulSeal - Optional soul seal phrase for added entropy
 */
function generateKeyPair(soulSeal = '') {
  // For demonstration purposes, we're using a simple hash
  // In a real implementation, this would use proper asymmetric cryptography
  const timestamp = new Date().getTime();
  const randomValue = Math.random().toString(36).substring(2);

  // Add soul seal for additional entropy
  const entropy = `${timestamp}${randomValue}${soulSeal}`;

  // Generate a "public key" (this is just for demonstration)
  const publicKey = sha256(entropy);

  // Generate a "private key" (this is just for demonstration)
  const privateKey = sha256(`${publicKey}${randomValue}${soulSeal}`);

  return { publicKey, privateKey };
}

/**
 * Simple SHA-256 hash function for demonstration
 * In a real implementation, use a proper cryptographic library
 */
function sha256(message) {
  // This is a placeholder for a real SHA-256 implementation
  // In a real app, use a proper crypto library
  let hash = 0;
  for (let i = 0; i < message.length; i++) {
    const char = message.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(16).padStart(64, '0');
}

/**
 * Generate visual signature for the identity
 */
function generateVisualSignature(name, nation, tribe, purpose, publicKey) {
  const ctx = identityCanvas.getContext('2d');

  // Clear canvas
  ctx.clearRect(0, 0, identityCanvas.width, identityCanvas.height);

  // Set background
  ctx.fillStyle = "#000";
  ctx.fillRect(0, 0, identityCanvas.width, identityCanvas.height);

  // Create a ceremonial border
  ctx.strokeStyle = "#a64dff"; // Purple
  ctx.lineWidth = 5;
  ctx.strokeRect(10, 10, identityCanvas.width - 20, identityCanvas.height - 20);

  // Add a gold inner border
  ctx.strokeStyle = "#ffd700"; // Gold
  ctx.lineWidth = 2;
  ctx.strokeRect(20, 20, identityCanvas.width - 40, identityCanvas.height - 40);

  // Generate a unique pattern based on the public key
  const centerX = identityCanvas.width / 2;
  const centerY = identityCanvas.height / 2;
  const maxRadius = Math.min(centerX, centerY) - 30;

  // Create a more minimal, elegant pattern based on the public key
  // First, draw a few key connection lines
  for (let i = 0; i < 3; i++) {
    const startAngle = (i / 3) * Math.PI * 2;
    const endAngle = ((i + 1.5) / 3) * Math.PI * 2;

    const startX = centerX + Math.cos(startAngle) * (maxRadius * 0.8);
    const startY = centerY + Math.sin(startAngle) * (maxRadius * 0.8);
    const endX = centerX + Math.cos(endAngle) * (maxRadius * 0.8);
    const endY = centerY + Math.sin(endAngle) * (maxRadius * 0.8);

    // Draw the connecting line
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = '#a64dff'; // Purple
    ctx.lineWidth = 1;
    ctx.stroke();
  }

  // Add a few distinctive nodes based on the hash
  for (let i = 0; i < 3; i++) {
    const value = parseInt(publicKey.substr(i * 10, 2), 16);
    const angle = (value / 255) * Math.PI * 2;
    const radius = maxRadius * 0.6;

    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;

    // Draw a circle at this point
    ctx.beginPath();
    ctx.arc(x, y, 5, 0, Math.PI * 2);
    ctx.fillStyle = '#a64dff'; // Purple
    ctx.fill();
  }

  // Add a subtle glow effect to the canvas
  identityCanvas.style.boxShadow = "0 0 30px rgba(166, 77, 255, 0.7)";

  // Update the caption
  sigCaption.innerHTML = `Your identity is now bound to the covenant.<br>Welcome, ${name} of ${tribe ? tribe : nation}.`;
}

/**
 * Create a new identity on the blockchain
 */
async function createIdentity() {
  // Get form values
  const name = fullnameInput.value.trim();
  const nation = nationSelect.value;
  const tribe = tribeSelect.value;
  const purpose = purposeInput.value.trim();
  const description = descriptionInput.value.trim();
  const birthdate = birthdateInput.value;
  const region = regionInput.value.trim();
  const soulSeal = soulSealInput.value.trim();
  const covenantAgreed = covenantCheckbox.checked;

  // Validate inputs
  if (!name) {
    showError("Please enter your full name");
    return;
  }

  if (!nation) {
    showError("Please select your ancestral nation");
    return;
  }

  if (tribeContainer.style.display !== 'none' && !tribe) {
    showError(`Please select your ${NATIONS_DATA[nation].type}`);
    return;
  }

  if (!birthdate) {
    showError("Please enter your date of birth");
    return;
  }

  if (!purpose) {
    showError("Please enter your purpose or calling");
    return;
  }

  if (!soulSeal) {
    showError("Please enter your soul seal phrase");
    return;
  }

  if (!covenantAgreed) {
    showError("You must affirm the covenant principles to create an identity");
    return;
  }

  try {
    // Show a ceremonial loading effect
    generateBtn.textContent = "Creating Identity...";
    generateBtn.disabled = true;

    // Add a pulsing effect to the button
    generateBtn.classList.add('creating-identity');

    // Generate key pair (with added entropy from soul seal)
    const { publicKey, privateKey } = generateKeyPair(soulSeal);

    // Prepare metadata
    const metadata = {
      name: name,
      nation: nation,
      tribe: tribe || null,
      birthdate: birthdate,
      region: region || null,
      purpose: purpose,
      soul_seal: soulSeal,
      description: description || null,
      created_at: new Date().toISOString()
    };

    // Simulate blockchain delay (1.5 seconds)
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Generate visual signature
    generateVisualSignature(name, nation, tribe, purpose, publicKey);

    // In a real implementation, this would call the blockchain API
    // For now, we'll simulate a successful response
    const identityId = sha256(publicKey + soulSeal);
    const createdAt = new Date().toLocaleString();

    // Show identity details
    identityIdSpan.textContent = identityId.substring(0, 16) + '...';
    publicKeySpan.textContent = publicKey.substring(0, 16) + '...';
    createdAtSpan.textContent = createdAt;
    identityDetails.style.display = 'block';

    // Show success message with ceremonial text
    showSuccess("Identity created and bound to the covenant. Welcome to Onnyx!");

    // Update the button state
    generateBtn.classList.remove('creating-identity');
    generateBtn.disabled = true;
    generateBtn.textContent = "Identity Created";
    generateBtn.style.backgroundColor = "#4CAF50"; // Green color to indicate success

    // Store the identity data in local storage (in a real app, handle this securely)
    localStorage.setItem('onnyx_private_key', privateKey);
    localStorage.setItem('onnyx_identity_id', identityId);
    localStorage.setItem('onnyx_identity_name', name);
    localStorage.setItem('onnyx_identity_nation', nation);
    localStorage.setItem('onnyx_identity_created', createdAt);

    // In a real implementation, we would call the API here
    // For example:
    /*
    const response = await fetch('/api/identity/createidentity', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: name,
        public_key: publicKey,
        metadata: metadata,
        message: `createidentity|${name}|${soulSeal}`,
        signature: "signature_placeholder" // In a real app, sign with private key
      })
    });

    if (!response.ok) {
      throw new Error('Failed to create identity');
    }

    const data = await response.json();
    identityIdSpan.textContent = data.identity_id;
    */

  } catch (error) {
    console.error('Error creating identity:', error);
    showError("Error creating identity. Please try again.");

    // Reset button state
    generateBtn.classList.remove('creating-identity');
    generateBtn.disabled = false;
    generateBtn.textContent = "🔗 Create My Identity";
  }
}

/**
 * Show success message
 */
function showSuccess(message) {
  successMessage.textContent = message;
  successMessage.style.display = 'block';
  errorMessage.style.display = 'none';
}

/**
 * Show error message
 */
function showError(message) {
  errorMessage.textContent = message;
  errorMessage.style.display = 'block';
  successMessage.style.display = 'none';
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
  const tooltips = document.querySelectorAll('.tooltip-icon');

  tooltips.forEach(tooltip => {
    tooltip.addEventListener('mouseenter', function() {
      const title = this.getAttribute('title');
      if (!title) return;

      // Create tooltip element
      const tooltipEl = document.createElement('div');
      tooltipEl.className = 'tooltip-popup';
      tooltipEl.textContent = title;

      // Position the tooltip
      const rect = this.getBoundingClientRect();
      tooltipEl.style.top = `${rect.top - 10}px`;
      tooltipEl.style.left = `${rect.left + rect.width / 2}px`;

      // Add to document
      document.body.appendChild(tooltipEl);

      // Store the tooltip element reference
      this.tooltipEl = tooltipEl;

      // Remove the title to prevent default browser tooltip
      this.oldTitle = title;
      this.setAttribute('title', '');

      // Animate in
      setTimeout(() => {
        tooltipEl.style.opacity = '1';
        tooltipEl.style.transform = 'translate(-50%, -100%)';
      }, 10);
    });

    tooltip.addEventListener('mouseleave', function() {
      if (this.tooltipEl) {
        // Animate out
        this.tooltipEl.style.opacity = '0';

        // Remove after animation
        setTimeout(() => {
          if (this.tooltipEl.parentNode) {
            this.tooltipEl.parentNode.removeChild(this.tooltipEl);
          }
        }, 300);

        // Restore title
        if (this.oldTitle) {
          this.setAttribute('title', this.oldTitle);
          this.oldTitle = null;
        }
      }
    });
  });

  // Add tooltip styles
  const style = document.createElement('style');
  style.textContent = `
    .tooltip-popup {
      position: fixed;
      background: rgba(0, 0, 0, 0.9);
      color: #fff;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 0.9rem;
      max-width: 250px;
      z-index: 1000;
      opacity: 0;
      transform: translate(-50%, -90%);
      transition: all 0.3s ease;
      border: 1px solid #a64dff;
      box-shadow: 0 0 10px rgba(166, 77, 255, 0.5);
      pointer-events: none;
    }

    .tooltip-popup::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border-width: 5px;
      border-style: solid;
      border-color: #a64dff transparent transparent transparent;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Generate visual signature preview
 */
function generateVisualSignaturePreview() {
  const name = fullnameInput.value.trim() || 'Anonymous';
  const nation = nationSelect.value || 'Unknown';
  const purpose = purposeInput.value.trim() || 'Seeker';
  const soulSeal = soulSealInput.value.trim() || 'Covenant Member';

  // Generate a temporary public key for preview
  const tempPublicKey = sha256(`${name}${nation}${purpose}${soulSeal}${Date.now()}`);

  // Generate the visual signature
  generateVisualSignature(name, nation, tribeSelect.value, purpose, tempPublicKey);

  // Show a preview message
  sigCaption.innerHTML = `Preview of your identity glyph.<br>This will be finalized when you create your identity.`;
}

/**
 * Handle image upload for visual signature
 */
function handleImageUpload(event) {
  const file = event.target.files[0];
  if (!file) return;

  // Check file type
  if (!file.type.match('image.*')) {
    showError('Please select an image file (JPEG, PNG, etc.)');
    return;
  }

  // Check file size (max 2MB)
  if (file.size > 2 * 1024 * 1024) {
    showError('Image size should be less than 2MB');
    return;
  }

  const reader = new FileReader();

  reader.onload = function(e) {
    const img = new Image();
    img.onload = function() {
      // Draw the uploaded image on the canvas
      const ctx = identityCanvas.getContext('2d');

      // Clear canvas
      ctx.clearRect(0, 0, identityCanvas.width, identityCanvas.height);

      // Set background
      ctx.fillStyle = "#000";
      ctx.fillRect(0, 0, identityCanvas.width, identityCanvas.height);

      // Calculate dimensions to maintain aspect ratio
      let width = img.width;
      let height = img.height;
      const maxDimension = Math.min(identityCanvas.width, identityCanvas.height) - 40;

      if (width > height) {
        if (width > maxDimension) {
          height = height * (maxDimension / width);
          width = maxDimension;
        }
      } else {
        if (height > maxDimension) {
          width = width * (maxDimension / height);
          height = maxDimension;
        }
      }

      // Center the image
      const x = (identityCanvas.width - width) / 2;
      const y = (identityCanvas.height - height) / 2;

      // Draw the image
      ctx.drawImage(img, x, y, width, height);

      // Add a ceremonial border
      ctx.strokeStyle = "#a64dff"; // Purple
      ctx.lineWidth = 5;
      ctx.strokeRect(10, 10, identityCanvas.width - 20, identityCanvas.height - 20);

      // Add a gold inner border
      ctx.strokeStyle = "#ffd700"; // Gold
      ctx.lineWidth = 2;
      ctx.strokeRect(20, 20, identityCanvas.width - 40, identityCanvas.height - 40);

      // Update caption
      sigCaption.innerHTML = `Custom image uploaded.<br>This will be your visual identity in the Onnyx ecosystem.`;
    };

    img.src = e.target.result;
  };

  reader.readAsDataURL(file);
}
