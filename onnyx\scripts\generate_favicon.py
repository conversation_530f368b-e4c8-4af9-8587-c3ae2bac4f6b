#!/usr/bin/env python3
"""
Generate favicon from ONNYX logo

Creates multiple favicon sizes for optimal browser compatibility.
"""

import os
import sys
from PIL import Image

def generate_favicon():
    """Generate favicon files from the ONNYX logo."""
    try:
        # Input and output paths
        logo_path = "onnyx_logo.png"
        favicon_dir = "web/static/images"
        
        if not os.path.exists(logo_path):
            print(f"❌ Logo file not found: {logo_path}")
            return False
        
        # Load the original logo
        logo = Image.open(logo_path)
        print(f"✅ Loaded logo: {logo.size}")
        
        # Ensure output directory exists
        os.makedirs(favicon_dir, exist_ok=True)
        
        # Generate different favicon sizes
        sizes = [16, 32, 48, 64, 128, 256]
        
        for size in sizes:
            # Resize logo maintaining aspect ratio
            favicon = logo.resize((size, size), Image.Resampling.LANCZOS)
            
            # Save as PNG
            favicon_path = os.path.join(favicon_dir, f"favicon-{size}x{size}.png")
            favicon.save(favicon_path, "PNG")
            print(f"✅ Generated: favicon-{size}x{size}.png")
        
        # Generate ICO file (multi-size)
        ico_sizes = [(16, 16), (32, 32), (48, 48)]
        ico_images = []
        
        for size in ico_sizes:
            ico_img = logo.resize(size, Image.Resampling.LANCZOS)
            ico_images.append(ico_img)
        
        ico_path = os.path.join(favicon_dir, "favicon.ico")
        ico_images[0].save(ico_path, format='ICO', sizes=ico_sizes)
        print(f"✅ Generated: favicon.ico")
        
        # Copy main logo as apple-touch-icon
        apple_icon = logo.resize((180, 180), Image.Resampling.LANCZOS)
        apple_path = os.path.join(favicon_dir, "apple-touch-icon.png")
        apple_icon.save(apple_path, "PNG")
        print(f"✅ Generated: apple-touch-icon.png")
        
        print("\n🎉 Favicon generation completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error generating favicon: {e}")
        return False

def main():
    """Main entry point."""
    print("🎨 ONNYX Favicon Generator")
    print("=" * 40)
    
    success = generate_favicon()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
