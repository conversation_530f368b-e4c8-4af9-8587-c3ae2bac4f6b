#!/usr/bin/env python3
"""
Simple ONNYX API Server

Basic API server for production environment health checks.
"""

import os
import sys
import json
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Create FastAPI app
app = FastAPI(
    title="ONNYX Blockchain API",
    description="Simple API for ONNYX blockchain production environment",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def read_root():
    """Root endpoint."""
    return {
        "message": "ONNYX Blockchain API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check database connection
        block_count = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
        tx_count = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
        
        return {
            "status": "healthy",
            "database": "connected",
            "blocks": block_count,
            "transactions": tx_count
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )

@app.get("/api/blocks")
async def get_blocks():
    """Get all blocks."""
    try:
        blocks = db.query("SELECT * FROM blocks ORDER BY block_height ASC")
        return {"blocks": blocks}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/api/blocks/latest")
async def get_latest_block():
    """Get the latest block."""
    try:
        latest = db.query_one("SELECT * FROM blocks ORDER BY block_height DESC LIMIT 1")
        return {"block": latest}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/api/transactions")
async def get_transactions():
    """Get all transactions."""
    try:
        transactions = db.query("SELECT * FROM transactions ORDER BY created_at DESC")
        return {"transactions": transactions}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/api/identities")
async def get_identities():
    """Get all identities."""
    try:
        identities = db.query("SELECT * FROM identities ORDER BY created_at DESC")
        return {"identities": identities}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/api/selas")
async def get_selas():
    """Get all Selas."""
    try:
        selas = db.query("SELECT * FROM selas ORDER BY created_at DESC")
        return {"selas": selas}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
