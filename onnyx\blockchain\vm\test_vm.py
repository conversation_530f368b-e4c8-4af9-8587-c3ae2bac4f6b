"""
Test script for the Onnyx VM.

This script tests the OnnyxVM class defined in vm.py.
"""

import sys
import os
import json

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from vm import OnnyxVM
from opcodes import identity, selas, tokens, ledger

def setup_test_data():
    """Set up test data for the VM tests."""
    # Set up identities
    identity.identities["id1"] = {"name": "Test Identity 1"}
    identity.identities["id2"] = {"name": "Test Identity 2"}
    identity.badges["id1"] = ["CREATOR"]

    # Set up Selas
    selas.selas["id1"] = {"name": "Test Sela 1"}

    # Set up tokens
    tokens.tokens["TKN"] = {"symbol": "TKN", "supply": 1000}

    # Set up ledger
    ledger.balances["id1:TKN"] = 500

def test_validate_transaction():
    """Test transaction validation."""
    print("\nTesting transaction validation...")

    vm = OnnyxVM()

    # Test valid mint transaction
    valid_tx = {
        "type": "mint",
        "txid": "tx1",
        "from": "id1",
        "data": {
            "symbol": "TKN2",
            "supply": 1000
        }
    }

    is_valid, error = vm.validate_transaction(valid_tx)
    print(f"Valid mint transaction: {is_valid}")
    if error:
        print(f"Error: {error}")

    # Test invalid mint transaction (exceeds cap)
    invalid_tx = {
        "type": "mint",
        "txid": "tx2",
        "from": "id1",
        "data": {
            "symbol": "TKN3",
            "supply": 2000  # Exceeds the default cap of 1000
        }
    }

    is_valid, error = vm.validate_transaction(invalid_tx)
    print(f"Invalid mint transaction: {is_valid}")
    if error:
        print(f"Error: {error}")

    # Test invalid transaction type
    invalid_type_tx = {
        "type": "unknown",
        "txid": "tx3",
        "from": "id1",
        "data": {}
    }

    is_valid, error = vm.validate_transaction(invalid_type_tx)
    print(f"Invalid transaction type: {is_valid}")
    if error:
        print(f"Error: {error}")

def test_execute_transaction():
    """Test transaction execution."""
    print("\nTesting transaction execution...")

    vm = OnnyxVM()

    # Test executing a valid mint transaction
    valid_tx = {
        "type": "mint",
        "txid": "tx1",
        "from": "id1",
        "data": {
            "symbol": "TKN2",
            "supply": 1000
        }
    }

    result = vm.execute_transaction(valid_tx)
    print(f"Execute valid transaction: {result['status']}")
    print(f"Message: {result['message']}")

    # Test executing an invalid transaction
    invalid_tx = {
        "type": "mint",
        "txid": "tx2",
        "from": "id1",
        "data": {
            "symbol": "TKN3",
            "supply": 2000  # Exceeds the default cap of 1000
        }
    }

    result = vm.execute_transaction(invalid_tx)
    print(f"Execute invalid transaction: {result['status']}")
    print(f"Message: {result['message']}")

    # Check transaction history
    history = vm.get_transaction_history()
    print(f"Transaction history count: {len(history)}")

    # Check transaction result
    tx_result = vm.get_transaction_result("tx1")
    if tx_result:
        print(f"Transaction result for tx1: {tx_result['status']}")

def test_pending_transactions():
    """Test pending transaction functionality."""
    print("\nTesting pending transactions...")

    vm = OnnyxVM()

    # Add a valid transaction to pending
    valid_tx = {
        "type": "mint",
        "txid": "tx1",
        "from": "id1",
        "data": {
            "symbol": "TKN2",
            "supply": 1000
        }
    }

    result = vm.add_pending_transaction(valid_tx)
    print(f"Add valid transaction to pending: {result['status']}")

    # Add an invalid transaction to pending
    invalid_tx = {
        "type": "mint",
        "txid": "tx2",
        "from": "id1",
        "data": {
            "symbol": "TKN3",
            "supply": 2000  # Exceeds the default cap of 1000
        }
    }

    result = vm.add_pending_transaction(invalid_tx)
    print(f"Add invalid transaction to pending: {result['status']}")

    # Check pending transactions
    pending = vm.get_pending_transactions()
    print(f"Pending transaction count: {len(pending)}")

    # Execute a pending transaction
    if pending:
        result = vm.execute_transaction(pending[0])
        print(f"Execute pending transaction: {result['status']}")

        # Check that it was removed from pending
        pending = vm.get_pending_transactions()
        print(f"Pending transaction count after execution: {len(pending)}")

def main():
    """Run all tests."""
    print("Testing Onnyx VM")
    print("===============")

    # Set up test data
    setup_test_data()

    # Run tests
    test_validate_transaction()
    test_execute_transaction()
    test_pending_transactions()

    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
