"""
Onnyx Nation API Routes

This module provides API routes for the Onnyx nation functionality.
"""

import json
import time
from flask import Blueprint, request, jsonify

from shared.models.nation import Nation
from shared.models.identity import Identity
from utils.validation import validate_request

# Create a blueprint for the nation routes
nation_bp = Blueprint('nation', __name__)

@nation_bp.route('/create', methods=['POST'])
def create_nation():
    """
    Create a new nation.
    
    Request body:
    {
        "name": "Nation Name",
        "description": "Nation Description",
        "founder_id": "identity_id",
        "metadata": {
            "flag": "🏁",
            "motto": "Nation Motto",
            "anthem": "Nation Anthem",
            ...
        }
    }
    
    Returns:
        A JSON response with the created nation
    """
    # Validate the request
    data = validate_request(request, ['name', 'description', 'founder_id'])
    
    # Check if the founder exists
    founder = Identity.get_by_id(data['founder_id'])
    if not founder:
        return jsonify({
            'success': False,
            'error': f"Founder with ID {data['founder_id']} not found"
        }), 404
    
    # Check if a nation with the same name already exists
    existing_nation = Nation.get_by_name(data['name'])
    if existing_nation:
        return jsonify({
            'success': False,
            'error': f"Nation with name {data['name']} already exists"
        }), 409
    
    # Create the nation
    try:
        nation = Nation.create(
            name=data['name'],
            description=data['description'],
            founder_id=data['founder_id'],
            metadata=data.get('metadata', {})
        )
        
        return jsonify({
            'success': True,
            'nation': nation.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@nation_bp.route('/get/<nation_id>', methods=['GET'])
def get_nation(nation_id):
    """
    Get a nation by ID.
    
    Args:
        nation_id: The nation ID
    
    Returns:
        A JSON response with the nation
    """
    # Get the nation
    nation = Nation.get_by_id(nation_id)
    if not nation:
        return jsonify({
            'success': False,
            'error': f"Nation with ID {nation_id} not found"
        }), 404
    
    return jsonify({
        'success': True,
        'nation': nation.to_dict()
    })

@nation_bp.route('/list', methods=['GET'])
def list_nations():
    """
    List all nations.
    
    Query parameters:
        limit: The maximum number of nations to return (default: 100)
        offset: The offset for pagination (default: 0)
    
    Returns:
        A JSON response with the nations
    """
    # Get the query parameters
    limit = int(request.args.get('limit', 100))
    offset = int(request.args.get('offset', 0))
    
    # Get the nations
    nations = Nation.filter("1=1", (), limit=limit, offset=offset)
    
    return jsonify({
        'success': True,
        'nations': [nation.to_dict() for nation in nations],
        'count': len(nations),
        'limit': limit,
        'offset': offset
    })

@nation_bp.route('/update/<nation_id>', methods=['PUT'])
def update_nation(nation_id):
    """
    Update a nation.
    
    Args:
        nation_id: The nation ID
    
    Request body:
    {
        "name": "Updated Nation Name",
        "description": "Updated Nation Description",
        "metadata": {
            "flag": "🏁",
            "motto": "Updated Nation Motto",
            "anthem": "Updated Nation Anthem",
            ...
        },
        "status": "active"
    }
    
    Returns:
        A JSON response with the updated nation
    """
    # Get the nation
    nation = Nation.get_by_id(nation_id)
    if not nation:
        return jsonify({
            'success': False,
            'error': f"Nation with ID {nation_id} not found"
        }), 404
    
    # Validate the request
    data = request.get_json()
    if not data:
        return jsonify({
            'success': False,
            'error': "Invalid request body"
        }), 400
    
    # Update the nation
    if 'name' in data:
        nation.name = data['name']
    
    if 'description' in data:
        nation.description = data['description']
    
    if 'metadata' in data:
        nation.metadata = data['metadata']
    
    if 'status' in data:
        nation.status = data['status']
    
    # Save the nation
    try:
        nation.save()
        
        return jsonify({
            'success': True,
            'nation': nation.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@nation_bp.route('/members/<nation_id>', methods=['GET'])
def get_nation_members(nation_id):
    """
    Get the members of a nation.
    
    Args:
        nation_id: The nation ID
    
    Query parameters:
        limit: The maximum number of members to return (default: 100)
        offset: The offset for pagination (default: 0)
    
    Returns:
        A JSON response with the members
    """
    # Get the nation
    nation = Nation.get_by_id(nation_id)
    if not nation:
        return jsonify({
            'success': False,
            'error': f"Nation with ID {nation_id} not found"
        }), 404
    
    # Get the query parameters
    limit = int(request.args.get('limit', 100))
    offset = int(request.args.get('offset', 0))
    
    # Get the members
    members = nation.get_members(limit=limit, offset=offset)
    
    return jsonify({
        'success': True,
        'members': members,
        'count': len(members),
        'total_count': nation.get_member_count(),
        'limit': limit,
        'offset': offset
    })

@nation_bp.route('/join/<nation_id>', methods=['POST'])
def join_nation(nation_id):
    """
    Join a nation.
    
    Args:
        nation_id: The nation ID
    
    Request body:
    {
        "identity_id": "identity_id"
    }
    
    Returns:
        A JSON response with the result
    """
    # Get the nation
    nation = Nation.get_by_id(nation_id)
    if not nation:
        return jsonify({
            'success': False,
            'error': f"Nation with ID {nation_id} not found"
        }), 404
    
    # Validate the request
    data = validate_request(request, ['identity_id'])
    
    # Get the identity
    identity = Identity.get_by_id(data['identity_id'])
    if not identity:
        return jsonify({
            'success': False,
            'error': f"Identity with ID {data['identity_id']} not found"
        }), 404
    
    # Check if the identity is already in a nation
    if identity.nation_id:
        return jsonify({
            'success': False,
            'error': f"Identity with ID {data['identity_id']} is already in a nation"
        }), 409
    
    # Update the identity
    identity.nation_id = nation_id
    
    # Save the identity
    try:
        identity.save()
        
        return jsonify({
            'success': True,
            'message': f"Identity with ID {data['identity_id']} joined nation with ID {nation_id}"
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@nation_bp.route('/leave', methods=['POST'])
def leave_nation():
    """
    Leave a nation.
    
    Request body:
    {
        "identity_id": "identity_id"
    }
    
    Returns:
        A JSON response with the result
    """
    # Validate the request
    data = validate_request(request, ['identity_id'])
    
    # Get the identity
    identity = Identity.get_by_id(data['identity_id'])
    if not identity:
        return jsonify({
            'success': False,
            'error': f"Identity with ID {data['identity_id']} not found"
        }), 404
    
    # Check if the identity is in a nation
    if not identity.nation_id:
        return jsonify({
            'success': False,
            'error': f"Identity with ID {data['identity_id']} is not in a nation"
        }), 409
    
    # Get the nation
    nation_id = identity.nation_id
    
    # Update the identity
    identity.nation_id = None
    
    # Save the identity
    try:
        identity.save()
        
        return jsonify({
            'success': True,
            'message': f"Identity with ID {data['identity_id']} left nation with ID {nation_id}"
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
