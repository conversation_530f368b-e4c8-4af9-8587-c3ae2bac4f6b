#!/usr/bin/env python
# generate_sample_data.py

import os
import sys
import importlib.util

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Change to the onnyx_viewer directory
viewer_dir = os.path.join(os.path.dirname(__file__), "onnyx_viewer")
os.chdir(viewer_dir)

# Import the sample data generator module
spec = importlib.util.spec_from_file_location("sample_data_generator",
                                             os.path.join(viewer_dir, "generate_sample_data.py"))
sample_data_generator = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sample_data_generator)

if __name__ == "__main__":
    sample_data_generator.main()
