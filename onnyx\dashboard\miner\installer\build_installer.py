"""
Onnyx Miner Installer Builder

This script builds an installer for the Onnyx Miner.
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path

def build_installer():
    """Build the installer."""
    print("Building Onnyx Miner installer...")
    
    # Get the current directory
    current_dir = Path(__file__).parent.absolute()
    
    # Get the project directory
    project_dir = current_dir.parent
    
    # Load the installer configuration
    config_path = current_dir / "installer_config.json"
    
    if not config_path.exists():
        print(f"Error: Installer configuration file not found: {config_path}")
        return False
    
    with open(config_path, "r") as f:
        config = json.load(f)
    
    # Create the build directory
    build_dir = project_dir / "build"
    dist_dir = project_dir / "dist"
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    os.makedirs(build_dir, exist_ok=True)
    os.makedirs(dist_dir, exist_ok=True)
    
    # Build the wheel
    print("Building wheel...")
    
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "wheel", ".", "-w", "dist"],
            cwd=project_dir,
            check=True
        )
    except subprocess.CalledProcessError as e:
        print(f"Error building wheel: {e}")
        return False
    
    # Find the wheel file
    wheel_files = list(dist_dir.glob("*.whl"))
    
    if not wheel_files:
        print("Error: No wheel file found")
        return False
    
    wheel_file = wheel_files[0]
    
    # Create the installer script
    print("Creating installer script...")
    
    installer_script = build_dir / "install_onnyx_miner.py"
    
    with open(installer_script, "w") as f:
        f.write(f"""#!/usr/bin/env python3
\"\"\"
Onnyx Miner Installer

This script installs the Onnyx Miner.
\"\"\"

import os
import sys
import subprocess
import shutil
import tempfile
import argparse
from pathlib import Path

def install_onnyx_miner():
    \"\"\"Install the Onnyx Miner.\"\"\"
    print("Installing Onnyx Miner...")
    
    # Parse arguments
    parser = argparse.ArgumentParser(description="Install Onnyx Miner")
    parser.add_argument("--identity-id", help="The identity ID")
    parser.add_argument("--name", help="The identity name")
    parser.add_argument("--sela-id", help="The Sela ID")
    parser.add_argument("--sela-name", help="The Sela name")
    parser.add_argument("--sela-type", default="BUSINESS", help="The Sela type")
    parser.add_argument("--token-type", default="", help="The token type")
    parser.add_argument("--api-url", default="http://localhost:8000", help="The API URL")
    parser.add_argument("--config", help="The path to the configuration file")
    
    args = parser.parse_args()
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Download the wheel file
        wheel_url = "{config['wheel_url']}"
        wheel_file = os.path.join(temp_dir, "onnyx_miner.whl")
        
        try:
            import requests
            print(f"Downloading Onnyx Miner from {wheel_url}...")
            response = requests.get(wheel_url)
            response.raise_for_status()
            
            with open(wheel_file, "wb") as f:
                f.write(response.content)
        except Exception as e:
            print(f"Error downloading wheel file: {{e}}")
            return False
        
        # Install the wheel file
        try:
            print("Installing Onnyx Miner...")
            subprocess.run(
                [sys.executable, "-m", "pip", "install", wheel_file],
                check=True
            )
        except subprocess.CalledProcessError as e:
            print(f"Error installing Onnyx Miner: {{e}}")
            return False
        
        # Set up the Onnyx Miner
        if args.identity_id and args.name and args.sela_id and args.sela_name:
            try:
                print("Setting up Onnyx Miner...")
                cmd = [
                    "onnyx", "setup",
                    "--identity-id", args.identity_id,
                    "--name", args.name,
                    "--sela-id", args.sela_id,
                    "--sela-name", args.sela_name,
                    "--sela-type", args.sela_type,
                    "--api-url", args.api_url
                ]
                
                if args.token_type:
                    cmd.extend(["--token-type", args.token_type])
                
                if args.config:
                    cmd.extend(["--config", args.config])
                
                subprocess.run(cmd, check=True)
            except subprocess.CalledProcessError as e:
                print(f"Error setting up Onnyx Miner: {{e}}")
                return False
        
        print("Onnyx Miner installed successfully!")
        print("To start the GUI, run: onnyx gui")
        
        return True

if __name__ == "__main__":
    install_onnyx_miner()
""")
    
    # Create the installer configuration
    installer_config = build_dir / "installer_config.json"
    
    with open(installer_config, "w") as f:
        json.dump({
            "wheel_url": config["wheel_url"],
            "version": config["version"]
        }, f, indent=2)
    
    # Create the installer package
    print("Creating installer package...")
    
    installer_dir = dist_dir / "installer"
    os.makedirs(installer_dir, exist_ok=True)
    
    shutil.copy(installer_script, installer_dir / "install_onnyx_miner.py")
    shutil.copy(installer_config, installer_dir / "installer_config.json")
    shutil.copy(wheel_file, installer_dir / wheel_file.name)
    
    # Create a zip file
    print("Creating zip file...")
    
    shutil.make_archive(
        dist_dir / "onnyx-miner-installer",
        "zip",
        installer_dir
    )
    
    print(f"Installer created: {dist_dir / 'onnyx-miner-installer.zip'}")
    
    return True

if __name__ == "__main__":
    build_installer()
