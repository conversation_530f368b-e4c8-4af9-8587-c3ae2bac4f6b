# src/judah/engine.py

import time
import json
import os
from typing import Dict, Any, List, Optional, Tuple
from src.tokens.ledger import TokenLedger
from src.zeman.ledger import ZemanLedger
from src.etzem.score import EtzemScorer
from src.identity.registry import IdentityRegistry
from src.business.sela import SelaRegistry
from src.chain.txlog import TxLogger

class JudahEngine:
    """
    Judah Engine - The core economic logic layer of Onnyx.
    
    The Judah Engine governs:
    - Transaction Fees: Dynamic fees with Zeman/Etzem discounts
    - Rebate Mechanics: Rebates for creators, builders, educators
    - ONX Flow Control: Fee routing, burn %, reward pool tracking
    - Staking Pool Support: Lock ONX for roles, rewards, or privileges
    - Economic Governance: Council scrolls can adjust parameters
    """
    
    def __init__(self, token_ledger: TokenLedger = None, zeman_ledger: ZemanLedger = None,
                 etzem_scorer: EtzemScorer = None, identity_registry: IdentityRegistry = None,
                 sela_registry: SelaRegistry = None, txlog: TxLogger = None, config_path: str = None):
        """
        Initialize the Judah Engine.
        
        Args:
            token_ledger: The Token Ledger to use for token balance management.
            zeman_ledger: The Zeman Ledger to use for time credit tracking.
            etzem_scorer: The Etzem Scorer to use for trust score calculation.
            identity_registry: The Identity Registry to use for identity information.
            sela_registry: The Sela Registry to use for business information.
            txlog: The Transaction Logger to use for transaction logging.
            config_path: Path to the configuration file. If None, a default path in the data directory will be used.
        """
        self.ledger = token_ledger or TokenLedger()
        self.zeman = zeman_ledger or ZemanLedger()
        self.etzem = etzem_scorer or EtzemScorer()
        self.identity = identity_registry or IdentityRegistry()
        self.sela = sela_registry or SelaRegistry()
        self.txlog = txlog or TxLogger()
        
        # Set up configuration path
        if config_path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.config_path = os.path.join(data_dir, "judah_config.json")
        else:
            self.config_path = config_path
        
        # Default configuration
        self.config = {
            "base_fee": 0.05,  # Base transaction fee (5%)
            "burn_percent": 0.3,  # Percentage of fees to burn (30%)
            "reward_percent": 0.7,  # Percentage of fees to reward pool (70%)
            "rebate_roles": ["CREATOR", "EDUCATOR", "BUILDER"],  # Roles eligible for rebates
            "rebate_percent": 0.5,  # Percentage of fees to rebate (50%)
            "zeman_discount_tiers": [
                {"hours": 100, "discount": 0.5},  # 50% discount for 100+ hours
                {"hours": 50, "discount": 0.25},  # 25% discount for 50+ hours
                {"hours": 0, "discount": 0.0}  # 0% discount for 0+ hours
            ],
            "etzem_discount_tiers": [
                {"score": 300, "discount": 0.3},  # 30% discount for 300+ Etzem score
                {"score": 200, "discount": 0.2},  # 20% discount for 200+ Etzem score
                {"score": 100, "discount": 0.1},  # 10% discount for 100+ Etzem score
                {"score": 0, "discount": 0.0}  # 0% discount for 0+ Etzem score
            ],
            "staking_tiers": [
                {"amount": 10000, "benefits": ["GOVERNANCE", "REDUCED_FEES", "PRIORITY_PROCESSING"]},
                {"amount": 5000, "benefits": ["GOVERNANCE", "REDUCED_FEES"]},
                {"amount": 1000, "benefits": ["REDUCED_FEES"]}
            ],
            "staking_lock_period": 30 * 24 * 60 * 60,  # 30 days in seconds
            "staking_reward_rate": 0.05,  # 5% annual reward rate
            "min_fee": 0.01,  # Minimum fee (1%)
            "reward_pool_address": "reward_pool",  # Address of the reward pool
            "burn_address": "burn_address",  # Address of the burn pool
            "last_updated": int(time.time()),
            "last_updated_by": "system"
        }
        
        # Load configuration
        self._load_config()
    
    def _load_config(self):
        """Load the configuration from the file."""
        try:
            if os.path.exists(self.config_path) and os.path.getsize(self.config_path) > 0:
                with open(self.config_path, "r") as f:
                    self.config = json.load(f)
        except Exception as e:
            print(f"Error loading Judah Engine configuration: {str(e)}")
    
    def _save_config(self):
        """Save the configuration to the file."""
        with open(self.config_path, "w") as f:
            json.dump(self.config, f, indent=2)
    
    def calculate_zeman_discount(self, identity_id: str) -> float:
        """
        Calculate the Zeman discount for an identity.
        
        Args:
            identity_id: The identity ID to calculate the discount for.
            
        Returns:
            The Zeman discount (0.0 to 1.0).
        """
        # Get Zeman hours
        hours = self.zeman.get_total(identity_id)
        
        # Calculate discount based on tiers
        for tier in self.config["zeman_discount_tiers"]:
            if hours >= tier["hours"]:
                return tier["discount"]
        
        return 0.0
    
    def calculate_etzem_discount(self, identity_id: str) -> float:
        """
        Calculate the Etzem discount for an identity.
        
        Args:
            identity_id: The identity ID to calculate the discount for.
            
        Returns:
            The Etzem discount (0.0 to 1.0).
        """
        # Get Etzem score
        etzem_score = self.etzem.calculate(identity_id)["score"]
        
        # Calculate discount based on tiers
        for tier in self.config["etzem_discount_tiers"]:
            if etzem_score >= tier["score"]:
                return tier["discount"]
        
        return 0.0
    
    def calculate_total_discount(self, identity_id: str) -> Dict[str, Any]:
        """
        Calculate the total discount for an identity.
        
        Args:
            identity_id: The identity ID to calculate the discount for.
            
        Returns:
            A dictionary with the discount details.
        """
        # Calculate individual discounts
        zeman_discount = self.calculate_zeman_discount(identity_id)
        etzem_discount = self.calculate_etzem_discount(identity_id)
        
        # Calculate total discount (capped at 70%)
        total_discount = min(zeman_discount + etzem_discount, 0.7)
        
        return {
            "zeman_discount": zeman_discount,
            "etzem_discount": etzem_discount,
            "total_discount": total_discount
        }
    
    def apply_transaction_fee(self, identity_id: str, tx_type: str = "standard", 
                             amount: float = 0.0, token_id: str = "ONX") -> Dict[str, Any]:
        """
        Apply a transaction fee to an identity.
        
        Args:
            identity_id: The identity ID to apply the fee to.
            tx_type: The type of transaction.
            amount: The amount of the transaction (for percentage-based fees).
            token_id: The token ID of the transaction.
            
        Returns:
            A dictionary with the fee details.
            
        Raises:
            ValueError: If the identity does not have enough ONX to pay the fee.
        """
        # Calculate discount
        discount_info = self.calculate_total_discount(identity_id)
        total_discount = discount_info["total_discount"]
        
        # Calculate fee
        base_fee = self.config["base_fee"]
        if tx_type == "percentage":
            # For percentage-based fees (e.g., token transfers)
            fee_amount = amount * base_fee * (1 - total_discount)
        else:
            # For standard fees (e.g., identity creation)
            fee_amount = base_fee * (1 - total_discount)
        
        # Ensure minimum fee
        fee_amount = max(fee_amount, self.config["min_fee"])
        
        # Check if the identity has enough ONX
        onx_balance = self.ledger.get_balance(identity_id, "ONX")
        if onx_balance < fee_amount:
            raise ValueError(f"Insufficient ONX to pay transaction fee. Required: {fee_amount}, Available: {onx_balance}")
        
        # Calculate burn and reward amounts
        burn_amount = fee_amount * self.config["burn_percent"]
        reward_amount = fee_amount * self.config["reward_percent"]
        
        # Apply the fee
        self.ledger.debit(identity_id, "ONX", fee_amount)
        
        # Credit the reward pool
        self.ledger.credit(self.config["reward_pool_address"], "ONX", reward_amount)
        
        # Burn the burn amount (by not crediting it anywhere)
        
        # Log the transaction
        self.txlog.record("transaction_fee", {
            "identity_id": identity_id,
            "tx_type": tx_type,
            "amount": amount,
            "token_id": token_id,
            "fee_amount": fee_amount,
            "burn_amount": burn_amount,
            "reward_amount": reward_amount,
            "discount": total_discount
        })
        
        return {
            "fee_amount": fee_amount,
            "burn_amount": burn_amount,
            "reward_amount": reward_amount,
            "discount": total_discount,
            "discount_details": discount_info
        }
    
    def apply_rebate(self, identity_id: str, role: str, amount: float) -> Dict[str, Any]:
        """
        Apply a rebate to an identity based on their role.
        
        Args:
            identity_id: The identity ID to apply the rebate to.
            role: The role of the identity.
            amount: The amount to rebate.
            
        Returns:
            A dictionary with the rebate details.
            
        Raises:
            ValueError: If the role is not eligible for rebates.
        """
        # Check if the role is eligible for rebates
        if role not in self.config["rebate_roles"]:
            raise ValueError(f"Role '{role}' is not eligible for rebates.")
        
        # Calculate rebate amount
        rebate_amount = amount * self.config["rebate_percent"]
        
        # Apply the rebate
        self.ledger.credit(identity_id, "ONX", rebate_amount)
        
        # Log the transaction
        self.txlog.record("rebate", {
            "identity_id": identity_id,
            "role": role,
            "amount": amount,
            "rebate_amount": rebate_amount,
            "rebate_percent": self.config["rebate_percent"]
        })
        
        return {
            "identity_id": identity_id,
            "role": role,
            "amount": amount,
            "rebate_amount": rebate_amount,
            "rebate_percent": self.config["rebate_percent"]
        }
    
    def stake_onx(self, identity_id: str, amount: float, lock_period: int = None) -> Dict[str, Any]:
        """
        Stake ONX tokens for an identity.
        
        Args:
            identity_id: The identity ID to stake for.
            amount: The amount to stake.
            lock_period: The lock period in seconds. If None, the default lock period will be used.
            
        Returns:
            A dictionary with the staking details.
            
        Raises:
            ValueError: If the identity does not have enough ONX to stake.
        """
        # Check if the identity has enough ONX
        onx_balance = self.ledger.get_balance(identity_id, "ONX")
        if onx_balance < amount:
            raise ValueError(f"Insufficient ONX to stake. Required: {amount}, Available: {onx_balance}")
        
        # Set lock period
        if lock_period is None:
            lock_period = self.config["staking_lock_period"]
        
        # Calculate unlock time
        unlock_time = int(time.time()) + lock_period
        
        # Calculate benefits
        benefits = []
        for tier in self.config["staking_tiers"]:
            if amount >= tier["amount"]:
                benefits = tier["benefits"]
                break
        
        # Apply the stake
        self.ledger.debit(identity_id, "ONX", amount)
        
        # Create stake record
        stake_id = f"stake_{identity_id}_{int(time.time())}"
        stake = {
            "id": stake_id,
            "identity_id": identity_id,
            "amount": amount,
            "lock_period": lock_period,
            "unlock_time": unlock_time,
            "benefits": benefits,
            "created_at": int(time.time()),
            "status": "active"
        }
        
        # Store the stake record
        # In a real implementation, this would be stored in a database
        # For now, we'll just log it
        self.txlog.record("stake_onx", stake)
        
        return stake
    
    def unstake_onx(self, stake_id: str) -> Dict[str, Any]:
        """
        Unstake ONX tokens for an identity.
        
        Args:
            stake_id: The ID of the stake to unstake.
            
        Returns:
            A dictionary with the unstaking details.
            
        Raises:
            ValueError: If the stake is not found or not yet unlocked.
        """
        # In a real implementation, this would retrieve the stake from a database
        # For now, we'll just raise an error
        raise ValueError("Unstaking not implemented yet.")
    
    def get_staking_tiers(self) -> List[Dict[str, Any]]:
        """
        Get the staking tiers.
        
        Returns:
            A list of staking tiers.
        """
        return self.config["staking_tiers"]
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get the Judah Engine configuration.
        
        Returns:
            The configuration.
        """
        return self.config
    
    def update_config(self, updates: Dict[str, Any], updated_by: str) -> Dict[str, Any]:
        """
        Update the Judah Engine configuration.
        
        Args:
            updates: The updates to apply to the configuration.
            updated_by: The identity ID of the updater.
            
        Returns:
            The updated configuration.
        """
        # Apply updates
        for key, value in updates.items():
            if key in self.config:
                self.config[key] = value
        
        # Update metadata
        self.config["last_updated"] = int(time.time())
        self.config["last_updated_by"] = updated_by
        
        # Save configuration
        self._save_config()
        
        # Log the update
        self.txlog.record("update_judah_config", {
            "updates": updates,
            "updated_by": updated_by
        })
        
        return self.config
