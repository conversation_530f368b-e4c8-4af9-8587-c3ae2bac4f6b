# src/vm/onnyxscript.py

import logging
from typing import List, Dict, Any, Optional, Tuple

from models.token import Token
from models.transaction import Transaction
from src.tokens.registry import TokenRegistry
from src.tokens.ledger import TokenLedger

# Set up logging
logger = logging.getLogger("onnyx.vm.onnyxscript")

class OnnyxScriptVM:
    """
    Virtual Machine for executing OnnyxScript operations.

    This VM provides a stack-based execution environment for token operations
    in the Onnyx blockchain.
    """

    def __init__(self, registry: TokenRegistry, ledger: TokenLedger):
        """
        Initialize the OnnyxScript VM.

        Args:
            registry: The token registry
            ledger: The token ledger
        """
        self.registry = registry
        self.ledger = ledger
        self.stack: List[Any] = []
        self.execution_log: List[Dict[str, Any]] = []

        logger.info("OnnyxScript VM initialized")

    def execute(self, script: List[Tuple[str, Any]]) -> List[Any]:
        """
        Execute a list of operations in the OnnyxScript VM.

        Args:
            script: A list of tuples (opcode, args) to execute

        Returns:
            The final state of the stack
        """
        self.stack = []  # Reset stack
        self.execution_log = []  # Reset execution log

        for op in script:
            opcode = op[0]
            args = op[1] if len(op) > 1 else None

            try:
                if opcode == "OP_SPAWN_TOKEN":
                    self._spawn_token(*args)
                elif opcode == "OP_MINT_TOKEN":
                    self._mint_token(*args)
                elif opcode == "OP_TRANSFER_TOKEN":
                    self._transfer_token(*args)
                elif opcode == "OP_GET_TOKEN_METADATA":
                    self._get_token_metadata(args)
                elif opcode == "OP_BURN_TOKEN":
                    self._burn_token(*args)
                elif opcode == "OP_GET_TOKEN_BALANCE":
                    self._get_token_balance(*args)
                elif opcode == "OP_GET_TOKEN_SUPPLY":
                    self._get_token_supply(args)
                elif opcode == "OP_CHECK_TOKEN_OWNER":
                    self._check_token_owner(*args)
                else:
                    raise ValueError(f"Unknown opcode: {opcode}")

                # Log successful execution
                self.execution_log.append({
                    "opcode": opcode,
                    "args": args,
                    "status": "SUCCESS"
                })

                logger.debug(f"Successfully executed {opcode}")

            except Exception as e:
                # Log failed execution
                self.execution_log.append({
                    "opcode": opcode,
                    "args": args,
                    "status": "FAILED",
                    "error": str(e)
                })

                logger.error(f"Failed to execute {opcode}: {str(e)}")
                raise  # Re-raise the exception

        return self.stack

    def _spawn_token(self, name: str, symbol: str, creator_id: str, token_type: str, supply: int, metadata: Dict[str, Any] = None, mintable: bool = True, transferable: bool = True):
        """
        Create a new token and register it in the token registry.

        Args:
            name: The name of the token
            symbol: The symbol of the token
            creator_id: The identity ID of the token creator
            token_type: The type of token (BASE, IDENTITY, FORKED, ACCESS, REPUTATION)
            supply: The initial supply of the token
            metadata: Optional metadata for the token
            mintable: Whether the token can be minted
            transferable: Whether the token can be transferred
        """
        # Create token metadata if not provided
        if metadata is None:
            metadata = {}

        # Add mintable and transferable flags to metadata
        metadata["mintable"] = mintable
        metadata["transferable"] = transferable

        # Create and register the token
        token = self.registry.fork_token(
            name=name,
            symbol=symbol,
            creator_id=creator_id,
            token_type=token_type,
            supply=supply,
            metadata=metadata,
            mintable=mintable,
            transferable=transferable
        )

        logger.info(f"Created token {token.token_id}: {name} ({symbol})")

        # Push the token ID onto the stack
        self.stack.append(token.token_id)

    def _mint_token(self, token_id: str, amount: int, to_id: str, minter_id: str):
        """
        Mint additional tokens.

        Args:
            token_id: The ID of the token to mint
            amount: The amount to mint
            to_id: The identity ID to mint to
            minter_id: The identity ID of the minter
        """
        # Use the ledger's mint method which handles all the validation
        tx = self.ledger.mint(
            token_id=token_id,
            to_id=to_id,
            amount=amount,
            minter_id=minter_id
        )

        logger.info(f"Minted {amount} of token {token_id} to {to_id}")

        # Push the transaction onto the stack
        self.stack.append(tx)

    def _transfer_token(self, token_id: str, from_id: str, to_id: str, amount: int, memo: str = None):
        """
        Transfer tokens from one identity to another.

        Args:
            token_id: The ID of the token to transfer
            from_id: The identity ID to transfer from
            to_id: The identity ID to transfer to
            amount: The amount to transfer
            memo: Optional memo for the transaction
        """
        # Use the ledger's transfer method
        tx = self.ledger.transfer(
            token_id=token_id,
            from_id=from_id,
            to_id=to_id,
            amount=amount,
            memo=memo
        )

        logger.info(f"Transferred {amount} of token {token_id} from {from_id} to {to_id}")

        # Push the transaction onto the stack
        self.stack.append(tx)

    def _get_token_metadata(self, token_id: str):
        """
        Get the metadata for a token.

        Args:
            token_id: The ID of the token
        """
        # Get the token from the database
        token = Token.get_by_id(token_id)

        if not token:
            logger.warning(f"Token {token_id} not found")
            self.stack.append(None)
        else:
            logger.info(f"Retrieved metadata for token {token_id}")
            self.stack.append(token.metadata)

    def _burn_token(self, token_id: str, from_id: str, amount: int, burner_id: str):
        """
        Burn tokens.

        Args:
            token_id: The ID of the token to burn
            from_id: The identity ID to burn from
            amount: The amount to burn
            burner_id: The identity ID of the burner
        """
        # Use the ledger's burn method
        tx = self.ledger.burn(
            token_id=token_id,
            from_id=from_id,
            amount=amount,
            burner_id=burner_id
        )

        logger.info(f"Burned {amount} of token {token_id} from {from_id}")

        # Push the transaction onto the stack
        self.stack.append(tx)

    def _get_token_balance(self, identity_id: str, token_id: str):
        """
        Get the balance of a token for an identity.

        Args:
            identity_id: The identity ID to check
            token_id: The ID of the token
        """
        balance = self.ledger.get_balance(identity_id, token_id)
        logger.info(f"Retrieved balance for token {token_id} for identity {identity_id}: {balance}")
        self.stack.append(balance)

    def _get_token_supply(self, token_id: str):
        """
        Get the total supply of a token.

        Args:
            token_id: The ID of the token
        """
        # Get the token from the database
        token = Token.get_by_id(token_id)

        if not token:
            logger.warning(f"Token {token_id} not found")
            self.stack.append(0)
        else:
            logger.info(f"Retrieved supply for token {token_id}: {token.supply}")
            self.stack.append(token.supply)

    def _check_token_owner(self, token_id: str, identity_id: str):
        """
        Check if an identity is the owner of a token.

        Args:
            token_id: The ID of the token
            identity_id: The identity ID to check
        """
        # Get the token from the database
        token = Token.get_by_id(token_id)

        if not token:
            logger.warning(f"Token {token_id} not found")
            self.stack.append(False)
        else:
            is_owner = token.creator_id == identity_id
            logger.info(f"Checked if identity {identity_id} is owner of token {token_id}: {is_owner}")
            self.stack.append(is_owner)

    def get_execution_log(self) -> List[Dict[str, Any]]:
        """
        Get the execution log.

        Returns:
            The execution log containing details of all executed operations
        """
        logger.debug(f"Returning execution log with {len(self.execution_log)} entries")
        return self.execution_log
