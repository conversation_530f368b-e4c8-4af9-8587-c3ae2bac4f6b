"""
Onnyx Sela Routes

This module provides API routes for Sela operations.
"""

import logging
import time
import uuid
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from sela.registry.sela_registry import SelaRegistry
from identity.registry import IdentityRegistry

from shared.models.sela import Sela
from shared.models.identity import Identity
from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.routes.sela")

# Create router
router = APIRouter()

# Create instances
selas = SelaRegistry()
identities = IdentityRegistry()

@router.post("/sela/register")
def register_sela(
    sela_id: Optional[str] = None,
    name: str = Body(...),
    founder_id: str = Body(...),
    sela_type: str = Body(...),
    token_id: Optional[str] = Body(None),
    metadata: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Register a new Sela.

    Args:
        sela_id: The Sela ID (optional, will be generated if not provided)
        name: The Sela name
        founder_id: The founder's identity ID
        sela_type: The Sela type (e.g., "BUSINESS", "NONPROFIT", "COMMUNITY")
        token_id: The token ID associated with the Sela (optional)
        metadata: Additional metadata for the Sela (optional)

    Returns:
        Information about the registered Sela
    """
    try:
        logger.info(f"Registering Sela: {name} with founder {founder_id}")

        # Generate Sela ID if not provided
        if not sela_id:
            sela_id = f"sela_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            logger.debug(f"Generated Sela ID: {sela_id}")

        # Check if the founder identity exists
        founder = Identity.get_by_id(founder_id)
        if not founder:
            logger.warning(f"Identity with ID '{founder_id}' not found")
            raise Exception(f"Identity with ID '{founder_id}' not found")

        # Create the Sela in the database
        sela = Sela.create(
            sela_id=sela_id,
            name=name,
            founder_id=founder_id,
            sela_type=sela_type,
            token_id=token_id,
            metadata=metadata or {}
        )

        # Register the Sela with the registry
        selas.register_sela(sela_id, name, founder_id, sela_type, token_id)

        # Link the Sela to the founder's identity
        identities.link_sela(founder_id, sela_id, "FOUNDER")

        # Add the Sela to the founder's metadata
        if "founded_selas" not in founder.metadata:
            founder.metadata["founded_selas"] = []

        if sela_id not in founder.metadata["founded_selas"]:
            founder.metadata["founded_selas"].append(sela_id)
            founder.save()

        # Create a Sela registration transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SELA_REGISTRATION",
            sender=founder_id,
            status="CONFIRMED",
            data={
                "op": "OP_REGISTER_SELA",
                "sela_id": sela_id,
                "name": name,
                "sela_type": sela_type,
                "founder_id": founder_id,
                "token_id": token_id
            }
        )

        logger.info(f"Sela registered: {sela_id}")
        return {
            "status": "created",
            "sela": sela.to_dict()
        }
    except Exception as e:
        logger.error(f"Error registering Sela: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/join")
def join_sela(
    sela_id: str = Body(...),
    identity_id: str = Body(...),
    role: str = Body("MEMBER")
) -> Dict[str, Any]:
    """
    Add an identity to a Sela.

    Args:
        sela_id: The Sela ID
        identity_id: The identity ID to add
        role: The role of the identity in the Sela (default: "MEMBER")

    Returns:
        Information about the joined Sela
    """
    try:
        logger.info(f"Adding identity {identity_id} to Sela {sela_id} with role {role}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the Sela exists
        sela = Sela.get_by_id(sela_id)
        if not sela:
            logger.warning(f"Sela with ID '{sela_id}' not found")
            raise Exception(f"Sela with ID '{sela_id}' not found")

        # Add the identity to the Sela
        sela.add_member(identity_id, role)

        # Join the Sela in the registry
        selas.join_sela(sela_id, identity_id, role)

        # Link the Sela to the identity
        identities.link_sela(identity_id, sela_id, role)

        # Add the Sela to the identity's metadata
        if "joined_selas" not in identity.metadata:
            identity.metadata["joined_selas"] = []

        if sela_id not in identity.metadata["joined_selas"]:
            identity.metadata["joined_selas"].append(sela_id)
            identity.save()

        # Create a Sela join transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SELA_JOIN",
            sender=identity_id,
            status="CONFIRMED",
            data={
                "op": "OP_JOIN_SELA",
                "sela_id": sela_id,
                "identity_id": identity_id,
                "role": role
            }
        )

        logger.info(f"Identity {identity_id} joined Sela {sela_id} with role {role}")
        return {
            "status": "joined",
            "sela_id": sela_id,
            "identity_id": identity_id,
            "role": role
        }
    except Exception as e:
        logger.error(f"Error joining Sela: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/leave")
def leave_sela(
    sela_id: str = Body(...),
    identity_id: str = Body(...)
) -> Dict[str, Any]:
    """
    Remove an identity from a Sela.

    Args:
        sela_id: The Sela ID
        identity_id: The identity ID to remove

    Returns:
        Information about the left Sela
    """
    try:
        logger.info(f"Removing identity {identity_id} from Sela {sela_id}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the Sela exists
        sela = Sela.get_by_id(sela_id)
        if not sela:
            logger.warning(f"Sela with ID '{sela_id}' not found")
            raise Exception(f"Sela with ID '{sela_id}' not found")

        # Get the role before leaving
        role = sela.roles.get(identity_id, "MEMBER")

        # Remove the identity from the Sela
        sela.remove_member(identity_id)

        # Leave the Sela in the registry
        selas.leave_sela(sela_id, identity_id)

        # Unlink the Sela from the identity
        identities.unlink_sela(identity_id, sela_id, role)

        # Remove the Sela from the identity's metadata
        if "joined_selas" in identity.metadata and sela_id in identity.metadata["joined_selas"]:
            identity.metadata["joined_selas"].remove(sela_id)
            identity.save()

        # Create a Sela leave transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SELA_LEAVE",
            sender=identity_id,
            status="CONFIRMED",
            data={
                "op": "OP_LEAVE_SELA",
                "sela_id": sela_id,
                "identity_id": identity_id
            }
        )

        logger.info(f"Identity {identity_id} left Sela {sela_id}")
        return {
            "status": "left",
            "sela_id": sela_id,
            "identity_id": identity_id
        }
    except Exception as e:
        logger.error(f"Error leaving Sela: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/{sela_id}/update-role")
def update_role(
    sela_id: str,
    identity_id: str = Body(...),
    role: str = Body(...)
) -> Dict[str, Any]:
    """
    Update the role of an identity in a Sela.

    Args:
        sela_id: The Sela ID
        identity_id: The identity ID
        role: The new role

    Returns:
        Information about the updated role
    """
    try:
        logger.info(f"Updating role of identity {identity_id} in Sela {sela_id} to {role}")

        # Check if the identity exists
        identity = Identity.get_by_id(identity_id)
        if not identity:
            logger.warning(f"Identity with ID '{identity_id}' not found")
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the Sela exists
        sela = Sela.get_by_id(sela_id)
        if not sela:
            logger.warning(f"Sela with ID '{sela_id}' not found")
            raise Exception(f"Sela with ID '{sela_id}' not found")

        # Check if the identity is a member of the Sela
        if identity_id not in sela.members:
            logger.warning(f"Identity {identity_id} is not a member of Sela {sela_id}")
            raise Exception(f"Identity {identity_id} is not a member of Sela {sela_id}")

        # Get the old role
        old_role = sela.roles.get(identity_id, "MEMBER")

        # Update the role
        sela.update_role(identity_id, role)

        # Update the role in the registry
        selas.update_role(sela_id, identity_id, role)

        # Update the role in the identity
        identities.update_sela_role(identity_id, sela_id, role)

        # Create a role update transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="SELA_ROLE_UPDATE",
            sender=sela.founder_id,
            recipient=identity_id,
            status="CONFIRMED",
            data={
                "op": "OP_UPDATE_SELA_ROLE",
                "sela_id": sela_id,
                "identity_id": identity_id,
                "old_role": old_role,
                "new_role": role
            }
        )

        logger.info(f"Role of identity {identity_id} in Sela {sela_id} updated to {role}")
        return {
            "status": "role_updated",
            "sela_id": sela_id,
            "identity_id": identity_id,
            "old_role": old_role,
            "new_role": role
        }
    except Exception as e:
        logger.error(f"Error updating role: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/{sela_id}/add-service")
def add_service(
    sela_id: str,
    service: str = Body(...)
) -> Dict[str, Any]:
    """
    Add a service to a Sela.

    Args:
        sela_id: The Sela ID
        service: The service to add

    Returns:
        Information about the added service
    """
    try:
        logger.info(f"Adding service {service} to Sela {sela_id}")

        # Check if the Sela exists
        sela = Sela.get_by_id(sela_id)
        if not sela:
            logger.warning(f"Sela with ID '{sela_id}' not found")
            raise Exception(f"Sela with ID '{sela_id}' not found")

        # Add the service
        sela.add_service(service)

        # Add the service in the registry
        selas.add_service(sela_id, service)

        logger.info(f"Service {service} added to Sela {sela_id}")
        return {
            "status": "service_added",
            "sela_id": sela_id,
            "service": service
        }
    except Exception as e:
        logger.error(f"Error adding service: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/{sela_id}/remove-service")
def remove_service(
    sela_id: str,
    service: str = Body(...)
) -> Dict[str, Any]:
    """
    Remove a service from a Sela.

    Args:
        sela_id: The Sela ID
        service: The service to remove

    Returns:
        Information about the removed service
    """
    try:
        logger.info(f"Removing service {service} from Sela {sela_id}")

        # Check if the Sela exists
        sela = Sela.get_by_id(sela_id)
        if not sela:
            logger.warning(f"Sela with ID '{sela_id}' not found")
            raise Exception(f"Sela with ID '{sela_id}' not found")

        # Remove the service
        sela.remove_service(service)

        # Remove the service in the registry
        selas.remove_service(sela_id, service)

        logger.info(f"Service {service} removed from Sela {sela_id}")
        return {
            "status": "service_removed",
            "sela_id": sela_id,
            "service": service
        }
    except Exception as e:
        logger.error(f"Error removing service: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/sela/{sela_id}")
def get_sela(sela_id: str) -> Dict[str, Any]:
    """
    Get a Sela by ID.

    Args:
        sela_id: The Sela ID

    Returns:
        The Sela
    """
    logger.info(f"Getting Sela: {sela_id}")

    # Check if the Sela exists
    sela = Sela.get_by_id(sela_id)
    if not sela:
        logger.warning(f"Sela with ID '{sela_id}' not found")
        raise HTTPException(status_code=404, detail=f"Sela with ID '{sela_id}' not found")

    logger.info(f"Retrieved Sela {sela_id}")
    return sela.to_dict()

@router.get("/selas")
def list_selas() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all Selas.

    Returns:
        All Selas in the registry
    """
    logger.info("Listing all Selas")

    # Get all Selas
    selas_list = Sela.get_all()

    logger.info(f"Retrieved {len(selas_list)} Selas")
    return {
        "selas": [sela.to_dict() for sela in selas_list]
    }

@router.get("/selas/founder/{founder_id}")
def get_selas_by_founder(founder_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all Selas founded by an identity.

    Args:
        founder_id: The founder's identity ID

    Returns:
        A list of Selas founded by the identity
    """
    logger.info(f"Getting Selas by founder: {founder_id}")

    # Check if the identity exists
    identity = Identity.get_by_id(founder_id)
    if not identity:
        logger.warning(f"Identity with ID '{founder_id}' not found")
        raise HTTPException(status_code=404, detail=f"Identity with ID '{founder_id}' not found")

    # Get Selas by founder
    selas_list = Sela.find_by_founder(founder_id)

    logger.info(f"Retrieved {len(selas_list)} Selas founded by {founder_id}")
    return {
        "founder_id": founder_id,
        "selas": [sela.to_dict() for sela in selas_list]
    }

@router.get("/selas/member/{member_id}")
def get_selas_by_member(member_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all Selas that an identity is a member of.

    Args:
        member_id: The member's identity ID

    Returns:
        A list of Selas that the identity is a member of
    """
    logger.info(f"Getting Selas by member: {member_id}")

    # Check if the identity exists
    identity = Identity.get_by_id(member_id)
    if not identity:
        logger.warning(f"Identity with ID '{member_id}' not found")
        raise HTTPException(status_code=404, detail=f"Identity with ID '{member_id}' not found")

    # Get Selas by member
    selas_list = Sela.find_by_member(member_id)

    logger.info(f"Retrieved {len(selas_list)} Selas that {member_id} is a member of")
    return {
        "member_id": member_id,
        "selas": [sela.to_dict() for sela in selas_list]
    }
