# ONNYX Official Miner Configuration
# This file identifies an official ONNYX miner package

[miner]
# Official ONNYX miner identification
is_official_miner = true
miner_version = "1.0.0"
miner_tier = "optimized"  # basic, optimized, pro

# Mining performance multipliers
base_mining_power = 2  # 2x multiplier for optimized tier
max_mining_power = 5   # Maximum allowed for this tier

# Authentication and verification
miner_signature = "ONNYX_OFFICIAL_MINER_v1.0.0"
verification_key = "onnyx_miner_2024_verification"

# Performance settings
target_block_time = 10  # seconds
difficulty_adjustment = "auto"
mining_algorithm = "proof_of_trust_hybrid"

[security]
# Anti-spoofing measures
require_signature_verification = true
validate_mining_tier = true
enforce_power_limits = true

# Audit and logging
log_mining_activity = true
track_performance_metrics = true
report_to_network = true

[network]
# Network connection settings
default_node_url = "http://127.0.0.1:5000"
api_endpoint = "/api/v1"
mining_endpoint = "/api/v1/mine"

# Validator registration
auto_register_validator = false
require_manual_approval = true

[rewards]
# Reward calculation settings
use_tier_multiplier = true
base_reward = 1.0
performance_bonus = true

# Distribution settings
immediate_payout = true
track_earnings = true

[compatibility]
# Backward compatibility
support_legacy_mining = true
migrate_existing_miners = true
preserve_block_history = true

[development]
# Development and testing settings
debug_mode = false
test_network = false
simulation_mode = false
