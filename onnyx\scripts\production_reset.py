#!/usr/bin/env python3
"""
ONNYX Production Reset Script

Comprehensive cleanup of all demo/test data to prepare for production launch.
This script will:
1. Create a backup of the current database
2. Clear all demo data from database tables
3. Remove demo key files and JSON caches
4. Reset auto-increment counters
5. Preserve table schemas and indexes
6. Prepare system for real-world data

CRITICAL: This will permanently delete all existing data!
"""

import os
import sys
import json
import shutil
import sqlite3
import logging
from datetime import datetime
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.production_reset")

def create_backup():
    """Create a timestamped backup of the current database."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"shared/db/onnyx_backup_{timestamp}.db"
        
        logger.info(f"Creating backup at {backup_path}")
        db.backup(backup_path)
        
        logger.info(f"✅ Backup created successfully: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"❌ Failed to create backup: {e}")
        raise

def clear_database_tables():
    """Clear all data from database tables while preserving schema."""
    tables_to_clear = [
        'voice_scroll_votes',
        'voice_scrolls', 
        'stakes',
        'zeman_credits',
        'etzem_scores',
        'balances',
        'tokens',
        'sela_members',
        'selas',
        'identity_badges',
        'identities',
        'transactions',
        'blocks',
        'event_logs',
        'validator_rotation',
        'validator_queue',
        'chain_parameters'
    ]
    
    try:
        logger.info("Clearing database tables...")
        
        # Disable foreign key constraints temporarily
        db.execute("PRAGMA foreign_keys = OFF")
        
        for table in tables_to_clear:
            if db.table_exists(table):
                logger.info(f"  Clearing table: {table}")
                db.execute(f"DELETE FROM {table}")
                
                # Reset auto-increment counter
                db.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
            else:
                logger.warning(f"  Table {table} does not exist")
        
        # Re-enable foreign key constraints
        db.execute("PRAGMA foreign_keys = ON")
        
        logger.info("✅ Database tables cleared successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to clear database tables: {e}")
        raise

def clear_json_cache_files():
    """Remove all JSON cache files containing demo data."""
    json_files_to_clear = [
        'shared/db/blockchain.json',
        'shared/db/mempool.json',
        'shared/db/token_ledger.json',
        'shared/db/identities.json',
        'shared/db/selas.json',
        'shared/db/tokens.json',
        'shared/db/transactions.json',
        'shared/db/activity_ledger.json',
        'shared/db/activity_log.json',
        'shared/db/event_log.json',
        'shared/db/rotation.json',
        'shared/db/rotation_registry.json',
        'shared/db/scrolls.json',
        'shared/db/wallet.json',
        'blockchain.json',
        'mempool.json',
        'token_ledger.json'
    ]
    
    try:
        logger.info("Clearing JSON cache files...")
        
        for file_path in json_files_to_clear:
            if os.path.exists(file_path):
                logger.info(f"  Removing: {file_path}")
                os.remove(file_path)
            else:
                logger.debug(f"  File not found: {file_path}")
        
        logger.info("✅ JSON cache files cleared successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to clear JSON cache files: {e}")
        raise

def clear_demo_keys():
    """Remove all demo cryptographic key files."""
    try:
        keys_dir = Path("shared/db/keys")
        
        if keys_dir.exists():
            logger.info("Clearing demo cryptographic keys...")
            
            # Remove all UUID-named key directories (demo keys)
            for item in keys_dir.iterdir():
                if item.is_dir() and len(item.name) == 36:  # UUID length
                    logger.info(f"  Removing demo key directory: {item.name}")
                    shutil.rmtree(item)
                elif item.is_file() and item.name.endswith('.pem'):
                    logger.info(f"  Removing demo key file: {item.name}")
                    item.unlink()
            
            logger.info("✅ Demo keys cleared successfully")
        else:
            logger.info("Keys directory does not exist")
            
    except Exception as e:
        logger.error(f"❌ Failed to clear demo keys: {e}")
        raise

def initialize_production_state():
    """Initialize the system for production use."""
    try:
        logger.info("Initializing production state...")
        
        # Create empty JSON files with proper structure
        empty_structures = {
            'blockchain.json': [],
            'mempool.json': [],
            'token_ledger.json': {}
        }
        
        for filename, structure in empty_structures.items():
            with open(filename, 'w') as f:
                json.dump(structure, f, indent=2)
            logger.info(f"  Created empty {filename}")
        
        # Ensure keys directory exists
        os.makedirs("shared/db/keys", exist_ok=True)
        
        logger.info("✅ Production state initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize production state: {e}")
        raise

def verify_cleanup():
    """Verify that the cleanup was successful."""
    try:
        logger.info("Verifying cleanup...")
        
        # Check database tables are empty
        tables_to_check = ['identities', 'selas', 'transactions', 'blocks']
        
        for table in tables_to_check:
            if db.table_exists(table):
                count = db.query_one(f"SELECT COUNT(*) as count FROM {table}")['count']
                if count == 0:
                    logger.info(f"  ✅ Table {table}: 0 records")
                else:
                    logger.warning(f"  ⚠️  Table {table}: {count} records remaining")
            else:
                logger.warning(f"  ⚠️  Table {table} does not exist")
        
        # Check JSON files are empty/minimal
        json_files_to_check = ['blockchain.json', 'mempool.json', 'token_ledger.json']
        
        for filename in json_files_to_check:
            if os.path.exists(filename):
                with open(filename, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list) and len(data) == 0:
                        logger.info(f"  ✅ {filename}: Empty array")
                    elif isinstance(data, dict) and len(data) == 0:
                        logger.info(f"  ✅ {filename}: Empty object")
                    else:
                        logger.warning(f"  ⚠️  {filename}: Contains data")
            else:
                logger.warning(f"  ⚠️  {filename}: File missing")
        
        logger.info("✅ Cleanup verification completed")
        
    except Exception as e:
        logger.error(f"❌ Failed to verify cleanup: {e}")
        raise

def main():
    """Execute the complete production reset process."""
    logger.info("🚀 ONNYX PRODUCTION RESET - STARTING")
    logger.info("=" * 60)
    
    try:
        # Step 1: Create backup
        backup_path = create_backup()
        
        # Step 2: Clear database tables
        clear_database_tables()
        
        # Step 3: Clear JSON cache files
        clear_json_cache_files()
        
        # Step 4: Clear demo keys
        clear_demo_keys()
        
        # Step 5: Initialize production state
        initialize_production_state()
        
        # Step 6: Verify cleanup
        verify_cleanup()
        
        logger.info("=" * 60)
        logger.info("🎉 PRODUCTION RESET COMPLETED SUCCESSFULLY!")
        logger.info(f"📁 Backup saved at: {backup_path}")
        logger.info("🔄 System ready for real-world data")
        logger.info("⚠️  All demo data has been permanently removed")
        
        return 0
        
    except Exception as e:
        logger.error("=" * 60)
        logger.error(f"💥 PRODUCTION RESET FAILED: {e}")
        logger.error("🔄 System may be in an inconsistent state")
        logger.error("📁 Restore from backup if needed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
