# src/db/schema/zeman.py

from src.db.manager import db_manager

def create_zeman_tables():
    """
    Create the tables for the Zeman system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()

    # Create zeman_entries table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS zeman_entries (
        entry_id TEXT PRIMARY KEY,
        identity_id TEXT NOT NULL,
        hours REAL NOT NULL,
        description TEXT,
        source TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        redeemed INTEGER NOT NULL DEFAULT 0,
        redeemed_at INTEGER,
        redemption_type TEXT,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create zeman_redemptions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS zeman_redemptions (
        redemption_id TEXT PRIMARY KEY,
        identity_id TEXT NOT NULL,
        hours REAL NOT NULL,
        redemption_type TEXT NOT NULL,
        redemption_value REAL NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create zeman_contract_links table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS zeman_contract_links (
        zeman_entry_id TEXT NOT NULL,
        contract_id TEXT NOT NULL,
        PRIMARY KEY (zeman_entry_id),
        FOREIGN KEY (zeman_entry_id) REFERENCES zeman_entries (entry_id) ON DELETE CASCADE,
        FOREIGN KEY (contract_id) REFERENCES service_contracts (contract_id) ON DELETE CASCADE
    )
    ''')

    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_zeman_entries_identity_id ON zeman_entries (identity_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_zeman_entries_redeemed ON zeman_entries (redeemed)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_zeman_redemptions_identity_id ON zeman_redemptions (identity_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_zeman_redemptions_redemption_type ON zeman_redemptions (redemption_type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_zeman_contract_links_contract_id ON zeman_contract_links (contract_id)')

    conn.commit()
