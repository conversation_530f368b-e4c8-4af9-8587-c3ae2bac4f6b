"""
Sela Routes

Public Sela profiles and business directory.
"""

import os
import sys
import json
import logging
from flask import Blueprint, render_template, request, jsonify

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.identity import Identity
from shared.db.db import db

logger = logging.getLogger("onnyx.web.sela")

sela_bp = Blueprint('sela', __name__)

@sela_bp.route('/')
def directory():
    """Sela directory page."""
    try:
        # Get search parameters
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 12
        offset = (page - 1) * per_page

        # Build query
        where_conditions = ["s.status = 'active'"]
        params = []

        if category:
            where_conditions.append("s.category = ?")
            params.append(category)

        if search:
            where_conditions.append("(s.name LIKE ? OR s.metadata LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # Get Selas with owner information
        selas = db.query(f"""
            SELECT s.*, i.name as owner_name, i.email as owner_email
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE {where_clause}
            ORDER BY s.created_at DESC
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        # Parse metadata for each Sela
        for sela in selas:
            try:
                sela['metadata_parsed'] = json.loads(sela['metadata'])
            except:
                sela['metadata_parsed'] = {}

        # Get total count for pagination
        total_count = db.query_one(f"""
            SELECT COUNT(*) as count
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE {where_clause}
        """, params)['count']

        # Get available categories
        categories = db.query("""
            SELECT DISTINCT category
            FROM selas
            WHERE status = 'active'
            ORDER BY category
        """)

        # Calculate pagination
        total_pages = (total_count + per_page - 1) // per_page

        # Get network statistics for the header
        total_blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
        total_transactions = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']

        return render_template('sela/directory.html',
                             selas=selas,
                             categories=[c['category'] for c in categories],
                             current_category=category,
                             current_search=search,
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count,
                             total_blocks=total_blocks,
                             total_transactions=total_transactions)

    except Exception as e:
        logger.error(f"Error loading Sela directory: {e}")
        # Get basic network stats even on error
        try:
            total_blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
            total_transactions = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
        except:
            total_blocks = 0
            total_transactions = 0

        return render_template('sela/directory.html',
                             selas=[],
                             categories=[],
                             total_blocks=total_blocks,
                             total_transactions=total_transactions,
                             error="Error loading directory")

@sela_bp.route('/<sela_id>')
def profile(sela_id):
    """Public Sela profile page."""
    try:
        # Get Sela with owner information
        sela = db.query_one("""
            SELECT s.*, i.name as owner_name, i.email as owner_email, i.public_key
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.sela_id = ?
        """, (sela_id,))

        if not sela:
            return render_template('errors/404.html'), 404

        # Parse metadata
        try:
            sela['metadata_parsed'] = json.loads(sela['metadata'])
        except:
            sela['metadata_parsed'] = {}

        # Get Sela's transactions
        sela_transactions = db.query("""
            SELECT * FROM transactions
            WHERE sender = ? OR data LIKE ?
            ORDER BY created_at DESC
            LIMIT 10
        """, (sela['identity_id'], f'%{sela_id}%'))

        # Parse transaction data
        for tx in sela_transactions:
            try:
                tx['data_parsed'] = json.loads(tx['data'])
            except:
                tx['data_parsed'] = {}

        # Get token balances if available
        token_balances = []
        if db.table_exists('token_balances'):
            token_balances = db.query("""
                SELECT tb.*, t.name, t.symbol, t.category
                FROM token_balances tb
                JOIN tokens t ON tb.token_id = t.token_id
                WHERE tb.identity_id = ?
                ORDER BY tb.balance DESC
            """, (sela['identity_id'],))

        # Get Etzem score if available
        etzem_score = None
        if db.table_exists('etzem_scores'):
            etzem_score = db.query_one("""
                SELECT * FROM etzem_scores
                WHERE identity_id = ?
            """, (sela['identity_id'],))

        # Calculate basic metrics
        metrics = {
            'total_transactions': len(sela_transactions),
            'total_tokens': sum(tb.get('balance', 0) for tb in token_balances),
            'trust_score': etzem_score.get('composite_score', 0) if etzem_score else 0,
            'registration_date': sela['created_at']
        }

        return render_template('sela/profile.html',
                             sela=sela,
                             transactions=sela_transactions,
                             token_balances=token_balances,
                             etzem_score=etzem_score,
                             metrics=metrics)

    except Exception as e:
        logger.error(f"Error loading Sela profile {sela_id}: {e}")
        return render_template('errors/500.html'), 500

@sela_bp.route('/<sela_id>/contact', methods=['POST'])
def contact(sela_id):
    """Handle contact form submission."""
    try:
        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        message = request.form.get('message', '').strip()

        if not all([name, email, message]):
            return jsonify({'success': False, 'error': 'All fields are required'})

        # Get Sela information
        sela = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
        if not sela:
            return jsonify({'success': False, 'error': 'Sela not found'})

        # Log the contact attempt (in a real system, you'd send an email)
        contact_data = {
            'sela_id': sela_id,
            'contact_name': name,
            'contact_email': email,
            'message': message,
            'timestamp': int(time.time())
        }

        # Store in event logs
        db.insert('event_logs', {
            'event_type': 'sela_contact',
            'entity_id': sela_id,
            'data': json.dumps(contact_data),
            'timestamp': int(time.time())
        })

        logger.info(f"Contact form submitted for Sela {sela_id} by {email}")

        return jsonify({'success': True, 'message': 'Message sent successfully!'})

    except Exception as e:
        logger.error(f"Error handling contact form: {e}")
        return jsonify({'success': False, 'error': 'Failed to send message'})

@sela_bp.route('/api/search')
def api_search():
    """API endpoint for Sela search."""
    try:
        query = request.args.get('q', '').strip()
        limit = request.args.get('limit', 10, type=int)

        if not query:
            return jsonify([])

        # Search Selas
        results = db.query("""
            SELECT s.sela_id, s.name, s.category, i.name as owner_name
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.status = 'active'
            AND (s.name LIKE ? OR s.category LIKE ? OR s.metadata LIKE ?)
            ORDER BY s.name
            LIMIT ?
        """, (f"%{query}%", f"%{query}%", f"%{query}%", limit))

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error in Sela search API: {e}")
        return jsonify([])

import time
