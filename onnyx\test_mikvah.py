"""
Test the Onnyx Mikvah Engine

This script tests the Onnyx Mikvah Engine by minting tokens.
"""

import os
import sys
import json
import time

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from identity.registry import IdentityRegistry
from business.sela_registry import SelaRegistry
from governance.etzem_engine import <PERSON><PERSON>em<PERSON><PERSON><PERSON>
from yovel.limits import YovelLimiter
from yovel.mikvah_engine import <PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>

def setup_test_data():
    """Set up test data for the Mikvah Engine test."""
    # Create identity registry
    identity_registry = IdentityRegistry()
    
    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "<PERSON>", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: Alice")
    
    try:
        bob = identity_registry.register_identity("bob", "<PERSON>", "0x987654321fedc<PERSON>")
        print(f"Created identity: <PERSON>")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: <PERSON>")
    
    try:
        charlie = identity_registry.register_identity("charlie", "<PERSON>", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")
    
    print("\nTest identities:")
    print(f"  Alice: {alice['identity_id']}")
    print(f"  Bob: {bob['identity_id']}")
    print(f"  Charlie: {charlie['identity_id']}")
    
    # Create Sela registry
    sela_registry = SelaRegistry()
    
    # Create Etzem engine
    etzem_engine = EtzemEngine()
    
    # Create Yovel limiter
    yovel_limiter = YovelLimiter(identity_registry, etzem_engine)
    
    # Create Mikvah engine
    mikvah_engine = MikvahEngine(identity_registry, sela_registry, etzem_engine, yovel_limiter)
    
    return identity_registry, sela_registry, etzem_engine, yovel_limiter, mikvah_engine

def test_mikvah_engine():
    """Test the Mikvah Engine functionality."""
    # Set up test data
    identity_registry, sela_registry, etzem_engine, yovel_limiter, mikvah_engine = setup_test_data()
    
    # Set up a Sela for Bob
    try:
        sela = sela_registry.register_sela(
            "bobs_barbershop",
            "Bob's Barbershop",
            "bob",
            "BUSINESS",
            "BARBER_TOKEN"
        )
        identity_registry.link_sela("bob", "bobs_barbershop", "FOUNDER")
        print("\nRegistered Sela for Bob")
    except Exception as e:
        print(f"\nUsing existing Sela for Bob: {str(e)}")
    
    # Set up a Sela for Alice
    try:
        sela = sela_registry.register_sela(
            "alices_salon",
            "Alice's Salon",
            "alice",
            "BUSINESS",
            "SALON_TOKEN"
        )
        identity_registry.link_sela("alice", "alices_salon", "FOUNDER")
        print("Registered Sela for Alice")
    except Exception as e:
        print(f"Using existing Sela for Alice: {str(e)}")
    
    # Set Etzem scores
    print("\nSetting Etzem scores...")
    
    # Alice: High Etzem score (90)
    alice_etzem = {
        "consistency_score": 90,
        "token_impact": 85,
        "reputation": 95,
        "labor_contribution": 90,
        "final_etzem": 90
    }
    
    # Bob: Medium Etzem score (60)
    bob_etzem = {
        "consistency_score": 60,
        "token_impact": 55,
        "reputation": 65,
        "labor_contribution": 60,
        "final_etzem": 60
    }
    
    # Charlie: Low Etzem score (30)
    charlie_etzem = {
        "consistency_score": 30,
        "token_impact": 25,
        "reputation": 35,
        "labor_contribution": 30,
        "final_etzem": 30
    }
    
    # Mock the calculate_etzem_score method
    etzem_engine.calculate_etzem_score = lambda identity_id: (
        alice_etzem if identity_id == "alice" else
        bob_etzem if identity_id == "bob" else
        charlie_etzem
    )
    
    # Mock the calculate_mint_cap method
    yovel_limiter.calculate_mint_cap = lambda identity_id: (
        2000 if identity_id == "alice" else
        1500 if identity_id == "bob" else
        1000
    )
    
    # Check mint eligibility
    print("\nChecking mint eligibility...")
    
    alice_loyalty_eligibility = mikvah_engine.check_mint_eligibility("alice", "LOYALTY")
    bob_loyalty_eligibility = mikvah_engine.check_mint_eligibility("bob", "LOYALTY")
    charlie_loyalty_eligibility = mikvah_engine.check_mint_eligibility("charlie", "LOYALTY")
    
    alice_currency_eligibility = mikvah_engine.check_mint_eligibility("alice", "CURRENCY")
    bob_currency_eligibility = mikvah_engine.check_mint_eligibility("bob", "CURRENCY")
    charlie_currency_eligibility = mikvah_engine.check_mint_eligibility("charlie", "CURRENCY")
    
    print("\nLoyalty token eligibility:")
    print(f"  Alice: {alice_loyalty_eligibility['is_eligible']} (Mint cap: {alice_loyalty_eligibility['mint_cap']})")
    print(f"  Bob: {bob_loyalty_eligibility['is_eligible']} (Mint cap: {bob_loyalty_eligibility['mint_cap']})")
    print(f"  Charlie: {charlie_loyalty_eligibility['is_eligible']} (Mint cap: {charlie_loyalty_eligibility['mint_cap']})")
    
    print("\nCurrency token eligibility:")
    print(f"  Alice: {alice_currency_eligibility['is_eligible']} (Mint cap: {alice_currency_eligibility['mint_cap']})")
    print(f"  Bob: {bob_currency_eligibility['is_eligible']} (Mint cap: {bob_currency_eligibility['mint_cap']})")
    print(f"  Charlie: {charlie_currency_eligibility['is_eligible']} (Mint cap: {charlie_currency_eligibility['mint_cap']})")
    
    # Mint tokens
    print("\nMinting tokens...")
    
    try:
        alice_token = mikvah_engine.mint_token(
            "alice",
            "ALICE_LOYALTY",
            "LOYALTY",
            1000,
            {"name": "Alice Loyalty Token", "symbol": "ALT"}
        )
        print(f"Minted token for Alice: {alice_token['token_id']} (Supply: {alice_token['supply']})")
    except Exception as e:
        print(f"Failed to mint token for Alice: {str(e)}")
    
    try:
        bob_token = mikvah_engine.mint_token(
            "bob",
            "BOB_LOYALTY",
            "LOYALTY",
            1000,
            {"name": "Bob Loyalty Token", "symbol": "BLT"}
        )
        print(f"Minted token for Bob: {bob_token['token_id']} (Supply: {bob_token['supply']})")
    except Exception as e:
        print(f"Failed to mint token for Bob: {str(e)}")
    
    try:
        charlie_token = mikvah_engine.mint_token(
            "charlie",
            "CHARLIE_LOYALTY",
            "LOYALTY",
            500,
            {"name": "Charlie Loyalty Token", "symbol": "CLT"}
        )
        print(f"Minted token for Charlie: {charlie_token['token_id']} (Supply: {charlie_token['supply']})")
    except Exception as e:
        print(f"Failed to mint token for Charlie: {str(e)}")
    
    print("\nMikvah Engine test completed successfully!")

if __name__ == "__main__":
    test_mikvah_engine()
