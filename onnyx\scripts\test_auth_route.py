#!/usr/bin/env python3
"""
Test Auth Route

Simple test to check if the auth registration route is working.
"""

import os
import sys
import requests

def test_auth_route():
    """Test the auth registration route."""
    print("🔍 Testing Auth Registration Route...")
    
    try:
        # Test the main page first
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        print(f"Main page: HTTP {response.status_code}")
        
        # Test the register choice page
        response = requests.get("http://127.0.0.1:5000/register", timeout=10)
        print(f"Register choice: HTTP {response.status_code}")
        
        # Test the auth login page
        response = requests.get("http://127.0.0.1:5000/auth/login", timeout=10)
        print(f"Auth login: HTTP {response.status_code}")
        
        # Test the problematic registration page
        response = requests.get("http://127.0.0.1:5000/auth/register/identity", timeout=10)
        print(f"Auth register identity: HTTP {response.status_code}")
        
        if response.status_code != 200:
            print(f"Error response: {response.text[:500]}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_auth_route()
