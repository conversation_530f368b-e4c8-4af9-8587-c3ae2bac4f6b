"""
Onnyx Identity Registry Module

This module provides the IdentityRegistry class for managing identities.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional

class IdentityRegistry:
    """
    IdentityRegistry manages identities in the Onnyx ecosystem.

    An identity is a unique, persistent entity that can participate in the Onnyx ecosystem.
    """

    def __init__(self, path: str = "data/identities.json"):
        """
        Initialize the IdentityRegistry.

        Args:
            path: Path to the identity registry JSON file
        """
        self.path = path
        self.identities = self._load()

        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.path), exist_ok=True)

    def _load(self) -> Dict[str, Any]:
        """
        Load the identity registry from the JSON file.

        Returns:
            The identity registry as a dictionary
        """
        if os.path.exists(self.path):
            try:
                with open(self.path, "r") as f:
                    data = json.load(f)
                    # Ensure the data is a dictionary
                    if isinstance(data, dict):
                        return data
                    else:
                        return {}
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}

    def _save(self) -> None:
        """Save the identity registry to the JSON file."""
        with open(self.path, "w") as f:
            json.dump(self.identities, f, indent=2)

    def register_identity(self, identity_id: str, name: str, public_key: str) -> Dict[str, Any]:
        """
        Register a new identity.

        Args:
            identity_id: The identity ID
            name: The identity name
            public_key: The identity's public key

        Returns:
            The newly created identity

        Raises:
            Exception: If the identity already exists
        """
        if identity_id in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' already exists")

        self.identities[identity_id] = {
            "identity_id": identity_id,
            "name": name,
            "public_key": public_key,
            "created_at": int(time.time()),
            "reputation": 0,
            "etzem_score": 0,
            "badges": [],
            "joined_selas": [],
            "founded_selas": []
        }

        self._save()
        return self.identities[identity_id]

    def get_identity(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an identity by ID.

        Args:
            identity_id: The identity ID

        Returns:
            The identity or None if not found
        """
        return self.identities.get(identity_id)

    def update_identity(self, identity_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Update an identity.

        Args:
            identity_id: The identity ID
            **kwargs: The fields to update

        Returns:
            The updated identity or None if not found

        Raises:
            Exception: If the identity does not exist
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        for key, value in kwargs.items():
            if key in ["identity_id", "created_at"]:
                continue  # Don't allow updating these fields
            self.identities[identity_id][key] = value

        self._save()
        return self.identities[identity_id]

    def add_badge(self, identity_id: str, badge: str) -> None:
        """
        Add a badge to an identity.

        Args:
            identity_id: The identity ID
            badge: The badge to add

        Raises:
            Exception: If the identity does not exist or the badge already exists
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        if "badges" not in self.identities[identity_id]:
            self.identities[identity_id]["badges"] = []

        if badge in self.identities[identity_id]["badges"]:
            raise Exception(f"Badge '{badge}' already exists for identity '{identity_id}'")

        self.identities[identity_id]["badges"].append(badge)
        self._save()

    def remove_badge(self, identity_id: str, badge: str) -> None:
        """
        Remove a badge from an identity.

        Args:
            identity_id: The identity ID
            badge: The badge to remove

        Raises:
            Exception: If the identity does not exist or the badge does not exist
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        if "badges" not in self.identities[identity_id] or badge not in self.identities[identity_id]["badges"]:
            raise Exception(f"Badge '{badge}' does not exist for identity '{identity_id}'")

        self.identities[identity_id]["badges"].remove(badge)
        self._save()

    def update_reputation(self, identity_id: str, reputation: int) -> None:
        """
        Update the reputation of an identity.

        Args:
            identity_id: The identity ID
            reputation: The new reputation

        Raises:
            Exception: If the identity does not exist
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        self.identities[identity_id]["reputation"] = reputation
        self._save()

    def update_etzem_score(self, identity_id: str, etzem_score: float) -> None:
        """
        Update the Etzem score of an identity.

        Args:
            identity_id: The identity ID
            etzem_score: The new Etzem score

        Raises:
            Exception: If the identity does not exist
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        self.identities[identity_id]["etzem_score"] = etzem_score
        self._save()

    def link_sela(self, identity_id: str, sela_id: str, role: str) -> None:
        """
        Link an identity to a Sela.

        Args:
            identity_id: The identity ID
            sela_id: The Sela ID
            role: The role of the identity in the Sela

        Raises:
            Exception: If the identity does not exist
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Add the Sela to the appropriate list
        if role == "FOUNDER":
            if "founded_selas" not in self.identities[identity_id]:
                self.identities[identity_id]["founded_selas"] = []

            if sela_id not in self.identities[identity_id]["founded_selas"]:
                self.identities[identity_id]["founded_selas"].append(sela_id)
        else:
            if "joined_selas" not in self.identities[identity_id]:
                self.identities[identity_id]["joined_selas"] = []

            if sela_id not in self.identities[identity_id]["joined_selas"]:
                self.identities[identity_id]["joined_selas"].append(sela_id)

        # Add the role badge
        badge = f"SELA_{role.upper()}"
        if "badges" not in self.identities[identity_id]:
            self.identities[identity_id]["badges"] = []

        if badge not in self.identities[identity_id]["badges"]:
            self.identities[identity_id]["badges"].append(badge)

        self._save()

    def unlink_sela(self, identity_id: str, sela_id: str, role: str) -> None:
        """
        Unlink an identity from a Sela.

        Args:
            identity_id: The identity ID
            sela_id: The Sela ID
            role: The role of the identity in the Sela

        Raises:
            Exception: If the identity does not exist or the Sela is not linked
        """
        if identity_id not in self.identities:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Remove the Sela from the appropriate list
        if role == "FOUNDER":
            if "founded_selas" not in self.identities[identity_id] or sela_id not in self.identities[identity_id]["founded_selas"]:
                raise Exception(f"Sela '{sela_id}' is not linked to identity '{identity_id}' as founder")

            self.identities[identity_id]["founded_selas"].remove(sela_id)
        else:
            if "joined_selas" not in self.identities[identity_id] or sela_id not in self.identities[identity_id]["joined_selas"]:
                raise Exception(f"Sela '{sela_id}' is not linked to identity '{identity_id}' as member")

            self.identities[identity_id]["joined_selas"].remove(sela_id)

        # Check if the identity is still linked to any Selas with the same role
        has_other_selas_with_role = False
        if role == "FOUNDER" and "founded_selas" in self.identities[identity_id] and self.identities[identity_id]["founded_selas"]:
            has_other_selas_with_role = True
        elif role != "FOUNDER" and "joined_selas" in self.identities[identity_id] and self.identities[identity_id]["joined_selas"]:
            has_other_selas_with_role = True

        # Remove the role badge if no other Selas with the same role
        if not has_other_selas_with_role:
            badge = f"SELA_{role.upper()}"
            if "badges" in self.identities[identity_id] and badge in self.identities[identity_id]["badges"]:
                self.identities[identity_id]["badges"].remove(badge)

        self._save()

    def get_all_identities(self) -> Dict[str, Any]:
        """
        Get all identities.

        Returns:
            All identities in the registry
        """
        return self.identities
