# src/db/schema/etzem.py

from src.db.manager import db_manager

def create_etzem_tables():
    """
    Create the tables for the Etzem system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()

    # Create etzem_scores table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS etzem_scores (
        identity_id TEXT PRIMARY KEY,
        etzem_score REAL NOT NULL,
        consistency REAL NOT NULL,
        tx_score REAL NOT NULL,
        trust_weight REAL NOT NULL,
        badge_bonus REAL NOT NULL,
        sela_participation REAL NOT NULL,
        token_impact REAL NOT NULL,
        last_updated INTEGER NOT NULL,
        FOREIG<PERSON> KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create activity_etzem table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS activity_etzem (
        identity_id TEXT PRIMARY KEY,
        consistency_score REAL NOT NULL,
        token_impact REAL NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create identity_badges table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS identity_badges (
        identity_id TEXT NOT NULL,
        badge TEXT NOT NULL,
        awarded_at INTEGER NOT NULL,
        PRIMARY KEY (identity_id, badge),
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create etzem_score_history table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS etzem_score_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        identity_id TEXT NOT NULL,
        etzem_score REAL NOT NULL,
        updated_at INTEGER NOT NULL,
        reason TEXT,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_etzem_scores_etzem_score ON etzem_scores (etzem_score)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_identity_badges_badge ON identity_badges (badge)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_etzem_score_history_identity_id ON etzem_score_history (identity_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_etzem_score_history_updated_at ON etzem_score_history (updated_at)')

    conn.commit()
