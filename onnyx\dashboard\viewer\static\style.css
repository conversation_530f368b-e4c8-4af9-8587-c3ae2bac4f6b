/* Base Styles */
:root {
    --bg-primary: #0a0e17;
    --bg-secondary: #121a29;
    --bg-tertiary: #1e2a3d;
    --bg-card: #1a2436;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --accent-primary: #6200ea;
    --accent-secondary: #b388ff;
    --accent-tertiary: #4a148c;
    --success: #00c853;
    --warning: #ffd600;
    --error: #ff1744;
    --info: #00b0ff;
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --gradient-primary: linear-gradient(135deg, #6200ea, #b388ff);
    --gradient-dark: linear-gradient(135deg, #121a29, #1e2a3d);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-image: radial-gradient(circle at 50% 50%, rgba(98, 0, 234, 0.05) 0%, rgba(10, 14, 23, 0) 70%);
}

a {
    color: var(--accent-secondary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--accent-primary);
}

/* Modern Header & Navigation */
header {
    background-color: var(--bg-secondary);
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-icon {
    font-size: 1.8rem;
    margin-right: 0.5rem;
    color: var(--accent-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
    letter-spacing: 1px;
}

.subtitle {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-left: 0.5rem;
    padding-left: 0.5rem;
    border-left: 1px solid var(--bg-tertiary);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
}

/* Main Navigation */
#main-nav {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    font-weight: 500;
}

.nav-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.nav-link:hover, .nav-link.active {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.nav-link.active {
    color: var(--accent-primary);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px 3px 0 0;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-toggle .fa-chevron-down {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown.open .dropdown-toggle .fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    list-style: none;
    padding: 0.5rem 0;
}

.dropdown.open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    transition: var(--transition);
}

.dropdown-menu li a i {
    margin-right: 0.75rem;
}

.dropdown-menu li a:hover, .dropdown-menu li a.active {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--accent-secondary);
}

/* Search Container */
.search-container {
    position: relative;
    margin-left: 1rem;
}

.search-container form {
    display: flex;
    align-items: center;
}

.search-container input {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    width: 220px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.search-container input:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-primary);
    width: 280px;
}

.search-container button {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.search-container button:hover {
    background-color: var(--accent-secondary);
}

/* Main Content */
main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
    min-height: calc(100vh - 200px);
}

.section {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0.7;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--bg-tertiary);
    padding-bottom: 0.75rem;
    flex-wrap: wrap;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 1.5rem;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Auto-refresh Controls */
.auto-refresh-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-left: auto;
}

.refresh-button {
    background-color: var(--bg-tertiary);
    border: none;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.refresh-button:hover {
    background-color: var(--accent-primary);
    color: white;
}

.auto-refresh-toggle {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.auto-refresh-toggle input[type="checkbox"] {
    margin: 0;
}

.auto-refresh-interval {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.auto-refresh-interval input[type="number"] {
    width: 3.5rem;
    padding: 0.25rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--border-radius);
    color: var(--text-primary);
}

.auto-refresh-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.auto-refresh-info .enabled {
    color: var(--success);
}

.auto-refresh-info .disabled {
    color: var(--text-secondary);
}

#auto-refresh-countdown {
    font-weight: bold;
    color: var(--accent-primary);
}

.view-all {
    font-size: 0.9rem;
}

/* Cards */
.card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.03);
    position: relative;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: rgba(179, 136, 255, 0.1);
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at top right, rgba(98, 0, 234, 0.1), transparent 70%);
    pointer-events: none;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding-bottom: 0.75rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-secondary);
}

.card-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.card-content {
    position: relative;
    z-index: 1;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--bg-tertiary);
}

th {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    font-weight: 500;
}

tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.03);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: rgba(179, 136, 255, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(98, 0, 234, 0.05), transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--accent-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 1;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    position: relative;
    z-index: 1;
    font-weight: 500;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination a, .pagination span {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: var(--transition);
}

.pagination a:hover {
    background-color: var(--accent-primary);
}

.pagination .active {
    background-color: var(--accent-primary);
}

/* Modern Footer */
footer {
    background-color: var(--bg-secondary);
    padding: 3rem 2rem 1.5rem;
    margin-top: 3rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0.7;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.footer-section {
    position: relative;
}

.footer-section h3 {
    font-size: 1.25rem;
    margin-bottom: 1.25rem;
    color: var(--text-primary);
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 1.5px;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 1.25rem;
    line-height: 1.7;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.footer-section ul li a i {
    margin-right: 0.5rem;
    color: var(--accent-secondary);
    font-size: 0.9rem;
}

.footer-section ul li a:hover {
    color: var(--accent-secondary);
    transform: translateX(5px);
}

.learn-more-btn {
    margin-top: 1.5rem;
}

.learn-more-btn .btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
}

.learn-more-btn .btn-primary i {
    margin-right: 0.5rem;
}

.contact p {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.contact p i {
    margin-right: 0.75rem;
    color: var(--accent-secondary);
}

/* Social Links */
.social-links {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.social-links a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: var(--bg-tertiary);
    border-radius: 50%;
    color: var(--text-secondary);
    font-size: 1.1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.social-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.social-links a i {
    position: relative;
    z-index: 1;
}

.social-links a:hover {
    color: white;
    transform: translateY(-3px);
}

.social-links a:hover::before {
    opacity: 1;
}

/* Newsletter */
.newsletter {
    margin-top: 1.5rem;
}

.newsletter h4 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.newsletter form {
    display: flex;
    max-width: 300px;
}

.newsletter input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.newsletter button {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    background-color: var(--accent-primary);
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.newsletter button:hover {
    background-color: var(--accent-secondary);
}

/* Footer Bottom */
.footer-bottom {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.copyright {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.footer-menu ul {
    display: flex;
    list-style: none;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-menu ul li a {
    color: var(--text-secondary);
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-menu ul li a:hover {
    color: var(--accent-secondary);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .navbar {
        padding: 0.75rem 1rem;
    }

    main {
        padding: 0 1rem;
    }
}

@media (max-width: 992px) {
    .nav-link span {
        display: none;
    }

    .nav-link i {
        margin-right: 0;
        font-size: 1.2rem;
    }

    .search-container input {
        width: 180px;
    }

    .search-container input:focus {
        width: 220px;
    }
}

@media (max-width: 768px) {
    .navbar {
        justify-content: space-between;
    }

    .mobile-menu-toggle {
        display: block;
    }

    #main-nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--bg-secondary);
        box-shadow: var(--box-shadow);
        padding: 1rem;
        display: none;
        z-index: 100;
        border-top: 1px solid var(--bg-tertiary);
    }

    #main-nav.active {
        display: block;
    }

    .nav-links {
        flex-direction: column;
        width: 100%;
    }

    .nav-link {
        width: 100%;
        justify-content: flex-start;
    }

    .nav-link span {
        display: inline;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        width: 100%;
        margin-left: 1.5rem;
        display: none;
    }

    .dropdown.open .dropdown-menu {
        display: block;
    }

    .search-container {
        width: 100%;
        margin: 1rem 0 0;
    }

    .search-container form {
        width: 100%;
    }

    .search-container input {
        width: 100%;
    }

    .search-container input:focus {
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-menu ul {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .section {
        padding: 1rem;
    }

    .card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 2rem;
    }

    .auto-refresh-controls {
        width: 100%;
        margin-top: 1rem;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .pagination {
        flex-wrap: wrap;
    }
}

/* Detail Pages */
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.detail-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.detail-subtitle {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.meta-item {
    background-color: var(--bg-tertiary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
}

.meta-item i {
    margin-right: 0.5rem;
    color: var(--accent-primary);
}

.detail-content {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.detail-section {
    margin-bottom: 2rem;
}

.detail-section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--bg-tertiary);
    padding-bottom: 0.5rem;
}

.property-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.property-item {
    background-color: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.property-label {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.property-value {
    word-break: break-all;
}

.json-viewer {
    background-color: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--border-radius);
    overflow-x: auto;
    font-family: monospace;
    white-space: pre-wrap;
}

/* Status Tags */
.status-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-confirmed {
    background-color: var(--success);
    color: white;
}

.status-pending {
    background-color: var(--warning);
    color: black;
}

.status-failed {
    background-color: var(--error);
    color: white;
}

/* Transaction Types */
.tx-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tx-transfer {
    background-color: var(--info);
    color: white;
}

.tx-mint {
    background-color: var(--success);
    color: white;
}

.tx-burn {
    background-color: var(--error);
    color: white;
}

.tx-spawn {
    background-color: var(--accent-primary);
    color: white;
}

.tx-identity {
    background-color: var(--accent-secondary);
    color: black;
}

.tx-unknown {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Admin Page */
.admin-tabs {
    margin-top: 2rem;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid var(--bg-tertiary);
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.tab-button {
    padding: 0.75rem 1.5rem;
    background-color: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    white-space: nowrap;
}

.tab-button:hover {
    color: var(--text-primary);
    background-color: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
    color: var(--accent-primary);
    border-bottom: 2px solid var(--accent-primary);
}

.tab-content {
    display: none;
    padding: 1rem;
}

.tab-content.active {
    display: block;
}

.admin-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    transition: var(--transition);
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--accent-primary);
    outline: none;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

.btn-primary {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    text-align: center;
}

.btn-primary:hover {
    background-color: var(--accent-secondary);
    color: black;
}

.result-card {
    margin-top: 2rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-tertiary);
}

.result-card.success {
    border-left: 4px solid var(--success);
}

.result-card.error {
    border-left: 4px solid var(--error);
}

.result-card h4 {
    margin-bottom: 0.5rem;
}

/* Badge */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Coming Soon Page */
.coming-soon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 300px);
    padding: 2rem;
}

.coming-soon-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 3rem;
    max-width: 800px;
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.05);
    animation: fadeIn 1s ease-in-out;
}

.coming-soon-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: var(--gradient-primary);
}

.coming-soon-logo {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: var(--accent-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: pulse 2s infinite;
}

.coming-soon-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 2px;
}

.sela-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.sela-name {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.sela-type, .sela-owner {
    margin: 0.5rem 0;
    font-size: 1.1rem;
}

.sela-type-label, .sela-owner-label {
    color: var(--text-secondary);
    margin-right: 0.5rem;
}

.sela-type-value, .sela-owner-value {
    color: var(--accent-secondary);
    font-weight: 500;
}

.coming-soon-message {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

.coming-soon-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.feature i {
    font-size: 2rem;
    color: var(--accent-primary);
}

.feature span {
    font-weight: 500;
}

.coming-soon-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--bg-tertiary);
}

.coming-soon-footer p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.coming-soon-footer .btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.coming-soon-footer .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(98, 0, 234, 0.3);
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .navbar {
        padding: 0.75rem 1rem;
    }

    main {
        padding: 0 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .coming-soon-card {
        padding: 2rem;
    }

    .coming-soon-title {
        font-size: 2.5rem;
    }

    .sela-name {
        font-size: 1.75rem;
    }
}
