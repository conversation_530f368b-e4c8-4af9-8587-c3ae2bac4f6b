# src/api.py

from fastapi import FastAPI
from src.routes.token import token_router
from src.routes.identity import identity_router
from src.routes.explorer import explorer_router
from src.routes.sela import sela_router
from src.routes.zeman import zeman_router
from src.routes.yovel import yovel_router
from src.routes.etzem import etzem_router
from src.routes.governance import gov_router
from src.routes.council import council_router
from src.routes.mikvah import mikvah_router
from src.routes.judah import judah_router
from src.routes.staking import staking_router
from src.routes.harashim import router as harashim_router

# Create FastAPI app
app = FastAPI(
    title="Onnyx API",
    description="API for the Onnyx blockchain",
    version="0.1.0"
)

# Include routers
app.include_router(token_router)
app.include_router(identity_router)
app.include_router(explorer_router)
app.include_router(sela_router)
app.include_router(zeman_router)
app.include_router(yovel_router)
app.include_router(etzem_router)
app.include_router(gov_router)
app.include_router(council_router)
app.include_router(mikvah_router)
app.include_router(judah_router)
app.include_router(staking_router)
app.include_router(harashim_router)

# Root endpoint
@app.get("/")
def read_root():
    return {
        "name": "Onnyx API",
        "version": "0.1.0",
        "endpoints": [
            "/token",
            "/identity",
            "/explorer",
            "/sela",
            "/zeman",
            "/yovel",
            "/etzem",
            "/governance",
            "/council",
            "/mikvah",
            "/judah",
            "/staking",
            "/harashim"
        ]
    }
