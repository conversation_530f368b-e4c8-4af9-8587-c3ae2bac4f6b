#!/usr/bin/env python3
"""
Onnyx Miner Sela Node Creator

This script creates a new Sela node for the Onnyx blockchain.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def create_sela_node():
    """Create a new Sela node."""
    # Parse arguments
    parser = argparse.ArgumentParser(description="Create a new Sela node")
    parser.add_argument("--identity", required=True, help="The identity ID")
    parser.add_argument("--sela", required=True, help="The Sela name")
    parser.add_argument("--type", default="BUSINESS", help="The Sela type")
    parser.add_argument("--token-type", default="", help="The token type")
    parser.add_argument("--api-url", default="http://localhost:8000", help="The API URL")
    parser.add_argument("--config", help="The path to the configuration file")
    
    args = parser.parse_args()
    
    # Create a Sela ID from the name
    sela_id = args.sela.lower().replace(" ", "_")
    
    # Install the Onnyx Miner
    try:
        print("Installing Onnyx Miner...")
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "onnyx-miner"],
            check=True
        )
    except subprocess.CalledProcessError as e:
        print(f"Error installing Onnyx Miner: {e}")
        return False
    
    # Set up the Onnyx Miner
    try:
        print("Setting up Onnyx Miner...")
        cmd = [
            "onnyx", "setup",
            "--identity-id", args.identity,
            "--name", args.identity,
            "--sela-id", sela_id,
            "--sela-name", args.sela,
            "--sela-type", args.type,
            "--api-url", args.api_url
        ]
        
        if args.token_type:
            cmd.extend(["--token-type", args.token_type])
        
        if args.config:
            cmd.extend(["--config", args.config])
        
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error setting up Onnyx Miner: {e}")
        return False
    
    # Start the GUI
    try:
        print("Starting Onnyx Miner GUI...")
        subprocess.Popen(["onnyx", "gui"])
    except Exception as e:
        print(f"Error starting Onnyx Miner GUI: {e}")
        return False
    
    print("Sela node created successfully!")
    print(f"Identity: {args.identity}")
    print(f"Sela: {args.sela} ({sela_id})")
    print("GUI: http://localhost:5005")
    
    return True

if __name__ == "__main__":
    create_sela_node()
