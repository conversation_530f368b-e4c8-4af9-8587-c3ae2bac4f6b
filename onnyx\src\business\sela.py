# src/business/sela.py

import os
import json
import time
import uuid
from src.tokens.ledger import TokenLedger
from src.tokens.registry import TokenRegistry
from src.identity.registry import IdentityRegistry

class SelaRegistry:
    """
    Sela (Business) Registry for the Onnyx blockchain.
    
    A Sela is a business entity registered on the Onnyx blockchain.
    Registration requires staking ONX tokens, with the amount determined by the identity's reputation.
    """
    
    def __init__(self, db_path=None):
        """
        Initialize the Sela Registry.
        
        Args:
            db_path: Path to the registry database file. If None, a default path in the data directory will be used.
        """
        if db_path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.path = os.path.join(data_dir, "sela_registry.json")
        else:
            self.path = db_path
            
        self.selas = {}
        self._load()

    def _load(self):
        """Load the Sela registry from the database file."""
        try:
            if os.path.exists(self.path) and os.path.getsize(self.path) > 0:
                with open(self.path, "r") as f:
                    self.selas = json.load(f)
        except Exception as e:
            print(f"Error loading Sela registry: {str(e)}")
            self.selas = {}

    def _save(self):
        """Save the Sela registry to the database file."""
        with open(self.path, "w") as f:
            json.dump(self.selas, f, indent=2)

    def get_all(self):
        """
        Get all registered Selas.
        
        Returns:
            A list of all registered Selas.
        """
        return list(self.selas.values())
    
    def get_by_id(self, sela_id):
        """
        Get a Sela by its ID.
        
        Args:
            sela_id: The ID of the Sela to retrieve.
            
        Returns:
            The Sela with the specified ID, or None if not found.
        """
        for sela in self.selas.values():
            if sela["id"] == sela_id:
                return sela
        return None
    
    def get_by_owner(self, identity_id):
        """
        Get a Sela by its owner's identity ID.
        
        Args:
            identity_id: The identity ID of the Sela owner.
            
        Returns:
            The Sela owned by the specified identity, or None if not found.
        """
        return self.selas.get(identity_id)
    
    def get_by_sector(self, sector):
        """
        Get all Selas in a specific sector.
        
        Args:
            sector: The sector to filter by.
            
        Returns:
            A list of Selas in the specified sector.
        """
        return [sela for sela in self.selas.values() if sela["sector"] == sector]

    def register_sela(self, identity_id, name, sector, stake_required, ledger: TokenLedger, identity_registry: IdentityRegistry = None):
        """
        Register a new Sela.
        
        Args:
            identity_id: The identity ID of the Sela owner.
            name: The name of the Sela.
            sector: The sector of the Sela.
            stake_required: The amount of ONX tokens required to stake.
            ledger: The token ledger to use for staking.
            identity_registry: Optional identity registry to verify identity.
            
        Returns:
            The newly registered Sela.
            
        Raises:
            Exception: If the identity has already registered a Sela or has insufficient ONX tokens.
        """
        # Check if the identity has already registered a Sela
        if identity_id in self.selas:
            raise Exception("This identity has already registered a Sela.")
        
        # Verify identity if registry is provided
        if identity_registry:
            identity = identity_registry.get_identity(identity_id)
            if not identity:
                raise Exception("Identity not found.")
            
            # Check if identity has required soulbound badges
            if not self._has_required_badges(identity):
                raise Exception("Identity does not have the required soulbound badges for Sela registration.")
        
        # Check if the identity has enough ONX tokens
        balance = ledger.get_balance(identity_id, "ONX")
        if balance < stake_required:
            raise Exception(f"Insufficient ONX to register. Required: {stake_required}, You have: {balance}")
        
        # Debit the stake amount from the identity
        ledger.debit(identity_id, "ONX", stake_required)
        
        # Generate a unique Sela ID
        sela_id = f"sela_{uuid.uuid4().hex[:8]}_{identity_id[:8]}"
        
        # Create the Sela record
        sela = {
            "id": sela_id,
            "owner": identity_id,
            "name": name,
            "sector": sector,
            "stake": stake_required,
            "created_at": int(time.time()),
            "status": "active",
            "members": [identity_id],
            "governance": {
                "voting_threshold": 0.51,  # 51% majority
                "voting_period": 7 * 24 * 60 * 60,  # 7 days in seconds
                "proposals": []
            },
            "metadata": {}
        }
        
        # Add the Sela to the registry
        self.selas[identity_id] = sela
        self._save()
        
        return sela
    
    def update_sela(self, identity_id, field, value):
        """
        Update a Sela's information.
        
        Args:
            identity_id: The identity ID of the Sela owner.
            field: The field to update.
            value: The new value for the field.
            
        Returns:
            The updated Sela.
            
        Raises:
            Exception: If the identity has not registered a Sela or the field is not allowed to be updated.
        """
        # Check if the identity has registered a Sela
        if identity_id not in self.selas:
            raise Exception("This identity has not registered a Sela.")
        
        # Check if the field is allowed to be updated
        allowed_fields = ["name", "sector", "metadata"]
        if field not in allowed_fields:
            raise Exception(f"Field '{field}' cannot be updated.")
        
        # Update the field
        if field == "metadata":
            # Merge metadata instead of replacing it
            self.selas[identity_id]["metadata"].update(value)
        else:
            self.selas[identity_id][field] = value
        
        self._save()
        
        return self.selas[identity_id]
    
    def add_member(self, sela_id, identity_id, identity_registry: IdentityRegistry = None):
        """
        Add a member to a Sela.
        
        Args:
            sela_id: The ID of the Sela.
            identity_id: The identity ID of the member to add.
            identity_registry: Optional identity registry to verify identity.
            
        Returns:
            The updated Sela.
            
        Raises:
            Exception: If the Sela is not found or the identity is already a member.
        """
        # Get the Sela
        sela = self.get_by_id(sela_id)
        if not sela:
            raise Exception("Sela not found.")
        
        # Verify identity if registry is provided
        if identity_registry:
            identity = identity_registry.get_identity(identity_id)
            if not identity:
                raise Exception("Identity not found.")
        
        # Check if the identity is already a member
        if identity_id in sela["members"]:
            raise Exception("Identity is already a member of this Sela.")
        
        # Add the identity to the members list
        sela["members"].append(identity_id)
        self._save()
        
        return sela
    
    def remove_member(self, sela_id, identity_id):
        """
        Remove a member from a Sela.
        
        Args:
            sela_id: The ID of the Sela.
            identity_id: The identity ID of the member to remove.
            
        Returns:
            The updated Sela.
            
        Raises:
            Exception: If the Sela is not found, the identity is not a member, or the identity is the owner.
        """
        # Get the Sela
        sela = self.get_by_id(sela_id)
        if not sela:
            raise Exception("Sela not found.")
        
        # Check if the identity is the owner
        if identity_id == sela["owner"]:
            raise Exception("Cannot remove the owner from the Sela.")
        
        # Check if the identity is a member
        if identity_id not in sela["members"]:
            raise Exception("Identity is not a member of this Sela.")
        
        # Remove the identity from the members list
        sela["members"].remove(identity_id)
        self._save()
        
        return sela
    
    def _has_required_badges(self, identity):
        """
        Check if an identity has the required soulbound badges for Sela registration.
        
        Args:
            identity: The identity to check.
            
        Returns:
            True if the identity has the required badges, False otherwise.
        """
        # Check for the "VERIFIED" badge
        if "badge:verified" not in identity.reputation:
            return False
        
        # In the future, we can add more badge requirements here
        
        return True
