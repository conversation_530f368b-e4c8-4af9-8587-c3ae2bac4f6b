"""
Onnyx Token Routes

This module provides API routes for token operations.
"""

import logging
import uuid
import time
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from tokens.ledger.ledger.ledger.registry import TokenRegistry
from tokens.ledger.ledger.ledger.ledger import TokenLedger
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry
from yovel.limits import calculate_yovel_cap
from identity.trust.etzem_engine import EtzemEngine

from shared.models.token import Token
from shared.models.identity import Identity
from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.routes.tokens")

# Create router
router = APIRouter()

# Create instances
token_registry = TokenRegistry()
token_ledger = TokenLedger()
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()
etzem_engine = EtzemEngine()

@router.post("/tokens/mint")
def mint_token(
    token_id: str = None,
    name: str = Body(...),
    symbol: str = Body(...),
    creator_id: str = Body(...),
    supply: int = Body(...),
    category: str = Body("general"),
    decimals: int = Body(2),
    metadata: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Mint a new token.

    Args:
        token_id: The token ID (optional, will be generated if not provided)
        name: The token name
        symbol: The token symbol
        creator_id: The creator's identity ID
        supply: The initial supply
        category: The token category (e.g., "loyalty", "equity", "community")
        decimals: The number of decimal places
        metadata: Additional metadata for the token

    Returns:
        Information about the minted token
    """
    try:
        logger.info(f"Minting token: {name} ({symbol}) by {creator_id}")

        # Generate token ID if not provided
        if not token_id:
            token_id = f"token_{uuid.uuid4().hex[:16]}_{int(time.time())}"
            logger.debug(f"Generated token ID: {token_id}")

        # Check if the creator identity exists
        creator = Identity.get_by_id(creator_id)
        if not creator:
            logger.warning(f"Identity with ID '{creator_id}' not found")
            raise Exception(f"Identity with ID '{creator_id}' not found")

        # Check if the creator is a Selaholder
        is_selaholder = False
        if "founded_selas" in creator.metadata and creator.metadata["founded_selas"]:
            is_selaholder = True
        elif "joined_selas" in creator.metadata and creator.metadata["joined_selas"]:
            is_selaholder = True

        if not is_selaholder:
            logger.warning(f"Identity {creator_id} is not a Selaholder")
            raise Exception("Only Selaholders can mint Mikvah tokens")

        # Calculate the Etzem score
        etzem_data = etzem_engine.compute_etzem(creator_id)
        etzem_score = etzem_data["etzem"]

        # Calculate the Yovel mint cap
        mint_cap = calculate_yovel_cap(etzem_score, category)

        # Check if the supply is within the mint cap
        if supply > mint_cap:
            logger.warning(f"Supply ({supply}) exceeds mint cap of {mint_cap} for category '{category}'")
            raise Exception(f"Supply ({supply}) exceeds mint cap of {mint_cap} for category '{category}'")

        # Prepare metadata
        if metadata is None:
            metadata = {}

        # Add decimals to metadata
        metadata["decimals"] = decimals

        # Create the token in the database
        token = Token.create(
            token_id=token_id,
            name=name,
            symbol=symbol,
            creator_id=creator_id,
            token_type=category,
            supply=supply,
            metadata=metadata
        )

        # Register the token with the registry
        token_registry.register_token(
            token_id,
            name,
            symbol,
            creator_id,
            supply,
            category,
            decimals,
            metadata
        )

        # Credit the initial supply to the creator
        token_ledger.credit(creator_id, token_id, supply)

        # Create a mint transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="MINT",
            sender=creator_id,
            recipient=creator_id,
            token_id=token_id,
            amount=supply,
            status="CONFIRMED",
            data={
                "op": "OP_MINT_TOKEN",
                "token_id": token_id,
                "amount": supply,
                "to_id": creator_id,
                "minter_id": creator_id
            }
        )

        logger.info(f"Token minted: {token_id}")
        return {
            "status": "token_minted",
            "token": {
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_id": token.creator_id,
                "supply": token.supply,
                "token_type": token.token_type,
                "metadata": token.metadata
            }
        }
    except Exception as e:
        logger.error(f"Error minting token: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tokens/{token_id}")
def get_token(token_id: str) -> Dict[str, Any]:
    """
    Get a token by ID.

    Args:
        token_id: The token ID

    Returns:
        The token
    """
    logger.info(f"Getting token: {token_id}")

    token = Token.get_by_id(token_id)
    if not token:
        logger.warning(f"Token with ID '{token_id}' not found")
        raise HTTPException(status_code=404, detail=f"Token with ID '{token_id}' not found")

    return {
        "token_id": token.token_id,
        "name": token.name,
        "symbol": token.symbol,
        "creator_id": token.creator_id,
        "supply": token.supply,
        "token_type": token.token_type,
        "metadata": token.metadata
    }

@router.get("/tokens/symbol/{symbol}")
def get_token_by_symbol(symbol: str) -> Dict[str, Any]:
    """
    Get a token by symbol.

    Args:
        symbol: The token symbol

    Returns:
        The token
    """
    logger.info(f"Getting token by symbol: {symbol}")

    tokens = Token.find_by_symbol(symbol)
    if not tokens:
        logger.warning(f"Token with symbol '{symbol}' not found")
        raise HTTPException(status_code=404, detail=f"Token with symbol '{symbol}' not found")

    # Return the first token with this symbol
    token = tokens[0]

    return {
        "token_id": token.token_id,
        "name": token.name,
        "symbol": token.symbol,
        "creator_id": token.creator_id,
        "supply": token.supply,
        "token_type": token.token_type,
        "metadata": token.metadata
    }

@router.get("/tokens")
def list_tokens(category: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all tokens.

    Args:
        category: Filter by token category

    Returns:
        All tokens in the registry
    """
    logger.info(f"Listing tokens with category filter: {category}")

    if category:
        tokens = Token.find_by_type(category)
    else:
        tokens = Token.get_all()

    return {
        "tokens": [
            {
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_id": token.creator_id,
                "supply": token.supply,
                "token_type": token.token_type,
                "metadata": token.metadata
            }
            for token in tokens
        ]
    }

@router.get("/tokens/creator/{creator_id}")
def get_tokens_by_creator(creator_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all tokens created by an identity.

    Args:
        creator_id: The creator's identity ID

    Returns:
        A list of tokens created by the identity
    """
    logger.info(f"Getting tokens by creator: {creator_id}")

    tokens = Token.find_by_creator(creator_id)

    return {
        "tokens": [
            {
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_id": token.creator_id,
                "supply": token.supply,
                "token_type": token.token_type,
                "metadata": token.metadata
            }
            for token in tokens
        ]
    }

@router.get("/sela/{sela_id}/tokens")
def get_tokens_by_sela(sela_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all tokens associated with a Sela.

    Args:
        sela_id: The Sela ID

    Returns:
        A list of tokens associated with the Sela
    """
    logger.info(f"Getting tokens by Sela: {sela_id}")

    # Check if the Sela exists
    sela = sela_registry.get_sela(sela_id)
    if not sela:
        logger.warning(f"Sela with ID '{sela_id}' not found")
        raise HTTPException(status_code=404, detail=f"Sela with ID '{sela_id}' not found")

    # Get tokens created by the Sela founder
    founder_id = sela["founder"]
    tokens = Token.find_by_creator(founder_id)

    # Filter tokens that have the Sela ID in their metadata
    sela_tokens = []
    for token in tokens:
        metadata = token.metadata
        if isinstance(metadata, dict) and metadata.get("sela_id") == sela_id:
            sela_tokens.append({
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_id": token.creator_id,
                "supply": token.supply,
                "token_type": token.token_type,
                "metadata": token.metadata
            })

    return {
        "sela_id": sela_id,
        "sela_name": sela["name"],
        "tokens": sela_tokens
    }

@router.get("/tokenregistry/categories")
def get_token_categories() -> Dict[str, List[str]]:
    """
    Get all token categories.

    Returns:
        All token categories
    """
    logger.info("Getting token categories")

    # Get all tokens
    tokens = Token.get_all()

    # Extract unique categories
    categories = set(token.token_type for token in tokens)

    return {
        "categories": list(categories)
    }

@router.post("/tokens/transfer")
def transfer_token(
    token_id: str = Body(...),
    sender_id: str = Body(...),
    recipient_id: str = Body(...),
    amount: int = Body(...),
    memo: Optional[str] = Body(None)
) -> Dict[str, Any]:
    """
    Transfer tokens from one identity to another.

    Args:
        token_id: The token ID
        sender_id: The sender's identity ID
        recipient_id: The recipient's identity ID
        amount: The amount to transfer
        memo: Optional memo

    Returns:
        Information about the transfer
    """
    try:
        logger.info(f"Transferring {amount} of token {token_id} from {sender_id} to {recipient_id}")

        # Check if the token exists
        token = Token.get_by_id(token_id)
        if not token:
            logger.warning(f"Token with ID '{token_id}' not found")
            raise Exception(f"Token with ID '{token_id}' not found")

        # Check if the sender exists
        sender = Identity.get_by_id(sender_id)
        if not sender:
            logger.warning(f"Identity with ID '{sender_id}' not found")
            raise Exception(f"Identity with ID '{sender_id}' not found")

        # Check if the recipient exists
        recipient = Identity.get_by_id(recipient_id)
        if not recipient:
            logger.warning(f"Identity with ID '{recipient_id}' not found")
            raise Exception(f"Identity with ID '{recipient_id}' not found")

        # Check if the token is transferable
        if not token.metadata.get("transferable", True):
            logger.warning(f"Token {token_id} is not transferable")
            raise Exception(f"Token {token.name} ({token_id}) is not transferable")

        # Check if the sender has enough balance
        balance = token_ledger.get_balance(sender_id, token_id)
        if balance < amount:
            logger.warning(f"Insufficient balance: {balance} < {amount}")
            raise Exception(f"Insufficient balance: {balance} < {amount}")

        # Transfer the tokens
        token_ledger.transfer(sender_id, recipient_id, token_id, amount)

        # Create a transfer transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="TRANSFER",
            sender=sender_id,
            recipient=recipient_id,
            token_id=token_id,
            amount=amount,
            status="CONFIRMED",
            data={
                "op": "OP_TRANSFER_TOKEN",
                "token_id": token_id,
                "from_id": sender_id,
                "to_id": recipient_id,
                "amount": amount,
                "memo": memo
            }
        )

        logger.info(f"Transfer successful: {tx_id}")
        return {
            "status": "transfer_successful",
            "tx_id": tx_id,
            "token_id": token_id,
            "sender_id": sender_id,
            "recipient_id": recipient_id,
            "amount": amount,
            "memo": memo
        }
    except Exception as e:
        logger.error(f"Error transferring tokens: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/tokens/burn")
def burn_token(
    token_id: str = Body(...),
    burner_id: str = Body(...),
    amount: int = Body(...)
) -> Dict[str, Any]:
    """
    Burn tokens.

    Args:
        token_id: The token ID
        burner_id: The burner's identity ID
        amount: The amount to burn

    Returns:
        Information about the burn
    """
    try:
        logger.info(f"Burning {amount} of token {token_id} by {burner_id}")

        # Check if the token exists
        token = Token.get_by_id(token_id)
        if not token:
            logger.warning(f"Token with ID '{token_id}' not found")
            raise Exception(f"Token with ID '{token_id}' not found")

        # Check if the burner exists
        burner = Identity.get_by_id(burner_id)
        if not burner:
            logger.warning(f"Identity with ID '{burner_id}' not found")
            raise Exception(f"Identity with ID '{burner_id}' not found")

        # Check if the burner is the token creator
        if token.creator_id != burner_id:
            logger.warning(f"Only the token creator can burn tokens")
            raise Exception("Only the token creator can burn tokens")

        # Check if the burner has enough balance
        balance = token_ledger.get_balance(burner_id, token_id)
        if balance < amount:
            logger.warning(f"Insufficient balance: {balance} < {amount}")
            raise Exception(f"Insufficient balance: {balance} < {amount}")

        # Burn the tokens
        token_ledger.debit(burner_id, token_id, amount)

        # Update the token supply
        token.supply -= amount
        token.save()

        # Create a burn transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="BURN",
            sender=burner_id,
            token_id=token_id,
            amount=amount,
            status="CONFIRMED",
            data={
                "op": "OP_BURN",
                "token_id": token_id,
                "burner_id": burner_id,
                "amount": amount
            }
        )

        logger.info(f"Burn successful: {tx_id}")
        return {
            "status": "burn_successful",
            "tx_id": tx_id,
            "token_id": token_id,
            "burner_id": burner_id,
            "amount": amount
        }
    except Exception as e:
        logger.error(f"Error burning tokens: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))