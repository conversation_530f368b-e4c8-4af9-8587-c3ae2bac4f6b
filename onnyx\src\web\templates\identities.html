{% extends 'base.html' %}

{% block title %}Onnyx Explorer - Identities{% endblock %}

{% block content %}
    {% with active_tab='identities' %}
    {% include 'explorer_hero.html' %}
    {% include 'explorer_nav.html' %}
    {% endwith %}

    {% if identities %}
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Identity ID</th>
                    <th>Tokens</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for identity in identities %}
                    <tr>
                        <td>{{ identity.name }}</td>
                        <td>{{ identity.identity_id }}</td>
                        <td>0</td>
                        <td>
                            <a href="{{ url_for('identity_detail', identity_id=identity.identity_id) }}">View Details</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="pagination">
            {% if offset > 0 %}
                <a href="{{ url_for('identities', limit=limit, offset=max(0, offset - limit)) }}">⬅ Previous</a>
            {% endif %}

            <span>Showing {{ offset + 1 }} to {{ (offset + limit) if (offset + limit < total) else total }} of {{ total }}</span>

            {% if offset + limit < total %}
                <a href="{{ url_for('identities', limit=limit, offset=offset + limit) }}">Next ➡</a>
            {% endif %}
        </div>
    {% else %}
        <p>No identities found.</p>
    {% endif %}
{% endblock %}
