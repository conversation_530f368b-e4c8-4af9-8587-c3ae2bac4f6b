# Onnyx Nation Layer

The Nation layer is a key component of the Onnyx blockchain that organizes identities into communities with shared governance and economic interests.

## Overview

Nations in Onnyx provide a way to group identities that share common characteristics, governance, or geographic location. Each identity can belong to one nation at a time, and nations are created and managed by their founders.

Nations provide:

- **Community Organization**: Group identities with shared values, interests, or geographic location
- **Collective Governance**: Nations can participate in governance as a unified entity
- **Economic Cooperation**: Enable collaborative economic activities within a trusted group
- **Identity Context**: Provide additional context and meaning to identities
- **Reputation Aggregation**: Nations can build collective reputation based on member activities

## Nation Properties

A Nation in Onnyx has the following properties:

- `nation_id`: Unique identifier for the nation
- `name`: Name of the nation
- `description`: Description of the nation
- `founder_id`: Identity ID of the nation's founder
- `metadata`: Additional metadata (flag, anthem, motto, etc.)
- `status`: Status of the nation (active, inactive, etc.)
- `created_at`: Timestamp when the nation was created
- `updated_at`: Timestamp when the nation was last updated

## Nation-Identity Relationship

- Each Identity belongs to one Nation (or none)
- A Nation can have many Identities
- The `nation_id` field in the Identity model references the Nation

## API Endpoints

The Nation layer provides the following API endpoints:

### Create a Nation

```
POST /nation/create
```

Request body:
```json
{
    "name": "Nation Name",
    "description": "Nation Description",
    "founder_id": "identity_id",
    "metadata": {
        "flag": "🏁",
        "motto": "Nation Motto",
        "anthem": "Nation Anthem"
    }
}
```

### Get Nation Information

```
GET /nation/get/<nation_id>
```

### List Nations

```
GET /nation/list?limit=100&offset=0
```

### Update Nation Information

```
PUT /nation/update/<nation_id>
```

Request body:
```json
{
    "name": "Updated Nation Name",
    "description": "Updated Nation Description",
    "metadata": {
        "flag": "🏁",
        "motto": "Updated Nation Motto",
        "anthem": "Updated Nation Anthem"
    },
    "status": "active"
}
```

### Get Nation Members

```
GET /nation/members/<nation_id>?limit=100&offset=0
```

### Join a Nation

```
POST /nation/join/<nation_id>
```

Request body:
```json
{
    "identity_id": "identity_id"
}
```

### Leave a Nation

```
POST /nation/leave
```

Request body:
```json
{
    "identity_id": "identity_id"
}
```

## Database Schema

The Nation layer uses the following database tables:

### Nations Table

```sql
CREATE TABLE IF NOT EXISTS nations (
    nation_id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    founder_id TEXT NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with nation metadata
    status TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (founder_id) REFERENCES identities (identity_id)
);
```

### Identities Table (Updated)

```sql
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    public_key TEXT NOT NULL,
    nation_id TEXT,
    metadata TEXT NOT NULL,  -- JSON object with identity metadata
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (nation_id) REFERENCES nations (nation_id)
);
```

## Implementation Details

The Nation layer is implemented in the following files:

- `models/nation.py`: The Nation model
- `api/nation_routes.py`: The Nation API routes

### Nation Model

The Nation model provides the following methods:

- `get_by_id(nation_id)`: Get a nation by ID
- `get_by_name(name)`: Get a nation by name
- `get_members(limit, offset)`: Get the identities that are members of this nation
- `get_member_count()`: Get the number of identities that are members of this nation
- `get_total_etzem_score()`: Get the total Etzem score of all identities in this nation
- `save()`: Save the nation
- `create(name, description, founder_id, metadata)`: Create a new nation

### Identity Model (Updated)

The Identity model has been updated to include the following:

- `nation_id` field: The ID of the nation this identity belongs to
- `get_nation()`: Get the nation this identity belongs to
- `get_by_nation(nation_id, limit, offset)`: Get identities by nation

## Future Enhancements

Potential future enhancements for the Nation layer include:

- **Nation Governance**: Allow nations to have their own governance structures and decision-making processes
- **Nation Economy**: Enable nations to have their own economic systems and currencies
- **Nation Alliances**: Allow nations to form alliances and cooperate with each other
- **Nation Territories**: Enable nations to claim and manage virtual territories
- **Nation Events**: Allow nations to organize and participate in events
- **Nation Achievements**: Enable nations to earn achievements and badges
- **Nation Leaderboards**: Create leaderboards for nations based on various metrics
- **Nation Diplomacy**: Enable nations to engage in diplomacy and trade with each other
