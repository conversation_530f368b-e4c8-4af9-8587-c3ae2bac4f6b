{% extends "base.html" %}

{% block title %}Onnyx Economic Structure - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Onnyx Economic Structure</h2>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Introduction to Onnyx</h3>
        </div>
        <div class="card-content">
            <p>Onnyx is an identity-centric blockchain where identity, ownership, and contribution are encoded on-chain and protected by mathematics, not governments. Unlike traditional blockchains that focus primarily on tokens and transactions, Onnyx places identities at the center of its ecosystem.</p>
            <p>The Onnyx economic structure is built on five key pillars that work together to create a sustainable, fair, and trust-based economy.</p>
        </div>
    </div>

    <div class="economic-pillars">
        <div class="pillar-card" id="zeman">
            <div class="pillar-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h3>Zeman System</h3>
            <p>Time/work credit system that tracks hours of service</p>
            <a href="#zeman-details" class="btn-primary">Learn More</a>
        </div>

        <div class="pillar-card" id="sela">
            <div class="pillar-icon">
                <i class="fas fa-building"></i>
            </div>
            <h3>Sela Registry</h3>
            <p>Business registry system requiring ONX token stakes</p>
            <a href="#sela-details" class="btn-primary">Learn More</a>
        </div>

        <div class="pillar-card" id="yovel">
            <div class="pillar-icon">
                <i class="fas fa-balance-scale"></i>
            </div>
            <h3>Yovel Limiter</h3>
            <p>Token mint limiter based on reputation and trust</p>
            <a href="#yovel-details" class="btn-primary">Learn More</a>
        </div>

        <div class="pillar-card" id="etzem">
            <div class="pillar-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3>Etzem Trust</h3>
            <p>Trust score system for on-chain integrity</p>
            <a href="#etzem-details" class="btn-primary">Learn More</a>
        </div>

        <div class="pillar-card" id="voice">
            <div class="pillar-icon">
                <i class="fas fa-scroll"></i>
            </div>
            <h3>Voice Scrolls</h3>
            <p>Governance system with weighted voting</p>
            <a href="#voice-details" class="btn-primary">Learn More</a>
        </div>
    </div>

    <div class="detail-section" id="zeman-details">
        <h3 class="detail-section-title">Zeman System</h3>
        <div class="card">
            <div class="card-content">
                <p>The Zeman system is a time/work credit system that tracks hours of service contributed to the Onnyx ecosystem. These credits can be converted into various benefits:</p>

                <ul class="feature-list">
                    <li>
                        <span class="feature-icon"><i class="fas fa-hammer"></i></span>
                        <div class="feature-content">
                            <h4>Minting Power</h4>
                            <p>Zeman hours directly influence how many tokens an identity can mint, ensuring that those who contribute more to the ecosystem have greater creation capabilities.</p>
                        </div>
                    </li>

                    <li>
                        <span class="feature-icon"><i class="fas fa-percentage"></i></span>
                        <div class="feature-content">
                            <h4>Fee Discounts</h4>
                            <p>Accumulated Zeman hours provide discounts on transaction fees, rewarding active participants with lower costs.</p>
                        </div>
                    </li>

                    <li>
                        <span class="feature-icon"><i class="fas fa-chart-line"></i></span>
                        <div class="feature-content">
                            <h4>Reputation Boosts</h4>
                            <p>Consistent contribution of time and work enhances an identity's Etzem trust score, increasing their standing in the community.</p>
                        </div>
                    </li>

                    <li>
                        <span class="feature-icon"><i class="fas fa-exchange-alt"></i></span>
                        <div class="feature-content">
                            <h4>Redeemable Value</h4>
                            <p>Zeman hours can be exchanged for ONX tokens or other valuable assets within the ecosystem, providing tangible rewards for contribution.</p>
                        </div>
                    </li>
                </ul>

                <div class="info-box">
                    <p><strong>How Zeman Hours Are Earned:</strong> Participants earn Zeman hours by providing services, contributing to development, participating in governance, or performing other valuable activities for the Onnyx ecosystem.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-section" id="sela-details">
        <h3 class="detail-section-title">Sela Registry</h3>
        <div class="card">
            <div class="card-content">
                <p>The Sela Registry is a business registry system that requires ONX token stakes to register entities on the blockchain. This creates a barrier to entry that ensures commitment and reduces spam.</p>

                <div class="tier-table">
                    <div class="tier-header">
                        <h4>Sela Tiers</h4>
                        <p>Different tiers based on identity reputation scores</p>
                    </div>

                    <div class="tier-grid">
                        <div class="tier-card">
                            <div class="tier-name">Bronze</div>
                            <div class="tier-stake">100 ONX</div>
                            <div class="tier-requirement">Etzem Score: 10+</div>
                            <ul class="tier-benefits">
                                <li>Basic business registration</li>
                                <li>Simple token creation</li>
                                <li>Standard transaction limits</li>
                            </ul>
                        </div>

                        <div class="tier-card">
                            <div class="tier-name">Silver</div>
                            <div class="tier-stake">500 ONX</div>
                            <div class="tier-requirement">Etzem Score: 25+</div>
                            <ul class="tier-benefits">
                                <li>Enhanced business features</li>
                                <li>Increased token creation limits</li>
                                <li>Reduced transaction fees</li>
                                <li>Basic governance participation</li>
                            </ul>
                        </div>

                        <div class="tier-card">
                            <div class="tier-name">Gold</div>
                            <div class="tier-stake">1,000 ONX</div>
                            <div class="tier-requirement">Etzem Score: 50+</div>
                            <ul class="tier-benefits">
                                <li>Premium business features</li>
                                <li>High token creation limits</li>
                                <li>Significantly reduced fees</li>
                                <li>Enhanced governance weight</li>
                                <li>Ability to sponsor other identities</li>
                            </ul>
                        </div>

                        <div class="tier-card">
                            <div class="tier-name">Platinum</div>
                            <div class="tier-stake">5,000 ONX</div>
                            <div class="tier-requirement">Etzem Score: 75+</div>
                            <ul class="tier-benefits">
                                <li>All Gold benefits</li>
                                <li>Maximum token creation limits</li>
                                <li>Council eligibility</li>
                                <li>Ability to create special token types</li>
                                <li>Priority transaction processing</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="info-box">
                    <p><strong>Benefits of Sela Registration:</strong> Registered Selas gain enhanced reputation, the ability to mint tokens, participate in governance, and access special features of the Onnyx ecosystem.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-section" id="yovel-details">
        <h3 class="detail-section-title">Yovel Limiter</h3>
        <div class="card">
            <div class="card-content">
                <p>The Yovel Limiter is a token mint control system that caps how many tokens an identity can mint based on their reputation, contribution, and trust level within the ecosystem.</p>

                <div class="formula-box">
                    <h4>Minting Limit Formula</h4>
                    <div class="formula">
                        <span class="formula-text">Mint Limit = (Zeman Hours × Multiplier) + (Etzem Score × Weight) + Base Allowance</span>
                    </div>
                    <p>This formula ensures that minting power is earned through contribution and trust, not just capital.</p>
                </div>

                <ul class="feature-list">
                    <li>
                        <span class="feature-icon"><i class="fas fa-user-clock"></i></span>
                        <div class="feature-content">
                            <h4>Zeman-Based Limits</h4>
                            <p>Time and labor contributed to the ecosystem directly increases minting capacity, rewarding active participants.</p>
                        </div>
                    </li>

                    <li>
                        <span class="feature-icon"><i class="fas fa-medal"></i></span>
                        <div class="feature-content">
                            <h4>Reputation-Based Limits</h4>
                            <p>Higher Etzem scores grant increased minting privileges, incentivizing trustworthy behavior.</p>
                        </div>
                    </li>

                    <li>
                        <span class="feature-icon"><i class="fas fa-history"></i></span>
                        <div class="feature-content">
                            <h4>Time-Based Recovery</h4>
                            <p>Minting limits regenerate over time, allowing consistent but controlled token creation.</p>
                        </div>
                    </li>

                    <li>
                        <span class="feature-icon"><i class="fas fa-building"></i></span>
                        <div class="feature-content">
                            <h4>Sela Requirements</h4>
                            <p>Higher-tier token minting requires Sela registration, ensuring business accountability.</p>
                        </div>
                    </li>
                </ul>

                <div class="info-box">
                    <p><strong>Purpose of Yovel:</strong> The Yovel system prevents token spam and ensures that new tokens are created by trusted identities with proven track records, maintaining the quality and integrity of the Onnyx token ecosystem.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-section" id="etzem-details">
        <h3 class="detail-section-title">Etzem Trust Score</h3>
        <div class="card">
            <div class="card-content">
                <p>The Etzem Trust Score is a comprehensive on-chain integrity rating that measures an identity's trustworthiness and contribution to the Onnyx ecosystem.</p>

                <div class="score-components">
                    <div class="component-card">
                        <div class="component-icon"><i class="fas fa-clock"></i></div>
                        <h4>Zeman Hours</h4>
                        <p>Time and labor contributed to the ecosystem</p>
                    </div>

                    <div class="component-card">
                        <div class="component-icon"><i class="fas fa-award"></i></div>
                        <h4>Reputation Badges</h4>
                        <p>Achievements and recognitions earned</p>
                    </div>

                    <div class="component-card">
                        <div class="component-icon"><i class="fas fa-building"></i></div>
                        <h4>Sela Registration</h4>
                        <p>Business registration status and tier</p>
                    </div>

                    <div class="component-card">
                        <div class="component-icon"><i class="fas fa-coins"></i></div>
                        <h4>Token Activity</h4>
                        <p>Quality and usage of created tokens</p>
                    </div>

                    <div class="component-card">
                        <div class="component-icon"><i class="fas fa-vote-yea"></i></div>
                        <h4>Governance Participation</h4>
                        <p>Involvement in Voice Scrolls and voting</p>
                    </div>

                    <div class="component-card">
                        <div class="component-icon"><i class="fas fa-history"></i></div>
                        <h4>Account Longevity</h4>
                        <p>Time active in the Onnyx ecosystem</p>
                    </div>
                </div>

                <div class="score-ranges">
                    <h4>Etzem Score Ranges</h4>
                    <div class="range-grid">
                        <div class="range-item">
                            <div class="range-label">0-25</div>
                            <div class="range-bar" style="--fill: 25%; --color: var(--error);"></div>
                            <div class="range-description">New or Unproven</div>
                        </div>

                        <div class="range-item">
                            <div class="range-label">26-50</div>
                            <div class="range-bar" style="--fill: 50%; --color: var(--warning);"></div>
                            <div class="range-description">Established</div>
                        </div>

                        <div class="range-item">
                            <div class="range-label">51-75</div>
                            <div class="range-bar" style="--fill: 75%; --color: var(--info);"></div>
                            <div class="range-description">Trusted</div>
                        </div>

                        <div class="range-item">
                            <div class="range-label">76-100</div>
                            <div class="range-bar" style="--fill: 100%; --color: var(--success);"></div>
                            <div class="range-description">Exemplary</div>
                        </div>
                    </div>
                </div>

                <div class="info-box">
                    <p><strong>Benefits of High Etzem Score:</strong> Higher Etzem scores unlock increased minting limits, reduced fees, governance weight, Sela tier eligibility, and access to special features within the Onnyx ecosystem.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-section" id="voice-details">
        <h3 class="detail-section-title">Voice Scrolls Governance</h3>
        <div class="card">
            <div class="card-content">
                <p>The Voice Scrolls governance system enables decentralized decision-making in the Onnyx ecosystem, with voting power weighted by Etzem score rather than token holdings.</p>

                <div class="governance-structure">
                    <div class="structure-item">
                        <h4><i class="fas fa-users"></i> Council of Twelve Tribes</h4>
                        <p>The Council consists of 12 elected representatives, each representing a different "tribe" or aspect of the ecosystem. Council members have special voting powers and can propose high-priority Voice Scrolls.</p>
                    </div>

                    <div class="structure-item">
                        <h4><i class="fas fa-scroll"></i> Voice Scrolls</h4>
                        <p>Voice Scrolls are governance proposals that can be submitted by any identity with sufficient Etzem score. They can propose parameter changes, feature requests, economic policy updates, or other governance actions.</p>
                    </div>

                    <div class="structure-item">
                        <h4><i class="fas fa-balance-scale"></i> Etzem-Weighted Voting</h4>
                        <p>Voting power is determined by Etzem score rather than token holdings, ensuring that those who have contributed most to the ecosystem have the greatest say in its governance.</p>
                    </div>
                </div>

                <div class="process-flow">
                    <h4>Governance Process Flow</h4>
                    <div class="flow-steps">
                        <div class="flow-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h5>Proposal</h5>
                                <p>An identity submits a Voice Scroll with a detailed proposal.</p>
                            </div>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h5>Discussion</h5>
                                <p>Community discusses and debates the proposal.</p>
                            </div>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h5>Voting</h5>
                                <p>Identities cast votes weighted by their Etzem score.</p>
                            </div>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h5>Resolution</h5>
                                <p>Proposal is accepted or rejected based on voting outcome.</p>
                            </div>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h5>Implementation</h5>
                                <p>Accepted proposals are implemented by the development team.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="info-box">
                    <p><strong>Key Advantage:</strong> By weighting votes by Etzem score rather than token holdings, the Voice Scrolls system ensures that governance is controlled by those who have contributed most to the ecosystem, not just those with the most capital.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .economic-pillars {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .pillar-card {
        background-color: var(--bg-card);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        transition: var(--transition);
        border: 1px solid rgba(255, 255, 255, 0.03);
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .pillar-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        border-color: rgba(179, 136, 255, 0.1);
    }

    .pillar-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--accent-primary);
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(98, 0, 234, 0.1);
        margin-bottom: 1rem;
    }

    .pillar-card h3 {
        margin-bottom: 0.5rem;
        font-size: 1.25rem;
    }

    .pillar-card p {
        color: var(--text-secondary);
        margin-bottom: 1.5rem;
    }

    .pillar-card .btn-primary {
        margin-top: auto;
    }

    .feature-list {
        list-style: none;
        margin: 1.5rem 0;
    }

    .feature-list li {
        display: flex;
        margin-bottom: 1.5rem;
        background-color: var(--bg-tertiary);
        padding: 1rem;
        border-radius: var(--border-radius);
    }

    .feature-icon {
        font-size: 1.5rem;
        color: var(--accent-secondary);
        margin-right: 1rem;
        display: flex;
        align-items: center;
    }

    .feature-content h4 {
        margin-bottom: 0.5rem;
        color: var(--accent-secondary);
    }

    .info-box {
        background-color: rgba(98, 0, 234, 0.1);
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-top: 1.5rem;
        border-left: 4px solid var(--accent-primary);
    }

    .tier-table {
        margin: 1.5rem 0;
    }

    .tier-header {
        margin-bottom: 1rem;
    }

    .tier-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .tier-card {
        background-color: var(--bg-tertiary);
        border-radius: var(--border-radius);
        padding: 1rem;
        transition: var(--transition);
    }

    .tier-card:hover {
        transform: translateY(-5px);
    }

    .tier-name {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--accent-secondary);
    }

    .tier-stake {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .tier-requirement {
        color: var(--text-secondary);
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }

    .tier-benefits {
        list-style: none;
    }

    .tier-benefits li {
        margin-bottom: 0.5rem;
        position: relative;
        padding-left: 1.5rem;
    }

    .tier-benefits li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--success);
    }

    /* Formula Box */
    .formula-box {
        background-color: var(--bg-tertiary);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        margin: 1.5rem 0;
        text-align: center;
    }

    .formula {
        margin: 1rem 0;
        padding: 1rem;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
        display: inline-block;
    }

    .formula-text {
        font-family: monospace;
        font-size: 1.1rem;
        color: var(--accent-secondary);
    }

    /* Score Components */
    .score-components {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .component-card {
        background-color: var(--bg-tertiary);
        border-radius: var(--border-radius);
        padding: 1rem;
        text-align: center;
        transition: var(--transition);
    }

    .component-card:hover {
        transform: translateY(-5px);
    }

    .component-icon {
        font-size: 2rem;
        color: var(--accent-secondary);
        margin-bottom: 0.75rem;
    }

    .component-card h4 {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .component-card p {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    /* Score Ranges */
    .score-ranges {
        margin: 2rem 0;
    }

    .score-ranges h4 {
        margin-bottom: 1rem;
    }

    .range-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .range-item {
        margin-bottom: 1rem;
    }

    .range-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .range-bar {
        height: 8px;
        background-color: var(--bg-tertiary);
        border-radius: 4px;
        margin-bottom: 0.5rem;
        position: relative;
        overflow: hidden;
    }

    .range-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: var(--fill);
        background-color: var(--color);
        border-radius: 4px;
    }

    .range-description {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    /* Governance Structure */
    .governance-structure {
        margin: 1.5rem 0;
    }

    .structure-item {
        background-color: var(--bg-tertiary);
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .structure-item h4 {
        margin-bottom: 0.5rem;
        color: var(--accent-secondary);
    }

    .structure-item h4 i {
        margin-right: 0.5rem;
    }

    /* Process Flow */
    .process-flow {
        margin: 2rem 0;
    }

    .process-flow h4 {
        margin-bottom: 1rem;
    }

    .flow-steps {
        position: relative;
    }

    .flow-steps::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 15px;
        width: 2px;
        background-color: var(--bg-tertiary);
    }

    .flow-step {
        display: flex;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .step-number {
        width: 30px;
        height: 30px;
        background-color: var(--accent-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 1rem;
        position: relative;
        z-index: 1;
    }

    .step-content {
        flex: 1;
    }

    .step-content h5 {
        margin-bottom: 0.25rem;
        color: var(--text-primary);
    }

    .step-content p {
        color: var(--text-secondary);
    }

    /* Detail Section */
    .detail-section {
        margin-top: 3rem;
        scroll-margin-top: 100px;
    }

    .detail-section-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: var(--text-primary);
        position: relative;
        padding-left: 1rem;
    }

    .detail-section-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 1.5rem;
        background: var(--gradient-primary);
        border-radius: 2px;
    }
</style>
{% endblock %}
