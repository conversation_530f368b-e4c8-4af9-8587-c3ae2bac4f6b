"""
Initialize Database for Tests

This script initializes the database with the necessary tables and sample data for testing.
"""

import os
import sys
import json
import time
import sqlite3
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("onnyx.init_db_for_tests")

def create_db_path():
    """Create the database path."""
    # Create the data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    os.makedirs(data_dir, exist_ok=True)

    # Create the db directory if it doesn't exist
    db_dir = os.path.join(data_dir, "db")
    os.makedirs(db_dir, exist_ok=True)

    # Set the database path
    db_path = os.path.join(db_dir, "onnyx.db")

    logger.info(f"Database path: {db_path}")

    return db_path

def create_tables(db_path):
    """Create the tables in the database."""
    # Read the schema file
    schema_path = os.path.join(os.path.dirname(__file__), "schemas", "schema.sql")

    if not os.path.exists(schema_path):
        logger.error(f"Schema file not found: {schema_path}")
        return False

    with open(schema_path, "r") as f:
        schema = f.read()

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Execute the schema
    try:
        cursor.executescript(schema)
        conn.commit()
        logger.info("Tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        return False
    finally:
        conn.close()

def create_sample_data(db_path):
    """Create sample data in the database."""
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Create genesis identity
        genesis_id = "genesis"
        cursor.execute(
            "INSERT OR REPLACE INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                genesis_id,
                "Genesis Identity",
                "genesis_public_key",
                json.dumps({
                    "type": "system",
                    "description": "Genesis identity for the Onnyx blockchain"
                }),
                0,
                0
            )
        )

        # Create test identity
        test_id = "test_identity"
        cursor.execute(
            "INSERT OR REPLACE INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                test_id,
                "Test Identity",
                "test_public_key",
                json.dumps({
                    "type": "individual",
                    "description": "Test identity"
                }),
                int(time.time()),
                int(time.time())
            )
        )

        # Create ONX token
        onx_id = "ONX"
        cursor.execute(
            "INSERT OR REPLACE INTO tokens (token_id, name, symbol, creator_id, supply, category, decimals, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                onx_id,
                "Onnyx",
                "ONX",
                genesis_id,
                1000000,
                "system",
                18,
                0,
                json.dumps({
                    "description": "Native token of the Onnyx blockchain"
                })
            )
        )

        # Create test token
        test_token_id = "TEST"
        cursor.execute(
            "INSERT OR REPLACE INTO tokens (token_id, name, symbol, creator_id, supply, category, decimals, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                test_token_id,
                "Test Token",
                "TEST",
                test_id,
                1000,
                "test",
                2,
                int(time.time()),
                json.dumps({
                    "description": "Test token"
                })
            )
        )

        # Create genesis block
        genesis_block_hash = "genesis_block_hash"
        cursor.execute(
            "INSERT OR REPLACE INTO blocks (block_hash, block_height, previous_hash, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                genesis_block_hash,
                0,
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                1,
                0,
                genesis_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                0
            )
        )

        # Create genesis transaction
        genesis_tx_id = "genesis_tx"
        cursor.execute(
            "INSERT OR REPLACE INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                genesis_tx_id,
                genesis_block_hash,
                0,
                "OP_GENESIS",
                json.dumps({
                    "token_id": onx_id,
                    "amount": 1000000,
                    "recipient": genesis_id,
                    "description": "Genesis transaction for the Onnyx blockchain"
                }),
                genesis_id,
                "genesis_signature",
                "CONFIRMED",
                0
            )
        )

        # Create test block
        test_block_hash = "test_block_hash"
        cursor.execute(
            "INSERT OR REPLACE INTO blocks (block_hash, block_height, previous_hash, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                test_block_hash,
                1,
                genesis_block_hash,
                int(time.time()),
                4,
                12345,
                test_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                int(time.time())
            )
        )

        # Create test transaction
        test_tx_id = "test_tx"
        cursor.execute(
            "INSERT OR REPLACE INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                test_tx_id,
                test_block_hash,
                int(time.time()),
                "OP_SEND",
                json.dumps({
                    "token_id": onx_id,
                    "amount": 100,
                    "recipient": test_id,
                    "description": "Test transaction"
                }),
                genesis_id,
                "test_signature",
                "CONFIRMED",
                int(time.time())
            )
        )

        # Create test Sela
        test_sela_id = "test_sela"
        cursor.execute(
            "INSERT OR REPLACE INTO selas (sela_id, identity_id, name, category, stake_amount, stake_token_id, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                test_sela_id,
                test_id,
                "Test Sela",
                "BUSINESS",
                1000,
                onx_id,
                int(time.time()),
                json.dumps({
                    "description": "Test Sela",
                    "services": ["Test Service"],
                    "roles": {test_id: "OWNER"},
                    "members": [test_id]
                })
            )
        )

        # Create test Etzem score
        cursor.execute(
            "INSERT OR REPLACE INTO etzem_scores (identity_id, composite_score, zeman_score, reputation_score, sela_score, token_activity_score, governance_score, longevity_score, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                test_id,
                100,
                20,
                30,
                10,
                15,
                15,
                10,
                int(time.time())
            )
        )

        # Create test activity
        cursor.execute(
            "INSERT OR REPLACE INTO activity_ledger (id, identity_id, activity_type, data, timestamp) VALUES (?, ?, ?, ?, ?)",
            (
                1,
                test_id,
                "TEST",
                json.dumps({
                    "description": "Test activity",
                    "sela_id": test_sela_id,
                    "key": "value"
                }),
                int(time.time())
            )
        )

        # Create test Zeman credits
        cursor.execute(
            "INSERT OR REPLACE INTO zeman_credits (id, identity_id, hours, description, issuer_id, timestamp, tx_id) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (
                1,
                test_id,
                2.5,
                "Test service",
                genesis_id,
                int(time.time()),
                test_tx_id
            )
        )

        # Create test Voice Scroll
        test_scroll_id = "test_scroll"
        cursor.execute(
            "INSERT OR REPLACE INTO voice_scrolls (scroll_id, title, description, proposer_id, status, proposal_type, params, voting_start, voting_end, implementation_time, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                test_scroll_id,
                "Test Scroll",
                "Test Voice Scroll",
                test_id,
                "VOTING",
                "PARAMETER_CHANGE",
                json.dumps({
                    "param": "test_param",
                    "value": "test_value"
                }),
                int(time.time()),
                int(time.time()) + 7 * 24 * 60 * 60,
                None,
                int(time.time()),
                int(time.time())
            )
        )

        # Create test vote
        cursor.execute(
            "INSERT OR REPLACE INTO scroll_votes (id, scroll_id, voter_id, vote, weight, timestamp, tx_id) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (
                1,
                test_scroll_id,
                test_id,
                "YES",
                100,
                int(time.time()),
                test_tx_id
            )
        )

        # Create test chain parameter
        cursor.execute(
            "INSERT OR REPLACE INTO chain_parameters (key, value, default_value, description, category, last_updated, last_updated_by, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "test_param",
                "100",
                "50",
                "Test parameter",
                "test",
                int(time.time()),
                test_id,
                json.dumps({})
            )
        )

        # Create test rotation
        cursor.execute(
            "INSERT OR REPLACE INTO rotation (id, current_validator, queue, update_interval, min_etzem_score, required_badges, last_updated) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (
                "test_rotation",
                test_sela_id,
                json.dumps([test_sela_id]),
                3600,
                100,
                json.dumps(["VALIDATOR"]),
                int(time.time())
            )
        )

        # Commit the changes
        conn.commit()
        logger.info("Sample data created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        return False
    finally:
        conn.close()

def main():
    """Initialize the database for tests."""
    # Create the database path
    db_path = create_db_path()

    # Create the tables
    if not create_tables(db_path):
        logger.error("Failed to create tables")
        return

    # Create sample data
    if not create_sample_data(db_path):
        logger.error("Failed to create sample data")
        return

    logger.info("Database initialized successfully")

if __name__ == "__main__":
    main()
