/**
 * Auto-refresh functionality for Onnyx Transaction Viewer
 */

// Auto-refresh configuration
const DEFAULT_REFRESH_INTERVAL = 30; // seconds
const MIN_REFRESH_INTERVAL = 5; // seconds
const MAX_REFRESH_INTERVAL = 300; // seconds

// Initialize auto-refresh
document.addEventListener('DOMContentLoaded', function() {
    // Get the auto-refresh controls
    const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
    const autoRefreshInterval = document.getElementById('auto-refresh-interval');
    const autoRefreshStatus = document.getElementById('auto-refresh-status');
    const autoRefreshCountdown = document.getElementById('auto-refresh-countdown');
    
    if (!autoRefreshToggle || !autoRefreshInterval || !autoRefreshStatus || !autoRefreshCountdown) {
        console.warn('Auto-refresh controls not found');
        return;
    }
    
    // Initialize state
    let isAutoRefreshEnabled = localStorage.getItem('autoRefreshEnabled') === 'true';
    let refreshInterval = parseInt(localStorage.getItem('refreshInterval')) || DEFAULT_REFRESH_INTERVAL;
    let countdownTimer = null;
    let remainingTime = refreshInterval;
    
    // Update UI based on state
    autoRefreshToggle.checked = isAutoRefreshEnabled;
    autoRefreshInterval.value = refreshInterval;
    updateAutoRefreshStatus();
    
    // Start auto-refresh if enabled
    if (isAutoRefreshEnabled) {
        startCountdown();
    }
    
    // Toggle auto-refresh
    autoRefreshToggle.addEventListener('change', function() {
        isAutoRefreshEnabled = autoRefreshToggle.checked;
        localStorage.setItem('autoRefreshEnabled', isAutoRefreshEnabled);
        
        if (isAutoRefreshEnabled) {
            startCountdown();
        } else {
            stopCountdown();
        }
        
        updateAutoRefreshStatus();
    });
    
    // Update refresh interval
    autoRefreshInterval.addEventListener('change', function() {
        refreshInterval = parseInt(autoRefreshInterval.value) || DEFAULT_REFRESH_INTERVAL;
        
        // Ensure interval is within bounds
        refreshInterval = Math.max(MIN_REFRESH_INTERVAL, Math.min(refreshInterval, MAX_REFRESH_INTERVAL));
        
        autoRefreshInterval.value = refreshInterval;
        localStorage.setItem('refreshInterval', refreshInterval);
        
        // Restart countdown if auto-refresh is enabled
        if (isAutoRefreshEnabled) {
            stopCountdown();
            startCountdown();
        }
        
        updateAutoRefreshStatus();
    });
    
    // Start countdown timer
    function startCountdown() {
        remainingTime = refreshInterval;
        updateCountdown();
        
        countdownTimer = setInterval(function() {
            remainingTime--;
            
            if (remainingTime <= 0) {
                // Refresh the page
                window.location.reload();
            } else {
                updateCountdown();
            }
        }, 1000);
    }
    
    // Stop countdown timer
    function stopCountdown() {
        if (countdownTimer) {
            clearInterval(countdownTimer);
            countdownTimer = null;
        }
        
        remainingTime = refreshInterval;
        updateCountdown();
    }
    
    // Update countdown display
    function updateCountdown() {
        if (isAutoRefreshEnabled) {
            autoRefreshCountdown.textContent = `${remainingTime}s`;
            autoRefreshCountdown.style.display = 'inline';
        } else {
            autoRefreshCountdown.style.display = 'none';
        }
    }
    
    // Update auto-refresh status display
    function updateAutoRefreshStatus() {
        if (isAutoRefreshEnabled) {
            autoRefreshStatus.textContent = `Auto-refresh every ${refreshInterval} seconds`;
            autoRefreshStatus.classList.add('enabled');
            autoRefreshStatus.classList.remove('disabled');
        } else {
            autoRefreshStatus.textContent = 'Auto-refresh disabled';
            autoRefreshStatus.classList.add('disabled');
            autoRefreshStatus.classList.remove('enabled');
        }
    }
    
    // Manual refresh button
    const manualRefreshButton = document.getElementById('manual-refresh');
    if (manualRefreshButton) {
        manualRefreshButton.addEventListener('click', function() {
            window.location.reload();
        });
    }
});
