<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Onnyx Platform{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="48x48" href="{{ url_for('static', filename='images/favicon-48x48.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <meta name="theme-color" content="#00fff7">

    <!-- Google Fonts - Orbitron & Montserrat -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS with custom config -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'orbitron': ['Orbitron', 'monospace'],
                        'montserrat': ['Montserrat', 'sans-serif'],
                    },
                    colors: {
                        'onyx': {
                            'black': '#1a1a1a',    // Brightened for better accessibility
                            'gray': '#2a2a2a',     // Improved contrast base
                            'light': '#3a3a3a',    // Lighter accent areas
                        },
                        'cyber': {
                            'cyan': '#00fff7',     // Enhanced with glow effects
                            'purple': '#9a00ff',   // Boosted vibrancy
                            'blue': '#0066ff',     // Increased visibility
                        },
                        'text': {
                            'primary': '#eeeeee',     // Brighter primary text
                            'secondary': '#d1d5db',   // Improved secondary text (gray-300)
                            'tertiary': '#9ca3af',    // Enhanced tertiary text (gray-400)
                            'muted': '#6b7280',       // Muted text (gray-500)
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}?v=compact-fix-2024">

    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- QR Code library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Alpine.js for interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    {% block head %}{% endblock %}
</head>
<body class="bg-onyx-black text-text-primary font-montserrat overflow-x-hidden"
      style="min-height: 100vh; display: flex; flex-direction: column; margin: 0; padding: 0;">
    <!-- Modern Navigation Bar -->
    <nav class="modern-nav fixed top-0 left-0 right-0 z-50">
        <div class="nav-container">
            <!-- Left Section: Logo -->
            <div class="nav-left">
                <a href="{{ url_for('index') }}" class="nav-logo-link">
                    <div class="nav-logo-container">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="nav-logo-img">
                    </div>
                    <span class="nav-logo-text">ONNYX</span>
                </a>
            </div>

            <!-- Center Section: Main Navigation -->
            <div class="nav-center">
                <div class="nav-links-container">
                    <a href="{{ url_for('index') }}" class="nav-link-modern">
                        <span class="nav-link-icon">🏠</span>
                        <span class="nav-link-text">Home</span>
                    </a>
                    <a href="{{ url_for('sela.directory') }}" class="nav-link-modern">
                        <span class="nav-link-icon">🏢</span>
                        <span class="nav-link-text">Validators</span>
                    </a>
                    <a href="{{ url_for('explorer.index') }}" class="nav-link-modern">
                        <span class="nav-link-icon">🔍</span>
                        <span class="nav-link-text">Explorer</span>
                    </a>
                    {% if current_user %}
                    <a href="{{ url_for('dashboard.overview') }}" class="nav-link-modern">
                        <span class="nav-link-icon">📊</span>
                        <span class="nav-link-text">Dashboard</span>
                    </a>
                    <a href="{{ url_for('auto_mining.dashboard') }}" class="nav-link-modern">
                        <span class="nav-link-icon">⚡</span>
                        <span class="nav-link-text">Auto-Mining</span>
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Right Section: User Menu / Auth Buttons -->
            <div class="nav-right">
                {% if current_user %}
                    <!-- Modern User Profile Button -->
                    <div class="user-profile-section" x-data="{ open: false }" id="user-dropdown">
                        <button @click="open = !open"
                                id="user-dropdown-button"
                                class="user-profile-button"
                                aria-expanded="false"
                                aria-haspopup="true">
                            <div class="user-avatar">
                                <span class="user-initial">{{ current_user.name[0].upper() }}</span>
                            </div>
                            <div class="user-info">
                                <span class="user-name">{{ current_user.name }}</span>
                                <span class="user-status">Online</span>
                            </div>
                            <svg class="dropdown-arrow" :class="open ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Modern Dropdown Menu -->
                        <div x-show="open"
                             @click.away="open = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             id="user-dropdown-menu"
                             class="user-dropdown-menu"
                             style="display: none;">
                            <div class="dropdown-header">
                                <div class="dropdown-user-info">
                                    <div class="dropdown-avatar">
                                        <span>{{ current_user.name[0].upper() }}</span>
                                    </div>
                                    <div class="dropdown-details">
                                        <span class="dropdown-name">{{ current_user.name }}</span>
                                        <span class="dropdown-email">{{ current_user.email or 'No email' }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('dashboard.overview') }}" class="dropdown-item">
                                <span class="dropdown-icon">📊</span>
                                <span>Dashboard</span>
                            </a>
                            <a href="{{ url_for('dashboard.identity_details') }}" class="dropdown-item">
                                <span class="dropdown-icon">👤</span>
                                <span>My Identity</span>
                            </a>
                            <a href="{{ url_for('dashboard.selas') }}" class="dropdown-item">
                                <span class="dropdown-icon">🏢</span>
                                <span>My Validators</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('auth.logout') }}" class="dropdown-item logout-item">
                                <span class="dropdown-icon">🚪</span>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                {% else %}
                    <!-- Modern Guest Buttons -->
                    <div class="guest-buttons">
                        <a href="{{ url_for('auth.login') }}" class="guest-button secondary">
                            <span class="button-icon">🔑</span>
                            <span>Access Portal</span>
                        </a>
                        <a href="{{ url_for('register_choice') }}" class="guest-button primary">
                            <span class="button-icon">✨</span>
                            <span>Verify Identity</span>
                        </a>
                    </div>
                {% endif %}
            </div>

            <!-- Mobile Menu Toggle -->
            <div class="mobile-menu-toggle" x-data="{ mobileOpen: false }" @click.away="mobileOpen = false">
                <button @click="mobileOpen = !mobileOpen" class="mobile-toggle-btn">
                    <div class="hamburger" :class="{ 'active': mobileOpen }">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>

                <!-- Mobile Menu Overlay -->
                <div class="mobile-menu-overlay md:hidden" x-show="mobileOpen"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="transition ease-in duration-300"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     style="display: none;">
                    <div class="mobile-menu-items">
                        <a href="{{ url_for('index') }}" class="mobile-menu-item" @click="mobileOpen = false">
                            🏠 Home
                        </a>
                        <a href="{{ url_for('sela.directory') }}" class="mobile-menu-item" @click="mobileOpen = false">
                            🏢 Validators
                        </a>
                        <a href="{{ url_for('explorer.index') }}" class="mobile-menu-item" @click="mobileOpen = false">
                            🔍 Explorer
                        </a>
                        {% if current_user %}
                        <a href="{{ url_for('dashboard.overview') }}" class="mobile-menu-item" @click="mobileOpen = false">
                            📊 Dashboard
                        </a>
                        <a href="{{ url_for('auto_mining.dashboard') }}" class="mobile-menu-item" @click="mobileOpen = false">
                            ⚡ Auto-Mining
                        </a>
                        <div class="mobile-menu-divider"></div>
                        <a href="{{ url_for('auth.logout') }}" class="mobile-menu-item logout" @click="mobileOpen = false">
                            🚪 Logout
                        </a>
                        {% else %}
                        <div class="mobile-menu-divider"></div>
                        <a href="{{ url_for('auth.login') }}" class="mobile-menu-item" @click="mobileOpen = false">
                            🔑 Access Portal
                        </a>
                        <a href="{{ url_for('register_choice') }}" class="mobile-menu-item primary" @click="mobileOpen = false">
                            ✨ Verify Identity
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
    </nav>

    <!-- Main Content Wrapper - Flex container that expands to push footer down -->
    <div style="flex: 1 0 auto; display: flex; flex-direction: column; min-height: calc(100vh - 64px);">
        <!-- Content area with top padding for fixed nav -->
        <main style="flex: 1 0 auto; padding-top: 4rem; padding-bottom: 4rem; width: 100%; position: relative;">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                    {% for category, message in messages %}
                    <div class="glass-card mb-4 p-4 rounded-xl border {% if category == 'error' %}border-red-500/30 bg-red-500/10 text-red-300{% elif category == 'success' %}border-green-500/30 bg-green-500/10 text-green-300{% elif category == 'warning' %}border-yellow-500/30 bg-yellow-500/10 text-yellow-300{% else %}border-cyber-cyan/30 bg-cyber-cyan/10 text-cyber-cyan{% endif %} backdrop-blur-sm">
                        <div class="flex items-center space-x-3">
                            {% if category == 'error' %}
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'success' %}
                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'warning' %}
                            <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            {% else %}
                            <svg class="w-5 h-5 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% endif %}
                            <span class="font-medium">{{ message }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endwith %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Unified Footer - Consistent Across All Pages -->
    <footer class="footer-responsive" style="flex-shrink: 0; margin-top: auto; position: relative; width: 100%; z-index: 10;">
        <div class="footer-grid">
            <!-- ONNYX Platform Section -->
            <div class="footer-section">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-14 h-14 rounded-xl flex items-center justify-center shadow-lg shadow-cyber-cyan/30 bg-white/8 backdrop-blur-sm border border-white/15">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="w-12 h-12 object-contain"
                             style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                    </div>
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">ONNYX</h3>
                        <p class="text-sm text-gray-300 font-medium">Trustworthy Commerce</p>
                    </div>
                </div>
                <p class="text-gray-300 text-sm leading-relaxed mb-6">
                    The Digital Backbone of Trustworthy Commerce. Blockchain-powered verification platform enabling transparent business operations through cryptographic identity management and decentralized validation networks.
                </p>
                <div class="footer-network-status">
                    <div class="footer-network-indicator">
                        <div class="footer-network-dot"></div>
                        <span>Network Online</span>
                    </div>
                    <div class="footer-network-indicator">
                        <span class="font-mono text-xs">v1.0.0</span>
                    </div>
                </div>
            </div>

            <!-- Platform Navigation Section -->
            <div class="footer-section">
                <h3>Platform</h3>
                <ul class="space-y-1">
                    <li><a href="{{ url_for('sela.directory') }}" class="footer-link">
                        <span class="mr-3">🏢</span><span>Validator Network</span>
                    </a></li>
                    <li><a href="{{ url_for('explorer.index') }}" class="footer-link">
                        <span class="mr-3">🔍</span><span>Blockchain Explorer</span>
                    </a></li>
                    <li><a href="{{ url_for('register_choice') }}" class="footer-link">
                        <span class="mr-3">🔐</span><span>Identity Verification</span>
                    </a></li>
                    <li><a href="#" class="footer-link">
                        <span class="mr-3">⚡</span><span>Auto-Mining</span>
                    </a></li>
                </ul>
            </div>

            <!-- Network Statistics Section -->
            <div class="footer-section">
                <h3>Network Stats</h3>
                <ul class="space-y-1">
                    <li class="footer-stat-item">
                        <span class="stat-label">Verified Identities</span>
                        <span class="stat-value">{{ platform_stats.identities }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Active Validators</span>
                        <span class="stat-value">{{ platform_stats.selas }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Total Transactions</span>
                        <span class="stat-value">{{ platform_stats.transactions }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Blocks Secured</span>
                        <span class="stat-value">{{ platform_stats.blocks }}</span>
                    </li>
                </ul>
            </div>

            <!-- Technology Stack Section -->
            <div class="footer-section">
                <h3>Technology</h3>
                <ul class="space-y-1">
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-cyan"></div>
                        <span class="footer-tech-label">Quantum-Resistant Security</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-purple"></div>
                        <span class="footer-tech-label">Etzem Trust Protocol</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-blue"></div>
                        <span class="footer-tech-label">Mikvah Token Economy</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-green-400"></div>
                        <span class="footer-tech-label">Decentralized Validation</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p>&copy; 2024 ONNYX Platform. Securing the future of digital commerce.</p>
                <div class="flex items-center space-x-6 text-sm">
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>Network Online</span>
                    </span>
                    <span class="font-mono">v1.0.0</span>
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                        <span>Blockchain Active</span>
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <!-- Enhanced Scroll Effects -->
    <script src="{{ url_for('static', filename='js/scroll-effects.js') }}"></script>

    <!-- Navigation Dropdown Fallback Script -->
    <script>
        // Fallback dropdown functionality if Alpine.js fails
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownButton = document.getElementById('user-dropdown-button');
            const dropdownMenu = document.getElementById('user-dropdown-menu');

            if (dropdownButton && dropdownMenu) {
                // Fallback click handler
                dropdownButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle dropdown visibility
                    const isVisible = dropdownMenu.style.display !== 'none';
                    dropdownMenu.style.display = isVisible ? 'none' : 'block';

                    // Update aria-expanded
                    dropdownButton.setAttribute('aria-expanded', !isVisible);
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                        dropdownMenu.style.display = 'none';
                        dropdownButton.setAttribute('aria-expanded', 'false');
                    }
                });

                // Close dropdown on escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        dropdownMenu.style.display = 'none';
                        dropdownButton.setAttribute('aria-expanded', 'false');
                    }
                });
            }

            // Debug: Log navigation visibility
            const mainNav = document.querySelector('.hidden.md\\:ml-12.md\\:flex.md\\:space-x-1');
            if (mainNav) {
                console.log('Main navigation found:', mainNav);
                console.log('Main navigation computed style:', window.getComputedStyle(mainNav));
            } else {
                console.warn('Main navigation not found');
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
