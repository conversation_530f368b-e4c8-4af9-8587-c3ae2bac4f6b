<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Onnyx Platform{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="48x48" href="{{ url_for('static', filename='images/favicon-48x48.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <meta name="theme-color" content="#00fff7">

    <!-- Google Fonts - Orbitron & Montserrat -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS with custom config -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'orbitron': ['Orbitron', 'monospace'],
                        'montserrat': ['Montserrat', 'sans-serif'],
                    },
                    colors: {
                        'onyx': {
                            'black': '#1a1a1a',    // Brightened for better accessibility
                            'gray': '#2a2a2a',     // Improved contrast base
                            'light': '#3a3a3a',    // Lighter accent areas
                        },
                        'cyber': {
                            'cyan': '#00fff7',     // Enhanced with glow effects
                            'purple': '#9a00ff',   // Boosted vibrancy
                            'blue': '#0066ff',     // Increased visibility
                        },
                        'text': {
                            'primary': '#eeeeee',     // Brighter primary text
                            'secondary': '#d1d5db',   // Improved secondary text (gray-300)
                            'tertiary': '#9ca3af',    // Enhanced tertiary text (gray-400)
                            'muted': '#6b7280',       // Muted text (gray-500)
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- QR Code library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Alpine.js for interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    {% block head %}{% endblock %}
</head>
<body class="bg-onyx-black text-text-primary font-montserrat overflow-x-hidden"
      style="min-height: 100vh; display: flex; flex-direction: column; margin: 0; padding: 0;">
    <!-- Navigation -->
    <nav class="glass-nav fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-black/20 border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{{ url_for('index') }}" class="flex items-center space-x-3 group">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center shadow-lg shadow-cyber-cyan/20 group-hover:shadow-cyber-cyan/40 transition-all duration-300 bg-white/5 backdrop-blur-sm border border-white/10">
                            <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                                 alt="ONNYX Logo"
                                 class="w-8 h-8 object-contain filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                                 style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                        </div>
                        <span class="text-2xl font-orbitron font-bold bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                            ONNYX
                        </span>
                    </a>

                    <!-- Main Navigation -->
                    <div class="hidden md:ml-12 md:flex md:space-x-1">
                        <a href="{{ url_for('index') }}" class="nav-link px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/5 hover:text-cyber-cyan hover:shadow-lg hover:shadow-cyber-cyan/20">
                            Home
                        </a>
                        <a href="{{ url_for('sela.directory') }}" class="nav-link px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/5 hover:text-cyber-cyan hover:shadow-lg hover:shadow-cyber-cyan/20">
                            Validators
                        </a>
                        <a href="{{ url_for('explorer.index') }}" class="nav-link px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/5 hover:text-cyber-cyan hover:shadow-lg hover:shadow-cyber-cyan/20">
                            Explorer
                        </a>
                        {% if current_user %}
                        <a href="{{ url_for('dashboard.overview') }}" class="nav-link px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/5 hover:text-cyber-cyan hover:shadow-lg hover:shadow-cyber-cyan/20">
                            Dashboard
                        </a>
                        <a href="{{ url_for('auto_mining.dashboard') }}" class="nav-link px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/5 hover:text-cyber-cyan hover:shadow-lg hover:shadow-cyber-cyan/20">
                            Auto-Mining
                        </a>
                        {% endif %}
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-6">
                    <!-- Platform Stats -->
                    <div class="hidden lg:flex items-center space-x-6 text-xs font-medium">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse shadow-sm shadow-cyber-cyan/50"></div>
                            <span class="text-text-secondary" id="stat-identities">{{ platform_stats.identities }}</span>
                            <span class="text-text-tertiary">Identities</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-cyber-purple rounded-full animate-pulse shadow-sm shadow-cyber-purple/50"></div>
                            <span class="text-text-secondary" id="stat-selas">{{ platform_stats.selas }}</span>
                            <span class="text-text-tertiary">Validators</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-cyber-blue rounded-full animate-pulse shadow-sm shadow-cyber-blue/50"></div>
                            <span class="text-text-secondary" id="stat-transactions">{{ platform_stats.transactions }}</span>
                            <span class="text-text-tertiary">Transactions</span>
                        </div>
                    </div>

                    {% if current_user %}
                    <!-- Authenticated User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center space-x-3 glass-button px-4 py-2 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-cyber-cyan/20">
                            <div class="w-8 h-8 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-lg flex items-center justify-center">
                                <span class="text-onyx-black font-orbitron font-bold text-sm">{{ current_user.name[0].upper() }}</span>
                            </div>
                            <span class="hidden md:block font-medium">{{ current_user.name }}</span>
                            <svg class="w-4 h-4 transition-transform duration-300" :class="open ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95"
                             class="absolute right-0 mt-2 w-56 glass-card rounded-xl shadow-xl border border-white/10 py-2 z-50">
                            <a href="{{ url_for('dashboard.overview') }}" class="block px-4 py-3 text-sm text-white hover:bg-white/5 hover:text-cyber-cyan transition-all duration-200">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    </svg>
                                    <span>Dashboard</span>
                                </div>
                            </a>
                            <a href="{{ url_for('dashboard.identity_details') }}" class="block px-4 py-3 text-sm text-white hover:bg-white/5 hover:text-cyber-cyan transition-all duration-200">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span>My Identity</span>
                                </div>
                            </a>
                            <a href="{{ url_for('dashboard.selas') }}" class="block px-4 py-3 text-sm text-white hover:bg-white/5 hover:text-cyber-cyan transition-all duration-200">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                    </svg>
                                    <span>My Validators</span>
                                </div>
                            </a>
                            <div class="border-t border-white/10 my-2"></div>
                            <a href="{{ url_for('auth.logout') }}" class="block px-4 py-3 text-sm text-red-400 hover:bg-red-500/10 hover:text-red-300 transition-all duration-200">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    <span>Logout</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <!-- Guest User Menu -->
                    <div class="flex items-center space-x-4">
                        <a href="{{ url_for('auth.login') }}" class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/5 hover:text-cyber-cyan">
                            Access Portal
                        </a>
                        <a href="{{ url_for('register_choice') }}" class="glass-button-primary px-6 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                            Verify Identity
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden" x-data="{ open: false }">
            <!-- Mobile menu toggle button -->
            <button @click="open = !open"
                    class="mobile-menu-toggle fixed top-4 right-4 z-60 p-2 rounded-lg text-white hover:text-cyber-cyan focus:outline-none focus:ring-2 focus:ring-cyber-cyan/50 transition-all duration-300">
                <div class="hamburger" :class="{ 'active': open }">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>

            <!-- Mobile menu overlay -->
            <div x-show="open"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="mobile-menu-overlay"
                 :class="{ 'active': open }"
                 @click.away="open = false">

                <div class="mobile-menu-items">
                    <a href="{{ url_for('index') }}"
                       class="mobile-menu-item"
                       @click="open = false">
                        Home
                    </a>
                    <a href="{{ url_for('sela.directory') }}"
                       class="mobile-menu-item"
                       @click="open = false">
                        Validators
                    </a>
                    <a href="{{ url_for('explorer.index') }}"
                       class="mobile-menu-item"
                       @click="open = false">
                        Explorer
                    </a>
                    {% if current_user %}
                    <a href="{{ url_for('dashboard.overview') }}"
                       class="mobile-menu-item"
                       @click="open = false">
                        Dashboard
                    </a>
                    <a href="{{ url_for('auto_mining.dashboard') }}"
                       class="mobile-menu-item"
                       @click="open = false">
                        Auto-Mining
                    </a>
                    <a href="{{ url_for('auth.logout') }}"
                       class="mobile-menu-item"
                       style="background: rgba(239, 68, 68, 0.2); border-color: rgba(239, 68, 68, 0.3); color: #ef4444;"
                       @click="open = false">
                        Logout
                    </a>
                    {% else %}
                    <a href="{{ url_for('auth.login') }}"
                       class="mobile-menu-item"
                       @click="open = false">
                        Access Portal
                    </a>
                    <a href="{{ url_for('register_choice') }}"
                       class="mobile-menu-item"
                       style="background: linear-gradient(135deg, rgba(0, 212, 255, 0.25), rgba(139, 92, 246, 0.25)); border-color: var(--cyber-cyan); color: var(--cyber-cyan);"
                       @click="open = false">
                        Verify Identity
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Wrapper - Flex container that expands to push footer down -->
    <div style="flex: 1 0 auto; display: flex; flex-direction: column; min-height: calc(100vh - 64px);">
        <!-- Content area with top padding for fixed nav -->
        <main style="flex: 1 0 auto; padding-top: 4rem; padding-bottom: 4rem; width: 100%; position: relative;">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                    {% for category, message in messages %}
                    <div class="glass-card mb-4 p-4 rounded-xl border {% if category == 'error' %}border-red-500/30 bg-red-500/10 text-red-300{% elif category == 'success' %}border-green-500/30 bg-green-500/10 text-green-300{% elif category == 'warning' %}border-yellow-500/30 bg-yellow-500/10 text-yellow-300{% else %}border-cyber-cyan/30 bg-cyber-cyan/10 text-cyber-cyan{% endif %} backdrop-blur-sm">
                        <div class="flex items-center space-x-3">
                            {% if category == 'error' %}
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'success' %}
                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'warning' %}
                            <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            {% else %}
                            <svg class="w-5 h-5 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% endif %}
                            <span class="font-medium">{{ message }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endwith %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Unified Footer - Consistent Across All Pages -->
    <footer class="footer-responsive" style="flex-shrink: 0; margin-top: auto; position: relative; width: 100%; z-index: 10;">
        <div class="footer-grid">
            <!-- ONNYX Platform Section -->
            <div class="footer-section">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-14 h-14 rounded-xl flex items-center justify-center shadow-lg shadow-cyber-cyan/30 bg-white/8 backdrop-blur-sm border border-white/15">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="w-12 h-12 object-contain"
                             style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                    </div>
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">ONNYX</h3>
                        <p class="text-sm text-gray-300 font-medium">Trustworthy Commerce</p>
                    </div>
                </div>
                <p class="text-gray-300 text-sm leading-relaxed mb-6">
                    The Digital Backbone of Trustworthy Commerce. Blockchain-powered verification platform enabling transparent business operations through cryptographic identity management and decentralized validation networks.
                </p>
                <div class="footer-network-status">
                    <div class="footer-network-indicator">
                        <div class="footer-network-dot"></div>
                        <span>Network Online</span>
                    </div>
                    <div class="footer-network-indicator">
                        <span class="font-mono text-xs">v1.0.0</span>
                    </div>
                </div>
            </div>

            <!-- Platform Navigation Section -->
            <div class="footer-section">
                <h3>Platform</h3>
                <ul class="space-y-1">
                    <li><a href="{{ url_for('sela.directory') }}" class="footer-link">
                        <span class="mr-3">🏢</span><span>Validator Network</span>
                    </a></li>
                    <li><a href="{{ url_for('explorer.index') }}" class="footer-link">
                        <span class="mr-3">🔍</span><span>Blockchain Explorer</span>
                    </a></li>
                    <li><a href="{{ url_for('register_choice') }}" class="footer-link">
                        <span class="mr-3">🔐</span><span>Identity Verification</span>
                    </a></li>
                    <li><a href="#" class="footer-link">
                        <span class="mr-3">⚡</span><span>Auto-Mining</span>
                    </a></li>
                </ul>
            </div>

            <!-- Network Statistics Section -->
            <div class="footer-section">
                <h3>Network Stats</h3>
                <ul class="space-y-1">
                    <li class="footer-stat-item">
                        <span class="stat-label">Verified Identities</span>
                        <span class="stat-value">{{ platform_stats.identities }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Active Validators</span>
                        <span class="stat-value">{{ platform_stats.selas }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Total Transactions</span>
                        <span class="stat-value">{{ platform_stats.transactions }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Blocks Secured</span>
                        <span class="stat-value">{{ platform_stats.blocks }}</span>
                    </li>
                </ul>
            </div>

            <!-- Technology Stack Section -->
            <div class="footer-section">
                <h3>Technology</h3>
                <ul class="space-y-1">
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-cyan"></div>
                        <span class="footer-tech-label">Quantum-Resistant Security</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-purple"></div>
                        <span class="footer-tech-label">Etzem Trust Protocol</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-blue"></div>
                        <span class="footer-tech-label">Mikvah Token Economy</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-green-400"></div>
                        <span class="footer-tech-label">Decentralized Validation</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p>&copy; 2024 ONNYX Platform. Securing the future of digital commerce.</p>
                <div class="flex items-center space-x-6 text-sm">
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>Network Online</span>
                    </span>
                    <span class="font-mono">v1.0.0</span>
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                        <span>Blockchain Active</span>
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <!-- Enhanced Scroll Effects -->
    <script src="{{ url_for('static', filename='js/scroll-effects.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
