from flask import Flask, render_template, request, redirect, url_for, abort, jsonify
import json
import os
import datetime
import math
import time
import logging
import requests
import uuid
import hashlib
import random
import re
from data_sync import data_sync

# Configure logging
logger = logging.getLogger("onnyx.viewer.app")

app = Flask(__name__)

# Helper functions
def load_json(path):
    """Load JSON data from a file."""
    try:
        with open(path, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def format_timestamp(timestamp):
    """Format a Unix timestamp as a human-readable date."""
    return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

def get_transaction_by_id(txid):
    """Find a transaction by its ID."""
    # Check in blockchain
    blocks = load_json("data/blockchain.json")
    for block in blocks:
        for tx in block.get("transactions", []):
            if tx.get("txid") == txid or tx.get("id") == txid:
                return tx, block["index"]

    # Check in mempool
    mempool_txs = load_json("data/mempool.json")
    for tx in mempool_txs:
        if tx.get("txid") == txid or tx.get("id") == txid:
            return tx, "mempool"

    return None, None

def get_identity_by_id(identity_id):
    """Find an identity by its ID."""
    identities = load_json("data/identities.json")
    for identity in identities:
        if identity.get("id") == identity_id:
            return identity
    return None

def get_token_by_id(token_id):
    """Find a token by its ID."""
    tokens = load_json("data/tokens.json")
    for token in tokens:
        if token.get("id") == token_id:
            return token
    return None

def search_items(query):
    """Search for transactions, identities, and tokens."""
    results = {
        "transactions": [],
        "identities": [],
        "tokens": []
    }

    # Search transactions
    blocks = load_json("data/blockchain.json")
    for block in blocks:
        for tx in block.get("transactions", []):
            if (query in str(tx.get("txid", "")) or
                query in str(tx.get("id", "")) or
                query in str(tx)):
                results["transactions"].append({
                    "txid": tx.get("txid", tx.get("id", "")),
                    "block": block["index"],
                    "type": tx.get("type", "unknown"),
                    "data": tx
                })

    # Search mempool
    mempool_txs = load_json("data/mempool.json")
    for tx in mempool_txs:
        if (query in str(tx.get("txid", "")) or
            query in str(tx.get("id", "")) or
            query in str(tx)):
            results["transactions"].append({
                "txid": tx.get("txid", tx.get("id", "")),
                "block": "mempool",
                "type": tx.get("type", "unknown"),
                "data": tx
            })

    # Search identities
    identities = load_json("data/identities.json")
    for identity in identities:
        if (query in str(identity.get("id", "")) or
            query in str(identity.get("name", "")) or
            query in str(identity)):
            results["identities"].append(identity)

    # Search tokens
    tokens = load_json("data/tokens.json")
    for token in tokens:
        if (query in str(token.get("id", "")) or
            query in str(token.get("name", "")) or
            query in str(token.get("symbol", "")) or
            query in str(token)):
            results["tokens"].append(token)

    return results

# Routes
@app.route("/")
def index():
    """Home page with summary statistics and dashboard."""
    blocks = load_json("data/blockchain.json")
    tokens = load_json("data/tokens.json")
    identities = load_json("data/identities.json")
    mempool_txs = load_json("data/mempool.json")

    # Calculate basic statistics
    total_blocks = len(blocks)
    latest_block = blocks[-1] if blocks else {"index": 0, "timestamp": 0, "transactions": []}
    total_transactions = sum(len(block.get("transactions", [])) for block in blocks)
    pending_transactions = len(mempool_txs)
    total_tokens = len(tokens)
    total_identities = len(identities)

    # Calculate advanced statistics

    # Token statistics
    token_types = {}
    for token in tokens:
        token_type = token.get("type", "unknown")
        token_types[token_type] = token_types.get(token_type, 0) + 1

    # Calculate total token supply
    total_token_supply = sum(token.get("supply", 0) for token in tokens)

    # Identity statistics
    avg_reputation = sum(identity.get("reputation", 0) for identity in identities) / len(identities) if identities else 0

    # Count active Selas (business entities)
    total_selas = sum(1 for identity in identities if identity.get("metadata", {}).get("sela_registered", False))

    # Transaction type statistics
    tx_types = {}
    for block in blocks:
        for tx in block.get("transactions", []):
            tx_type = tx.get("type", "unknown")
            tx_types[tx_type] = tx_types.get(tx_type, 0) + 1

    # Calculate average transactions per block
    avg_txs_per_block = total_transactions / total_blocks if total_blocks > 0 else 0

    # Calculate blockchain size (in KB)
    blockchain_size = len(json.dumps(blocks)) / 1024

    # Get recent blocks
    recent_blocks = blocks[-5:] if blocks else []
    recent_blocks.reverse()  # Show newest first

    # Get recent transactions
    recent_txs = []
    for block in reversed(blocks[-5:]):
        for tx in block.get("transactions", [])[:5]:
            recent_txs.append({
                "txid": tx.get("txid", tx.get("id", "")),
                "block": block["index"],
                "type": tx.get("type", "unknown"),
                "timestamp": block.get("timestamp", 0)
            })
            if len(recent_txs) >= 5:
                break
        if len(recent_txs) >= 5:
            break

    # Get recent mempool transactions
    recent_mempool = sorted(mempool_txs, key=lambda x: x.get("timestamp", 0), reverse=True)[:5]

    # Get sync status
    sync_status = data_sync.get_sync_status()
    last_sync_time = sync_status["last_sync"]

    return render_template(
        "index.html",
        # Basic statistics
        total_blocks=total_blocks,
        latest_block=latest_block,
        total_transactions=total_transactions,
        pending_transactions=pending_transactions,
        total_tokens=total_tokens,
        total_identities=total_identities,

        # Advanced statistics
        token_types=token_types,
        total_token_supply=total_token_supply,
        avg_reputation=avg_reputation,
        total_selas=total_selas,
        tx_types=tx_types,
        avg_txs_per_block=avg_txs_per_block,
        blockchain_size=blockchain_size,

        # Recent data
        recent_blocks=recent_blocks,
        recent_txs=recent_txs,
        recent_mempool=recent_mempool,

        # Sync status
        sync_status=sync_status,
        last_sync_time=last_sync_time,

        format_timestamp=format_timestamp
    )

@app.route("/transactions")
def transactions():
    """List all confirmed transactions with pagination and filtering."""
    # Get pagination parameters
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))

    # Get filter parameters
    tx_type = request.args.get("type")
    from_id = request.args.get("from")
    to_id = request.args.get("to")
    token_id = request.args.get("token")
    start_time = request.args.get("start_time")
    end_time = request.args.get("end_time")

    blocks = load_json("data/blockchain.json")
    txs = []

    # Extract all transactions from blocks
    for block in blocks:
        for tx in block.get("transactions", []):
            txs.append({
                "txid": tx.get("txid", tx.get("id", "")),
                "block": block["index"],
                "type": tx.get("type", "unknown"),
                "timestamp": block.get("timestamp", 0),
                "data": tx
            })

    # Apply filters
    filtered_txs = txs

    if tx_type:
        filtered_txs = [tx for tx in filtered_txs if tx["type"] == tx_type]

    if from_id:
        filtered_txs = [tx for tx in filtered_txs if tx["data"].get("from") == from_id]

    if to_id:
        filtered_txs = [tx for tx in filtered_txs if tx["data"].get("to") == to_id]

    if token_id:
        filtered_txs = [tx for tx in filtered_txs if tx["data"].get("token") == token_id or tx["data"].get("tokenId") == token_id]

    if start_time:
        try:
            start_timestamp = int(start_time)
            filtered_txs = [tx for tx in filtered_txs if tx["timestamp"] >= start_timestamp]
        except ValueError:
            pass

    if end_time:
        try:
            end_timestamp = int(end_time)
            filtered_txs = [tx for tx in filtered_txs if tx["timestamp"] <= end_timestamp]
        except ValueError:
            pass

    # Sort transactions by block (descending)
    filtered_txs = sorted(filtered_txs, key=lambda x: x["block"], reverse=True)

    # Paginate
    total_items = len(filtered_txs)
    total_pages = math.ceil(total_items / per_page)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_txs = filtered_txs[start_idx:end_idx]

    # Get unique transaction types for filter dropdown
    tx_types = sorted(list(set(tx["type"] for tx in txs)))

    return render_template(
        "transactions.html",
        txs=paginated_txs,
        page=page,
        total_pages=total_pages,
        per_page=per_page,
        total_items=total_items,
        tx_types=tx_types,
        filters={
            "type": tx_type,
            "from": from_id,
            "to": to_id,
            "token": token_id,
            "start_time": start_time,
            "end_time": end_time
        },
        format_timestamp=format_timestamp
    )

@app.route("/mempool")
def mempool():
    """List all pending transactions in the mempool with pagination and filtering."""
    # Get pagination parameters
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))

    # Get filter parameters
    tx_type = request.args.get("type")
    from_id = request.args.get("from")
    to_id = request.args.get("to")
    token_id = request.args.get("token")

    txs = load_json("data/mempool.json")

    # Apply filters
    filtered_txs = txs

    if tx_type:
        filtered_txs = [tx for tx in filtered_txs if tx.get("type") == tx_type]

    if from_id:
        filtered_txs = [tx for tx in filtered_txs if tx.get("from") == from_id]

    if to_id:
        filtered_txs = [tx for tx in filtered_txs if tx.get("to") == to_id]

    if token_id:
        filtered_txs = [tx for tx in filtered_txs if tx.get("token") == token_id or tx.get("tokenId") == token_id]

    # Sort transactions by timestamp (descending)
    filtered_txs = sorted(filtered_txs, key=lambda x: x.get("timestamp", 0), reverse=True)

    # Paginate
    total_items = len(filtered_txs)
    total_pages = math.ceil(total_items / per_page)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_txs = filtered_txs[start_idx:end_idx]

    # Get unique transaction types for filter dropdown
    tx_types = sorted(list(set(tx.get("type", "unknown") for tx in txs)))

    return render_template(
        "mempool.html",
        txs=paginated_txs,
        page=page,
        total_pages=total_pages,
        per_page=per_page,
        total_items=total_items,
        tx_types=tx_types,
        filters={
            "type": tx_type,
            "from": from_id,
            "to": to_id,
            "token": token_id
        },
        format_timestamp=format_timestamp
    )

@app.route("/tokens")
def tokens():
    """List all tokens with pagination and filtering."""
    # Get pagination parameters
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))

    # Get filter parameters
    token_type = request.args.get("type")
    creator_id = request.args.get("creator")
    token_name = request.args.get("name")
    token_symbol = request.args.get("symbol")

    tokens_data = load_json("data/tokens.json")

    # Apply filters
    filtered_tokens = tokens_data

    if token_type:
        filtered_tokens = [token for token in filtered_tokens if token.get("type") == token_type]

    if creator_id:
        filtered_tokens = [token for token in filtered_tokens if token.get("creator") == creator_id]

    if token_name:
        filtered_tokens = [token for token in filtered_tokens if token_name.lower() in token.get("name", "").lower()]

    if token_symbol:
        filtered_tokens = [token for token in filtered_tokens if token_symbol.lower() in token.get("symbol", "").lower()]

    # Sort tokens by creation time (descending) or name if no timestamp
    filtered_tokens = sorted(filtered_tokens, key=lambda x: (x.get("created_at", 0), x.get("name", "")), reverse=True)

    # Paginate
    total_items = len(filtered_tokens)
    total_pages = math.ceil(total_items / per_page)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_tokens = filtered_tokens[start_idx:end_idx]

    # Get unique token types for filter dropdown
    token_types = sorted(list(set(token.get("type", "unknown") for token in tokens_data)))

    return render_template(
        "tokens.html",
        tokens=paginated_tokens,
        page=page,
        total_pages=total_pages,
        per_page=per_page,
        total_items=total_items,
        token_types=token_types,
        filters={
            "type": token_type,
            "creator": creator_id,
            "name": token_name,
            "symbol": token_symbol
        },
        format_timestamp=format_timestamp
    )

@app.route("/identities")
def identities():
    """List all identities with pagination and filtering."""
    # Get pagination parameters
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 20))

    # Get filter parameters
    identity_name = request.args.get("name")
    min_reputation = request.args.get("min_reputation")
    max_reputation = request.args.get("max_reputation")

    identities_data = load_json("data/identities.json")

    # Apply filters
    filtered_identities = identities_data

    if identity_name:
        filtered_identities = [identity for identity in filtered_identities if identity_name.lower() in identity.get("name", "").lower()]

    if min_reputation:
        try:
            min_rep = int(min_reputation)
            filtered_identities = [identity for identity in filtered_identities if identity.get("reputation", 0) >= min_rep]
        except ValueError:
            pass

    if max_reputation:
        try:
            max_rep = int(max_reputation)
            filtered_identities = [identity for identity in filtered_identities if identity.get("reputation", 0) <= max_rep]
        except ValueError:
            pass

    # Sort identities by reputation (descending) then name
    filtered_identities = sorted(filtered_identities, key=lambda x: (-x.get("reputation", 0), x.get("name", "")))

    # Paginate
    total_items = len(filtered_identities)
    total_pages = math.ceil(total_items / per_page)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_identities = filtered_identities[start_idx:end_idx]

    return render_template(
        "identities.html",
        identities=paginated_identities,
        page=page,
        total_pages=total_pages,
        per_page=per_page,
        total_items=total_items,
        filters={
            "name": identity_name,
            "min_reputation": min_reputation,
            "max_reputation": max_reputation
        },
        format_timestamp=format_timestamp
    )

@app.route("/search")
def search():
    """Search for transactions, identities, and tokens."""
    query = request.args.get("q", "")
    if not query:
        return render_template("search.html", query="", results=None)

    results = search_items(query)
    return render_template("search.html", query=query, results=results)

@app.route("/sela/<sela_id>")
def sela_shop_window(sela_id):
    """Display a coming soon page for a Sela shop window."""
    # Extract Sela name and type from the ID
    sela_name = None
    sela_type = None
    owner_name = None

    # Try to parse the sela_id to get a readable name
    if sela_id:
        # Check if it's one of our demo Selas
        if sela_id == "alice_salon":
            sela_name = "Alice's Beauty Salon"
            sela_type = "BEAUTY"
            owner_name = "Alice"
        elif sela_id == "bob_barber":
            sela_name = "Bob's Barbershop"
            sela_type = "BARBER"
            owner_name = "Bob"
        elif sela_id == "charlie_cafe":
            sela_name = "Charlie's Cafe"
            sela_type = "CAFE"
            owner_name = "Charlie"
        else:
            # Try to make a readable name from the ID
            words = re.findall(r'[A-Za-z][a-z]*', sela_id)
            if words:
                sela_name = ' '.join(word.capitalize() for word in words)
                sela_type = "BUSINESS"
                owner_name = words[0].capitalize() if words else "Unknown"

    return render_template(
        "coming_soon.html",
        sela_id=sela_id,
        sela_name=sela_name or f"Sela {sela_id}",
        sela_type=sela_type or "BUSINESS",
        owner_name=owner_name or "Unknown"
    )

@app.route("/sela/network")
def network_coming_soon():
    """Display network nodes information."""
    # For now, this is a placeholder that will show a coming soon page
    return render_template(
        "coming_soon.html",
        sela_id="network",
        sela_name="Onnyx Network",
        sela_type="NETWORK",
        owner_name="Onnyx"
    )

@app.errorhandler(404)
def page_not_found(e):
    """Handle 404 errors by showing a coming soon page."""
    path = request.path

    # Check if this is a Sela shop window request
    sela_match = re.match(r'/sela/([a-zA-Z0-9_-]+)', path)
    if sela_match:
        sela_id = sela_match.group(1)
        return sela_shop_window(sela_id)

    # For other 404 errors, redirect to home
    return redirect(url_for('index'))

@app.route("/tx/<txid>")
def tx_detail(txid):
    """Show details of a specific transaction."""
    tx, block_index = get_transaction_by_id(txid)
    if not tx:
        abort(404)

    # Get block timestamp if available
    timestamp = None
    if block_index != "mempool":
        blocks = load_json("data/blockchain.json")
        for block in blocks:
            if block["index"] == block_index:
                timestamp = block.get("timestamp")
                break

    return render_template(
        "tx_detail.html",
        tx=tx,
        block_index=block_index,
        timestamp=timestamp,
        format_timestamp=format_timestamp
    )

@app.route("/identity/<identity_id>")
def identity_detail(identity_id):
    """Show details of a specific identity."""
    identity = get_identity_by_id(identity_id)
    if not identity:
        abort(404)

    # Find transactions related to this identity
    related_txs = []
    blocks = load_json("data/blockchain.json")
    for block in blocks:
        for tx in block.get("transactions", []):
            if (identity_id in str(tx.get("from", "")) or
                identity_id in str(tx.get("to", "")) or
                identity_id in str(tx.get("identity", "")) or
                identity_id in str(tx)):
                related_txs.append({
                    "txid": tx.get("txid", tx.get("id", "")),
                    "block": block["index"],
                    "type": tx.get("type", "unknown"),
                    "timestamp": block.get("timestamp", 0)
                })

    return render_template(
        "identity_detail.html",
        identity=identity,
        related_txs=related_txs,
        format_timestamp=format_timestamp
    )

@app.route("/token/<token_id>")
def token_detail(token_id):
    """Show details of a specific token."""
    token = get_token_by_id(token_id)
    if not token:
        abort(404)

    # Find transactions related to this token
    related_txs = []
    blocks = load_json("data/blockchain.json")
    for block in blocks:
        for tx in block.get("transactions", []):
            if (token_id in str(tx.get("token", "")) or
                token_id in str(tx.get("tokenId", "")) or
                token_id in str(tx)):
                related_txs.append({
                    "txid": tx.get("txid", tx.get("id", "")),
                    "block": block["index"],
                    "type": tx.get("type", "unknown"),
                    "timestamp": block.get("timestamp", 0)
                })

    return render_template(
        "token_detail.html",
        token=token,
        related_txs=related_txs,
        format_timestamp=format_timestamp
    )

# Add a refresh endpoint to manually reload data
@app.route("/refresh")
def refresh():
    """Manually refresh data from the Onnyx node."""
    try:
        data_sync.sync()
        return jsonify({
            "success": True,
            "message": "Data refreshed successfully",
            "timestamp": time.time()
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Error refreshing data: {str(e)}",
            "timestamp": time.time()
        }), 500

# Add a status endpoint to check the sync status
@app.route("/status")
def status():
    """Get the status of the data sync."""
    return jsonify({
        "sync": data_sync.get_sync_status(),
        "timestamp": time.time()
    })

@app.route("/economic-structure")
def economic_structure():
    """Show the Onnyx economic structure page."""
    return render_template("economic_structure.html")

@app.route("/nodes")
def nodes():
    """Show node status and connected peers."""
    # Get blockchain data
    blocks = load_json("data/blockchain.json")

    # Calculate local node statistics
    local_node = {
        "id": "local_node",
        "type": "local",
        "block_height": len(blocks),
        "latest_block_hash": blocks[-1]["hash"] if blocks else "",
        "latest_block_time": blocks[-1]["timestamp"] if blocks else 0,
        "status": "active"
    }

    # Try to get peer data from the API
    peers = []
    try:
        response = requests.get(f"{data_sync.api_url}/peers")
        if response.status_code == 200:
            peers = response.json()
        else:
            # Fallback to sample peer data
            peers = [
                {
                    "id": "peer_1",
                    "url": "ws://peer1.onnyx.chain:8080",
                    "block_height": len(blocks) - 1,
                    "status": "active",
                    "last_seen": time.time() - 60
                },
                {
                    "id": "peer_2",
                    "url": "ws://peer2.onnyx.chain:8080",
                    "block_height": len(blocks),
                    "status": "active",
                    "last_seen": time.time() - 120
                },
                {
                    "id": "peer_3",
                    "url": "ws://peer3.onnyx.chain:8080",
                    "block_height": len(blocks) + 1,
                    "status": "syncing",
                    "last_seen": time.time() - 30
                }
            ]
    except Exception as e:
        logger.error(f"Error getting peer data: {str(e)}")
        # Fallback to sample peer data
        peers = [
            {
                "id": "peer_1",
                "url": "ws://peer1.onnyx.chain:8080",
                "block_height": len(blocks) - 1,
                "status": "active",
                "last_seen": time.time() - 60
            },
            {
                "id": "peer_2",
                "url": "ws://peer2.onnyx.chain:8080",
                "block_height": len(blocks),
                "status": "active",
                "last_seen": time.time() - 120
            },
            {
                "id": "peer_3",
                "url": "ws://peer3.onnyx.chain:8080",
                "block_height": len(blocks) + 1,
                "status": "syncing",
                "last_seen": time.time() - 30
            }
        ]

    # Get sync status
    sync_status = data_sync.get_sync_status()

    return render_template(
        "nodes.html",
        local_node=local_node,
        peers=peers,
        sync_status=sync_status,
        format_timestamp=format_timestamp
    )

# Admin routes
@app.route("/admin")
def admin():
    """Admin tools for managing the blockchain."""
    return render_template("admin.html")

@app.route("/admin/transaction", methods=["POST"])
def admin_submit_transaction():
    """Submit a test transaction to the Onnyx system."""
    try:
        # Get form data
        tx_type = request.form.get("type")
        from_id = request.form.get("from")
        to_id = request.form.get("to")
        token_id = request.form.get("token")
        amount = request.form.get("amount")
        name = request.form.get("name")
        symbol = request.form.get("symbol")

        # Create transaction based on type
        tx = {
            "txid": f"tx_{uuid.uuid4().hex[:8]}",
            "type": tx_type,
            "timestamp": int(time.time())
        }

        if tx_type == "transfer":
            tx.update({
                "from": from_id,
                "to": to_id,
                "token": token_id,
                "amount": int(amount) if amount else 0
            })
        elif tx_type == "mint":
            tx.update({
                "from": from_id,
                "token": token_id,
                "amount": int(amount) if amount else 0
            })
        elif tx_type == "burn":
            tx.update({
                "from": from_id,
                "token": token_id,
                "amount": int(amount) if amount else 0
            })
        elif tx_type == "spawn":
            tx.update({
                "from": from_id,
                "name": name,
                "symbol": symbol,
                "supply": int(amount) if amount else 1000
            })

        # Try to submit the transaction to the Onnyx API
        try:
            # Determine the appropriate API endpoint based on transaction type
            if tx_type == "transfer":
                api_endpoint = f"{data_sync.api_url}/token/sendtoken"
                payload = {
                    "from_identity": from_id,
                    "to_identity": to_id,
                    "token_id": token_id,
                    "amount": int(amount) if amount else 0
                }
            elif tx_type == "mint":
                api_endpoint = f"{data_sync.api_url}/token/minttoken"
                payload = {
                    "identity_id": from_id,
                    "token_id": token_id,
                    "amount": int(amount) if amount else 0
                }
            elif tx_type == "burn":
                api_endpoint = f"{data_sync.api_url}/token/burntoken"
                payload = {
                    "identity_id": from_id,
                    "token_id": token_id,
                    "amount": int(amount) if amount else 0
                }
            elif tx_type == "spawn":
                api_endpoint = f"{data_sync.api_url}/token/forktoken"
                payload = {
                    "identity_id": from_id,
                    "name": name,
                    "symbol": symbol,
                    "initial_supply": int(amount) if amount else 1000
                }

            # Send the request to the API
            response = requests.post(api_endpoint, json=payload)

            if response.status_code == 200:
                # API call successful, get the transaction ID from the response
                api_response = response.json()
                tx["txid"] = api_response.get("txid", tx["txid"])

                logger.info(f"Transaction submitted to API: {tx['txid']}")

                # Force a data sync to get the latest mempool
                data_sync.sync()

                return render_template(
                    "admin.html",
                    tx_result={
                        "success": True,
                        "message": f"Transaction of type {tx_type} submitted successfully to Onnyx API",
                        "txid": tx["txid"]
                    }
                )
            else:
                # API call failed, fall back to local storage
                logger.warning(f"API call failed with status {response.status_code}: {response.text}")
                raise Exception(f"API call failed: {response.text}")

        except requests.RequestException as e:
            # API call failed, fall back to local storage
            logger.warning(f"API call failed: {str(e)}")

            # Add transaction to mempool
            mempool = load_json("data/mempool.json")
            mempool.append(tx)

            # Save mempool
            with open(os.path.join("data", "mempool.json"), "w") as f:
                json.dump(mempool, f, indent=2)

            return render_template(
                "admin.html",
                tx_result={
                    "success": True,
                    "message": f"Transaction of type {tx_type} added to local mempool (API unavailable)",
                    "txid": tx["txid"]
                }
            )

    except Exception as e:
        logger.error(f"Error submitting transaction: {str(e)}")
        return render_template(
            "admin.html",
            tx_result={
                "success": False,
                "message": f"Error submitting transaction: {str(e)}"
            }
        )

@app.route("/admin/token", methods=["POST"])
def admin_create_token():
    """Create a test token in the Onnyx system."""
    try:
        # Get form data
        name = request.form.get("name")
        symbol = request.form.get("symbol")
        creator = request.form.get("creator")
        token_type = request.form.get("type")
        supply = request.form.get("supply")
        max_supply = request.form.get("max_supply")
        mintable = "mintable" in request.form
        burnable = "burnable" in request.form
        transferable = "transferable" in request.form

        # Create token object
        token_id = f"token_{uuid.uuid4().hex[:8]}"
        token = {
            "id": token_id,
            "name": name,
            "symbol": symbol,
            "creator": creator,
            "type": token_type,
            "supply": int(supply) if supply else 0,
            "max_supply": int(max_supply) if max_supply else None,
            "mintable": mintable,
            "burnable": burnable,
            "transferable": transferable,
            "created_at": int(time.time()),
            "metadata": {
                "description": f"Test token created via admin interface",
                "admin_created": True
            }
        }

        # Try to create the token via the Onnyx API
        try:
            # Use the forktoken endpoint to create a new token
            api_endpoint = f"{data_sync.api_url}/token/forktoken"
            payload = {
                "identity_id": creator,
                "name": name,
                "symbol": symbol,
                "initial_supply": int(supply) if supply else 0,
                "max_supply": int(max_supply) if max_supply and max_supply.strip() else None,
                "token_type": token_type,
                "mintable": mintable,
                "burnable": burnable,
                "transferable": transferable,
                "metadata": {
                    "description": f"Test token created via admin interface",
                    "admin_created": True
                }
            }

            # Send the request to the API
            response = requests.post(api_endpoint, json=payload)

            if response.status_code == 200:
                # API call successful, get the token ID from the response
                api_response = response.json()
                token_id = api_response.get("token_id", token_id)
                token["id"] = token_id

                logger.info(f"Token created via API: {token_id}")

                # Force a data sync to get the latest token registry
                data_sync.sync()

                return render_template(
                    "admin.html",
                    token_result={
                        "success": True,
                        "message": f"Token {name} ({symbol}) created successfully via Onnyx API",
                        "token_id": token_id
                    }
                )
            else:
                # API call failed, fall back to local storage
                logger.warning(f"API call failed with status {response.status_code}: {response.text}")
                raise Exception(f"API call failed: {response.text}")

        except requests.RequestException as e:
            # API call failed, fall back to local storage
            logger.warning(f"API call failed: {str(e)}")

            # Add token to registry
            tokens = load_json("data/tokens.json")
            tokens.append(token)

            # Save tokens
            with open(os.path.join("data", "tokens.json"), "w") as f:
                json.dump(tokens, f, indent=2)

            return render_template(
                "admin.html",
                token_result={
                    "success": True,
                    "message": f"Token {name} ({symbol}) added to local registry (API unavailable)",
                    "token_id": token_id
                }
            )

    except Exception as e:
        logger.error(f"Error creating token: {str(e)}")
        return render_template(
            "admin.html",
            token_result={
                "success": False,
                "message": f"Error creating token: {str(e)}"
            }
        )

@app.route("/admin/identity", methods=["POST"])
def admin_create_identity():
    """Create a test identity in the Onnyx system."""
    try:
        # Get form data
        name = request.form.get("name")
        reputation = request.form.get("reputation")

        # Create identity object
        identity_id = f"id_{uuid.uuid4().hex[:8]}"
        identity = {
            "id": identity_id,
            "name": name,
            "public_key": hashlib.sha256(identity_id.encode()).hexdigest(),
            "reputation": int(reputation) if reputation else 0,
            "created_at": int(time.time()),
            "tokens_created": 0,
            "metadata": {
                "bio": f"Test identity created via admin interface",
                "admin_created": True
            }
        }

        # Try to create the identity via the Onnyx API
        try:
            # Use the createidentity endpoint to create a new identity
            api_endpoint = f"{data_sync.api_url}/identity/createidentity"
            payload = {
                "name": name,
                "public_key": hashlib.sha256(identity_id.encode()).hexdigest(),
                "metadata": {
                    "bio": f"Test identity created via admin interface",
                    "admin_created": True,
                    "initial_reputation": int(reputation) if reputation else 0
                }
            }

            # Send the request to the API
            response = requests.post(api_endpoint, json=payload)

            if response.status_code == 200:
                # API call successful, get the identity ID from the response
                api_response = response.json()
                identity_id = api_response.get("identity_id", identity_id)
                identity["id"] = identity_id

                logger.info(f"Identity created via API: {identity_id}")

                # Force a data sync to get the latest identity registry
                data_sync.sync()

                return render_template(
                    "admin.html",
                    identity_result={
                        "success": True,
                        "message": f"Identity {name} created successfully via Onnyx API",
                        "identity_id": identity_id
                    }
                )
            else:
                # API call failed, fall back to local storage
                logger.warning(f"API call failed with status {response.status_code}: {response.text}")
                raise Exception(f"API call failed: {response.text}")

        except requests.RequestException as e:
            # API call failed, fall back to local storage
            logger.warning(f"API call failed: {str(e)}")

            # Add identity to registry
            identities = load_json("data/identities.json")
            identities.append(identity)

            # Save identities
            with open(os.path.join("data", "identities.json"), "w") as f:
                json.dump(identities, f, indent=2)

            return render_template(
                "admin.html",
                identity_result={
                    "success": True,
                    "message": f"Identity {name} added to local registry (API unavailable)",
                    "identity_id": identity_id
                }
            )

    except Exception as e:
        logger.error(f"Error creating identity: {str(e)}")
        return render_template(
            "admin.html",
            identity_result={
                "success": False,
                "message": f"Error creating identity: {str(e)}"
            }
        )

@app.route("/admin/scroll", methods=["POST"])
def admin_propose_scroll():
    """Propose a test Voice Scroll in the Onnyx governance system."""
    try:
        # Get form data
        title = request.form.get("title")
        description = request.form.get("description")
        proposer = request.form.get("proposer")
        category = request.form.get("category")
        expiry_days = request.form.get("expiry_days")

        # Create scroll ID
        scroll_id = f"scroll_{uuid.uuid4().hex[:8]}"

        # Try to propose the scroll via the Onnyx API
        try:
            # Use the governance/propose endpoint to create a new proposal
            api_endpoint = f"{data_sync.api_url}/governance/propose"
            payload = {
                "title": title,
                "description": description,
                "proposer_id": proposer,
                "category": category,
                "expiry_days": int(expiry_days) if expiry_days else 7
            }

            # Send the request to the API
            response = requests.post(api_endpoint, json=payload)

            if response.status_code == 200:
                # API call successful, get the scroll ID from the response
                api_response = response.json()
                scroll_id = api_response.get("scroll_id", scroll_id)

                logger.info(f"Voice Scroll proposed via API: {scroll_id}")

                # Force a data sync to get the latest governance data
                data_sync.sync()

                return render_template(
                    "admin.html",
                    scroll_result={
                        "success": True,
                        "message": f"Voice Scroll '{title}' proposed successfully via Onnyx API",
                        "scroll_id": scroll_id
                    }
                )
            else:
                # API call failed, fall back to local confirmation
                logger.warning(f"API call failed with status {response.status_code}: {response.text}")
                raise Exception(f"API call failed: {response.text}")

        except requests.RequestException as e:
            # API call failed, fall back to local confirmation
            logger.warning(f"API call failed: {str(e)}")

            # Return success (we don't actually save scrolls locally)
            return render_template(
                "admin.html",
                scroll_result={
                    "success": True,
                    "message": f"Voice Scroll '{title}' proposal simulated (API unavailable)",
                    "scroll_id": scroll_id
                }
            )

    except Exception as e:
        logger.error(f"Error proposing scroll: {str(e)}")
        return render_template(
            "admin.html",
            scroll_result={
                "success": False,
                "message": f"Error proposing scroll: {str(e)}"
            }
        )

@app.route("/admin/mine", methods=["POST"])
def admin_mine_block():
    """Mine a test block in the Onnyx blockchain."""
    try:
        # Get form data
        num_txs = request.form.get("num_txs")
        num_txs = int(num_txs) if num_txs else 0

        # Try to mine a block via the Onnyx API
        try:
            # Use the mine endpoint to mine a new block
            api_endpoint = f"{data_sync.api_url}/mine"
            payload = {
                "num_transactions": num_txs
            }

            # Send the request to the API
            response = requests.post(api_endpoint, json=payload)

            if response.status_code == 200:
                # API call successful, get the block details from the response
                api_response = response.json()
                block_index = api_response.get("block_index")
                block_hash = api_response.get("block_hash")
                num_transactions = api_response.get("num_transactions", num_txs)

                logger.info(f"Block mined via API: {block_index}")

                # Force a data sync to get the latest blockchain
                data_sync.sync()

                return render_template(
                    "admin.html",
                    mining_result={
                        "success": True,
                        "message": f"Block {block_index} mined successfully via Onnyx API",
                        "block_index": block_index,
                        "block_hash": block_hash,
                        "num_txs": num_transactions
                    }
                )
            else:
                # API call failed, fall back to local mining
                logger.warning(f"API call failed with status {response.status_code}: {response.text}")
                raise Exception(f"API call failed: {response.text}")

        except requests.RequestException as e:
            # API call failed, fall back to local mining
            logger.warning(f"API call failed: {str(e)}")

            # Get blockchain and mempool
            blocks = load_json("data/blockchain.json")
            mempool = load_json("data/mempool.json")

            # Get transactions from mempool
            transactions = mempool[:num_txs]

            # Create block
            latest_block = blocks[-1] if blocks else {"index": -1, "hash": "0" * 64}
            block = {
                "index": latest_block["index"] + 1,
                "previous_hash": latest_block.get("hash", "0" * 64),
                "timestamp": int(time.time()),
                "transactions": transactions,
                "nonce": random.randint(0, 1000000)
            }

            # Calculate block hash
            block_string = json.dumps({k: v for k, v in block.items() if k != "hash"}, sort_keys=True)
            block["hash"] = hashlib.sha256(block_string.encode()).hexdigest()

            # Add block to blockchain
            blocks.append(block)

            # Save blockchain
            with open(os.path.join("data", "blockchain.json"), "w") as f:
                json.dump(blocks, f, indent=2)

            # Remove mined transactions from mempool
            for tx in transactions:
                if tx in mempool:
                    mempool.remove(tx)

            # Save mempool
            with open(os.path.join("data", "mempool.json"), "w") as f:
                json.dump(mempool, f, indent=2)

            return render_template(
                "admin.html",
                mining_result={
                    "success": True,
                    "message": f"Block {block['index']} mined locally (API unavailable)",
                    "block_index": block["index"],
                    "block_hash": block["hash"],
                    "num_txs": len(transactions)
                }
            )

    except Exception as e:
        logger.error(f"Error mining block: {str(e)}")
        return render_template(
            "admin.html",
            mining_result={
                "success": False,
                "message": f"Error mining block: {str(e)}"
            }
        )

# Start the data sync when the app starts
with app.app_context():
    data_sync.start()

if __name__ == "__main__":
    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)

    # Create empty JSON files if they don't exist
    for filename in ["blockchain.json", "mempool.json", "identities.json", "tokens.json"]:
        filepath = os.path.join("data", filename)
        if not os.path.exists(filepath):
            with open(filepath, "w") as f:
                json.dump([], f)

    # Start the data sync
    data_sync.start()

    app.run(host="0.0.0.0", port=8080, debug=True)
