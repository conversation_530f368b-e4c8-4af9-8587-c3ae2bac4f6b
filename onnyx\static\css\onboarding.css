/* Onnyx Onboarding Portal Styles */

/* Base Styles */
:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #f39c12;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --success-color: #27ae60;
    --warning-color: #e67e22;
    --danger-color: #e74c3c;
    --text-color: #333;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* Header Styles */
.onboarding-header {
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    padding: 2rem 1rem;
    position: relative;
}

.logo-container {
    margin-bottom: 1rem;
}

.logo {
    width: 100px;
    height: auto;
}

.onboarding-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.tagline {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Main Container */
.onboarding-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Declaration Section */
.declaration {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
}

.declaration h2 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.mission-statement {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.value-proposition {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.shield-graphic {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1.5rem;
}

.shield-icon {
    width: 80px;
    height: auto;
    margin-bottom: 1rem;
}

.shield-text {
    font-weight: 500;
    color: var(--success-color);
}

/* Onboarding Steps */
.onboarding-steps {
    margin-bottom: 3rem;
}

.onboarding-steps h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.step-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    position: relative;
    padding-left: 4rem;
}

.step-number {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.step-card h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.step-card p {
    margin-bottom: 1rem;
}

.step-button {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.step-button:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

/* Protection Benefits */
.protection-benefits {
    margin-bottom: 3rem;
}

.protection-benefits h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.benefit-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.benefit-card h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.benefit-card p {
    margin-bottom: 1rem;
}

.benefit-card blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    font-style: italic;
    color: var(--dark-color);
}

/* Real-World Examples */
.real-world-examples {
    margin-bottom: 3rem;
}

.real-world-examples h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.example-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.example-card h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.example-card p {
    margin-bottom: 1rem;
}

.example-card ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.example-card li {
    margin-bottom: 0.5rem;
}

/* Call to Action */
.call-to-action {
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.call-to-action h2 {
    margin-bottom: 1rem;
}

.call-to-action p {
    margin-bottom: 1.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 1.1rem;
    transition: var(--transition);
}

.cta-button:hover {
    background-color: #e67e22;
    color: white;
    transform: translateY(-2px);
}

/* Footer */
.onboarding-footer {
    background-color: var(--dark-color);
    color: white;
    padding: 2rem 1rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.footer-logo {
    margin-bottom: 1rem;
}

.footer-logo-img {
    width: 80px;
    height: auto;
}

.footer-links h4 {
    margin-bottom: 1rem;
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--light-color);
}

.footer-links a:hover {
    color: var(--accent-color);
}

.footer-quote {
    max-width: 300px;
}

.footer-quote blockquote {
    font-style: italic;
    opacity: 0.9;
}

.copyright {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
    }
    
    .footer-logo, .footer-links, .footer-quote {
        margin-bottom: 1.5rem;
    }
}

@media (min-width: 768px) {
    .onboarding-steps {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .step-card {
        margin-bottom: 0;
    }
    
    .protection-benefits {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .benefit-card {
        margin-bottom: 0;
    }
    
    .real-world-examples {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .example-card {
        margin-bottom: 0;
    }
}
