#!/usr/bin/env python3
"""
Onnyx Database Initialization Script

This script initializes the Onnyx database by creating the necessary tables
and migrating data from JSON files to the SQLite database.
"""

import os
import sys
import json
import sqlite3
import logging
import time
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("onnyx.scripts.init_db")

def ensure_directory(path):
    """
    Ensure a directory exists.

    Args:
        path: Path to the directory
    """
    os.makedirs(path, exist_ok=True)
    logger.info(f"Ensured directory exists: {path}")

def init_db(db_path, schema_path):
    """
    Initialize the database with the schema.

    Args:
        db_path: Path to the database file
        schema_path: Path to the schema file
    """
    # Ensure the directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Read the schema file
    with open(schema_path, 'r') as f:
        schema = f.read()

    # Execute the schema
    cursor.executescript(schema)

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    logger.info(f"Initialized database at {db_path}")

def migrate_identities(json_path, db_path):
    """
    Migrate identities from a JSON file to the database.

    Args:
        json_path: Path to the JSON file
        db_path: Path to the database file
    """
    if not os.path.exists(json_path) or os.path.getsize(json_path) == 0:
        logger.warning(f"Identities file does not exist or is empty: {json_path}")
        return

    # Load the JSON file
    with open(json_path, 'r') as f:
        identities = json.load(f)

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Insert the identities
    for identity_id, identity in identities.items():
        cursor.execute(
            "INSERT OR REPLACE INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                identity_id,
                identity.get('name', ''),
                identity.get('public_key', ''),
                json.dumps(identity.get('metadata', {})),
                identity.get('created_at', int(time.time())),
                int(time.time())
            )
        )

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    logger.info(f"Migrated {len(identities)} identities from {json_path} to {db_path}")

def migrate_tokens(json_path, db_path):
    """
    Migrate tokens from a JSON file to the database.

    Args:
        json_path: Path to the JSON file
        db_path: Path to the database file
    """
    if not os.path.exists(json_path) or os.path.getsize(json_path) == 0:
        logger.warning(f"Tokens file does not exist or is empty: {json_path}")
        return

    # Load the JSON file
    with open(json_path, 'r') as f:
        tokens = json.load(f)

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Insert the tokens
    for token_id, token in tokens.items():
        cursor.execute(
            "INSERT OR REPLACE INTO tokens (token_id, name, symbol, creator_id, supply, category, decimals, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                token_id,
                token.get('name', ''),
                token.get('symbol', ''),
                token.get('creator_id', ''),
                token.get('supply', 0),
                token.get('category', ''),
                token.get('decimals', 0),
                token.get('created_at', int(time.time())),
                json.dumps(token.get('metadata', {}))
            )
        )

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    logger.info(f"Migrated {len(tokens)} tokens from {json_path} to {db_path}")

def migrate_blockchain(json_path, db_path):
    """
    Migrate blockchain data from a JSON file to the database.

    Args:
        json_path: Path to the JSON file
        db_path: Path to the database file
    """
    if not os.path.exists(json_path) or os.path.getsize(json_path) == 0:
        logger.warning(f"Blockchain file does not exist or is empty: {json_path}")
        return

    # Load the JSON file
    with open(json_path, 'r') as f:
        blockchain = json.load(f)

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Insert the blocks
    for block in blockchain.get('blocks', []):
        cursor.execute(
            "INSERT OR REPLACE INTO blocks (block_hash, block_height, previous_hash, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                block.get('hash', ''),
                block.get('height', 0),
                block.get('previous_hash', ''),
                block.get('timestamp', 0),
                block.get('difficulty', 0),
                block.get('nonce', 0),
                block.get('miner', ''),
                json.dumps(block.get('transactions', [])),
                block.get('merkle_root', ''),
                block.get('size', 0),
                block.get('version', '1.0'),
                int(time.time())
            )
        )

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    logger.info(f"Migrated {len(blockchain.get('blocks', []))} blocks from {json_path} to {db_path}")

def migrate_mempool(json_path, db_path):
    """
    Migrate mempool data from a JSON file to the database.

    Args:
        json_path: Path to the JSON file
        db_path: Path to the database file
    """
    if not os.path.exists(json_path) or os.path.getsize(json_path) == 0:
        logger.warning(f"Mempool file does not exist or is empty: {json_path}")
        return

    # Load the JSON file
    with open(json_path, 'r') as f:
        mempool = json.load(f)

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Insert the transactions
    for tx_id, tx in mempool.items():
        cursor.execute(
            "INSERT OR REPLACE INTO mempool (tx_id, timestamp, op, data, sender, signature, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (
                tx_id,
                tx.get('timestamp', 0),
                tx.get('op', ''),
                json.dumps(tx.get('data', {})),
                tx.get('sender', ''),
                tx.get('signature', ''),
                int(time.time())
            )
        )

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    logger.info(f"Migrated {len(mempool)} transactions from {json_path} to {db_path}")

def main():
    """
    Main function to initialize the Onnyx database.
    """
    # Define paths
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    data_dir = os.path.join(root_dir, 'data')
    runtime_dir = os.path.join(root_dir, 'runtime')
    schema_path = os.path.join(root_dir, 'shared', 'schemas', 'production_schema.sql')
    db_path = os.path.join(root_dir, 'shared', 'db', 'onnyx.db')

    # Initialize the database
    init_db(db_path, schema_path)

    # Migrate data from JSON files to the database
    migrate_identities(os.path.join(data_dir, 'identities.json'), db_path)
    migrate_tokens(os.path.join(data_dir, 'tokens.json'), db_path)
    migrate_blockchain(os.path.join(data_dir, 'blockchain.json'), db_path)
    migrate_mempool(os.path.join(data_dir, 'mempool.json'), db_path)

    logger.info("Onnyx database initialized successfully.")

if __name__ == "__main__":
    main()
