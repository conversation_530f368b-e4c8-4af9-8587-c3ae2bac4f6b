#!/usr/bin/env python3
"""
ONNYX Platform Enhanced Scroll Effects Test
Tests the implementation of scroll effects and logo updates across all platform pages.
"""

import os
import sys
import requests
import time
from pathlib import Path

def test_scroll_effects_files():
    """Test that scroll effects files exist and are properly integrated."""
    print("🔍 Testing Scroll Effects Files...")
    
    files_to_check = [
        "web/static/js/scroll-effects.js",
        "web/static/css/main.css",
        "web/templates/base.html"
    ]
    
    results = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file_path} ({size} bytes)")
            results.append(True)
        else:
            print(f"  ❌ {file_path} (missing)")
            results.append(False)
    
    return all(results)

def test_css_scroll_classes():
    """Test that CSS scroll effect classes are defined."""
    print("\n🎨 Testing CSS Scroll Effect Classes...")
    
    css_file = "web/static/css/main.css"
    if not os.path.exists(css_file):
        print(f"  ❌ CSS file not found: {css_file}")
        return False
    
    with open(css_file, 'r') as f:
        css_content = f.read()
    
    scroll_classes = [
        ".scroll-progress",
        ".scroll-animate",
        ".blur-on-scroll",
        ".scroll-snap-container",
        ".glow-on-scroll",
        ".nav-scroll-enhanced",
        ".floating-particle",
        "progressGlow",
        "floatEnhanced"
    ]
    
    results = []
    for class_name in scroll_classes:
        if class_name in css_content:
            print(f"  ✅ {class_name} found")
            results.append(True)
        else:
            print(f"  ❌ {class_name} missing")
            results.append(False)
    
    return all(results)

def test_javascript_integration():
    """Test that JavaScript scroll effects are properly integrated."""
    print("\n📜 Testing JavaScript Integration...")
    
    js_file = "web/static/js/scroll-effects.js"
    if not os.path.exists(js_file):
        print(f"  ❌ JavaScript file not found: {js_file}")
        return False
    
    with open(js_file, 'r') as f:
        js_content = f.read()
    
    js_features = [
        "ONNYXScrollEffects",
        "createScrollProgressBar",
        "initIntersectionObserver",
        "initBlurEffects",
        "initSmoothScrolling",
        "initNavigationEffects",
        "prefers-reduced-motion"
    ]
    
    results = []
    for feature in js_features:
        if feature in js_content:
            print(f"  ✅ {feature} implemented")
            results.append(True)
        else:
            print(f"  ❌ {feature} missing")
            results.append(False)
    
    return all(results)

def test_base_template_integration():
    """Test that base template includes scroll effects."""
    print("\n🏗️ Testing Base Template Integration...")
    
    template_file = "web/templates/base.html"
    if not os.path.exists(template_file):
        print(f"  ❌ Template file not found: {template_file}")
        return False
    
    with open(template_file, 'r') as f:
        template_content = f.read()
    
    integrations = [
        "scroll-effects.js",
        "onnyx_logo.png",
        "scroll-behavior: smooth"
    ]
    
    results = []
    for integration in integrations:
        if integration in template_content:
            print(f"  ✅ {integration} integrated")
            results.append(True)
        else:
            print(f"  ❌ {integration} missing")
            results.append(False)
    
    return all(results)

def test_logo_updates():
    """Test that logo references are updated correctly."""
    print("\n🎨 Testing Logo Updates...")
    
    # Check that new logo file exists
    logo_file = "web/static/images/onnyx_logo.png"
    if not os.path.exists(logo_file):
        print(f"  ❌ Logo file not found: {logo_file}")
        return False
    
    print(f"  ✅ Logo file exists: {logo_file}")
    
    # Check template files for correct logo references
    template_files = [
        "web/templates/base.html",
        "web/templates/index.html",
        "web/templates/auth/login.html"
    ]
    
    results = [True]  # Logo file exists
    for template_file in template_files:
        if os.path.exists(template_file):
            with open(template_file, 'r') as f:
                content = f.read()
            
            if "onnyx_logo.png" in content:
                print(f"  ✅ {template_file}: Logo reference found")
                results.append(True)
            else:
                print(f"  ❌ {template_file}: No logo reference")
                results.append(False)
        else:
            print(f"  ⚠️  {template_file}: File not found")
            results.append(False)
    
    return all(results)

def test_web_pages_scroll_effects():
    """Test that web pages load and include scroll effects."""
    print("\n🌐 Testing Web Page Scroll Effects...")
    
    pages_to_test = [
        ("Homepage", "http://127.0.0.1:5000/"),
        ("Explorer", "http://127.0.0.1:5000/explorer/"),
        ("Sela Directory", "http://127.0.0.1:5000/sela/"),
        ("Dashboard", "http://127.0.0.1:5000/dashboard/")
    ]
    
    results = []
    for name, url in pages_to_test:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                content = response.text
                
                # Check for scroll effects integration
                scroll_effects_found = "scroll-effects.js" in content
                logo_found = "onnyx_logo.png" in content
                css_found = "scroll-progress" in content or "scroll-animate" in content
                
                if scroll_effects_found and logo_found:
                    print(f"  ✅ {name}: Scroll effects and logo integrated")
                    results.append(True)
                elif scroll_effects_found:
                    print(f"  ⚠️  {name}: Scroll effects found, logo check needed")
                    results.append(True)
                else:
                    print(f"  ❌ {name}: Scroll effects not found")
                    results.append(False)
            else:
                print(f"  ❌ {name}: HTTP {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def test_performance_optimizations():
    """Test that performance optimizations are in place."""
    print("\n⚡ Testing Performance Optimizations...")
    
    css_file = "web/static/css/main.css"
    js_file = "web/static/js/scroll-effects.js"
    
    optimizations = []
    
    # Check CSS optimizations
    if os.path.exists(css_file):
        with open(css_file, 'r') as f:
            css_content = f.read()
        
        css_optimizations = [
            "will-change",
            "transform: translateZ(0)",
            "backface-visibility: hidden",
            "prefers-reduced-motion"
        ]
        
        for opt in css_optimizations:
            if opt in css_content:
                print(f"  ✅ CSS: {opt}")
                optimizations.append(True)
            else:
                print(f"  ❌ CSS: {opt} missing")
                optimizations.append(False)
    
    # Check JS optimizations
    if os.path.exists(js_file):
        with open(js_file, 'r') as f:
            js_content = f.read()
        
        js_optimizations = [
            "passive: true",
            "IntersectionObserver",
            "requestAnimationFrame",
            "prefers-reduced-motion"
        ]
        
        for opt in js_optimizations:
            if opt in js_content:
                print(f"  ✅ JS: {opt}")
                optimizations.append(True)
            else:
                print(f"  ❌ JS: {opt} missing")
                optimizations.append(False)
    
    return all(optimizations)

def main():
    """Run all scroll effects tests."""
    print("🚀 ONNYX ENHANCED SCROLL EFFECTS TEST")
    print("=" * 50)
    
    tests = [
        ("Scroll Effects Files", test_scroll_effects_files),
        ("CSS Scroll Classes", test_css_scroll_classes),
        ("JavaScript Integration", test_javascript_integration),
        ("Base Template Integration", test_base_template_integration),
        ("Logo Updates", test_logo_updates),
        ("Web Page Integration", test_web_pages_scroll_effects),
        ("Performance Optimizations", test_performance_optimizations)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"\n❌ {test_name} test failed: {e}")
            results.append(False)
    
    print("\n📊 TEST SUMMARY")
    print("-" * 30)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All scroll effects tests PASSED!")
        print("✨ Enhanced scroll effects successfully implemented!")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
