{% extends "base.html" %}

{% block title %}Dashboard Overview - ONNYX Platform{% endblock %}

{% block content %}
<div class="dashboard-content hero-gradient cyber-grid relative py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Header Section -->
        <div class="text-center mb-16">
            <!-- ONNYX Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Command Center</span>
            </h1>
            <p class="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                Welcome back, {{ current_user.name }}. Your ONNYX network control dashboard.
            </p>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                <div class="text-3xl font-orbitron font-bold text-cyber-cyan-glow mb-2">{{ stats.active_selas }}</div>
                <div class="text-sm text-text-tertiary uppercase tracking-wider">Active Validators</div>
            </div>
            <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                <div class="text-3xl font-orbitron font-bold text-cyber-purple-glow mb-2">{{ stats.total_blocks_mined }}</div>
                <div class="text-sm text-text-tertiary uppercase tracking-wider">Blocks Mined</div>
            </div>
            <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                <div class="text-3xl font-orbitron font-bold text-cyber-blue-glow mb-2">{{ stats.total_mining_power }}x</div>
                <div class="text-sm text-text-tertiary uppercase tracking-wider">Mining Power</div>
            </div>
            <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                <div class="text-3xl font-orbitron font-bold text-green-400 mb-2" style="text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);">{{ stats.total_mining_rewards|round(2) }}</div>
                <div class="text-sm text-text-tertiary uppercase tracking-wider">Mining Rewards</div>
            </div>
        </div>

        <!-- Mining Tier Status -->
        {% if user_selas %}
        <div class="glass-card p-6 mb-16 neuro-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-white">Mining Tier: {{ stats.highest_mining_tier|title }}</h3>
                        <p class="text-text-secondary">
                            {% if stats.highest_mining_tier == 'basic' %}
                                Standard CPU mining with 1x reward multiplier
                            {% elif stats.highest_mining_tier == 'optimized' %}
                                ONNYX Optimized miner with 2x-5x reward multiplier
                            {% elif stats.highest_mining_tier == 'pro' %}
                                ONNYX Pro validator with 5x+ reward multiplier
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-orbitron font-bold text-cyber-cyan-glow">{{ stats.total_mining_power }}x</div>
                    <div class="text-sm text-text-tertiary">Total Power</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Identity Information -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Identity Card -->
                <div class="glass-card p-8 neuro-card">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">🔐 Identity Profile</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Identity ID</label>
                            <div class="p-3 glass-card rounded-lg font-mono text-sm text-cyber-cyan break-all">
                                {{ current_user.identity_id }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Name</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.name }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Email</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.email }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Status</label>
                            <div class="p-3 glass-card rounded-lg">
                                <span class="badge-success">{{ current_user.status|title }}</span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Role</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.role or 'Not specified' }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Registered</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.created_at|timestamp_to_date if current_user.created_at else 'Unknown' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="glass-card p-8 neuro-card">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-orbitron font-bold text-cyber-purple">💫 Recent Activity</h2>
                        <a href="{{ url_for('dashboard.transactions') }}" class="text-cyber-purple hover:text-white transition-colors text-sm">
                            View All →
                        </a>
                    </div>

                    {% if user_transactions %}
                        <div class="space-y-4">
                            {% for tx in user_transactions[:5] %}
                            <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300 data-stream">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="text-sm font-orbitron font-bold text-white">{{ tx.op or 'Transaction' }}</span>
                                            <span class="badge-success">{{ tx.status|title }}</span>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            TX: <span class="font-mono text-cyber-purple hash-truncate">{{ tx.tx_id }}</span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500">{{ tx.created_at|timestamp_to_time if tx.created_at else 'Unknown' }}</div>
                                        <div class="text-xs text-gray-600">{{ tx.created_at|timestamp_to_date if tx.created_at else '' }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500">No transactions yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Quick Actions -->
                <div class="glass-card p-8 neuro-card">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-6">🚀 Quick Actions</h2>

                    <div class="space-y-4">
                        <a href="{{ url_for('auth.register_sela') }}"
                           class="w-full glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            🏢 Register Validator
                        </a>

                        <button onclick="mineBlock()"
                                class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            ⛏️ Mine Block
                        </button>

                        <a href="{{ url_for('explorer.index') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            🔍 Explore Network
                        </a>

                        <a href="{{ url_for('sela.directory') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                           🌐 Validator Directory
                        </a>
                    </div>
                </div>

                <!-- My Validators -->
                <div class="glass-card p-8 neuro-card">
                    <h2 class="text-2xl font-orbitron font-bold text-green-400 mb-6">🏢 My Validators</h2>

                    {% if user_selas %}
                        <div class="space-y-4">
                            {% for sela in user_selas %}
                            <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-orbitron font-bold text-white">{{ sela.name }}</h3>
                                    <div class="flex items-center space-x-2">
                                        {% if sela.mining_tier == 'pro' %}
                                        <span class="badge-success bg-gradient-to-r from-cyber-cyan to-cyber-purple">PRO {{ sela.mining_power }}x</span>
                                        {% elif sela.mining_tier == 'optimized' %}
                                        <span class="badge-success bg-gradient-to-r from-cyber-blue to-cyber-cyan">OPT {{ sela.mining_power }}x</span>
                                        {% else %}
                                        <span class="badge-success">BASIC {{ sela.mining_power }}x</span>
                                        {% endif %}
                                        <span class="badge-success">{{ sela.status|title }}</span>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-400 space-y-1">
                                    <div>Category: {{ sela.category }}</div>
                                    <div>ID: <span class="font-mono text-cyber-cyan hash-truncate">{{ sela.sela_id }}</span></div>
                                    <div class="flex items-center justify-between">
                                        <span>Rewards: {{ sela.mining_rewards_earned|round(2) }}</span>
                                        <span>Blocks: {{ sela.blocks_mined }}</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 text-sm mb-3">No validators registered</p>
                            <a href="{{ url_for('auth.register_sela') }}"
                               class="text-cyber-cyan hover:text-white text-sm transition-colors">
                                Register your first validator →
                            </a>
                        </div>
                    {% endif %}
                </div>

                <!-- Network Status -->
                <div class="glass-card p-8 neuro-card">
                    <h2 class="text-2xl font-orbitron font-bold text-yellow-400 mb-6">📡 Network Status</h2>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Network</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-green-400 font-orbitron">ONLINE</span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Latest Block</span>
                            <span class="text-cyber-cyan font-mono">{{ latest_block or 'N/A' }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Active Validators</span>
                            <span class="text-cyber-purple font-mono">{{ total_validators or 0 }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Network Hashrate</span>
                            <span class="text-cyber-blue font-mono">{{ network_hashrate or 'Auto' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function mineBlock() {
    try {
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = '⛏️ Mining...';
        button.disabled = true;

        const response = await fetch('/api/mine-block', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            Onnyx.utils.showNotification(`Block mined! Height: ${result.block_height}`, 'success');
        } else {
            Onnyx.utils.showNotification(result.error || 'Mining failed', 'error');
        }
    } catch (error) {
        console.error('Mining error:', error);
        Onnyx.utils.showNotification('Mining failed', 'error');
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
}
</script>
{% endblock %}
