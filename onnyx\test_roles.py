"""
Test the Onnyx Role Progression Logic

This script tests the Onnyx Role Progression Logic by assigning roles based on Etzem scores.
"""

import os
import sys
import json
import time

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from identity.registry import IdentityRegistry
from governance.etzem_engine import EtzemEngine
from governance.role_progression import RoleProgression

def setup_test_data():
    """Set up test data for the Role Progression test."""
    # Create identity registry
    identity_registry = IdentityRegistry()
    
    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "<PERSON>", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: Alice")
    
    try:
        bob = identity_registry.register_identity("bob", "<PERSON>", "0x987654321fedcba")
        print(f"Created identity: <PERSON>")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: <PERSON>")
    
    try:
        charlie = identity_registry.register_identity("char<PERSON>", "<PERSON>", "0xabcdef123456789")
        print(f"Created identity: <PERSON>")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")
    
    print("\nTest identities:")
    print(f"  Alice: {alice['identity_id']}")
    print(f"  Bob: {bob['identity_id']}")
    print(f"  Charlie: {charlie['identity_id']}")
    
    # Create Etzem engine
    etzem_engine = EtzemEngine()
    
    # Create Role Progression
    role_progression = RoleProgression(identity_registry, etzem_engine)
    
    return identity_registry, etzem_engine, role_progression

def test_role_progression():
    """Test the Role Progression functionality."""
    # Set up test data
    identity_registry, etzem_engine, role_progression = setup_test_data()
    
    # Add STAKER badge to Alice
    print("\nAdding STAKER badge to Alice...")
    identity_registry.add_badge("alice", "STAKER")
    
    # Set Etzem scores
    print("\nSetting Etzem scores...")
    
    # Alice: High Etzem score (90) and has STAKER badge
    # Should be eligible for all roles
    alice_etzem = {
        "consistency_score": 90,
        "token_impact": 85,
        "reputation": 95,
        "labor_contribution": 90,
        "final_etzem": 90
    }
    
    # Bob: Medium Etzem score (60)
    # Should be eligible for PROPOSAL_ELIGIBLE
    bob_etzem = {
        "consistency_score": 60,
        "token_impact": 55,
        "reputation": 65,
        "labor_contribution": 60,
        "final_etzem": 60
    }
    
    # Charlie: Low Etzem score (30)
    # Should not be eligible for any roles
    charlie_etzem = {
        "consistency_score": 30,
        "token_impact": 25,
        "reputation": 35,
        "labor_contribution": 30,
        "final_etzem": 30
    }
    
    # Mock the calculate_etzem_score method
    etzem_engine.calculate_etzem_score = lambda identity_id: (
        alice_etzem if identity_id == "alice" else
        bob_etzem if identity_id == "bob" else
        charlie_etzem
    )
    
    # Check role eligibility
    print("\nChecking role eligibility...")
    
    alice_eligibility = role_progression.check_role_eligibility("alice")
    bob_eligibility = role_progression.check_role_eligibility("bob")
    charlie_eligibility = role_progression.check_role_eligibility("charlie")
    
    print("\nAlice's role eligibility:")
    for role, eligible in alice_eligibility.items():
        print(f"  {role}: {eligible}")
    
    print("\nBob's role eligibility:")
    for role, eligible in bob_eligibility.items():
        print(f"  {role}: {eligible}")
    
    print("\nCharlie's role eligibility:")
    for role, eligible in charlie_eligibility.items():
        print(f"  {role}: {eligible}")
    
    # Update role badges
    print("\nUpdating role badges...")
    
    alice = role_progression.update_role_badges("alice")
    bob = role_progression.update_role_badges("bob")
    charlie = role_progression.update_role_badges("charlie")
    
    print("\nAlice's badges:")
    print(f"  {alice.get('badges', [])}")
    
    print("\nBob's badges:")
    print(f"  {bob.get('badges', [])}")
    
    print("\nCharlie's badges:")
    print(f"  {charlie.get('badges', [])}")
    
    print("\nRole Progression test completed successfully!")

if __name__ == "__main__":
    test_role_progression()
