# Yovel: Token Mint Limiter

Yovel (יוֹבֵל) is a token mint limiter based on trust, reputation, and Zeman - the safeguard against pump-and-dump abuse and unchecked minting.

## Overview

In ancient law, the Yovel (Jubilee) was a time when debts were reset, slaves freed, and land returned to families. In Onnyx, it represents economic reset rules to enforce fairness, contribution, and sustainability.

Yovel is a cap on how many tokens an identity can mint, based on:

- **Zeman** (Time/labor contributed)
- **Etzem** (Reputation score)
- **Trust level** (badge, history, stake held)

## Components

### YovelLimiter

The `YovelLimiter` class determines token minting caps based on trust, reputation, and Zeman. It provides methods for:

- Calculating minting caps
- Checking mint allowances
- Getting and setting parameters
- Getting top minters

## Minting Cap Calculation

The minting cap is calculated using the following formula:

```
mint_cap = base_cap + zeman_bonus + reputation_bonus + badge_bonus + stake_bonus
```

Where:

- **base_cap**: Base minting cap for all identities (1,000 tokens)
- **zeman_bonus**: <PERSON>eman hours × 100
- **reputation_bonus**: Reputation score × 10
- **badge_bonus**: Bonuses for having specific badges
  - TRUSTED badge: +5,000 tokens
  - VERIFIED badge: +2,000 tokens
  - DEVELOPER badge: +3,000 tokens
  - COMMUNITY badge: +1,500 tokens
- **stake_bonus**: ONX tokens held × 5

## Time-Based Limits

In addition to the overall minting cap, there are also time-based limits:

- **Daily limit**: Maximum 10,000 tokens per day
- **Weekly limit**: Maximum 50,000 tokens per week
- **Monthly limit**: Maximum 200,000 tokens per month

## API Endpoints

### Get Minting Cap

```
GET /yovel/mint_cap?identity_id=string
```

### Check Mint Allowance

```
POST /yovel/check_mint_allowance
```

Request body:
```json
{
  "identity_id": "string",
  "token_id": "string",
  "amount": 0
}
```

### Get Parameters

```
GET /yovel/parameters
```

### Set Parameter

```
POST /yovel/set_parameter
```

Request body:
```json
{
  "parameter": "string",
  "value": "any",
  "admin_key": "string"
}
```

### Get Top Minters

```
GET /yovel/top_minters?limit=10
```

### Get Mint Cap Explanation

```
GET /yovel/mint_cap_explanation?identity_id=string
```

## Example Output

```json
{
  "identity_id": "1a2b3c...",
  "name": "Example Identity",
  "mint_cap": 21800,
  "components": {
    "base_cap": 1000,
    "zeman_bonus": 10000,
    "reputation_bonus": 1000,
    "badge_bonus": 5000,
    "stake_bonus": 4800
  },
  "limits": {
    "daily": 10000,
    "weekly": 21800,
    "monthly": 21800
  }
}
```

In this example, the identity has:
- 100 Zeman hours → 10,000 tokens
- Reputation score 100 → 1,000 tokens
- TRUSTED badge → +5,000 tokens
- 960 ONX tokens → 4,800 tokens

## Integration with Token Minting

The Yovel limiter is integrated with the token minting process to enforce the minting caps. When a token is minted, the Yovel limiter is consulted to check if the identity is allowed to mint the requested amount of tokens.

```python
# Check if the identity is allowed to mint the requested amount of tokens
result = yovel_limiter.check_mint_allowance(
    identity_id=identity_id,
    token_id=token_id,
    amount=amount
)

if not result["allowed"]:
    raise Exception(f"Mint not allowed: {result['reason']}")
```

## Future Enhancements

- **Dynamic Parameters**: Adjust parameters based on network activity
- **Governance**: Allow governance to adjust parameters
- **Token-Specific Limits**: Set different limits for different token types
- **Time-Based Decay**: Reduce minting caps over time to encourage regular contribution
- **Integration with VM**: Integrate with the OnnyxScript VM to enforce minting caps at the VM level
