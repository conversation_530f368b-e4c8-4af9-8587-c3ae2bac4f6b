# src/routes/explorer.py

from fastapi import APIRouter, HTTPException, Query
from src.chain.txlog import TxLogger
from src.tokens.registry import TokenRegistry
from src.identity.registry import IdentityRegistry
from src.tokens.ledger import TokenLedger
from src.node.node import OnnyxNode

# Initialize components
txlog = TxLogger()
token_registry = TokenRegistry()
identity_registry = IdentityRegistry()
ledger = TokenLedger(token_registry=token_registry)
node = OnnyxNode()

# Create router
explorer_router = APIRouter(prefix="/explorer", tags=["explorer"])

@explorer_router.get("/stats")
def get_stats():
    """
    Get overall statistics for the Onnyx blockchain.
    """
    # Get blockchain stats
    try:
        blockchain = node.blockchain
        latest_block = blockchain.get_latest_block()
        height = len(blockchain.chain) if hasattr(blockchain, 'chain') else 0
        latest_block_hash = latest_block.hash if latest_block else None
        latest_block_timestamp = latest_block.timestamp if latest_block else None
    except Exception:
        # If there's an error getting blockchain stats, use default values
        height = 0
        latest_block_hash = None
        latest_block_timestamp = None

    # Get token stats
    tokens = token_registry.list_tokens()
    token_count = len(tokens)

    # Get identity stats
    identities = identity_registry.get_all_identities()
    identity_count = len(identities)

    # Get transaction stats
    transactions = txlog.get_all()
    transaction_count = len(transactions)

    # Calculate total supply of all tokens
    total_supply = sum(token.supply for token in tokens)

    # Calculate total holders
    holder_count = 0
    for address, balances in ledger.balances.items():
        if any(balance > 0 for balance in balances.values()):
            holder_count += 1

    return {
        "blockchain": {
            "height": height,
            "latest_block_hash": latest_block_hash,
            "latest_block_timestamp": latest_block_timestamp,
        },
        "tokens": {
            "count": token_count,
            "total_supply": total_supply,
            "holder_count": holder_count
        },
        "identities": {
            "count": identity_count
        },
        "transactions": {
            "count": transaction_count
        }
    }

@explorer_router.get("/search")
def search(query: str):
    """
    Search for tokens, identities, or transactions by ID or name.
    """
    results = {
        "tokens": [],
        "identities": [],
        "transactions": []
    }

    # Search for tokens
    tokens = token_registry.list_tokens()
    for token in tokens:
        if (
            query.lower() in token.token_id.lower() or
            query.lower() in token.name.lower() or
            query.lower() in token.symbol.lower()
        ):
            results["tokens"].append({
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_identity": token.creator_identity,
                "type": token.token_type.value,
                "supply": token.supply
            })

    # Search for identities
    identities = identity_registry.get_all_identities()
    for identity in identities:
        if (
            query.lower() in identity.identity_id.lower() or
            query.lower() in identity.name.lower()
        ):
            results["identities"].append({
                "identity_id": identity.identity_id,
                "name": identity.name,
                "public_key": identity.public_key
            })

    # Search for transactions
    transactions = txlog.get_all()
    for tx in transactions:
        if query.lower() in tx["txid"].lower():
            results["transactions"].append(tx)

    return results

@explorer_router.get("/recent")
def get_recent():
    """
    Get recent activity on the Onnyx blockchain.
    """
    # Get recent transactions
    recent_transactions = txlog.get_all(limit=10)

    # Get recent tokens
    tokens = token_registry.list_tokens()
    recent_tokens = sorted(tokens, key=lambda t: t.creation_time if hasattr(t, 'creation_time') else 0, reverse=True)[:5]

    # Get recent identities
    identities = identity_registry.get_all_identities()
    recent_identities = sorted(identities, key=lambda i: i.creation_time if hasattr(i, 'creation_time') else 0, reverse=True)[:5]

    return {
        "transactions": recent_transactions,
        "tokens": [
            {
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "creator_identity": token.creator_identity,
                "type": token.token_type.value,
                "supply": token.supply
            }
            for token in recent_tokens
        ],
        "identities": [
            {
                "identity_id": identity.identity_id,
                "name": identity.name,
                "public_key": identity.public_key
            }
            for identity in recent_identities
        ]
    }
