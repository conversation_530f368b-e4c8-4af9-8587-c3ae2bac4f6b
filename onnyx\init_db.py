#!/usr/bin/env python3
"""
Initialize the Onnyx Database

This script initializes the Onnyx database by creating the schema and migrating data from JSON files.
"""

import os
import sys
import json
import time
import logging
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("init_db.log")
    ]
)
logger = logging.getLogger("onnyx.init_db")

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.db.init import initialize_database
from src.db.manager import db_manager

def init_db(force=False):
    """Initialize the database."""
    logger.info("Initializing database...")

    # Initialize the database using our new schema
    initialize_database(force)

    logger.info("Database initialized successfully.")

def migrate_identities():
    """Migrate identities from JSON to SQLite."""
    logger.info("Migrating identities...")

    # Load identities from JSON
    identities_path = os.path.join(os.path.dirname(__file__), "data", "identities.json")
    if not os.path.exists(identities_path):
        logger.warning(f"Identities file not found: {identities_path}")
        return

    try:
        with open(identities_path, "r") as f:
            identities = json.load(f)

        # Get a database connection
        conn = db_manager.get_connection()
        cursor = conn.cursor()

        # Insert identities into the database
        for identity_id, identity in identities.items():
            # Insert the identity
            cursor.execute('''
            INSERT INTO identities (identity_id, name, public_key, nation, metadata, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                identity_id,
                identity.get("name", identity_id),
                identity.get("public_key", ""),
                identity.get("nation", None),
                json.dumps(identity.get("metadata", {})),
                identity.get("created_at", int(time.time()))
            ))

            # Insert badges
            for badge in identity.get("badges", []):
                cursor.execute('''
                INSERT INTO identity_badges (identity_id, badge, awarded_at)
                VALUES (?, ?, ?)
                ''', (
                    identity_id,
                    badge,
                    int(time.time())
                ))

        # Commit the changes
        conn.commit()

        logger.info(f"Migrated {len(identities)} identities.")
    except Exception as e:
        logger.error(f"Error migrating identities: {str(e)}")

def migrate_selas():
    """Migrate Selas from JSON to SQLite."""
    logger.info("Migrating Selas...")

    # Load Selas from JSON
    selas_path = os.path.join(os.path.dirname(__file__), "data", "selas.json")
    if not os.path.exists(selas_path):
        logger.warning(f"Selas file not found: {selas_path}")
        return

    try:
        with open(selas_path, "r") as f:
            selas = json.load(f)

        # Insert Selas into the database
        for sela_id, sela in selas.items():
            # Insert the Sela
            db.insert("selas", {
                "id": sela_id,
                "name": sela.get("name", sela_id),
                "founder": sela.get("founder", ""),
                "type": sela.get("type", "BUSINESS"),
                "token_type": sela.get("token_type", ""),
                "metadata": json.dumps(sela.get("metadata", {})),
                "created_at": sela.get("created_at", int(time.time())),
                "updated_at": sela.get("updated_at", int(time.time()))
            })

            # Insert members
            for member_id, role in sela.get("members", {}).items():
                db.insert("sela_members", {
                    "sela_id": sela_id,
                    "identity_id": member_id,
                    "role": role,
                    "joined_at": int(time.time())
                })

        logger.info(f"Migrated {len(selas)} Selas.")
    except Exception as e:
        logger.error(f"Error migrating Selas: {str(e)}")

def migrate_tokens():
    """Migrate tokens from JSON to SQLite."""
    logger.info("Migrating tokens...")

    # Load tokens from JSON
    tokens_path = os.path.join(os.path.dirname(__file__), "data", "tokens.json")
    if not os.path.exists(tokens_path):
        logger.warning(f"Tokens file not found: {tokens_path}")
        return

    try:
        with open(tokens_path, "r") as f:
            tokens = json.load(f)

        # Insert tokens into the database
        for token_id, token in tokens.items():
            # Insert the token
            db.insert("tokens", {
                "id": token_id,
                "name": token.get("name", token_id),
                "symbol": token.get("symbol", token_id),
                "decimals": token.get("decimals", 18),
                "supply": token.get("supply", 0),
                "creator": token.get("creator", ""),
                "category": token.get("category", ""),
                "metadata": json.dumps(token.get("metadata", {})),
                "created_at": token.get("created_at", int(time.time())),
                "updated_at": token.get("updated_at", int(time.time()))
            })

        logger.info(f"Migrated {len(tokens)} tokens.")
    except Exception as e:
        logger.error(f"Error migrating tokens: {str(e)}")

def migrate_balances():
    """Migrate balances from JSON to SQLite."""
    logger.info("Migrating balances...")

    # Load balances from JSON
    balances_path = os.path.join(os.path.dirname(__file__), "data", "balances.json")
    if not os.path.exists(balances_path):
        logger.warning(f"Balances file not found: {balances_path}")
        return

    try:
        with open(balances_path, "r") as f:
            balances = json.load(f)

        # Insert balances into the database
        for identity_id, tokens in balances.items():
            for token_id, amount in tokens.items():
                db.insert("balances", {
                    "identity_id": identity_id,
                    "token_id": token_id,
                    "amount": amount,
                    "updated_at": int(time.time())
                })

        logger.info(f"Migrated balances for {len(balances)} identities.")
    except Exception as e:
        logger.error(f"Error migrating balances: {str(e)}")

def migrate_blocks():
    """Migrate blocks from JSON to SQLite."""
    logger.info("Migrating blocks...")

    # Load blocks from JSON
    blocks_path = os.path.join(os.path.dirname(__file__), "data", "blocks.json")
    if not os.path.exists(blocks_path):
        logger.warning(f"Blocks file not found: {blocks_path}")
        return

    try:
        with open(blocks_path, "r") as f:
            blocks = json.load(f)

        # Insert blocks into the database
        for block in blocks:
            # Insert the block
            db.insert("blocks", {
                "index": block["index"],
                "timestamp": block["timestamp"],
                "proposer": block.get("proposer", block.get("signed_by", "")),
                "hash": block["hash"],
                "previous_hash": block["previous_hash"],
                "nonce": block.get("nonce", 0),
                "signature": block.get("signature", ""),
                "signed_by": block.get("signed_by", "")
            })

            # Insert transactions
            for tx in block.get("transactions", []):
                db.insert("transactions", {
                    "txid": tx.get("txid", f"tx_{int(time.time())}_{block['index']}"),
                    "block_index": block["index"],
                    "timestamp": tx.get("timestamp", block["timestamp"]),
                    "type": tx.get("type", ""),
                    "op": tx.get("op", ""),
                    "sender": tx.get("from", None),
                    "recipient": tx.get("to", None),
                    "token_id": tx.get("token_id", None),
                    "amount": tx.get("amount", None),
                    "data": json.dumps(tx.get("data", {}))
                })

        logger.info(f"Migrated {len(blocks)} blocks.")
    except Exception as e:
        logger.error(f"Error migrating blocks: {str(e)}")

def migrate_event_logs():
    """Migrate event logs from JSON to SQLite."""
    logger.info("Migrating event logs...")

    # Load event logs from JSON
    event_logs_path = os.path.join(os.path.dirname(__file__), "data", "event_log.json")
    if not os.path.exists(event_logs_path):
        logger.warning(f"Event logs file not found: {event_logs_path}")
        return

    try:
        with open(event_logs_path, "r") as f:
            event_logs = json.load(f)

        # Insert event logs into the database
        for log in event_logs:
            db.insert("event_logs", {
                "block_index": log["block_index"],
                "block_hash": log["block_hash"],
                "timestamp": log["timestamp"],
                "timestamp_human": log["timestamp_human"],
                "proposer": log["proposer"],
                "tx_count": log["tx_count"],
                "token_mints": log["token_mints"],
                "token_transfers": log["token_transfers"],
                "token_burns": log["token_burns"],
                "proposals": log["proposals"],
                "votes": log["votes"],
                "identities": log["identities"],
                "reputation_grants": log["reputation_grants"],
                "stakes": log["stakes"],
                "rewards": log["rewards"],
                "notable_events": json.dumps(log["notable_events"])
            })

        logger.info(f"Migrated {len(event_logs)} event logs.")
    except Exception as e:
        logger.error(f"Error migrating event logs: {str(e)}")

def migrate_chain_parameters():
    """Migrate chain parameters from JSON to SQLite."""
    logger.info("Migrating chain parameters...")

    # Load chain parameters from JSON
    chain_params_path = os.path.join(os.path.dirname(__file__), "data", "chain_params.json")
    if not os.path.exists(chain_params_path):
        logger.warning(f"Chain parameters file not found: {chain_params_path}")
        return

    try:
        with open(chain_params_path, "r") as f:
            chain_params = json.load(f)

        # Insert chain parameters into the database
        for key, value in chain_params.items():
            db.insert("chain_parameters", {
                "key": key,
                "value": json.dumps(value),
                "updated_at": int(time.time())
            })

        logger.info(f"Migrated {len(chain_params)} chain parameters.")
    except Exception as e:
        logger.error(f"Error migrating chain parameters: {str(e)}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Initialize the Onnyx database.')
    parser.add_argument('--force', action='store_true', help='Force reinitialization of the database')
    parser.add_argument('--migrate', action='store_true', help='Migrate data from JSON files')
    args = parser.parse_args()

    # Create the data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(__file__), 'data')
    os.makedirs(data_dir, exist_ok=True)

    # Initialize the database
    init_db(force=args.force)

    # Migrate data if requested
    if args.migrate:
        logger.info("Starting data migration...")
        migrate_identities()
        logger.info("Data migration completed successfully.")

    logger.info("Database initialization complete.")

if __name__ == "__main__":
    main()
