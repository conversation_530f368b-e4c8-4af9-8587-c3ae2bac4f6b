#!/usr/bin/env python3
"""
ONNYX Mining Monitor

Real-time monitoring of blockchain mining progress.
"""

import os
import sys
import time
import requests
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def get_mining_stats():
    """Get current mining statistics."""
    try:
        stats = {
            'blocks': db.query_one("SELECT COUNT(*) as count FROM blocks")['count'],
            'transactions': db.query_one("SELECT COUNT(*) as count FROM transactions")['count'],
            'pending_txs': db.query_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'pending'")['count'],
            'confirmed_txs': db.query_one("SELECT COUNT(*) as count FROM transactions WHERE status = 'confirmed'")['count'],
            'identities': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
            'selas': db.query_one("SELECT COUNT(*) as count FROM selas")['count']
        }
        
        # Get latest block
        latest_block = db.query_one("SELECT * FROM blocks ORDER BY block_height DESC LIMIT 1")
        if latest_block:
            stats['latest_block'] = {
                'height': latest_block['block_height'],
                'hash': latest_block['block_hash'][:16] + '...',
                'miner': latest_block['miner'],
                'timestamp': latest_block['timestamp']
            }
        
        return stats
    except Exception as e:
        return {'error': str(e)}

def main():
    """Monitor mining progress."""
    print("⛏️  ONNYX MINING MONITOR")
    print("=" * 50)
    print("Press Ctrl+C to stop monitoring\n")
    
    try:
        while True:
            stats = get_mining_stats()
            
            # Clear screen (works on most terminals)
            os.system('cls' if os.name == 'nt' else 'clear')
            
            print("⛏️  ONNYX MINING MONITOR")
            print("=" * 50)
            print(f"🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            if 'error' in stats:
                print(f"❌ Error: {stats['error']}")
            else:
                print(f"📦 Blocks Mined: {stats['blocks']}")
                print(f"📝 Total Transactions: {stats['transactions']}")
                print(f"⏳ Pending Transactions: {stats['pending_txs']}")
                print(f"✅ Confirmed Transactions: {stats['confirmed_txs']}")
                print(f"👤 Identities: {stats['identities']}")
                print(f"🏢 Selas: {stats['selas']}")
                
                if 'latest_block' in stats:
                    latest = stats['latest_block']
                    print()
                    print("🔗 Latest Block:")
                    print(f"   Height: #{latest['height']}")
                    print(f"   Hash: {latest['hash']}")
                    print(f"   Miner: {latest['miner']}")
                    print(f"   Time: {datetime.fromtimestamp(latest['timestamp']).strftime('%H:%M:%S')}")
            
            print()
            print("Press Ctrl+C to stop monitoring...")
            
            time.sleep(5)  # Update every 5 seconds
            
    except KeyboardInterrupt:
        print("\n👋 Mining monitor stopped")
    except Exception as e:
        print(f"\n❌ Monitor error: {e}")

if __name__ == "__main__":
    main()
