# tests/test_explorer.py

import unittest
import sys
import os
from fastapi.testclient import TestClient

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api import app
from src.chain.txlog import Tx<PERSON>ogger
from src.tokens.registry import TokenRegistry
from src.identity.registry import IdentityRegistry

class TestExplorer(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)
        self.txlog = TxLogger()
        self.token_registry = TokenRegistry()
        self.identity_registry = IdentityRegistry()

        # Create some test data
        self._create_test_data()

    def _create_test_data(self):
        """Create some test data for the explorer."""
        # Record some transactions
        self.txlog.record("createidentity", {
            "identity_id": "test_identity",
            "name": "Test Identity",
            "public_key": "test_pubkey"
        })

        self.txlog.record("forktoken", {
            "token_id": "test_token",
            "name": "Test Token",
            "symbol": "TST",
            "creator_identity": "test_identity",
            "token_type": "FORKED",
            "supply": 1000,
            "metadata": {
                "description": "Test token"
            },
            "mintable": True,
            "transferable": True
        })

        self.txlog.record("minttoken", {
            "token_id": "test_token",
            "amount": 500,
            "to_address": "test_identity",
            "caller_identity": "test_identity"
        })

    def test_explorer_stats(self):
        """Test the explorer stats endpoint."""
        response = self.client.get("/explorer/stats")

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertIn("blockchain", response.json())
        self.assertIn("tokens", response.json())
        self.assertIn("identities", response.json())
        self.assertIn("transactions", response.json())

    def test_explorer_search(self):
        """Test the explorer search endpoint."""
        # Search for a token
        response = self.client.get("/explorer/search?query=test")

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertIn("tokens", response.json())
        self.assertIn("identities", response.json())
        self.assertIn("transactions", response.json())

    def test_explorer_recent(self):
        """Test the explorer recent endpoint."""
        response = self.client.get("/explorer/recent")

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertIn("transactions", response.json())
        self.assertIn("tokens", response.json())
        self.assertIn("identities", response.json())

    def test_token_transactions(self):
        """Test the token transactions endpoint."""
        # Get transactions for a token
        response = self.client.get("/token/transactions?token_id=test_token")

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertIn("transactions", response.json())

        # Check that we have at least one transaction
        self.assertGreaterEqual(len(response.json()["transactions"]), 1)

    def test_identity_transactions(self):
        """Test the identity transactions endpoint."""
        # Create an identity through the API
        create_response = self.client.post("/identity/createidentity", json={
            "name": "Test Identity API",
            "public_key": "test_pubkey_api",
            "metadata": {"type": "individual"},
            "message": "test_message",
            "signature": "test_signature"
        })

        # Get the identity ID from the response
        identity_id = create_response.json()["identity_id"]

        # Get transactions for the identity
        response = self.client.get(f"/identity/transactions?identity_id={identity_id}")

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertIn("transactions", response.json())

        # There should be at least one transaction (the createidentity transaction)
        self.assertGreaterEqual(len(response.json()["transactions"]), 1)

if __name__ == "__main__":
    unittest.main()
