#!/usr/bin/env python3
"""
ONNYX Complete Mining System Test
Tests mining with mempool transactions and token rewards.
"""

import sqlite3
import time
import json
from blockchain.consensus.miner import BlockMiner

def show_database_state(title):
    """Display current database state."""
    print(f"\n📊 {title}")
    print("=" * 50)

    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()

        # Show blocks
        cursor.execute("SELECT block_number, block_hash, timestamp FROM blocks ORDER BY block_number")
        blocks = cursor.fetchall()
        print(f"🔗 Blocks ({len(blocks)}):")
        for block in blocks:
            print(f"   Block #{block[0]}: {block[1][:16]}... (time: {block[2]})")

        # Show mempool
        cursor.execute("SELECT transaction_id, transaction_type, amount, token_id, status FROM mempool ORDER BY created_at")
        mempool = cursor.fetchall()
        print(f"\n📝 Mempool ({len(mempool)}):")
        for tx in mempool:
            print(f"   {tx[0]}: {tx[1]} - {tx[2]} {tx[3]} ({tx[4]})")

        # Show token balances
        cursor.execute("""
            SELECT t.identity_id, i.name, t.token_id, t.balance
            FROM tokens t
            JOIN identities i ON t.identity_id = i.identity_id
            ORDER BY t.balance DESC
        """)
        tokens = cursor.fetchall()
        print(f"\n💰 Token Balances ({len(tokens)}):")
        for token in tokens:
            print(f"   {token[1]}: {token[3]} {token[2]}")

        # Show confirmed transactions
        cursor.execute("SELECT COUNT(*) FROM transactions WHERE status = 'confirmed'")
        confirmed_count = cursor.fetchone()[0]
        print(f"\n✅ Confirmed Transactions: {confirmed_count}")

        conn.close()

    except Exception as e:
        print(f"❌ Error showing database state: {e}")

def mine_block_with_transactions():
    """Mine a block that processes pending transactions."""
    print(f"\n⛏️ MINING BLOCK WITH TRANSACTIONS")
    print("=" * 40)

    try:
        # Get the first validator (ONNYX)
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.sela_id, s.identity_id, i.name, m.mining_power
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            JOIN mining m ON s.sela_id = m.sela_id
            WHERE s.status = 'active'
            ORDER BY m.mining_power DESC
            LIMIT 1
        """)

        validator = cursor.fetchone()
        conn.close()

        if not validator:
            print("❌ No validators found")
            return False

        sela_id, identity_id, name, mining_power = validator
        print(f"Mining with: {name} (Power: {mining_power}x)")

        # Initialize miner
        miner = BlockMiner()

        # Get initial state
        initial_stats = miner.get_mining_stats()
        print(f"Initial chain height: {initial_stats['chain_height']}")
        print(f"Initial mempool size: {initial_stats['mempool_size']}")

        # Mine a new block
        print("🔨 Mining new block...")
        block = miner.create_block(identity_id, sela_id)

        print(f"✅ Block mined successfully!")
        print(f"   Block #{block['index']}")
        print(f"   Hash: {block['hash'][:16]}...")
        print(f"   Transactions: {len(block['transactions'])}")
        print(f"   Miner: {name}")

        # Show transaction details
        print(f"\n📝 Block Transactions:")
        for i, tx in enumerate(block['transactions']):
            if isinstance(tx, dict):
                if tx.get('type') == 'reward' or tx.get('op') == 'OP_REWARD':
                    print(f"   {i+1}. Coinbase: {tx.get('amount', 0)} {tx.get('token_id', 'ONX')} → {name}")
                else:
                    tx_type = tx.get('transaction_type', tx.get('type', 'unknown'))
                    amount = tx.get('amount', 0)
                    token = tx.get('token_id', 'ONX')
                    print(f"   {i+1}. {tx_type}: {amount} {token}")
            else:
                print(f"   {i+1}. Transaction: {tx}")

        # Verify final state
        final_stats = miner.get_mining_stats()
        print(f"\nFinal chain height: {final_stats['chain_height']}")
        print(f"Final mempool size: {final_stats['mempool_size']}")

        return True

    except Exception as e:
        print(f"❌ Mining failed: {e}")
        return False

def verify_token_rewards():
    """Verify that mining rewards were properly credited."""
    print(f"\n💰 VERIFYING TOKEN REWARDS")
    print("=" * 30)

    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()

        # Check token balances after mining
        cursor.execute("""
            SELECT t.identity_id, i.name, t.token_id, t.balance, t.updated_at
            FROM tokens t
            JOIN identities i ON t.identity_id = i.identity_id
            WHERE t.balance > 0
            ORDER BY t.balance DESC
        """)

        rewards = cursor.fetchall()

        if rewards:
            print("✅ Mining rewards distributed:")
            for reward in rewards:
                print(f"   {reward[1]}: {reward[3]} {reward[2]} (updated: {reward[4]})")
            conn.close()
            return True
        else:
            print("⚠️ No mining rewards found")
            conn.close()
            return False

    except Exception as e:
        print(f"❌ Error verifying rewards: {e}")
        return False

def test_transaction_processing():
    """Test that mempool transactions were processed."""
    print(f"\n📝 VERIFYING TRANSACTION PROCESSING")
    print("=" * 35)

    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()

        # Check mempool status
        cursor.execute("SELECT status, COUNT(*) FROM mempool GROUP BY status")
        mempool_status = cursor.fetchall()

        print("Mempool transaction status:")
        for status, count in mempool_status:
            print(f"   {status}: {count} transactions")

        # Check if any transactions were moved to confirmed
        cursor.execute("SELECT COUNT(*) FROM transactions WHERE status = 'confirmed'")
        confirmed_count = cursor.fetchone()[0]

        print(f"Confirmed transactions: {confirmed_count}")

        conn.close()

        return True

    except Exception as e:
        print(f"❌ Error checking transaction processing: {e}")
        return False

def main():
    """Test complete mining system with transactions and rewards."""
    print("🚀 ONNYX COMPLETE MINING SYSTEM TEST")
    print("=" * 60)
    print("Testing mining with mempool transactions and token rewards")

    # Show initial state
    show_database_state("INITIAL DATABASE STATE")

    # Mine a block with transactions
    mining_success = mine_block_with_transactions()

    if mining_success:
        # Show state after mining
        show_database_state("DATABASE STATE AFTER MINING")

        # Verify rewards
        rewards_success = verify_token_rewards()

        # Verify transaction processing
        processing_success = test_transaction_processing()

        # Final results
        print(f"\n🎯 COMPLETE MINING TEST RESULTS")
        print("=" * 40)
        print(f"{'✅' if mining_success else '❌'} Block Mining: {'SUCCESS' if mining_success else 'FAILED'}")
        print(f"{'✅' if rewards_success else '❌'} Token Rewards: {'SUCCESS' if rewards_success else 'FAILED'}")
        print(f"{'✅' if processing_success else '❌'} Transaction Processing: {'SUCCESS' if processing_success else 'FAILED'}")

        overall_success = mining_success and rewards_success and processing_success

        print(f"\n🏆 OVERALL RESULT: {'✅ COMPLETE SUCCESS' if overall_success else '❌ PARTIAL SUCCESS'}")

        if overall_success:
            print("🎉 ONNYX mining system is fully operational!")
            print("⛏️ Blocks mined with transactions")
            print("💰 Rewards properly distributed")
            print("📝 Transaction processing working")
            print("🚀 Ready for continuous mining operations!")
        else:
            print("⚠️ Some components need attention")

        return overall_success
    else:
        print("❌ Mining test failed")
        return False

if __name__ == "__main__":
    main()
