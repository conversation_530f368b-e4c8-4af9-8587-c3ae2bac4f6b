{% extends "base.html" %}

{% block title %}Transaction Details - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="detail-header">
    <div>
        <h1 class="detail-title">Transaction Details</h1>
        <div class="detail-subtitle">{{ tx.txid }}</div>
    </div>
</div>

<div class="detail-meta">
    <div class="meta-item">
        <i class="fas fa-cube"></i>
        {% if block_index == "mempool" %}
        <span>Mempool (Pending)</span>
        {% else %}
        <span>Block #{{ block_index }}</span>
        {% endif %}
    </div>
    
    <div class="meta-item">
        <i class="fas fa-tag"></i>
        <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
    </div>
    
    {% if timestamp %}
    <div class="meta-item">
        <i class="fas fa-clock"></i>
        <span>{{ format_timestamp(timestamp) }}</span>
    </div>
    {% endif %}
    
    <div class="meta-item">
        <i class="fas fa-check-circle"></i>
        {% if block_index == "mempool" %}
        <span class="status-tag status-pending">Pending</span>
        {% else %}
        <span class="status-tag status-confirmed">Confirmed</span>
        {% endif %}
    </div>
</div>

<div class="detail-section">
    <h2 class="detail-section-title">Transaction Information</h2>
    
    <div class="property-list">
        {% if tx.from %}
        <div class="property-item">
            <div class="property-label">From</div>
            <div class="property-value">
                <a href="{{ url_for('identity_detail', identity_id=tx.from) }}">{{ tx.from }}</a>
            </div>
        </div>
        {% endif %}
        
        {% if tx.to %}
        <div class="property-item">
            <div class="property-label">To</div>
            <div class="property-value">
                <a href="{{ url_for('identity_detail', identity_id=tx.to) }}">{{ tx.to }}</a>
            </div>
        </div>
        {% endif %}
        
        {% if tx.token or tx.tokenId %}
        <div class="property-item">
            <div class="property-label">Token</div>
            <div class="property-value">
                <a href="{{ url_for('token_detail', token_id=tx.token or tx.tokenId) }}">{{ tx.token or tx.tokenId }}</a>
            </div>
        </div>
        {% endif %}
        
        {% if tx.amount is defined %}
        <div class="property-item">
            <div class="property-label">Amount</div>
            <div class="property-value">{{ tx.amount }}</div>
        </div>
        {% endif %}
        
        {% if tx.identity %}
        <div class="property-item">
            <div class="property-label">Identity</div>
            <div class="property-value">
                <a href="{{ url_for('identity_detail', identity_id=tx.identity) }}">{{ tx.identity }}</a>
            </div>
        </div>
        {% endif %}
        
        {% if tx.metadata %}
        <div class="property-item">
            <div class="property-label">Metadata</div>
            <div class="property-value">{{ tx.metadata }}</div>
        </div>
        {% endif %}
    </div>
</div>

<div class="detail-section">
    <h2 class="detail-section-title">Raw Transaction Data</h2>
    
    <div class="json-viewer">{{ tx|tojson(indent=2) }}</div>
</div>
{% endblock %}
