# OnnyxScript Language Reference

## Overview

OnnyxScript is a stack-based scripting language used in the Onnyx blockchain for transaction validation and token/identity operations. It is inspired by Bitcoin Script but extends it with additional opcodes for identity and token operations.

## Execution Model

OnnyxScript uses a stack-based execution model:

1. <PERSON><PERSON><PERSON> is executed from left to right
2. Data is pushed onto the stack
3. Opcodes operate on the stack
4. <PERSON><PERSON><PERSON> succeeds if the top stack item is non-zero (true) after execution

## Data Types

- **Integers**: 32-bit signed integers
- **Booleans**: Represented as 0 (false) or 1 (true)
- **Byte Arrays**: Variable-length arrays of bytes
- **Token IDs**: 32-byte identifiers for tokens
- **Identity IDs**: 32-byte identifiers for identities

## Standard Opcodes

### Constants

- **OP_0, OP_FALSE**: Push empty array onto the stack
- **OP_1 to OP_16**: Push number onto the stack
- **OP_1NEGATE**: Push -1 onto the stack
- **OP_TRUE**: Push 1 onto the stack

### Flow Control

- **OP_IF**: Execute statements if top stack value is not 0
- **OP_NOTIF**: Execute statements if top stack value is 0
- **OP_ELSE**: Execute statements if previous IF or NOTIF was not executed
- **OP_ENDIF**: End IF/ELSE block
- **OP_VERIFY**: Mark transaction as invalid if top stack value is not true
- **OP_RETURN**: Mark transaction as invalid

### Stack Operations

- **OP_TOALTSTACK**: Pop top item and push to alt stack
- **OP_FROMALTSTACK**: Pop top item from alt stack and push to stack
- **OP_2DROP**: Pop top two stack items
- **OP_2DUP**: Duplicate top two stack items
- **OP_3DUP**: Duplicate top three stack items
- **OP_2OVER**: Copy stack items 3 and 4 to top
- **OP_2ROT**: Move stack items 5 and 6 to top
- **OP_2SWAP**: Swap top two pairs of stack items
- **OP_IFDUP**: Duplicate top stack item if it is not 0
- **OP_DEPTH**: Push stack size onto the stack
- **OP_DROP**: Pop top stack item
- **OP_DUP**: Duplicate top stack item
- **OP_NIP**: Remove second-to-top stack item
- **OP_OVER**: Copy second-to-top stack item to top
- **OP_PICK**: Copy the nth stack item to top
- **OP_ROLL**: Move the nth stack item to top
- **OP_ROT**: Move third-to-top stack item to top
- **OP_SWAP**: Swap top two stack items
- **OP_TUCK**: Copy top stack item to before second-to-top item

### Bitwise Logic

- **OP_EQUAL**: Push 1 if top two stack items are equal, 0 otherwise
- **OP_EQUALVERIFY**: Same as OP_EQUAL followed by OP_VERIFY
- **OP_INVERT**: Invert all bits in top stack item
- **OP_AND**: Bitwise AND of top two stack items
- **OP_OR**: Bitwise OR of top two stack items
- **OP_XOR**: Bitwise XOR of top two stack items

### Arithmetic

- **OP_1ADD**: Add 1 to top stack item
- **OP_1SUB**: Subtract 1 from top stack item
- **OP_2MUL**: Multiply top stack item by 2
- **OP_2DIV**: Divide top stack item by 2
- **OP_NEGATE**: Negate top stack item
- **OP_ABS**: Absolute value of top stack item
- **OP_NOT**: Push 1 if top stack item is 0, 0 otherwise
- **OP_0NOTEQUAL**: Push 1 if top stack item is not 0, 0 otherwise
- **OP_ADD**: Add top two stack items
- **OP_SUB**: Subtract top stack item from second-to-top stack item
- **OP_MUL**: Multiply top two stack items
- **OP_DIV**: Divide second-to-top stack item by top stack item
- **OP_MOD**: Remainder after dividing second-to-top stack item by top stack item
- **OP_LSHIFT**: Left shift second-to-top stack item by top stack item
- **OP_RSHIFT**: Right shift second-to-top stack item by top stack item

### Cryptography

- **OP_SHA1**: SHA-1 hash of top stack item
- **OP_SHA256**: SHA-256 hash of top stack item
- **OP_HASH160**: RIPEMD-160 of SHA-256 of top stack item
- **OP_HASH256**: SHA-256 of SHA-256 of top stack item
- **OP_CODESEPARATOR**: Mark location for signature verification
- **OP_CHECKSIG**: Verify signature using public key
- **OP_CHECKSIGVERIFY**: Same as OP_CHECKSIG followed by OP_VERIFY
- **OP_CHECKMULTISIG**: Verify multiple signatures using multiple public keys
- **OP_CHECKMULTISIGVERIFY**: Same as OP_CHECKMULTISIG followed by OP_VERIFY

## Onnyx-Specific Opcodes

### Token Operations

- **OP_TOKENSPAWN**: Create a new token
  - Parameters: name, symbol, decimals, initial_supply, metadata
  - Returns: token_id

- **OP_TOKENMINT**: Mint tokens
  - Parameters: token_id, amount, recipient
  - Returns: success (0 or 1)

- **OP_TOKENBURN**: Burn tokens
  - Parameters: token_id, amount
  - Returns: success (0 or 1)

- **OP_TOKENTRANSFER**: Transfer tokens
  - Parameters: token_id, amount, recipient
  - Returns: success (0 or 1)

- **OP_TOKENBALANCE**: Get token balance
  - Parameters: token_id, identity_id
  - Returns: balance

### Identity Operations

- **OP_IDREGISTER**: Register a new identity
  - Parameters: name, public_key, metadata
  - Returns: identity_id

- **OP_IDUPDATE**: Update identity information
  - Parameters: identity_id, field, value
  - Returns: success (0 or 1)

- **OP_IDVERIFY**: Verify identity ownership
  - Parameters: identity_id, signature
  - Returns: success (0 or 1)

- **OP_IDREPUTATION**: Get identity reputation score
  - Parameters: identity_id
  - Returns: reputation_score

## Script Examples

### Standard Pay-to-Public-Key-Hash (P2PKH)

```
scriptPubKey: OP_DUP OP_HASH160 <pubKeyHash> OP_EQUALVERIFY OP_CHECKSIG
scriptSig: <sig> <pubKey>
```

### Token Creation

```
OP_IDVERIFY <identity_id> <signature> OP_TOKENSPAWN "MyToken" "MTK" 8 1000000 <metadata>
```

### Token Transfer

```
OP_DUP OP_HASH160 <pubKeyHash> OP_EQUALVERIFY OP_CHECKSIG OP_TOKENTRANSFER <token_id> 100 <recipient>
```

### Identity Registration

```
OP_CHECKSIG OP_IDREGISTER "MyIdentity" <public_key> <metadata>
```

## Limitations

- Non-Turing complete (no loops)
- Limited script size (10KB)
- Limited execution steps (1000)
- Limited stack size (1000 items)
