"""
Onnyx Sela Registry Module

This module provides the SelaRegistry class for managing business entities (Selas).
"""

import os
import json
import time
from typing import Dict, List, Any, Optional

class SelaRegistry:
    """
    SelaRegistry manages business entities (Selas) in the Onnyx ecosystem.

    A Sela is a business entity that can offer services, have members, and mint tokens.
    """

    def __init__(self, path: str = "data/selas.json"):
        """
        Initialize the SelaRegistry.

        Args:
            path: Path to the Sela registry JSON file
        """
        self.path = path
        self.selas = self._load()

        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.path), exist_ok=True)

    def _load(self) -> Dict[str, Any]:
        """
        Load the Sela registry from the JSON file.

        Returns:
            The Sela registry as a dictionary
        """
        if os.path.exists(self.path):
            try:
                with open(self.path, "r") as f:
                    data = json.load(f)
                    # Ensure the data is a dictionary
                    if isinstance(data, dict):
                        return data
                    else:
                        return {}
            except (json.JSOND<PERSON>odeError, FileNotFoundError):
                return {}
        return {}

    def _save(self) -> None:
        """Save the Sela registry to the JSON file."""
        with open(self.path, "w") as f:
            json.dump(self.selas, f, indent=2)

    def register_sela(self, sela_id: str, name: str, founder_id: str,
                      sela_type: str, token_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Register a new Sela.

        Args:
            sela_id: The Sela ID
            name: The Sela name
            founder_id: The founder's identity ID
            sela_type: The Sela type (e.g., "BUSINESS", "NONPROFIT", "COMMUNITY")
            token_id: The token ID associated with the Sela (optional)

        Returns:
            The newly created Sela

        Raises:
            Exception: If the Sela already exists
        """
        if sela_id in self.selas:
            raise Exception(f"Sela with ID '{sela_id}' already exists")

        self.selas[sela_id] = {
            "sela_id": sela_id,
            "name": name,
            "founder": founder_id,
            "type": sela_type,
            "members": [founder_id],
            "created_at": int(time.time()),
            "token_id": token_id,
            "roles": {founder_id: "FOUNDER"},
            "services_offered": []
        }

        self._save()
        return self.selas[sela_id]

    def join_sela(self, sela_id: str, identity_id: str, role: str = "MEMBER") -> None:
        """
        Add an identity to a Sela.

        Args:
            sela_id: The Sela ID
            identity_id: The identity ID to add
            role: The role of the identity in the Sela (default: "MEMBER")

        Raises:
            Exception: If the Sela does not exist or the identity is already a member
        """
        if sela_id not in self.selas:
            raise Exception(f"Sela with ID '{sela_id}' not found")

        if identity_id in self.selas[sela_id]["members"]:
            raise Exception(f"Identity '{identity_id}' is already a member of Sela '{sela_id}'")

        self.selas[sela_id]["members"].append(identity_id)
        self.selas[sela_id]["roles"][identity_id] = role
        self._save()

    def leave_sela(self, sela_id: str, identity_id: str) -> None:
        """
        Remove an identity from a Sela.

        Args:
            sela_id: The Sela ID
            identity_id: The identity ID to remove

        Raises:
            Exception: If the Sela does not exist, the identity is not a member,
                      or the identity is the founder
        """
        if sela_id not in self.selas:
            raise Exception(f"Sela with ID '{sela_id}' not found")

        if identity_id not in self.selas[sela_id]["members"]:
            raise Exception(f"Identity '{identity_id}' is not a member of Sela '{sela_id}'")

        if identity_id == self.selas[sela_id]["founder"]:
            raise Exception(f"Founder '{identity_id}' cannot leave Sela '{sela_id}'")

        self.selas[sela_id]["members"].remove(identity_id)
        if identity_id in self.selas[sela_id]["roles"]:
            del self.selas[sela_id]["roles"][identity_id]
        self._save()

    def add_service(self, sela_id: str, service: str) -> None:
        """
        Add a service to a Sela.

        Args:
            sela_id: The Sela ID
            service: The service to add

        Raises:
            Exception: If the Sela does not exist or the service already exists
        """
        if sela_id not in self.selas:
            raise Exception(f"Sela with ID '{sela_id}' not found")

        if service in self.selas[sela_id]["services_offered"]:
            raise Exception(f"Service '{service}' already exists in Sela '{sela_id}'")

        self.selas[sela_id]["services_offered"].append(service)
        self._save()

    def remove_service(self, sela_id: str, service: str) -> None:
        """
        Remove a service from a Sela.

        Args:
            sela_id: The Sela ID
            service: The service to remove

        Raises:
            Exception: If the Sela does not exist or the service does not exist
        """
        if sela_id not in self.selas:
            raise Exception(f"Sela with ID '{sela_id}' not found")

        if service not in self.selas[sela_id]["services_offered"]:
            raise Exception(f"Service '{service}' does not exist in Sela '{sela_id}'")

        self.selas[sela_id]["services_offered"].remove(service)
        self._save()

    def update_role(self, sela_id: str, identity_id: str, role: str) -> None:
        """
        Update the role of an identity in a Sela.

        Args:
            sela_id: The Sela ID
            identity_id: The identity ID
            role: The new role

        Raises:
            Exception: If the Sela does not exist or the identity is not a member
        """
        if sela_id not in self.selas:
            raise Exception(f"Sela with ID '{sela_id}' not found")

        if identity_id not in self.selas[sela_id]["members"]:
            raise Exception(f"Identity '{identity_id}' is not a member of Sela '{sela_id}'")

        self.selas[sela_id]["roles"][identity_id] = role
        self._save()

    def get_sela(self, sela_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a Sela by ID.

        Args:
            sela_id: The Sela ID

        Returns:
            The Sela or None if not found
        """
        return self.selas.get(sela_id)

    def get_selas_by_founder(self, founder_id: str) -> List[Dict[str, Any]]:
        """
        Get all Selas founded by an identity.

        Args:
            founder_id: The founder's identity ID

        Returns:
            A list of Selas founded by the identity
        """
        return [sela for sela in self.selas.values() if sela["founder"] == founder_id]

    def get_selas_by_member(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get all Selas that an identity is a member of.

        Args:
            identity_id: The identity ID

        Returns:
            A list of Selas that the identity is a member of
        """
        return [sela for sela in self.selas.values() if identity_id in sela["members"]]

    def get_all_selas(self) -> Dict[str, Any]:
        """
        Get all Selas.

        Returns:
            All Selas in the registry
        """
        return self.selas
