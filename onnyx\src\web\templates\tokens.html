{% extends 'base.html' %}

{% block title %}Onnyx Explorer - Tokens{% endblock %}

{% block content %}
    {% with active_tab='tokens' %}
    {% include 'explorer_hero.html' %}
    {% include 'explorer_nav.html' %}
    {% endwith %}

    {% if tokens %}
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Symbol</th>
                    <th>Creator</th>
                    <th>Type</th>
                    <th>Supply</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for token in tokens %}
                    <tr>
                        <td>{{ token.name }}</td>
                        <td>{{ token.symbol }}</td>
                        <td>
                            <a href="{{ url_for('identity_detail', identity_id=token.creator_id) }}">
                                {{ token.creator_id }}
                            </a>
                        </td>
                        <td>{{ token.metadata.category if token.metadata and token.metadata.category is defined else (token.token_type.value if token.token_type and token.token_type.value is defined else token.token_type) }}</td>
                        <td>{{ token.supply }}</td>
                        <td>
                            <a href="{{ url_for('token_detail', token_id=token.token_id) }}">View Details</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="pagination">
            {% if offset > 0 %}
                <a href="{{ url_for('tokens', limit=limit, offset=max(0, offset - limit)) }}">⬅ Previous</a>
            {% endif %}

            <span>Showing {{ offset + 1 }} to {{ min(offset + limit, total) }} of {{ total }}</span>

            {% if offset + limit < total %}
                <a href="{{ url_for('tokens', limit=limit, offset=offset + limit) }}">Next ➡</a>
            {% endif %}
        </div>
    {% else %}
        <p>No tokens found.</p>
    {% endif %}
{% endblock %}
