"""
Onnyx Virtual Machine (VM) Implementation

This module implements the core VM functionality for executing transactions
on the Onnyx blockchain. It uses the opcodes defined in opcodes.py to validate
and execute different types of transactions.
"""

import json
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple

import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from opcodes import (
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    OP_BURN, OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE, OP_REWARD,
    execute_opcode, OpcodeError
)

class OnnyxVM:
    """
    Onnyx Virtual Machine for executing transactions.

    The VM validates and executes transactions on the Onnyx blockchain,
    maintaining the state of the blockchain and ensuring that all
    transactions follow the rules of the Onnyx protocol.
    """

    def __init__(self):
        """Initialize the Onnyx VM."""
        self.tx_history: List[Dict[str, Any]] = []
        self.pending_txs: List[Dict[str, Any]] = []
        self.tx_results: Dict[str, Dict[str, Any]] = {}

    def validate_transaction(self, tx: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate a transaction without executing it.

        Args:
            tx: The transaction to validate

        Returns:
            A tuple of (is_valid, error_message)
        """
        if not isinstance(tx, dict):
            return False, "Transaction must be a dictionary"

        # Check required fields
        if "type" not in tx:
            return False, "Transaction missing 'type' field"

        if "txid" not in tx:
            return False, "Transaction missing 'txid' field"

        # Map transaction type to opcode
        tx_type = tx["type"]
        opcode = self._map_type_to_opcode(tx_type)

        if not opcode:
            return False, f"Unknown transaction type: {tx_type}"

        # Validate the transaction using the appropriate opcode
        try:
            execute_opcode(opcode, tx)
            return True, None
        except OpcodeError as e:
            return False, str(e)

    def execute_transaction(self, tx: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a transaction.

        Args:
            tx: The transaction to execute

        Returns:
            A result dictionary with status and message
        """
        # Validate the transaction first
        is_valid, error = self.validate_transaction(tx)

        if not is_valid:
            result = {
                "status": "failed",
                "message": error,
                "txid": tx.get("txid", "unknown")
            }
            self.tx_results[tx.get("txid", "unknown")] = result
            return result

        # Execute the transaction based on its type
        tx_type = tx["type"]
        txid = tx["txid"]

        try:
            # Execute the transaction logic
            # This would typically update the blockchain state
            # For now, we'll just record the transaction

            # Add timestamp if not present
            if "timestamp" not in tx:
                tx["timestamp"] = int(time.time())

            # Add the transaction to history
            self.tx_history.append(tx)

            # Remove from pending if it was there
            if tx in self.pending_txs:
                self.pending_txs.remove(tx)

            # Create a success result
            result = {
                "status": "success",
                "message": f"Transaction {txid} executed successfully",
                "txid": txid,
                "timestamp": tx.get("timestamp")
            }

            self.tx_results[txid] = result
            return result

        except Exception as e:
            # Handle any execution errors
            result = {
                "status": "failed",
                "message": f"Execution error: {str(e)}",
                "txid": txid
            }
            self.tx_results[txid] = result
            return result

    def add_pending_transaction(self, tx: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a transaction to the pending queue.

        Args:
            tx: The transaction to add

        Returns:
            A result dictionary with status and message
        """
        # Validate the transaction first
        is_valid, error = self.validate_transaction(tx)

        if not is_valid:
            return {
                "status": "rejected",
                "message": error,
                "txid": tx.get("txid", "unknown")
            }

        # Generate a txid if not present
        if "txid" not in tx:
            tx_string = json.dumps(tx, sort_keys=True)
            tx["txid"] = hashlib.sha256(tx_string.encode()).hexdigest()

        # Add timestamp if not present
        if "timestamp" not in tx:
            tx["timestamp"] = int(time.time())

        # Add to pending transactions
        self.pending_txs.append(tx)

        return {
            "status": "pending",
            "message": f"Transaction {tx['txid']} added to pending queue",
            "txid": tx["txid"]
        }

    def get_pending_transactions(self) -> List[Dict[str, Any]]:
        """
        Get all pending transactions.

        Returns:
            A list of pending transactions
        """
        return self.pending_txs

    def get_transaction_history(self) -> List[Dict[str, Any]]:
        """
        Get the transaction history.

        Returns:
            A list of executed transactions
        """
        return self.tx_history

    def get_transaction_result(self, txid: str) -> Optional[Dict[str, Any]]:
        """
        Get the result of a transaction.

        Args:
            txid: The transaction ID

        Returns:
            The transaction result or None if not found
        """
        return self.tx_results.get(txid)

    def _map_type_to_opcode(self, tx_type: str) -> Optional[str]:
        """
        Map a transaction type to an opcode.

        Args:
            tx_type: The transaction type

        Returns:
            The corresponding opcode or None if not found
        """
        type_to_opcode = {
            "mint": OP_MINT,
            "send": OP_SEND,
            "identity": OP_IDENTITY,
            "scroll": OP_SCROLL,
            "burn": OP_BURN,
            "grant_reputation": OP_GRANT_REPUTATION,
            "stake": OP_STAKE,
            "vote": OP_VOTE,
            "reward": OP_REWARD
        }

        return type_to_opcode.get(tx_type.lower())

    def clear_pending_transactions(self) -> None:
        """Clear all pending transactions."""
        self.pending_txs = []

    def reset(self) -> None:
        """Reset the VM state."""
        self.tx_history = []
        self.pending_txs = []
        self.tx_results = {}
