"""
Mine a block on the Onnyx blockchain

This script mines a new block on the Onnyx blockchain.
"""

import argparse
import json
from consensus.miner import BlockMiner

def main():
    """Mine a block on the Onnyx blockchain."""
    parser = argparse.ArgumentParser(description="Mine a block on the Onnyx blockchain")
    parser.add_argument("--proposer", type=str, required=True, help="The identity ID of the block proposer")
    parser.add_argument("--pretty", action="store_true", help="Pretty-print the block")
    
    args = parser.parse_args()
    
    # Create miner
    miner = BlockMiner()
    
    # Mine block
    print(f"Mining block with proposer {args.proposer}...")
    block = miner.create_block(args.proposer)
    
    # Print block
    if args.pretty:
        print(json.dumps(block, indent=2))
    else:
        print(f"Block {block['index']} mined with hash {block['hash']}")
        print(f"Transactions: {len(block['transactions'])}")
        print(f"Timestamp: {block['timestamp']}")
    
    print(f"\nBlock reward of {BLOCK_REWARD} {TOKEN_ID} credited to {args.proposer}")

if __name__ == "__main__":
    # Constants
    BLOCK_REWARD = 10  # ONX
    TOKEN_ID = "ONX"
    
    main()
