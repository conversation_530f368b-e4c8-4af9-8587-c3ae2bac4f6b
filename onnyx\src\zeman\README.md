# Zeman: Time/Work Credit System

Zeman (זְמַן) is a revolutionary time/work credit system that turns labor, service, and creativity into on-chain value without needing capital.

## Overview

Zeman is Proof-of-Work that actually means something. It tracks hours, service, skill, or verified labor and converts it into:

- **Minting power** (Yovel logic)
- **Fee discounts**
- **Reputation boosts**
- **Redeemable value** (e.g. earn BOB_BUX or ONX)

## Components

### ZemanLedger

The `ZemanLedger` class tracks time credits per identity. It provides methods for:

- Adding time credits
- Getting total time credits
- Getting time credit entries
- Marking time credits as redeemed

### ZemanRedeemer

The `ZemanRedeemer` class converts Zeman time credits into rewards. It provides methods for:

- Redeeming time credits for token minting power
- Redeeming time credits for fee discounts
- Redeeming time credits for reputation boosts
- Redeeming time credits for ONX tokens

## Redemption Rates

The following redemption rates are used:

- **Minting power**: 10 Zeman hours = 1,000 tokens (Yovel logic)
- **Fee discount**: 100 Zeman hours = 25% fee discount
- **Reputation boost**: 50 Zeman hours = +10 reputation points
- **ONX tokens**: 5 Zeman hours = 1 ONX token

## API Endpoints

### Add Zeman Time Credits

```
POST /zeman/add
```

Request body:
```json
{
  "identity_id": "string",
  "hours": 0,
  "category": "string",
  "description": "string",
  "metadata": {},
  "message": "string",
  "signature": "string"
}
```

### Get Total Zeman Time Credits

```
GET /zeman/total?identity_id=string
```

### Get Zeman Time Credit History

```
GET /zeman/history?identity_id=string&category=string&redeemed=boolean&limit=100&offset=0
```

### Redeem for Minting Power

```
POST /zeman/redeem/minting-power
```

Request body:
```json
{
  "identity_id": "string",
  "token_id": "string",
  "hours_to_redeem": 0,
  "message": "string",
  "signature": "string"
}
```

### Redeem for Fee Discount

```
POST /zeman/redeem/fee-discount
```

Request body:
```json
{
  "identity_id": "string",
  "message": "string",
  "signature": "string"
}
```

### Redeem for Reputation Boost

```
POST /zeman/redeem/reputation-boost
```

Request body:
```json
{
  "identity_id": "string",
  "reputation_type": "string",
  "message": "string",
  "signature": "string"
}
```

### Redeem for ONX Tokens

```
POST /zeman/redeem/onx-tokens
```

Request body:
```json
{
  "identity_id": "string",
  "hours_to_redeem": 0,
  "message": "string",
  "signature": "string"
}
```

### Get Redemption Rates

```
GET /zeman/rates
```

### Get Top Contributors

```
GET /zeman/top-contributors?limit=10
```

## Use Cases

### Yovel Token Minting Rules

Must contribute 10 Zeman hours to mint 1,000 tokens.

```python
# Redeem Zeman hours for minting power
result = zeman_redeemer.redeem_for_minting_power(
    identity_id="identity_id",
    token_id="token_id",
    hours_to_redeem=10.0
)
```

### Sela Bonus

Sela that logs 50 Zeman hours gets fee rebate.

```python
# Check if the Sela has enough Zeman hours
total_hours = zeman_ledger.get_total(sela["owner"])
if total_hours >= 50:
    # Apply fee rebate
    fee = fee * 0.8  # 20% rebate
```

### Fee Discounts

If Zeman > 100 hours, reduce action fees by 25%.

```python
# Redeem Zeman hours for fee discount
result = zeman_redeemer.redeem_for_fee_discount(
    identity_id="identity_id"
)
```

### Governance Boost

Zeman hours can boost voting power temporarily.

```python
# Calculate voting power
voting_power = 1.0  # Base voting power
zeman_hours = zeman_ledger.get_total(identity_id)
if zeman_hours > 0:
    # Boost voting power based on Zeman hours
    voting_power += min(zeman_hours / 100, 1.0)  # Max 2x boost
```

## Future Enhancements

- **Verification**: Implement a verification system for Zeman time credits
- **Delegation**: Allow identities to delegate their Zeman time credits to others
- **Expiration**: Add expiration to Zeman time credits
- **Categories**: Add more categories for Zeman time credits
- **Integration**: Integrate with other systems like Sela and Yovel
