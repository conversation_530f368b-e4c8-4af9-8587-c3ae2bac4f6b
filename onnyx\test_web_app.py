#!/usr/bin/env python3

"""
Script to test the web application.
"""

import os
import sys
import logging
import time
import requests
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.test_web_app")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_statistics_api():
    """Test the statistics API."""
    try:
        # Get the statistics
        response = requests.get("http://localhost:8082/api/statistics")

        # Check the response
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Statistics: {data}")

            # Check the identity count
            identity_count = data.get("identity_count", 0)
            logger.info(f"Identity count: {identity_count}")

            return data
        else:
            logger.error(f"Error getting statistics: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error testing statistics API: {str(e)}")
        return None

def test_create_identity():
    """Test creating an identity."""
    try:
        # Create the identity
        data = {
            "name": f"Test Identity {int(time.time())}",
            "nation": "Israel",
            "tribe": "Judah",
            "dob": "01/01/2000",
            "region": "North America",
            "purpose": "Testing",
            "soul_seal": "test_seal"
        }

        response = requests.post("http://localhost:8082/create-identity", data=data)

        # Check the response
        if response.status_code == 200:
            logger.info("Identity created successfully")

            # Get the statistics again
            statistics = test_statistics_api()

            if statistics:
                # Check if the identity count increased
                identity_count = statistics.get("identity_count", 0)
                logger.info(f"Identity count after creation: {identity_count}")

            return True
        else:
            logger.error(f"Error creating identity: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"Error testing create identity: {str(e)}")
        return False

def main():
    """Main entry point."""
    logger.info("Testing web application...")

    # Test the statistics API
    logger.info("Testing statistics API...")
    statistics = test_statistics_api()

    if statistics:
        logger.info("Statistics API test passed")
    else:
        logger.error("Statistics API test failed")

    # Test creating an identity
    logger.info("Testing create identity...")
    success = test_create_identity()

    if success:
        logger.info("Create identity test passed")
    else:
        logger.error("Create identity test failed")

    logger.info("Web application test complete")

if __name__ == "__main__":
    main()
