"""
Onnyx VM Opcodes Module

This module defines the core transaction validation functions (opcodes) for the Onnyx VM.
Each opcode function validates a specific type of transaction and returns True if valid,
or raises an Exception with a clear error message if invalid.

Opcodes:
- OP_MINT: Mint a new Mikvah token
- OP_SEND: Transfer token or ONX
- OP_IDENTITY: Register a new soulbound identity
- OP_SCROLL: Submit a Voice Scroll proposal
- OP_BURN: Burn tokens
- OP_GRANT_REPUTATION: Grant reputation to an identity
- OP_STAKE: Stake tokens
- OP_VOTE: Vote on a governance proposal
- OP_REWARD: Distribute mining rewards
"""

import logging
import json
from typing import Dict, Any, Union, Optional, List

from shared.models.transaction import Transaction
from shared.models.token import Token
from shared.models.identity import Identity

# Set up logging
logger = logging.getLogger("onnyx.vm.opcodes")

class OpcodeError(Exception):
    """Custom exception for opcode validation errors."""
    pass

class OpcodeMintError(OpcodeError):
    """Exception raised for errors in the OP_MINT opcode."""
    pass

class OpcodeSendError(OpcodeError):
    """Exception raised for errors in the OP_SEND opcode."""
    pass

class OpcodeIdentityError(OpcodeError):
    """Exception raised for errors in the OP_IDENTITY opcode."""
    pass

class OpcodeScrollError(OpcodeError):
    """Exception raised for errors in the OP_SCROLL opcode."""
    pass

class OpcodeBurnError(OpcodeError):
    """Exception raised for errors in the OP_BURN opcode."""
    pass

class OpcodeGrantReputationError(OpcodeError):
    """Exception raised for errors in the OP_GRANT_REPUTATION opcode."""
    pass

class OpcodeStakeError(OpcodeError):
    """Exception raised for errors in the OP_STAKE opcode."""
    pass

class OpcodeVoteError(OpcodeError):
    """Exception raised for errors in the OP_VOTE opcode."""
    pass

class OpcodeRewardError(OpcodeError):
    """Exception raised for errors in the OP_REWARD opcode."""
    pass

# Import required modules
from src.yovel.limits import YovelLimiter
from src.tokens.registry import TokenRegistry
from src.tokens.ledger import TokenLedger
from src.identity.registry import IdentityRegistry
from src.business.sela import SelaRegistry
from src.governance.scrolls import VoiceScrolls

# Initialize global instances
try:
    yovel = YovelLimiter()
    tokens = TokenRegistry()
    ledger = TokenLedger()
    identity = IdentityRegistry()
    selas = SelaRegistry()
    scrolls = VoiceScrolls()
except Exception as e:
    logger.warning(f"Error initializing modules: {str(e)}")
    logger.warning("Using mock implementations instead")

    # Mock implementations for development and testing
    class YovelLimiter:
        def calculate_mint_cap(self, identity_id: str, category: str = "general") -> int:
            """Calculate the maximum amount of tokens an identity can mint."""
            logger.debug(f"Mock YovelLimiter: Calculating mint cap for {identity_id} in category {category}")
            return 1000  # Default cap for testing

    class TokenRegistry:
        def __init__(self):
            self.tokens = {}

        def get_token(self, token_id: str) -> Optional[Dict[str, Any]]:
            """Get a token by ID."""
            logger.debug(f"Mock TokenRegistry: Getting token {token_id}")
            # Try to get from the database first
            token = Token.get_by_id(token_id)
            if token:
                return {
                    "token_id": token.token_id,
                    "name": token.name,
                    "symbol": token.symbol,
                    "creator": token.creator_id,
                    "supply": token.supply,
                    "metadata": token.metadata
                }
            return self.tokens.get(token_id)

        def burn_token(self, token_id: str, amount: int) -> bool:
            """Burn tokens by reducing the supply."""
            logger.debug(f"Mock TokenRegistry: Burning {amount} of token {token_id}")
            # Try to update the database first
            token = Token.get_by_id(token_id)
            if token:
                token.supply -= amount
                token.save()
                return True

            if token_id in self.tokens:
                self.tokens[token_id]["supply"] -= amount
                return True
            return False

    class TokenLedger:
        def __init__(self):
            self.balances = {}
            self.stakes = {}  # For tracking staked tokens

        def get_balance(self, identity_id: str, token_id: str) -> int:
            """Get the balance of a token for an identity."""
            logger.debug(f"Mock TokenLedger: Getting balance of {token_id} for {identity_id}")
            key = f"{identity_id}:{token_id}"
            return self.balances.get(key, 0)

        def get_staked_balance(self, identity_id: str, token_id: str) -> int:
            """Get the staked balance of a token for an identity."""
            logger.debug(f"Mock TokenLedger: Getting staked balance of {token_id} for {identity_id}")
            key = f"{identity_id}:{token_id}"
            return self.stakes.get(key, 0)

        def stake_tokens(self, identity_id: str, token_id: str, amount: int, duration: int) -> bool:
            """Stake tokens for an identity."""
            logger.debug(f"Mock TokenLedger: Staking {amount} of {token_id} for {identity_id} for {duration} days")
            key = f"{identity_id}:{token_id}"
            if self.balances.get(key, 0) >= amount:
                self.balances[key] -= amount
                self.stakes[key] = self.stakes.get(key, 0) + amount
                return True
            return False

        def unstake_tokens(self, identity_id: str, token_id: str, amount: int) -> bool:
            """Unstake tokens for an identity."""
            logger.debug(f"Mock TokenLedger: Unstaking {amount} of {token_id} for {identity_id}")
            key = f"{identity_id}:{token_id}"
            if self.stakes.get(key, 0) >= amount:
                self.stakes[key] -= amount
                self.balances[key] = self.balances.get(key, 0) + amount
                return True
            return False

    class IdentityRegistry:
        def __init__(self):
            self.identities = {}
            self.badges = {}
            self.reputation = {}  # For tracking reputation scores

        def get_identity(self, identity_id: str) -> Optional[Dict[str, Any]]:
            """Get an identity by ID."""
            logger.debug(f"Mock IdentityRegistry: Getting identity {identity_id}")
            # Try to get from the database first
            identity = Identity.get_by_id(identity_id)
            if identity:
                return {
                    "identity_id": identity.identity_id,
                    "name": identity.name,
                    "public_key": identity.public_key,
                    "metadata": identity.metadata,
                    "founded_selas": identity.metadata.get("founded_selas", []),
                    "joined_selas": identity.metadata.get("joined_selas", [])
                }
            return self.identities.get(identity_id)

        def has_badge(self, identity_id: str, badge: str) -> bool:
            """Check if an identity has a badge."""
            logger.debug(f"Mock IdentityRegistry: Checking if {identity_id} has badge {badge}")
            # Try to get from the database first
            identity = Identity.get_by_id(identity_id)
            if identity:
                return badge in identity.metadata.get("badges", [])

            if identity_id not in self.badges:
                return False
            return badge in self.badges[identity_id]

        def get_reputation(self, identity_id: str) -> int:
            """Get the reputation score for an identity."""
            logger.debug(f"Mock IdentityRegistry: Getting reputation for {identity_id}")
            # Try to get from the database first
            identity = Identity.get_by_id(identity_id)
            if identity:
                return identity.metadata.get("reputation", 0)
            return self.reputation.get(identity_id, 0)

        def grant_reputation(self, identity_id: str, amount: int, granter_id: str) -> bool:
            """Grant reputation to an identity."""
            logger.debug(f"Mock IdentityRegistry: Granting {amount} reputation to {identity_id} from {granter_id}")
            # Try to update the database first
            identity = Identity.get_by_id(identity_id)
            if identity:
                current_reputation = identity.metadata.get("reputation", 0)
                identity.metadata["reputation"] = current_reputation + amount
                identity.save()
                return True

            if identity_id in self.identities:
                self.reputation[identity_id] = self.reputation.get(identity_id, 0) + amount
                return True
            return False

    class SelaRegistry:
        def __init__(self):
            self.selas = {}

        def get_sela(self, identity_id: str) -> Optional[Dict[str, Any]]:
            """Get a Sela by identity ID."""
            logger.debug(f"Mock SelaRegistry: Getting Sela for {identity_id}")
            return self.selas.get(identity_id)

    class VoiceScrolls:
        def __init__(self):
            self.scrolls = {}
            self.votes = {}  # For tracking votes on scrolls

        def get_scroll(self, scroll_id: str) -> Optional[Dict[str, Any]]:
            """Get a scroll by ID."""
            logger.debug(f"Mock VoiceScrolls: Getting scroll {scroll_id}")
            return self.scrolls.get(scroll_id)

        def vote_on_scroll(self, scroll_id: str, identity_id: str, vote: bool) -> bool:
            """Vote on a scroll."""
            logger.debug(f"Mock VoiceScrolls: {identity_id} voting {vote} on scroll {scroll_id}")
            if scroll_id in self.scrolls:
                if scroll_id not in self.votes:
                    self.votes[scroll_id] = {}
                self.votes[scroll_id][identity_id] = vote
                return True
            return False

        def get_votes(self, scroll_id: str) -> Dict[str, bool]:
            """Get all votes for a scroll."""
            logger.debug(f"Mock VoiceScrolls: Getting votes for scroll {scroll_id}")
            return self.votes.get(scroll_id, {})

    # Initialize mock instances
    yovel = YovelLimiter()
    tokens = TokenRegistry()
    ledger = TokenLedger()
    identity = IdentityRegistry()
    selas = SelaRegistry()
    scrolls = VoiceScrolls()

# Define opcode constants
OP_MINT = "OP_MINT"
OP_SEND = "OP_SEND"
OP_IDENTITY = "OP_IDENTITY"
OP_SCROLL = "OP_SCROLL"
OP_BURN = "OP_BURN"
OP_GRANT_REPUTATION = "OP_GRANT_REPUTATION"
OP_STAKE = "OP_STAKE"
OP_VOTE = "OP_VOTE"
OP_REWARD = "OP_REWARD"

def op_mint(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a token minting transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the sender (optional for coinbase transactions)
            - data: Token data including name, symbol, category, supply, and decimals

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeMintError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        sender = tx.sender
        token_data = tx.data
        tx_id = tx.tx_id
    else:
        sender = tx.get("from", tx.get("sender"))
        token_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating MINT transaction {tx_id}")

    # Check if this is a coinbase transaction (no sender)
    is_coinbase = not sender

    # Validate token data
    if not token_data:
        logger.error(f"MINT denied: missing token data in transaction {tx_id}")
        raise OpcodeMintError("MINT denied: missing token data")

    if "name" not in token_data:
        logger.error(f"MINT denied: missing token name in transaction {tx_id}")
        raise OpcodeMintError("MINT denied: missing token name")

    if "symbol" not in token_data:
        logger.error(f"MINT denied: missing token symbol in transaction {tx_id}")
        raise OpcodeMintError("MINT denied: missing token symbol")

    if "supply" not in token_data:
        logger.error(f"MINT denied: missing token supply in transaction {tx_id}")
        raise OpcodeMintError("MINT denied: missing token supply")

    # Get token category (default to "general")
    category = token_data.get("category", "general")

    # For regular (non-coinbase) transactions, validate sender and Sela registration
    if not is_coinbase:
        # Validate sender
        if not sender:
            logger.error(f"MINT denied: missing sender identity in transaction {tx_id}")
            raise OpcodeMintError("MINT denied: missing sender identity")

        # Check if sender identity exists
        sender_identity = identity.get_identity(sender)
        if not sender_identity:
            logger.error(f"MINT denied: sender identity {sender} does not exist in transaction {tx_id}")
            raise OpcodeMintError(f"MINT denied: sender identity {sender} does not exist")

        # Check if the sender is a Selaholder
        is_selaholder = False
        if "founded_selas" in sender_identity and sender_identity["founded_selas"]:
            is_selaholder = True
        elif "joined_selas" in sender_identity and sender_identity["joined_selas"]:
            is_selaholder = True

        if not is_selaholder:
            logger.error(f"MINT denied: Only Selaholders can mint Mikvah tokens in transaction {tx_id}")
            raise OpcodeMintError("MINT denied: Only Selaholders can mint Mikvah tokens")

        # Check if token symbol already exists
        existing_tokens = Token.find_by_symbol(token_data["symbol"])
        if existing_tokens:
            logger.error(f"MINT denied: token symbol {token_data['symbol']} already exists in transaction {tx_id}")
            raise OpcodeMintError(f"MINT denied: token symbol {token_data['symbol']} already exists")

        # Check Yovel minting cap
        cap = yovel.calculate_mint_cap(sender, category)
        if token_data["supply"] > cap:
            logger.error(f"MINT denied: exceeds mint cap of {cap} for category '{category}' in transaction {tx_id}")
            raise OpcodeMintError(f"MINT denied: exceeds mint cap of {cap} for category '{category}'")

    logger.info(f"MINT transaction {tx_id} is valid")
    return True

def op_send(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a token transfer transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the sender
            - data: Transfer data including token_id, to_id, and amount

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeSendError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        sender = tx.sender
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        sender = tx.get("from", tx.get("sender"))
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating SEND transaction {tx_id}")

    # Validate sender
    if not sender:
        logger.error(f"SEND denied: missing sender identity in transaction {tx_id}")
        raise OpcodeSendError("SEND denied: missing sender identity")

    sender_identity = identity.get_identity(sender)
    if not sender_identity:
        logger.error(f"SEND denied: sender identity {sender} does not exist in transaction {tx_id}")
        raise OpcodeSendError(f"SEND denied: sender identity {sender} does not exist")

    # Validate transaction data
    if not tx_data:
        logger.error(f"SEND denied: missing transaction data in transaction {tx_id}")
        raise OpcodeSendError("SEND denied: missing transaction data")

    token_id = tx_data.get("token_id")
    if not token_id:
        logger.error(f"SEND denied: missing token ID in transaction {tx_id}")
        raise OpcodeSendError("SEND denied: missing token ID")

    # Get recipient ID (support both "to" and "to_id" fields)
    recipient_id = tx_data.get("to_id", tx_data.get("to"))
    if not recipient_id:
        logger.error(f"SEND denied: missing recipient in transaction {tx_id}")
        raise OpcodeSendError("SEND denied: missing recipient")

    recipient_identity = identity.get_identity(recipient_id)
    if not recipient_identity:
        logger.error(f"SEND denied: recipient identity {recipient_id} does not exist in transaction {tx_id}")
        raise OpcodeSendError(f"SEND denied: recipient identity {recipient_id} does not exist")

    amount = tx_data.get("amount")
    if not amount:
        logger.error(f"SEND denied: missing amount in transaction {tx_id}")
        raise OpcodeSendError("SEND denied: missing amount")

    if amount <= 0:
        logger.error(f"SEND denied: amount must be positive in transaction {tx_id}")
        raise OpcodeSendError("SEND denied: amount must be positive")

    # Check if token exists
    token = tokens.get_token(token_id)
    if not token:
        logger.error(f"SEND denied: token {token_id} does not exist in transaction {tx_id}")
        raise OpcodeSendError(f"SEND denied: token {token_id} does not exist")

    # Check balance
    balance = ledger.get_balance(sender, token_id)
    if balance < amount:
        logger.error(f"SEND denied: insufficient balance ({balance} < {amount}) in transaction {tx_id}")
        raise OpcodeSendError(f"SEND denied: insufficient balance ({balance} < {amount})")

    logger.info(f"SEND transaction {tx_id} is valid")
    return True

def op_identity(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate an identity registration transaction.

    Args:
        tx: Transaction data containing:
            - data: Identity data including identity_id, name, and public_key

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeIdentityError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating IDENTITY transaction {tx_id}")

    # Validate transaction data
    if not tx_data:
        logger.error(f"IDENTITY denied: missing identity data in transaction {tx_id}")
        raise OpcodeIdentityError("IDENTITY denied: missing identity data")

    identity_id = tx_data.get("identity_id")
    if not identity_id:
        logger.error(f"IDENTITY denied: missing identity ID in transaction {tx_id}")
        raise OpcodeIdentityError("IDENTITY denied: missing identity ID")

    name = tx_data.get("name")
    if not name:
        logger.error(f"IDENTITY denied: missing identity name in transaction {tx_id}")
        raise OpcodeIdentityError("IDENTITY denied: missing identity name")

    public_key = tx_data.get("public_key")
    if not public_key:
        logger.error(f"IDENTITY denied: missing public key in transaction {tx_id}")
        raise OpcodeIdentityError("IDENTITY denied: missing public key")

    # Check if identity already exists
    existing_identity = identity.get_identity(identity_id)
    if existing_identity:
        logger.error(f"IDENTITY denied: identity {identity_id} already exists in transaction {tx_id}")
        raise OpcodeIdentityError(f"IDENTITY denied: identity {identity_id} already exists")

    # Check if identity exists in the database
    db_identity = Identity.get_by_id(identity_id)
    if db_identity:
        logger.error(f"IDENTITY denied: identity {identity_id} already exists in database in transaction {tx_id}")
        raise OpcodeIdentityError(f"IDENTITY denied: identity {identity_id} already exists in database")

    logger.info(f"IDENTITY transaction {tx_id} is valid")
    return True

def op_scroll(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a Voice Scroll proposal transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the sender
            - data: Scroll data including title, description, and category

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeScrollError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        sender = tx.sender
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        sender = tx.get("from", tx.get("sender"))
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating SCROLL transaction {tx_id}")

    # Validate sender
    if not sender:
        logger.error(f"SCROLL denied: missing sender identity in transaction {tx_id}")
        raise OpcodeScrollError("SCROLL denied: missing sender identity")

    sender_identity = identity.get_identity(sender)
    if not sender_identity:
        logger.error(f"SCROLL denied: sender identity {sender} does not exist in transaction {tx_id}")
        raise OpcodeScrollError(f"SCROLL denied: sender identity {sender} does not exist")

    # Validate transaction data
    if not tx_data:
        logger.error(f"SCROLL denied: missing scroll data in transaction {tx_id}")
        raise OpcodeScrollError("SCROLL denied: missing scroll data")

    title = tx_data.get("title")
    if not title:
        logger.error(f"SCROLL denied: missing scroll title in transaction {tx_id}")
        raise OpcodeScrollError("SCROLL denied: missing scroll title")

    description = tx_data.get("description")
    if not description:
        logger.error(f"SCROLL denied: missing scroll description in transaction {tx_id}")
        raise OpcodeScrollError("SCROLL denied: missing scroll description")

    category = tx_data.get("category")
    if not category:
        logger.error(f"SCROLL denied: missing scroll category in transaction {tx_id}")
        raise OpcodeScrollError("SCROLL denied: missing scroll category")

    # Check if sender has the required badge
    if not identity.has_badge(sender, "CREATOR"):
        logger.error(f"SCROLL denied: identity {sender} must have CREATOR badge or above in transaction {tx_id}")
        raise OpcodeScrollError(f"SCROLL denied: identity {sender} must have CREATOR badge or above")

    logger.info(f"SCROLL transaction {tx_id} is valid")
    return True

def op_burn(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a token burning transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the sender
            - data: Burn data including token_id and amount

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeBurnError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        sender = tx.sender
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        sender = tx.get("from", tx.get("sender"))
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating BURN transaction {tx_id}")

    # Validate sender
    if not sender:
        logger.error(f"BURN denied: missing sender identity in transaction {tx_id}")
        raise OpcodeBurnError("BURN denied: missing sender identity")

    sender_identity = identity.get_identity(sender)
    if not sender_identity:
        logger.error(f"BURN denied: sender identity {sender} does not exist in transaction {tx_id}")
        raise OpcodeBurnError(f"BURN denied: sender identity {sender} does not exist")

    # Validate transaction data
    if not tx_data:
        logger.error(f"BURN denied: missing transaction data in transaction {tx_id}")
        raise OpcodeBurnError("BURN denied: missing transaction data")

    token_id = tx_data.get("token_id")
    if not token_id:
        logger.error(f"BURN denied: missing token ID in transaction {tx_id}")
        raise OpcodeBurnError("BURN denied: missing token ID")

    amount = tx_data.get("amount")
    if not amount:
        logger.error(f"BURN denied: missing amount in transaction {tx_id}")
        raise OpcodeBurnError("BURN denied: missing amount")

    if amount <= 0:
        logger.error(f"BURN denied: amount must be positive in transaction {tx_id}")
        raise OpcodeBurnError("BURN denied: amount must be positive")

    # Check if token exists
    token = tokens.get_token(token_id)
    if not token:
        logger.error(f"BURN denied: token {token_id} does not exist in transaction {tx_id}")
        raise OpcodeBurnError(f"BURN denied: token {token_id} does not exist")

    # Check if sender is the token creator
    if token.get("creator") != sender:
        logger.error(f"BURN denied: only token creator can burn tokens in transaction {tx_id}")
        raise OpcodeBurnError("BURN denied: only token creator can burn tokens")

    # Check balance
    balance = ledger.get_balance(sender, token_id)
    if balance < amount:
        logger.error(f"BURN denied: insufficient balance ({balance} < {amount}) in transaction {tx_id}")
        raise OpcodeBurnError(f"BURN denied: insufficient balance ({balance} < {amount})")

    logger.info(f"BURN transaction {tx_id} is valid")
    return True

def op_grant_reputation(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a reputation granting transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the granter
            - data: Reputation data including to_identity and amount

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeGrantReputationError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        granter = tx.sender
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        granter = tx.get("from", tx.get("sender"))
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating GRANT_REPUTATION transaction {tx_id}")

    # Validate granter
    if not granter:
        logger.error(f"GRANT_REPUTATION denied: missing granter identity in transaction {tx_id}")
        raise OpcodeGrantReputationError("GRANT_REPUTATION denied: missing granter identity")

    granter_identity = identity.get_identity(granter)
    if not granter_identity:
        logger.error(f"GRANT_REPUTATION denied: granter identity {granter} does not exist in transaction {tx_id}")
        raise OpcodeGrantReputationError(f"GRANT_REPUTATION denied: granter identity {granter} does not exist")

    # Check if granter has the required badge
    if not identity.has_badge(granter, "VALIDATOR"):
        logger.error(f"GRANT_REPUTATION denied: granter {granter} must have VALIDATOR badge in transaction {tx_id}")
        raise OpcodeGrantReputationError(f"GRANT_REPUTATION denied: granter {granter} must have VALIDATOR badge")

    # Validate transaction data
    if not tx_data:
        logger.error(f"GRANT_REPUTATION denied: missing transaction data in transaction {tx_id}")
        raise OpcodeGrantReputationError("GRANT_REPUTATION denied: missing transaction data")

    # Get recipient ID (support both "to_identity" and "to_id" fields)
    recipient_id = tx_data.get("to_identity", tx_data.get("to_id"))
    if not recipient_id:
        logger.error(f"GRANT_REPUTATION denied: missing recipient identity in transaction {tx_id}")
        raise OpcodeGrantReputationError("GRANT_REPUTATION denied: missing recipient identity")

    recipient_identity = identity.get_identity(recipient_id)
    if not recipient_identity:
        logger.error(f"GRANT_REPUTATION denied: recipient identity {recipient_id} does not exist in transaction {tx_id}")
        raise OpcodeGrantReputationError(f"GRANT_REPUTATION denied: recipient identity {recipient_id} does not exist")

    amount = tx_data.get("amount")
    if amount is None:
        logger.error(f"GRANT_REPUTATION denied: missing amount in transaction {tx_id}")
        raise OpcodeGrantReputationError("GRANT_REPUTATION denied: missing amount")

    if amount <= 0:
        logger.error(f"GRANT_REPUTATION denied: amount must be positive in transaction {tx_id}")
        raise OpcodeGrantReputationError("GRANT_REPUTATION denied: amount must be positive")

    # Check if amount is within limits
    if amount > 100:
        logger.error(f"GRANT_REPUTATION denied: amount {amount} exceeds maximum (100) in transaction {tx_id}")
        raise OpcodeGrantReputationError(f"GRANT_REPUTATION denied: amount {amount} exceeds maximum (100)")

    logger.info(f"GRANT_REPUTATION transaction {tx_id} is valid")
    return True

def op_stake(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a token staking transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the staker
            - data: Stake data including token_id, amount, and duration

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeStakeError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        staker = tx.sender
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        staker = tx.get("from", tx.get("sender"))
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating STAKE transaction {tx_id}")

    # Validate staker
    if not staker:
        logger.error(f"STAKE denied: missing staker identity in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: missing staker identity")

    staker_identity = identity.get_identity(staker)
    if not staker_identity:
        logger.error(f"STAKE denied: staker identity {staker} does not exist in transaction {tx_id}")
        raise OpcodeStakeError(f"STAKE denied: staker identity {staker} does not exist")

    # Validate transaction data
    if not tx_data:
        logger.error(f"STAKE denied: missing transaction data in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: missing transaction data")

    token_id = tx_data.get("token_id")
    if not token_id:
        logger.error(f"STAKE denied: missing token ID in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: missing token ID")

    # Check if token exists
    token = tokens.get_token(token_id)
    if not token:
        logger.error(f"STAKE denied: token {token_id} does not exist in transaction {tx_id}")
        raise OpcodeStakeError(f"STAKE denied: token {token_id} does not exist")

    amount = tx_data.get("amount")
    if not amount:
        logger.error(f"STAKE denied: missing amount in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: missing amount")

    if amount <= 0:
        logger.error(f"STAKE denied: amount must be positive in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: amount must be positive")

    duration = tx_data.get("duration")
    if not duration:
        logger.error(f"STAKE denied: missing duration in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: missing duration")

    if duration <= 0:
        logger.error(f"STAKE denied: duration must be positive in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: duration must be positive")

    # Check balance
    balance = ledger.get_balance(staker, token_id)
    if balance < amount:
        logger.error(f"STAKE denied: insufficient balance ({balance} < {amount}) in transaction {tx_id}")
        raise OpcodeStakeError(f"STAKE denied: insufficient balance ({balance} < {amount})")

    # Check if token is stakeable (ONX token)
    if token_id != "ONX":
        logger.error(f"STAKE denied: only ONX tokens can be staked in transaction {tx_id}")
        raise OpcodeStakeError("STAKE denied: only ONX tokens can be staked")

    logger.info(f"STAKE transaction {tx_id} is valid")
    return True

def op_vote(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a governance voting transaction.

    Args:
        tx: Transaction data containing:
            - from/sender: Identity ID of the voter
            - data: Vote data including scroll_id and vote

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeVoteError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        voter = tx.sender
        tx_data = tx.data
        tx_id = tx.tx_id
    else:
        voter = tx.get("from", tx.get("sender"))
        tx_data = tx.get("data", {})
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Validating VOTE transaction {tx_id}")

    # Validate voter
    if not voter:
        logger.error(f"VOTE denied: missing voter identity in transaction {tx_id}")
        raise OpcodeVoteError("VOTE denied: missing voter identity")

    voter_identity = identity.get_identity(voter)
    if not voter_identity:
        logger.error(f"VOTE denied: voter identity {voter} does not exist in transaction {tx_id}")
        raise OpcodeVoteError(f"VOTE denied: voter identity {voter} does not exist")

    # Validate transaction data
    if not tx_data:
        logger.error(f"VOTE denied: missing transaction data in transaction {tx_id}")
        raise OpcodeVoteError("VOTE denied: missing transaction data")

    scroll_id = tx_data.get("scroll_id")
    if not scroll_id:
        logger.error(f"VOTE denied: missing scroll ID in transaction {tx_id}")
        raise OpcodeVoteError("VOTE denied: missing scroll ID")

    # Check if scroll exists
    scroll = scrolls.get_scroll(scroll_id)
    if not scroll:
        logger.error(f"VOTE denied: scroll {scroll_id} does not exist in transaction {tx_id}")
        raise OpcodeVoteError(f"VOTE denied: scroll {scroll_id} does not exist")

    vote_value = tx_data.get("vote")
    if vote_value is None:
        logger.error(f"VOTE denied: missing vote value in transaction {tx_id}")
        raise OpcodeVoteError("VOTE denied: missing vote value")

    # Check if vote value is valid (true/false or 1/0)
    if not isinstance(vote_value, (bool, int)) or (isinstance(vote_value, int) and vote_value not in [0, 1]):
        logger.error(f"VOTE denied: vote must be true/false or 1/0 in transaction {tx_id}")
        raise OpcodeVoteError("VOTE denied: vote must be true/false or 1/0")

    # Check if voter has minimum Etzem score
    reputation = identity.get_reputation(voter)
    if reputation < 10:
        logger.error(f"VOTE denied: insufficient reputation score ({reputation} < 10) in transaction {tx_id}")
        raise OpcodeVoteError(f"VOTE denied: insufficient reputation score ({reputation} < 10)")

    logger.info(f"VOTE transaction {tx_id} is valid")
    return True

def op_reward(tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Validate a mining reward transaction.

    Args:
        tx: Transaction data containing:
            - to/to_id: Identity ID of the reward recipient
            - amount: Reward amount
            - token_id: Token ID (usually ONX)

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeRewardError: If the transaction is invalid
    """
    # Extract data based on transaction type
    if isinstance(tx, Transaction):
        tx_data = tx.data
        tx_id = tx.tx_id

        # Get recipient from data
        recipient = tx_data.get("to_id", tx_data.get("to"))
        amount = tx_data.get("amount")
        token_id = tx_data.get("token_id")
    else:
        tx_id = tx.get("tx_id", "unknown")

        # Get recipient directly from tx or from data
        recipient = tx.get("to", tx.get("to_id"))
        if not recipient and "data" in tx:
            recipient = tx["data"].get("to_id", tx["data"].get("to"))

        amount = tx.get("amount")
        if amount is None and "data" in tx:
            amount = tx["data"].get("amount")

        token_id = tx.get("token_id")
        if not token_id and "data" in tx:
            token_id = tx["data"].get("token_id")

    logger.debug(f"Validating REWARD transaction {tx_id}")

    # Validate transaction data
    if not recipient:
        logger.error(f"REWARD denied: missing recipient identity in transaction {tx_id}")
        raise OpcodeRewardError("REWARD denied: missing recipient identity")

    if amount is None:
        logger.error(f"REWARD denied: missing reward amount in transaction {tx_id}")
        raise OpcodeRewardError("REWARD denied: missing reward amount")

    if not token_id:
        logger.error(f"REWARD denied: missing token ID in transaction {tx_id}")
        raise OpcodeRewardError("REWARD denied: missing token ID")

    # Validate recipient identity
    recipient_identity = identity.get_identity(recipient)
    if not recipient_identity:
        # For coinbase transactions, we'll allow non-existent identities
        # They will be created when the reward is claimed
        logger.warning(f"Recipient identity {recipient} does not exist in transaction {tx_id}, will be created later")
        pass

    # Validate reward amount
    if amount <= 0:
        logger.error(f"REWARD denied: amount must be positive in transaction {tx_id}")
        raise OpcodeRewardError("REWARD denied: amount must be positive")

    # Validate token ID
    if token_id != "ONX":
        logger.error(f"REWARD denied: token must be ONX in transaction {tx_id}")
        raise OpcodeRewardError("REWARD denied: token must be ONX")

    logger.info(f"REWARD transaction {tx_id} is valid")
    return True

# Opcode mapping
OPCODES = {
    OP_MINT: op_mint,
    OP_SEND: op_send,
    OP_IDENTITY: op_identity,
    OP_SCROLL: op_scroll,
    OP_BURN: op_burn,
    OP_GRANT_REPUTATION: op_grant_reputation,
    OP_STAKE: op_stake,
    OP_VOTE: op_vote,
    OP_REWARD: op_reward
}

def execute_opcode(opcode: str, tx: Union[Dict[str, Any], Transaction]) -> bool:
    """
    Execute the specified opcode with the given transaction data.

    Args:
        opcode: The opcode to execute
        tx: Transaction data (can be a dict or Transaction object)

    Returns:
        bool: True if the transaction is valid

    Raises:
        OpcodeError: If the opcode is invalid or the transaction is invalid
    """
    # Get transaction ID for logging
    if isinstance(tx, Transaction):
        tx_id = tx.tx_id
    else:
        tx_id = tx.get("tx_id", "unknown")

    logger.debug(f"Executing opcode {opcode} for transaction {tx_id}")

    if opcode not in OPCODES:
        logger.error(f"Invalid opcode: {opcode} in transaction {tx_id}")
        raise OpcodeError(f"Invalid opcode: {opcode}")

    try:
        result = OPCODES[opcode](tx)
        logger.info(f"Opcode {opcode} executed successfully for transaction {tx_id}")
        return result
    except OpcodeError as e:
        logger.error(f"Opcode {opcode} execution failed for transaction {tx_id}: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error executing opcode {opcode} for transaction {tx_id}: {str(e)}")
        raise OpcodeError(f"Unexpected error: {str(e)}")
