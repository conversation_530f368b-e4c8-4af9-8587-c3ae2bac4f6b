# src/cli/cli.py

import sys
import os
import json
import time
import logging
import uuid

from src.tokens.schema import TokenType
from src.tokens.registry import TokenRegistry
from src.tokens.ledger import TokenLedger
from src.vm.onnyxscript import OnnyxScriptVM
from src.identity.registry import IdentityRegistry
from src.wallet.wallet import OnnyxWallet
from src.node.node import OnnyxNode

from models.token import Token
from models.identity import Identity
from models.transaction import Transaction
from models.block import Block
from config.config import onnyx_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(onnyx_config.runtime_dir, "cli.log"))
    ]
)
logger = logging.getLogger("onnyx.cli")

# Initialize registries and VM
registry = TokenRegistry()
identity_registry = IdentityRegistry()
ledger = TokenLedger(token_registry=registry)
vm = OnnyxScriptVM(registry=registry, ledger=ledger)

logger.info("Onnyx CLI initialized")

def create_identity():
    """Create a new identity."""
    print("\n=== Create New Identity ===\n")
    logger.info("Creating new identity")

    name = input("Enter identity name: ")
    pubkey = input("Enter public key (or press Enter to generate one): ")

    if not pubkey:
        # In a real implementation, we would generate a proper key pair
        import hashlib
        pubkey = hashlib.sha256(f"{name}{time.time()}".encode()).hexdigest()
        print(f"Generated public key: {pubkey}")

    metadata = {}
    metadata["type"] = input("Type (individual, business, DAO): ")
    url = input("URL (optional): ")
    if url:
        metadata["url"] = url
    description = input("Description (optional): ")
    if description:
        metadata["description"] = description

    try:
        # Generate a unique identity ID
        identity_id = f"id_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Create the identity in the database
        identity = Identity.create(
            identity_id=identity_id,
            name=name,
            public_key=pubkey,
            metadata=metadata
        )

        # Register the identity with the registry
        identity_registry.register_identity(name, pubkey, metadata, identity_id)

        logger.info(f"Identity created: {identity_id}")
        print(f"\n[SUCCESS] Identity created!")
        print(f"Identity ID: {identity.identity_id}")
        print(f"Name: {identity.name}")
        print(f"Public Key: {identity.public_key}")
        print(f"Metadata: {identity.metadata}")
    except Exception as e:
        logger.error(f"Failed to create identity: {str(e)}")
        print(f"\n[ERROR] Failed to create identity: {str(e)}")

def list_identities():
    """List all registered identities."""
    print("\n=== Registered Identities ===\n")
    logger.info("Listing all identities")

    # Get identities from the database
    identities = Identity.get_all()

    if not identities:
        logger.info("No identities found")
        print("No identities registered yet.")
        return

    for identity in identities:
        print(f"ID: {identity.identity_id}")
        print(f"Name: {identity.name}")
        print(f"Public Key: {identity.public_key[:10]}...{identity.public_key[-10:]}")
        print(f"Type: {identity.metadata.get('type', 'N/A')}")
        print(f"Reputation Score: {identity.metadata.get('reputation', {}).get('score', 0)}")

        # Get tokens associated with this identity
        tokens = Token.find_by_creator(identity.identity_id)
        print(f"Tokens: {len(tokens)}")

        print("-" * 40)

    logger.info(f"Listed {len(identities)} identities")

def spawn_token():
    """Spawn a new token."""
    print("\n=== Spawn New Token ===\n")
    logger.info("Spawning new token")

    # First, list identities to help the user choose a creator
    list_identities()

    creator_id = input("\nCreator Identity ID: ")

    # Verify that the identity exists
    creator = Identity.get_by_id(creator_id)
    if not creator:
        logger.warning(f"Identity {creator_id} not found")
        print(f"[ERROR] Identity {creator_id} not found.")
        return

    name = input("Token Name: ")
    symbol = input("Symbol: ")
    token_type_str = input("Token Type (BASE, IDENTITY, FORKED, ACCESS, REPUTATION): ").upper()

    # Validate token type
    try:
        token_type = TokenType(token_type_str)
    except ValueError:
        logger.warning(f"Invalid token type: {token_type_str}")
        print(f"[ERROR] Invalid token type: {token_type_str}")
        print(f"Valid types: {', '.join([t.value for t in TokenType])}")
        return

    try:
        supply = int(input("Initial Supply: "))
    except ValueError:
        logger.warning("Supply must be a number")
        print("[ERROR] Supply must be a number.")
        return

    decimals = input("Decimals (default: 2): ")
    decimals = int(decimals) if decimals else 2

    mintable = input("Mintable (y/n, default: n): ").lower() == "y"
    transferable = input("Transferable (y/n, default: y): ").lower() != "n"

    metadata = {}
    description = input("Token Description: ")
    if description:
        metadata["description"] = description
    url = input("Token URL (optional): ")
    if url:
        metadata["url"] = url

    # Add decimals, mintable, and transferable to metadata
    metadata["decimals"] = decimals
    metadata["mintable"] = mintable
    metadata["transferable"] = transferable

    try:
        # Generate a unique token ID
        token_id = f"token_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Create the token in the database
        Token.create(
            token_id=token_id,
            name=name,
            symbol=symbol,
            creator_id=creator_id,
            token_type=token_type.value,
            supply=supply,
            metadata=metadata
        )

        # Use the VM to spawn the token
        script = [
            ("OP_SPAWN_TOKEN", (name, symbol, creator_id, token_type.value, supply, metadata, mintable, transferable))
        ]
        vm.execute(script)

        # Update the creator's reputation
        creator_reputation = creator.metadata.get("reputation", {})
        creator_reputation["minted_tokens"] = creator_reputation.get("minted_tokens", 0) + 1
        creator_reputation["score"] = creator_reputation.get("score", 0) + 5
        creator.metadata["reputation"] = creator_reputation
        creator.save()

        logger.info(f"Token spawned: {token_id}")
        print(f"\n[SUCCESS] Token spawned!")
        print(f"Token ID: {token_id}")
        print(f"Name: {name}")
        print(f"Symbol: {symbol}")
        print(f"Creator: {creator.name} ({creator_id})")
        print(f"Initial Supply: {supply}")
        print(f"Mintable: {mintable}")
        print(f"Transferable: {transferable}")
    except Exception as e:
        logger.error(f"Failed to spawn token: {str(e)}")
        print(f"\n[ERROR] Failed to spawn token: {str(e)}")

def list_tokens():
    """List all registered tokens."""
    print("\n=== Registered Tokens ===\n")
    logger.info("Listing all tokens")

    # Get tokens from the database
    tokens = Token.get_all()

    if not tokens:
        logger.info("No tokens found")
        print("No tokens registered yet.")
        return

    for token in tokens:
        creator = Identity.get_by_id(token.creator_id)
        creator_name = creator.name if creator else "Unknown"

        print(f"ID: {token.token_id}")
        print(f"Name: {token.name}")
        print(f"Symbol: {token.symbol}")
        print(f"Creator: {creator_name} ({token.creator_id})")
        print(f"Type: {token.token_type}")
        print(f"Supply: {token.supply}")
        print(f"Mintable: {token.metadata.get('mintable', False)}")
        print(f"Transferable: {token.metadata.get('transferable', True)}")
        print("-" * 40)

    logger.info(f"Listed {len(tokens)} tokens")

def mint_token():
    """Mint additional tokens."""
    print("\n=== Mint Tokens ===\n")
    logger.info("Minting tokens")

    # List tokens to help the user choose
    list_tokens()

    token_id = input("\nToken ID: ")

    # Verify that the token exists
    token = Token.get_by_id(token_id)
    if not token:
        logger.warning(f"Token {token_id} not found")
        print(f"[ERROR] Token {token_id} not found.")
        return

    if not token.metadata.get("mintable", False):
        logger.warning(f"Token {token.name} ({token_id}) is not mintable")
        print(f"[ERROR] Token {token.name} ({token_id}) is not mintable.")
        return

    # List identities to help the user choose a minter
    list_identities()

    minter_id = input("\nMinter Identity ID (must be token creator): ")

    # Verify that the minter is the token creator
    if minter_id != token.creator_id:
        logger.warning(f"Only the token creator can mint tokens")
        print(f"[ERROR] Only the token creator can mint tokens.")
        return

    try:
        amount = int(input("Amount to Mint: "))
    except ValueError:
        logger.warning("Amount must be a number")
        print("[ERROR] Amount must be a number.")
        return

    to_address = input("Recipient Address (default: minter): ")
    if not to_address:
        to_address = minter_id

    try:
        # Create a mint transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        tx = Transaction.create(
            tx_id=tx_id,
            tx_type="MINT",
            sender=minter_id,
            recipient=to_address,
            token_id=token_id,
            amount=amount,
            status="PENDING",
            data={
                "op": "OP_MINT_TOKEN",
                "token_id": token_id,
                "amount": amount,
                "to_id": to_address,
                "minter_id": minter_id
            }
        )

        # Use the VM to mint tokens
        script = [
            ("OP_MINT_TOKEN", (token_id, amount, to_address, minter_id))
        ]
        vm.execute(script)

        # Update the token supply
        token.supply += amount
        token.save()

        # Update the minter's reputation
        minter = Identity.get_by_id(minter_id)
        if minter:
            minter_reputation = minter.metadata.get("reputation", {})
            minter_reputation["transactions"] = minter_reputation.get("transactions", 0) + 1
            minter_reputation["score"] = minter_reputation.get("score", 0) + 1
            minter.metadata["reputation"] = minter_reputation
            minter.save()

        # Update transaction status
        tx.status = "CONFIRMED"
        tx.save()

        logger.info(f"Minted {amount} {token.symbol} to {to_address}")
        print(f"\n[SUCCESS] Minted {amount} {token.symbol} to {to_address}")
    except Exception as e:
        logger.error(f"Failed to mint tokens: {str(e)}")
        print(f"\n[ERROR] Failed to mint tokens: {str(e)}")

def transfer_token():
    """Transfer tokens from one address to another."""
    print("\n=== Transfer Tokens ===\n")
    logger.info("Transferring tokens")

    # List tokens to help the user choose
    list_tokens()

    token_id = input("\nToken ID: ")

    # Verify that the token exists
    token = Token.get_by_id(token_id)
    if not token:
        logger.warning(f"Token {token_id} not found")
        print(f"[ERROR] Token {token_id} not found.")
        return

    if not token.metadata.get("transferable", True):
        logger.warning(f"Token {token.name} ({token_id}) is not transferable")
        print(f"[ERROR] Token {token.name} ({token_id}) is not transferable.")
        return

    from_address = input("Sender Address: ")
    to_address = input("Recipient Address: ")

    # Check if the sender has enough balance
    balance = ledger.get_balance(from_address, token_id)
    print(f"Current balance: {balance} {token.symbol}")

    try:
        amount = int(input("Amount to Transfer: "))
    except ValueError:
        logger.warning("Amount must be a number")
        print("[ERROR] Amount must be a number.")
        return

    if balance < amount:
        logger.warning(f"Insufficient balance. {from_address} has {balance} {token.symbol}")
        print(f"[ERROR] Insufficient balance. {from_address} has {balance} {token.symbol}.")
        return

    memo = input("Memo (optional): ")

    try:
        # Create a transfer transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        tx = Transaction.create(
            tx_id=tx_id,
            tx_type="TRANSFER",
            sender=from_address,
            recipient=to_address,
            token_id=token_id,
            amount=amount,
            status="PENDING",
            data={
                "op": "OP_TRANSFER_TOKEN",
                "token_id": token_id,
                "from_id": from_address,
                "to_id": to_address,
                "amount": amount,
                "memo": memo
            }
        )

        # Use the VM to transfer tokens
        script = [
            ("OP_TRANSFER_TOKEN", (token_id, from_address, to_address, amount, memo))
        ]
        vm.execute(script)

        # Update reputation for both sender and recipient if they are identities
        sender = Identity.get_by_id(from_address)
        if sender:
            sender_reputation = sender.metadata.get("reputation", {})
            sender_reputation["transactions"] = sender_reputation.get("transactions", 0) + 1
            sender_reputation["score"] = sender_reputation.get("score", 0) + 1
            sender.metadata["reputation"] = sender_reputation
            sender.save()

        recipient = Identity.get_by_id(to_address)
        if recipient:
            recipient_reputation = recipient.metadata.get("reputation", {})
            recipient_reputation["transactions"] = recipient_reputation.get("transactions", 0) + 1
            recipient_reputation["score"] = recipient_reputation.get("score", 0) + 1
            recipient.metadata["reputation"] = recipient_reputation
            recipient.save()

        # Update transaction status
        tx.status = "CONFIRMED"
        tx.save()

        logger.info(f"Transferred {amount} {token.symbol} from {from_address} to {to_address}")
        print(f"\n[SUCCESS] Transferred {amount} {token.symbol} from {from_address} to {to_address}")
    except Exception as e:
        logger.error(f"Failed to transfer tokens: {str(e)}")
        print(f"\n[ERROR] Failed to transfer tokens: {str(e)}")

def check_balance():
    """Check token balance for an address."""
    print("\n=== Check Token Balance ===\n")
    logger.info("Checking token balance")

    address = input("Address: ")

    # Get all balances for the address
    balances = ledger.get_all_balances(address)

    if not balances:
        logger.info(f"{address} has no token balances")
        print(f"{address} has no token balances.")
        return

    print(f"\nBalances for {address}:")
    print("-" * 40)

    for token_id, amount in balances.items():
        token = Token.get_by_id(token_id)
        if token:
            print(f"{amount} {token.symbol} ({token.name})")
        else:
            print(f"{amount} {token_id} (Unknown Token)")

    logger.info(f"Listed balances for {address}")

def get_metadata():
    """Get metadata for a token."""
    print("\n=== Get Token Metadata ===\n")
    logger.info("Getting token metadata")

    # List tokens to help the user choose
    list_tokens()

    token_id = input("\nToken ID: ")

    # Verify that the token exists
    token = Token.get_by_id(token_id)
    if not token:
        logger.warning(f"Token {token_id} not found")
        print(f"[ERROR] Token {token_id} not found.")
        return

    # Get token metadata directly from the database
    metadata = token.metadata

    print(f"\nMetadata for {token.name} ({token.symbol}):")
    print("-" * 40)

    if not metadata:
        logger.info(f"No metadata available for token {token_id}")
        print("No metadata available.")
        return

    for key, value in metadata.items():
        print(f"{key}: {value}")

    logger.info(f"Retrieved metadata for token {token_id}")

def wallet_info():
    """Display wallet information."""
    print("\n=== Wallet Information ===\n")
    logger.info("Displaying wallet information")

    wallet = OnnyxWallet()

    print(f"Address: {wallet.get_address()}")
    print(f"Public Key: {wallet.get_public_key()}")
    print("\nPrivate Key: (DO NOT SHARE!)")
    print(f"{wallet.get_private_key()}")

    logger.info("Wallet information displayed")

def sign_message():
    """Sign a message with the wallet's private key."""
    print("\n=== Sign Message ===\n")
    logger.info("Signing message")

    wallet = OnnyxWallet()

    print(f"Using address: {wallet.get_address()}")
    message = input("Message to sign: ")

    signature = wallet.sign(message)

    logger.info(f"Message signed by {wallet.get_address()}")
    print("\n[SUCCESS] Message signed!")
    print(f"Message: {message}")
    print(f"Signature: {signature}")
    print("\nTo verify this signature, use the 'verify' command with your public key:")
    print(f"Public Key: {wallet.get_public_key()}")

def verify_message():
    """Verify a signed message."""
    print("\n=== Verify Message ===\n")
    logger.info("Verifying message signature")

    message = input("Message: ")
    signature = input("Signature: ")

    # Ask if verifying with the current wallet or an external public key
    use_current = input("Use current wallet for verification? (y/n, default: y): ").lower() != "n"

    if use_current:
        wallet = OnnyxWallet()
        result = wallet.verify(message, signature)
        logger.info(f"Verifying with current wallet: {wallet.get_address()}")
    else:
        public_key = input("Public Key: ")
        result = OnnyxWallet.verify_external(message, signature, public_key)
        logger.info(f"Verifying with external public key: {public_key}")

    if result:
        logger.info("Signature verification successful")
        print("\n[SUCCESS] Signature is valid!")
    else:
        logger.warning("Signature verification failed")
        print("\n[ERROR] Invalid signature.")

def generate_wallet():
    """Generate a new wallet."""
    print("\n=== Generate New Wallet ===\n")
    logger.info("Generating new wallet")

    # Ask for confirmation
    confirm = input("This will overwrite your existing wallet. Continue? (y/n): ").lower()
    if confirm != "y":
        logger.info("Wallet generation cancelled by user")
        print("Operation cancelled.")
        return

    wallet = OnnyxWallet()
    address = wallet.generate()

    logger.info(f"New wallet generated with address: {address}")
    print("\n[SUCCESS] New wallet generated!")
    print(f"Address: {address}")
    print(f"Public Key: {wallet.get_public_key()}")
    print("\nPrivate Key: (DO NOT SHARE!)")
    print(f"{wallet.get_private_key()}")

def mine_block():
    """Mine a new block with transactions from the mempool."""
    print("\n=== Mine Block ===\n")
    logger.info("Mining a new block")

    node = OnnyxNode()

    # Check if there are any transactions in the mempool
    mempool = node.get_mempool()
    if not mempool:
        logger.info("Mempool is empty")
        print("Mempool is empty. Add some transactions first or mine an empty block.")
        confirm = input("Mine empty block? (y/n): ").lower()
        if confirm != "y":
            logger.info("Mining cancelled by user")
            print("Mining cancelled.")
            return
    else:
        logger.info(f"Mempool contains {len(mempool)} transactions")
        print(f"Mempool contains {len(mempool)} transactions.")

    # Mine the block
    block_data = node.mine_block()

    # Create a Block object in the database
    block = Block.create(
        block_hash=block_data['hash'],
        previous_hash=block_data['previous_hash'],
        index=block_data['index'],
        timestamp=block_data['timestamp'],
        transactions=block_data['transactions'],
        nonce=block_data['nonce'],
        difficulty=block_data.get('difficulty', 4),
        miner=block_data.get('miner', 'unknown')
    )

    logger.info(f"Block #{block.index} mined with hash {block.block_hash}")
    print("\n[SUCCESS] Block mined!")
    print(f"Block #{block.index}")
    print(f"Hash: {block.block_hash}")
    print(f"Transactions: {len(block.transactions)}")
    print(f"Timestamp: {block.timestamp}")

    # Update transaction statuses
    for tx_data in block.transactions:
        if isinstance(tx_data, dict) and 'tx_id' in tx_data:
            tx = Transaction.get_by_id(tx_data['tx_id'])
            if tx:
                tx.status = "CONFIRMED"
                tx.block_hash = block.block_hash
                tx.block_index = block.index
                tx.save()
                logger.info(f"Transaction {tx.tx_id} confirmed in block #{block.index}")

    return block

def node_status():
    """Display the current status of the node."""
    print("\n=== Node Status ===\n")
    logger.info("Displaying node status")

    # We don't need the node instance anymore since we're using the database

    # Get blockchain height from the database
    latest_block = Block.get_latest()
    height = latest_block.index + 1 if latest_block else 0

    # Get mempool size
    pending_txs = Transaction.find_by_status("PENDING")
    mempool_size = len(pending_txs)

    print(f"Blockchain Height: {height}")
    if latest_block:
        print(f"Latest Block: #{latest_block.index} ({latest_block.block_hash[:8]}...)")
    else:
        print("No blocks in the chain yet")
    print(f"Mempool Size: {mempool_size} transactions")

    if latest_block:
        print("\nLatest Block Details:")
        print(f"Index: {latest_block.index}")
        print(f"Hash: {latest_block.block_hash}")
        print(f"Previous Hash: {latest_block.previous_hash}")
        print(f"Timestamp: {latest_block.timestamp}")
        print(f"Transactions: {len(latest_block.transactions)}")
        print(f"Nonce: {latest_block.nonce}")
        print(f"Miner: {latest_block.miner}")

    logger.info("Node status displayed")

def chain_dump():
    """Dump the entire blockchain."""
    print("\n=== Blockchain Dump ===\n")
    logger.info("Dumping blockchain")

    # Get all blocks from the database
    blocks = Block.get_all()

    if not blocks:
        logger.info("Blockchain is empty")
        print("Blockchain is empty.")
        return

    print(f"Chain Length: {len(blocks)} blocks\n")

    for block in blocks:
        print(f"Block #{block.index}")
        print(f"Hash: {block.block_hash}")
        print(f"Previous Hash: {block.previous_hash}")
        print(f"Timestamp: {block.timestamp}")
        print(f"Transactions: {len(block.transactions)}")
        print(f"Nonce: {block.nonce}")
        print(f"Miner: {block.miner}")
        print("-" * 40)

    logger.info(f"Dumped {len(blocks)} blocks")

def add_transaction():
    """Add a transaction to the mempool."""
    print("\n=== Add Transaction ===\n")
    logger.info("Adding transaction to mempool")

    # Get transaction details
    tx_type = input("Transaction Type (transfer, message, data): ")
    sender = input("Sender: ")
    recipient = input("Recipient: ")

    # Generate a unique transaction ID
    tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
    timestamp = int(time.time())

    if tx_type == "transfer":
        token_id = input("Token ID: ")
        try:
            amount = int(input("Amount: "))
        except ValueError:
            logger.warning("Amount must be a number")
            print("[ERROR] Amount must be a number.")
            return

        # Create transaction data
        tx_data = {
            "op": "OP_TRANSFER_TOKEN",
            "token_id": token_id,
            "from_id": sender,
            "to_id": recipient,
            "amount": amount
        }

        # Create the transaction in the database
        Transaction.create(
            tx_id=tx_id,
            tx_type="TRANSFER",
            sender=sender,
            recipient=recipient,
            token_id=token_id,
            amount=amount,
            timestamp=timestamp,
            status="PENDING",
            data=tx_data
        )
    else:
        data_content = input("Data: ")

        # Create transaction data
        tx_data = {
            "op": "OP_DATA",
            "content": data_content
        }

        # Create the transaction in the database
        Transaction.create(
            tx_id=tx_id,
            tx_type=tx_type.upper(),
            sender=sender,
            recipient=recipient,
            timestamp=timestamp,
            status="PENDING",
            data=tx_data
        )

    # Add the transaction to the mempool
    node = OnnyxNode()
    node.add_transaction({
        "tx_id": tx_id,
        "type": tx_type,
        "sender": sender,
        "recipient": recipient,
        "timestamp": timestamp,
        "data": tx_data
    })

    logger.info(f"Transaction {tx_id} added to mempool")
    print("\n[SUCCESS] Transaction added to mempool!")
    print(f"Transaction ID: {tx_id}")
    print(f"Type: {tx_type.upper()}")
    print(f"Sender: {sender}")
    print(f"Recipient: {recipient}")
    if tx_type == "transfer":
        print(f"Token: {token_id}")
        print(f"Amount: {amount}")
    else:
        print(f"Data: {data_content}")
    print(f"Timestamp: {timestamp}")
    print(f"Status: PENDING")

def validate_chain():
    """Validate the integrity of the blockchain."""
    print("\n=== Validate Blockchain ===\n")
    logger.info("Validating blockchain")

    # Get all blocks from the database
    blocks = Block.get_all()

    if not blocks:
        logger.info("Blockchain is empty")
        print("Blockchain is empty.")
        return

    # Validate the chain
    is_valid = True
    for i in range(1, len(blocks)):
        current = blocks[i]
        previous = blocks[i-1]

        # Check if the previous hash matches
        if current.previous_hash != previous.block_hash:
            logger.error(f"Block #{current.index} has invalid previous hash")
            print(f"[ERROR] Block #{current.index} has invalid previous hash")
            is_valid = False
            break

    if is_valid:
        logger.info("Blockchain validation successful")
        print("\n[SUCCESS] Blockchain is valid!")
    else:
        logger.error("Blockchain validation failed")
        print("\n[ERROR] Blockchain validation failed!")

def get_block():
    """Get a block by index or hash."""
    print("\n=== Get Block ===\n")
    logger.info("Getting block details")

    # Ask for block index or hash
    lookup_type = input("Look up by (index/hash): ").lower()

    block = None
    if lookup_type == "index":
        try:
            index = int(input("Block Index: "))
            logger.info(f"Looking up block by index: {index}")
            block = Block.get_by_index(index)
        except ValueError:
            logger.warning("Index must be a number")
            print("[ERROR] Index must be a number.")
            return
    elif lookup_type == "hash":
        block_hash = input("Block Hash: ")
        logger.info(f"Looking up block by hash: {block_hash}")
        block = Block.get_by_hash(block_hash)
    else:
        logger.warning(f"Invalid lookup type: {lookup_type}")
        print("[ERROR] Invalid lookup type. Use 'index' or 'hash'.")
        return

    if not block:
        logger.warning("Block not found")
        print("[ERROR] Block not found.")
        return

    print("\nBlock Details:")
    print(f"Index: {block.index}")
    print(f"Hash: {block.block_hash}")
    print(f"Previous Hash: {block.previous_hash}")
    print(f"Timestamp: {block.timestamp}")
    print(f"Transactions: {len(block.transactions)}")
    print(f"Nonce: {block.nonce}")
    print(f"Miner: {block.miner}")
    print(f"Difficulty: {block.difficulty}")

    # Ask if the user wants to see the transactions
    if block.transactions:
        show_txs = input("\nShow transactions? (y/n): ").lower() == "y"
        if show_txs:
            print("\nTransactions:")
            for i, tx_data in enumerate(block.transactions):
                print(f"\nTransaction #{i+1}:")

                # If we have the transaction ID, try to get the full transaction from the database
                if isinstance(tx_data, dict) and 'tx_id' in tx_data:
                    tx = Transaction.get_by_id(tx_data['tx_id'])
                    if tx:
                        print(f"ID: {tx.tx_id}")
                        print(f"Type: {tx.tx_type}")
                        print(f"Sender: {tx.sender}")
                        print(f"Recipient: {tx.recipient}")
                        if tx.token_id:
                            print(f"Token: {tx.token_id}")
                        if tx.amount:
                            print(f"Amount: {tx.amount}")
                        print(f"Status: {tx.status}")
                        print(f"Timestamp: {tx.timestamp}")
                        print(f"Data: {json.dumps(tx.data, indent=2)}")
                    else:
                        print(json.dumps(tx_data, indent=2))
                else:
                    print(json.dumps(tx_data, indent=2))

    logger.info(f"Retrieved block #{block.index} with hash {block.block_hash}")

def help_menu():
    """Display help menu."""
    print("""
Onnyx CLI - Available Commands:
  identity        Create new identity
  identities      List all identities
  spawn           Spawn a new token
  tokens          List all tokens
  mint            Mint more tokens
  transfer        Transfer tokens
  balance         Check balance
  metadata        Get token metadata

  wallet          View wallet information
  generate        Generate a new wallet
  sign            Sign a message
  verify          Verify a message signature

  mine            Mine a new block
  status          Get node status
  chain           Dump the blockchain
  tx              Add a transaction to mempool
  block           Get block by index or hash
  validate        Validate the blockchain

  help            Show this menu
  exit            Exit the CLI
""")

def main():
    """Main CLI entry point."""
    if len(sys.argv) > 1:
        # Command-line mode
        cmd = sys.argv[1]

        commands = {
            "identity": create_identity,
            "identities": list_identities,
            "spawn": spawn_token,
            "tokens": list_tokens,
            "mint": mint_token,
            "transfer": transfer_token,
            "balance": check_balance,
            "metadata": get_metadata,
            "wallet": wallet_info,
            "generate": generate_wallet,
            "sign": sign_message,
            "verify": verify_message,
            "mine": mine_block,
            "status": node_status,
            "chain": chain_dump,
            "tx": add_transaction,
            "block": get_block,
            "validate": validate_chain,
            "help": help_menu
        }

        if cmd in commands:
            commands[cmd]()
        else:
            print(f"Unknown command: {cmd}")
            help_menu()
    else:
        # Interactive mode
        print("\nWelcome to the Onnyx CLI!")
        help_menu()

        while True:
            cmd = input("\nonnyx> ").strip().lower()

            if cmd == "exit":
                print("Goodbye!")
                break
            elif cmd == "identity":
                create_identity()
            elif cmd == "identities":
                list_identities()
            elif cmd == "spawn":
                spawn_token()
            elif cmd == "tokens":
                list_tokens()
            elif cmd == "mint":
                mint_token()
            elif cmd == "transfer":
                transfer_token()
            elif cmd == "balance":
                check_balance()
            elif cmd == "metadata":
                get_metadata()
            elif cmd == "wallet":
                wallet_info()
            elif cmd == "generate":
                generate_wallet()
            elif cmd == "sign":
                sign_message()
            elif cmd == "verify":
                verify_message()
            elif cmd == "mine":
                mine_block()
            elif cmd == "status":
                node_status()
            elif cmd == "chain":
                chain_dump()
            elif cmd == "tx":
                add_transaction()
            elif cmd == "block":
                get_block()
            elif cmd == "validate":
                validate_chain()
            elif cmd == "help":
                help_menu()
            elif cmd == "":
                continue
            else:
                print(f"Unknown command: {cmd}")
                help_menu()

if __name__ == "__main__":
    main()
