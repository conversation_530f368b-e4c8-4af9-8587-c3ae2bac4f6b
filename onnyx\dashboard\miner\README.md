# Onnyx Miner

A mining node for the Onnyx blockchain, designed for Sela operators.

## Features

- **Identity Management**: Generate and manage cryptographic keys for your identity
- **Sela Integration**: Register and manage your Sela (business) on the Onnyx blockchain
- **Mining**: Mine blocks as a validator in the Onnyx network
- **Wallet**: Manage your tokens, balances, and stakes
- **Governance**: Propose and vote on Voice Scrolls
- **GUI Dashboard**: Monitor your node, wallet, and mining status

## Installation

### From PyPI

```bash
pip install onnyx-miner
```

### From Source

```bash
git clone https://github.com/onnyx/onnyx-miner.git
cd onnyx-miner
pip install -e .
```

## Quick Start

### Setup

```bash
onnyx setup --identity-id "your_identity" --name "Your Name" --sela-id "your_sela" --sela-name "Your Sela" --api-url "http://localhost:8000"
```

### Start the GUI

```bash
onnyx gui
```

### Mine a Block

```bash
onnyx mine
```

### Enable Auto Mining

```bash
onnyx auto-mine --enable
```

## CLI Commands

- `onnyx setup`: Set up the Onnyx Miner
- `onnyx sync`: Sync the local chain with the network
- `onnyx mine`: Mine a new block
- `onnyx auto-mine`: Enable or disable automatic mining
- `onnyx wallet`: Show wallet information
- `onnyx transfer`: Transfer tokens to another identity
- `onnyx stake`: Stake tokens
- `onnyx propose`: Propose a new Voice Scroll
- `onnyx vote`: Vote on a Voice Scroll
- `onnyx scrolls`: List Voice Scrolls
- `onnyx gui`: Start the GUI

## GUI Dashboard

The Onnyx Miner includes a web-based dashboard that provides a user-friendly interface for managing your node. The dashboard includes:

- **Home**: Overview of your node, chain status, and mining status
- **Wallet**: Manage your tokens, balances, and stakes
- **Governance**: Propose and vote on Voice Scrolls
- **Logs**: View the node logs
- **Sela**: View information about your Sela

To start the GUI, run:

```bash
onnyx gui
```

Then open your browser and navigate to http://localhost:5005.

## Configuration

The Onnyx Miner uses a YAML configuration file. By default, it looks for a file named `config.yaml` in the current directory or in `~/.onnyx/config.yaml`.

You can specify a different configuration file using the `ONNYX_CONFIG` environment variable:

```bash
export ONNYX_CONFIG=/path/to/your/config.yaml
```

### Example Configuration

```yaml
# Identity configuration
identity:
  id: "your_identity"
  name: "Your Name"
  keys_dir: "keys"

# Sela configuration
sela:
  id: "your_sela"
  name: "Your Sela"
  type: "BUSINESS"

# Node configuration
node:
  api_url: "http://localhost:8000"
  p2p_port: 9000
  data_dir: "data"
  auto_mine: false
  mine_interval: 60

# GUI configuration
gui:
  enabled: true
  port: 5005
  host: "127.0.0.1"
```

## License

MIT
