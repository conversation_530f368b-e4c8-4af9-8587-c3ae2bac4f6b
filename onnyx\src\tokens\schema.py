# src/tokens/schema.py

from enum import Enum

class TokenType(str, Enum):
    BASE = "BASE"
    IDENTITY = "IDENTITY"
    FORKED = "FORKED"
    ACCESS = "ACCESS"
    REPUTATION = "REPUTATION"

class OnnyxToken:
    def __init__(self, token_id, name, symbol, creator_identity, token_type, supply, decimals=2,
                 mintable=False, transferable=True, metadata=None):
        self.token_id = token_id
        self.name = name
        self.symbol = symbol
        self.creator_identity = creator_identity
        self.token_type = TokenType(token_type)
        self.supply = supply
        self.decimals = decimals
        self.mintable = mintable
        self.transferable = transferable
        self.metadata = metadata or {}

    def to_dict(self):
        return {
            "token_id": self.token_id,
            "name": self.name,
            "symbol": self.symbol,
            "creator_identity": self.creator_identity,
            "type": self.token_type.value,
            "supply": self.supply,
            "decimals": self.decimals,
            "mintable": self.mintable,
            "transferable": self.transferable,
            "metadata": self.metadata
        }

    @staticmethod
    def from_dict(data):
        return OnnyxToken(
            token_id=data["token_id"],
            name=data["name"],
            symbol=data["symbol"],
            creator_identity=data["creator_identity"],
            token_type=data["type"],
            supply=data["supply"],
            decimals=data.get("decimals", 2),
            mintable=data.get("mintable", False),
            transferable=data.get("transferable", True),
            metadata=data.get("metadata", {})
        )
