# src/tokens/ledger.py

import time
import logging
from typing import Dict, Any, List, Optional

from models.token import Token
from models.transaction import Transaction
from src.tokens.registry import TokenRegistry

# Set up logging
logger = logging.getLogger("onnyx.tokens.ledger")

class TokenLedger:
    def __init__(self, token_registry=None):
        """
        Initialize the token ledger.

        Args:
            token_registry: The token registry to use
        """
        self.token_registry = token_registry or TokenRegistry()

    def _record_transaction(self, operation: str, token_id: str, from_id: Optional[str], to_id: Optional[str], amount: int, memo: Optional[str] = None) -> Dict[str, Any]:
        """
        Record a transaction in the ledger history.

        Args:
            operation: The transaction operation (MINT, SEND, BURN)
            token_id: The token ID
            from_id: The sender identity ID
            to_id: The recipient identity ID
            amount: The transaction amount
            memo: An optional memo

        Returns:
            The transaction data
        """
        # Get the token
        token = self.token_registry.get_token(token_id)
        if not token:
            raise ValueError(f"Token {token_id} not found.")

        # Create transaction data
        data = {
            "token_id": token_id,
            "from_id": from_id,
            "to_id": to_id,
            "amount": amount,
            "memo": memo
        }

        # Create a signature (in a real system, this would be signed by the sender)
        signature = f"sig_{int(time.time())}"

        # Create the transaction
        tx = Transaction.create(
            op=operation,
            data=data,
            sender=from_id or to_id,  # Use the sender if available, otherwise the recipient
            signature=signature
        )

        logger.info(f"Recorded {operation} transaction: {tx.tx_id}")
        return tx.data

    def mint(self, token_id: str, to_id: str, amount: int, minter_id: str) -> Dict[str, Any]:
        """
        Mint new tokens to an identity.

        Args:
            token_id: The token ID
            to_id: The recipient identity ID
            amount: The amount to mint
            minter_id: The minter identity ID

        Returns:
            The transaction data
        """
        token = self.token_registry.get_token(token_id)
        if not token:
            raise ValueError(f"Token {token_id} not found.")

        # Check if the token is mintable
        if not token.metadata.get("mintable", False):
            raise ValueError(f"Token {token_id} is not mintable.")

        # Check if the minter is the token creator
        if token.creator_id != minter_id:
            raise ValueError(f"Only the token creator can mint new tokens.")

        # Mint the tokens
        token.mint(to_id, amount)

        # Record the transaction
        return self._record_transaction("OP_MINT", token_id, minter_id, to_id, amount)

    def burn(self, token_id: str, from_id: str, amount: int, burner_id: str) -> Dict[str, Any]:
        """
        Burn tokens from an identity.

        Args:
            token_id: The token ID
            from_id: The sender identity ID
            amount: The amount to burn
            burner_id: The burner identity ID

        Returns:
            The transaction data
        """
        token = self.token_registry.get_token(token_id)
        if not token:
            raise ValueError(f"Token {token_id} not found.")

        # Check if the burner is the token creator or the token holder
        if token.creator_id != burner_id and from_id != burner_id:
            raise ValueError(f"Only the token creator or token holder can burn tokens.")

        # Burn the tokens
        token.burn(from_id, amount)

        # Record the transaction
        return self._record_transaction("OP_BURN", token_id, from_id, None, amount)

    def transfer(self, token_id: str, from_id: str, to_id: str, amount: int, memo: Optional[str] = None) -> Dict[str, Any]:
        """
        Transfer tokens from one identity to another.

        Args:
            token_id: The token ID
            from_id: The sender identity ID
            to_id: The recipient identity ID
            amount: The amount to transfer
            memo: An optional memo

        Returns:
            The transaction data
        """
        token = self.token_registry.get_token(token_id)
        if not token:
            raise ValueError(f"Token {token_id} not found.")

        # Check if the token is transferable
        if not token.metadata.get("transferable", True):
            raise ValueError(f"Token {token_id} is not transferable.")

        # Transfer the tokens
        token.transfer(from_id, to_id, amount)

        # Record the transaction
        return self._record_transaction("OP_SEND", token_id, from_id, to_id, amount, memo)

    def get_balance(self, identity_id: str, token_id: str) -> int:
        """
        Get the balance of a specific token for an identity.

        Args:
            identity_id: The identity ID
            token_id: The token ID

        Returns:
            The token balance
        """
        token = self.token_registry.get_token(token_id)
        if not token:
            raise ValueError(f"Token {token_id} not found.")

        return token.get_balance(identity_id)

    def get_all_balances(self, identity_id: str) -> Dict[str, int]:
        """
        Get all token balances for an identity.

        Args:
            identity_id: The identity ID

        Returns:
            A dictionary mapping token IDs to balances
        """
        # Get all tokens
        tokens = self.token_registry.list_tokens()

        # Get the balance for each token
        balances = {}
        for token in tokens:
            balance = token.get_balance(identity_id)
            if balance > 0:
                balances[token.token_id] = balance

        return balances

    def get_transaction_history(self, identity_id: Optional[str] = None, token_id: Optional[str] = None, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get transaction history, optionally filtered by identity or token.

        Args:
            identity_id: The identity ID to filter by
            token_id: The token ID to filter by
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of transaction data
        """
        # Build the query
        query_parts = []
        params = []

        if identity_id:
            query_parts.append("(from_id = ? OR to_id = ?)")
            params.extend([identity_id, identity_id])

        if token_id:
            query_parts.append("token_id = ?")
            params.append(token_id)

        # Build the WHERE clause
        where_clause = " AND ".join(query_parts) if query_parts else ""

        # Get the transactions
        if where_clause:
            transactions = Transaction.filter(where_clause, tuple(params))
        else:
            transactions = Transaction.get_all()

        # Sort by timestamp (newest first)
        transactions.sort(key=lambda tx: tx.timestamp, reverse=True)

        # Apply pagination
        transactions = transactions[offset:offset+limit]

        # Convert to dictionaries
        return [tx.data for tx in transactions]
