/* ONNYX Platform - Enhanced Onyx Stone Theme */
/* Professional Enterprise-Grade Design System */

/* CSS Variables for consistent theming - ENHANCED BRIGHTNESS */
:root {
    /* Core Colors */
    --onyx-black: #1a1a1a;        /* Optimized for accessibility */
    --onyx-gray: #2a2a2a;         /* Improved contrast base */
    --onyx-light: #3a3a3a;        /* Lighter accent areas */
    --onyx-lighter: #4a4a4a;      /* Even lighter for subtle elements */

    /* Cyber Theme Colors */
    --cyber-cyan: #00d4ff;        /* Primary accent - enhanced vibrancy */
    --cyber-purple: #8b5cf6;      /* Secondary accent - professional purple */
    --cyber-blue: #0066ff;        /* Tertiary accent - increased visibility */
    --cyber-green: #10b981;       /* Success states */
    --cyber-red: #ef4444;         /* Error states */
    --cyber-yellow: #f59e0b;      /* Warning states */

    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.08);    /* Enhanced opacity for better contrast */
    --glass-border: rgba(255, 255, 255, 0.15); /* More visible borders */
    --glass-shadow: rgba(0, 255, 255, 0.1);
    --glass-hover: rgba(255, 255, 255, 0.12);  /* Hover state */

    /* Enhanced text colors for better readability */
    --text-primary: #eeeeee;      /* Brighter primary text */
    --text-secondary: #d1d5db;    /* Improved secondary text (gray-300) */
    --text-tertiary: #9ca3af;     /* Enhanced tertiary text (gray-400) */
    --text-muted: #6b7280;        /* Muted text (gray-500) */

    /* Section header theming */
    --section-header-color: var(--cyber-cyan);
    --section-spacing-top: 5rem;
    --section-spacing-bottom: 3rem;

    /* Enhanced Design System */
    /* Typography Scale */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-2xl: 1.5rem;      /* 24px */
    --font-size-3xl: 1.875rem;    /* 30px */
    --font-size-4xl: 2.25rem;     /* 36px */
    --font-size-5xl: 3rem;        /* 48px */

    /* Spacing Scale */
    --space-xs: 0.25rem;          /* 4px */
    --space-sm: 0.5rem;           /* 8px */
    --space-md: 1rem;             /* 16px */
    --space-lg: 1.5rem;           /* 24px */
    --space-xl: 2rem;             /* 32px */
    --space-2xl: 3rem;            /* 48px */
    --space-3xl: 4rem;            /* 64px */
    --space-4xl: 5rem;            /* 80px */

    /* Border Radius Scale */
    --radius-sm: 0.375rem;        /* 6px */
    --radius-md: 0.5rem;          /* 8px */
    --radius-lg: 0.75rem;         /* 12px */
    --radius-xl: 1rem;            /* 16px */
    --radius-2xl: 1.5rem;         /* 24px */

    /* Shadow System */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-cyber: 0 0 20px rgba(0, 212, 255, 0.3);
    --shadow-purple: 0 0 20px rgba(139, 92, 246, 0.3);

    /* Animation Timing */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.4), 0 0 40px rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.6), 0 0 60px rgba(0, 255, 255, 0.3);
    }
}

@keyframes purpleGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(154, 0, 255, 0.4), 0 0 40px rgba(154, 0, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(154, 0, 255, 0.6), 0 0 60px rgba(154, 0, 255, 0.3);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes countUp {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes logoGlow {
    0%, 100% {
        filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);
        box-shadow: 0 0 20px rgba(0, 255, 247, 0.3);
    }
    50% {
        filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(120%) contrast(110%);
        box-shadow: 0 0 30px rgba(0, 255, 247, 0.5);
    }
}

@keyframes logoSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes logoPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* Utility classes */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

.purple-glow-effect {
    animation: purpleGlow 2s ease-in-out infinite;
}

.float-effect {
    animation: float 3s ease-in-out infinite;
}

/* Enhanced text utility classes */
.text-primary-bright {
    color: var(--text-primary) !important;
}

.text-secondary-bright {
    color: var(--text-secondary) !important;
}

.text-tertiary-bright {
    color: var(--text-tertiary) !important;
}

.text-muted-bright {
    color: var(--text-muted) !important;
}

/* Enhanced accent colors with glow */
.text-cyber-cyan-glow {
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.text-cyber-purple-glow {
    color: var(--cyber-purple);
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.text-cyber-blue-glow {
    color: var(--cyber-blue);
    text-shadow: 0 0 10px rgba(0, 102, 255, 0.5);
}

/* SECTION TITLE CONSISTENCY - PART 1 SOLUTION */
.section-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--section-header-color);
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
    margin-top: var(--section-spacing-top);
    margin-bottom: var(--section-spacing-bottom);
    letter-spacing: 0.05em;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-cyan), transparent);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Section spacing standardization */
.section-container {
    margin-top: var(--section-spacing-top);
    margin-bottom: var(--section-spacing-bottom);
}

.section-content-buffer {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

.count-up {
    animation: countUp 0.6s ease-out;
}

/* Logo-specific utility classes */
.logo-glow {
    animation: logoGlow 3s ease-in-out infinite;
}

.logo-spin {
    animation: logoSpin 2s linear infinite;
}

.logo-pulse {
    animation: logoPulse 2s ease-in-out infinite;
}

.logo-loading {
    animation: logoSpin 1s linear infinite, logoPulse 2s ease-in-out infinite;
}

/* Logo container styles */
.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.logo-container:hover {
    transform: scale(1.05);
}

.logo-container.loading::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid transparent;
    border-top: 2px solid var(--cyber-cyan);
    border-radius: inherit;
    animation: logoSpin 1s linear infinite;
}

/* Glass Morphism Components - ENHANCED BRIGHTNESS */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.glass-nav {
    background: rgba(26, 26, 26, 0.9);  /* Slightly more opaque for better contrast */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 25px rgba(0, 255, 255, 0.4),
        0 0 50px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.glass-button-primary {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.25), rgba(154, 0, 255, 0.25));
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--cyber-cyan);
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.35), rgba(154, 0, 255, 0.35));
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.5),
        0 0 60px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.02);
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.7);
}

/* Neumorphism Components */
.neuro-card {
    background: var(--onyx-gray);
    border-radius: 20px;
    box-shadow:
        20px 20px 40px rgba(0, 0, 0, 0.5),
        -20px -20px 40px rgba(255, 255, 255, 0.02);
    transition: all 0.3s ease;
}

.neuro-card:hover {
    box-shadow:
        25px 25px 50px rgba(0, 0, 0, 0.6),
        -25px -25px 50px rgba(255, 255, 255, 0.03),
        0 0 30px rgba(0, 255, 255, 0.1);
    transform: translateY(-5px);
}

.neuro-inset {
    background: var(--onyx-gray);
    border-radius: 15px;
    box-shadow:
        inset 10px 10px 20px rgba(0, 0, 0, 0.5),
        inset -10px -10px 20px rgba(255, 255, 255, 0.02);
}

/* Form styles with Onyx theme - IMPROVED CONTRAST */
.form-input {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: #ffffff;                    /* Ensure pure white text */
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

.form-input:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.12);  /* Slightly more opaque for better readability */
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);  /* Improved placeholder visibility */
}

.form-select {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

.form-select:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-textarea {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    transition: all 0.3s ease;
    resize: vertical;
    font-family: 'Montserrat', sans-serif;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-checkbox {
    width: 18px;
    height: 18px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-checkbox:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.form-radio {
    width: 18px;
    height: 18px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    transition: all 0.3s ease;
}

.form-radio:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Special Effects */
.hero-gradient {
    background: linear-gradient(135deg,
        var(--onyx-black) 0%,
        var(--onyx-gray) 25%,
        var(--onyx-black) 50%,
        var(--onyx-light) 75%,
        var(--onyx-black) 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

.cyber-grid {
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
}

.hologram-text {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple), var(--cyber-blue));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

.data-stream {
    position: relative;
    overflow: hidden;
}

.data-stream::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 255, 0.2),
        transparent);
    animation: shimmer 3s infinite;
}

/* Badge styles with Onyx theme */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    font-family: 'Montserrat', sans-serif;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.badge-success {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.badge-warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.badge-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.badge-info {
    background: rgba(0, 255, 255, 0.2);
    color: var(--cyber-cyan);
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.badge-purple {
    background: rgba(154, 0, 255, 0.2);
    color: var(--cyber-purple);
    border: 1px solid rgba(154, 0, 255, 0.3);
}

/* Table styles with Onyx theme */
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    overflow: hidden;
}

.table th {
    padding: 16px 24px;
    background: rgba(0, 255, 255, 0.1);
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--glass-border);
}

.table td {
    padding: 16px 24px;
    color: white;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.table tbody tr:hover {
    background: rgba(0, 255, 255, 0.05);
}

/* Loading spinner with cyber theme */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-top: 2px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hash display with cyber styling */
.hash {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: var(--glass-bg);
    color: var(--cyber-cyan);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.hash-truncate {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--cyber-cyan);
}

/* Status indicators with glow effects */
.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.2);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.status-pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.2);
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
}

/* MODERN NAVIGATION BAR - COMPLETE REDESIGN */
/* Sleek, professional navigation with enhanced UX */

.modern-nav {
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.95) 0%,
        rgba(42, 42, 42, 0.9) 50%,
        rgba(26, 26, 26, 0.95) 100%) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1) !important;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.3),
        0 0 40px rgba(0, 255, 255, 0.05) !important;
    height: 70px !important;
    transition: all 0.3s ease !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 50 !important;
    width: 100% !important;
}

.nav-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 0 2rem !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    position: relative !important;
}

/* LEFT SECTION - LOGO */
.nav-left {
    display: flex !important;
    align-items: center !important;
    flex-shrink: 0 !important;
}

.nav-logo-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 12px;
}

.nav-logo-link:hover {
    background: rgba(0, 255, 255, 0.05);
    transform: translateY(-1px);
}

.nav-logo-container {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 4px 15px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.nav-logo-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.nav-logo-link:hover .nav-logo-container::before {
    left: 100%;
}

.nav-logo-img {
    width: 32px;
    height: 32px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.nav-logo-text {
    font-family: 'Orbitron', monospace;
    font-weight: 800;
    font-size: 1.75rem;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    letter-spacing: 0.05em;
}

/* CENTER SECTION - NAVIGATION LINKS */
.nav-center {
    flex: 1 !important;
    display: flex !important;
    justify-content: center !important;
    max-width: 600px !important;
    margin: 0 2rem !important;
}

.nav-links-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 0.5rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.nav-link-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
}

.nav-link-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1),
        rgba(139, 92, 246, 0.1));
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link-modern:hover::before {
    left: 0;
}

.nav-link-modern:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.08);
    box-shadow:
        0 4px 15px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-link-icon {
    font-size: 1.1rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.nav-link-modern:hover .nav-link-icon {
    opacity: 1;
    transform: scale(1.1);
}

.nav-link-text {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* RIGHT SECTION - USER MENU */
.nav-right {
    display: flex !important;
    align-items: center !important;
    flex-shrink: 0 !important;
}

/* USER PROFILE SECTION */
.user-profile-section {
    position: relative;
}

.user-profile-button {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    min-width: 200px;
}

.user-profile-button:hover {
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.3);
    flex-shrink: 0;
}

.user-initial {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--onyx-black);
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    font-size: 0.8rem;
    color: var(--cyber-cyan);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-status::before {
    content: '';
    width: 6px;
    height: 6px;
    background: var(--cyber-cyan);
    border-radius: 50%;
    box-shadow: 0 0 6px var(--cyber-cyan);
    animation: pulse 2s infinite;
}

.dropdown-arrow {
    width: 16px;
    height: 16px;
    color: var(--text-tertiary);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.user-profile-button:hover .dropdown-arrow {
    color: var(--cyber-cyan);
}

/* USER DROPDOWN MENU */
.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 0.75rem);
    right: 0;
    width: 280px;
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.98) 0%,
        rgba(42, 42, 42, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 60px rgba(0, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 9999;
    overflow: hidden;
}

.dropdown-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.05) 0%,
        rgba(139, 92, 246, 0.05) 100%);
}

.dropdown-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dropdown-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
    flex-shrink: 0;
}

.dropdown-avatar span {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.3rem;
    color: var(--onyx-black);
}

.dropdown-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    min-width: 0;
}

.dropdown-name {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-email {
    font-size: 0.85rem;
    color: var(--text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-divider {
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%);
    margin: 0.5rem 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.08) 0%,
        rgba(139, 92, 246, 0.08) 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.dropdown-item:hover::before {
    left: 0;
}

.dropdown-item:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.05);
    transform: translateX(4px);
}

.dropdown-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.dropdown-item:hover .dropdown-icon {
    opacity: 1;
    transform: scale(1.1);
}

.logout-item {
    color: #ef4444 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin-top: 0.5rem;
}

.logout-item:hover {
    color: #f87171 !important;
    background: rgba(239, 68, 68, 0.08) !important;
}

.logout-item::before {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.08) 0%,
        rgba(239, 68, 68, 0.12) 100%) !important;
}

/* GUEST BUTTONS */
.guest-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.guest-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    border-radius: 14px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

.guest-button.secondary {
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.guest-button.secondary:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.2);
    transform: translateY(-2px);
}

.guest-button.primary {
    color: var(--onyx-black);
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.guest-button.primary:hover {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.9),
        rgba(139, 92, 246, 0.9));
    box-shadow: 0 6px 25px rgba(0, 255, 255, 0.4);
    transform: translateY(-3px);
}

.button-icon {
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.guest-button:hover .button-icon {
    transform: scale(1.1);
}

/* MOBILE MENU TOGGLE */
.mobile-menu-toggle {
    display: none;
    position: relative;
}

.mobile-toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-toggle-btn:hover {
    background: rgba(0, 255, 255, 0.1);
}

.hamburger {
    width: 24px;
    height: 18px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.hamburger span {
    width: 100%;
    height: 2px;
    background: var(--cyber-cyan);
    border-radius: 1px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* RESPONSIVE DESIGN */
@media (max-width: 1024px) {
    .nav-center {
        margin: 0 1rem;
    }

    .nav-links-container {
        gap: 0.25rem;
        padding: 0.25rem;
    }

    .nav-link-modern {
        padding: 0.5rem 1rem;
        min-width: 100px;
        font-size: 0.85rem;
    }

    .user-profile-button {
        min-width: 160px;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-center {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .user-profile-button {
        min-width: auto;
        padding: 0.5rem;
    }

    .user-info {
        display: none;
    }

    .guest-buttons {
        gap: 0.5rem;
    }

    .guest-button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .button-icon {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 0.75rem;
    }

    .nav-logo-text {
        font-size: 1.5rem;
    }

    .nav-logo-container {
        width: 40px;
        height: 40px;
    }

    .nav-logo-img {
        width: 28px;
        height: 28px;
    }
}

/* ENHANCED COMPONENT SYSTEM */
/* Modern Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-lg);
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: var(--font-size-base);
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 44px; /* Accessibility - minimum touch target */
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    box-shadow: var(--shadow-cyber);
    font-weight: 700;
}

.btn-primary:hover {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.9),
        rgba(139, 92, 246, 0.9));
    box-shadow:
        var(--shadow-cyber),
        0 8px 25px rgba(0, 212, 255, 0.4);
    transform: translateY(-2px);
    color: var(--onyx-black);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--glass-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--glass-hover);
    border-color: var(--cyber-cyan);
    color: var(--cyber-cyan);
    box-shadow: var(--shadow-cyber);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
}

.btn-outline:hover {
    background: var(--cyber-cyan);
    color: var(--onyx-black);
    box-shadow: var(--shadow-cyber);
    transform: translateY(-2px);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
}

.btn-ghost:hover {
    background: var(--glass-bg);
    color: var(--cyber-cyan);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-lg {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

.btn-icon {
    padding: var(--space-md);
    min-width: 44px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Enhanced Card System */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        var(--cyber-cyan),
        transparent);
    opacity: 0;
    transition: var(--transition-normal);
}

.card:hover {
    background: var(--glass-hover);
    border-color: rgba(0, 212, 255, 0.3);
    transform: translateY(-4px);
    box-shadow:
        var(--shadow-xl),
        var(--shadow-cyber);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--glass-border);
}

.card-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin: var(--space-xs) 0 0 0;
}

.card-body {
    color: var(--text-secondary);
    line-height: 1.6;
}

.card-footer {
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-md);
}

/* MOBILE MENU OVERLAY */
.mobile-menu-overlay {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 9998;
    overflow-y: auto;
}

.mobile-menu-items {
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
    margin: 0 auto;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.mobile-menu-item:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    color: var(--cyber-cyan);
    transform: translateX(8px);
}

.mobile-menu-item.primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    border-color: transparent;
    font-weight: 600;
}

.mobile-menu-item.primary:hover {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.9),
        rgba(139, 92, 246, 0.9));
    transform: translateX(8px) scale(1.02);
}

.mobile-menu-item.logout {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.mobile-menu-item.logout:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
    color: #f87171;
}

.mobile-menu-divider {
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    margin: 1rem 0;
}

/* ENHANCED FORM SYSTEM */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: 0.025em;
}

.form-label.required::after {
    content: ' *';
    color: var(--cyber-red);
}

.form-control {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-family: 'Montserrat', sans-serif;
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    min-height: 44px; /* Accessibility */
}

.form-control:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 0 3px rgba(0, 212, 255, 0.1),
        var(--shadow-cyber);
    background: var(--glass-hover);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

.form-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.02);
}

.form-control.error {
    border-color: var(--cyber-red);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.success {
    border-color: var(--cyber-green);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin-top: var(--space-xs);
}

.form-error {
    font-size: var(--font-size-sm);
    color: var(--cyber-red);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.form-success {
    font-size: var(--font-size-sm);
    color: var(--cyber-green);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

/* Enhanced Select Styling */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300d4ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-md) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: var(--space-3xl);
}

/* Checkbox and Radio Styling */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.form-check-input {
    width: 20px;
    height: 20px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
}

.form-check-input:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: var(--shadow-cyber);
}

.form-check-input:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--onyx-black);
    font-weight: bold;
    font-size: 12px;
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-input[type="radio"]:checked::after {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--onyx-black);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.form-check-label {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

/* LAYOUT UTILITIES */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-xl);
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.grid {
    display: grid;
    gap: var(--space-xl);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

/* SPINNER ANIMATION */
.spinner {
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-left: 2px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar with cyber theme */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--onyx-gray);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--cyber-purple), var(--cyber-cyan));
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }

    .glass-card {
        margin: 8px;
        padding: 16px;
    }

    .hero-gradient {
        background-size: 200% 200%;
    }
}

@media (min-width: 641px) {
    .mobile-only {
        display: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-break {
        page-break-after: always;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-card {
        border: 2px solid white;
    }

    .glass-button {
        border: 2px solid var(--cyber-cyan);
    }
}

/* MOBILE NAVIGATION IMPROVEMENTS - PART 2A SOLUTION */
.mobile-menu-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 60;
    width: 24px;
    height: 24px;
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 55;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-items {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
    text-align: center;
}

.mobile-menu-item {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    min-width: 200px;
}

.mobile-menu-item:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

/* Hamburger animation */
.hamburger {
    width: 18px;
    height: 18px;
    position: relative;
    cursor: pointer;
}

.hamburger span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: var(--text-primary);
    border-radius: 1px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: 0.3s ease-in-out;
}

.hamburger span:nth-child(1) {
    top: 0px;
}

.hamburger span:nth-child(2) {
    top: 7px;
}

.hamburger span:nth-child(3) {
    top: 14px;
}

.hamburger.active span:nth-child(1) {
    top: 7px;
    transform: rotate(135deg);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.hamburger.active span:nth-child(3) {
    top: 7px;
    transform: rotate(-135deg);
}

/* VISUAL CONSISTENCY FIXES - COMPREHENSIVE SOLUTION */
/* Fix opacity inconsistencies and ensure proper footer positioning */

/* OPACITY STANDARDIZATION - Remove reduced opacity from all components */
/* All interactive elements should have full opacity for better visibility */

/* Glass card components - Enhanced visibility */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.12); /* Increased from 0.1 */
    border-color: rgba(0, 255, 255, 0.4); /* Increased from 0.3 */
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 255, 0.3), /* Increased from 0.2 */
        inset 0 1px 0 rgba(255, 255, 255, 0.2); /* Increased from 0.15 */
    opacity: 1 !important; /* Force full opacity */
}

/* Button components - Full opacity */
.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.15); /* Increased from 0.12 */
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 25px rgba(0, 255, 255, 0.5), /* Increased from 0.4 */
        0 0 50px rgba(0, 255, 255, 0.3), /* Increased from 0.2 */
        inset 0 1px 0 rgba(255, 255, 255, 0.25); /* Increased from 0.2 */
    transform: translateY(-2px);
    opacity: 1 !important; /* Force full opacity */
}

.glass-button-primary {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.3), rgba(154, 0, 255, 0.3)); /* Increased from 0.25 */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--cyber-cyan);
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.6); /* Increased from 0.5 */
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3); /* Increased from 0.2 */
    opacity: 1 !important; /* Force full opacity */
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.4), rgba(154, 0, 255, 0.4)); /* Increased from 0.35 */
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.6), /* Increased from 0.5 */
        0 0 60px rgba(0, 255, 255, 0.4), /* Increased from 0.3 */
        inset 0 1px 0 rgba(255, 255, 255, 0.3); /* Increased from 0.2 */
    transform: translateY(-2px) scale(1.02);
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.8); /* Increased from 0.7 */
    opacity: 1 !important; /* Force full opacity */
}

/* Navigation elements - Full opacity */
.glass-nav {
    background: rgba(26, 26, 26, 0.95); /* Increased from 0.9 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Increased from 0.1 */
    opacity: 1 !important; /* Force full opacity */
}

/* Text elements - Enhanced visibility */
.text-text-primary {
    color: var(--text-primary);
    opacity: 1 !important; /* Force full opacity */
}

.text-text-secondary {
    color: var(--text-secondary);
    opacity: 1 !important; /* Force full opacity */
}

.text-text-tertiary {
    color: var(--text-tertiary);
    opacity: 1 !important; /* Force full opacity */
}

/* FORCE FULL OPACITY FOR ALL COMPONENTS - COMPREHENSIVE FIX */
/* Remove ALL opacity reductions from decorative elements */
.opacity-70 { opacity: 1 !important; }
.opacity-60 { opacity: 1 !important; }
.opacity-50 { opacity: 1 !important; } /* Changed from 0.8 to 1.0 */
.opacity-40 { opacity: 1 !important; } /* Changed from 0.7 to 1.0 */
.opacity-30 { opacity: 1 !important; }
.opacity-20 { opacity: 1 !important; }
.opacity-10 { opacity: 1 !important; }

/* Force full opacity on all Tailwind opacity classes */
.opacity-0 { opacity: 0 !important; } /* Keep hidden elements hidden */
.opacity-5 { opacity: 1 !important; }
.opacity-25 { opacity: 1 !important; }
.opacity-75 { opacity: 1 !important; }
.opacity-90 { opacity: 1 !important; }
.opacity-95 { opacity: 1 !important; }
.opacity-100 { opacity: 1 !important; }

/* HERO SECTION OPACITY FIXES - FORCE FULL VISIBILITY */
.hero-section,
.hero-content,
.hero-title,
.hero-subtitle,
.hero-description,
.hero-stats,
.hero-buttons,
.hero-gradient {
    opacity: 1 !important;
}

/* TEXT ELEMENT OPACITY FIXES - ENSURE ALL TEXT IS FULLY VISIBLE */
h1, h2, h3, h4, h5, h6,
p, span, div, a,
.text-lg, .text-xl, .text-2xl, .text-3xl, .text-4xl, .text-5xl,
.text-sm, .text-xs,
.text-gray-300, .text-gray-400, .text-gray-500,
.text-white, .text-primary, .text-secondary {
    opacity: 1 !important;
}

/* COMPONENT OPACITY FIXES - ALL INTERACTIVE ELEMENTS */
.card, .stats-card, .feature-card,
.button, .btn, .glass-button,
.nav-link, .menu-item,
.logo, .icon,
.badge, .tag, .label,
.input, .form-control,
.modal, .popup, .tooltip {
    opacity: 1 !important;
}

/* BACKGROUND AND DECORATIVE ELEMENT FIXES */
.bg-gradient, .gradient-bg,
.particle, .floating-element,
.decoration, .accent,
.border, .divider, .separator {
    opacity: 1 !important;
}

/* ANIMATION AND TRANSITION OPACITY OVERRIDES */
.fade-in, .slide-in, .zoom-in,
.animate-pulse, .animate-ping, .animate-bounce,
.transition-opacity, .transition-all {
    opacity: 1 !important;
}

/* COMPREHENSIVE ELEMENT OPACITY ENFORCEMENT */
/* Force full opacity on all common element types */
* {
    /* Override any inline opacity styles */
}

/* Specific element type overrides */
div, section, article, main, header, footer, nav,
h1, h2, h3, h4, h5, h6, p, span, a, button,
img, svg, canvas, video, iframe,
form, input, textarea, select, label,
table, tr, td, th, thead, tbody, tfoot,
ul, ol, li, dl, dt, dd {
    /* Ensure no element has reduced opacity unless explicitly hidden */
}

/* Override any JavaScript-applied opacity styles */
[style*="opacity"] {
    opacity: 1 !important;
}

/* Exception for completely hidden elements */
[style*="opacity: 0"],
[style*="opacity:0"],
.opacity-0,
.hidden,
[hidden] {
    opacity: 0 !important;
}

/* CRITICAL FOOTER POSITIONING FIXES - EMERGENCY OVERRIDE */
/* Force proper page layout with footer that never overlaps content */

/* Root layout setup for proper flexbox structure */
html {
    height: 100% !important;
    scroll-behavior: smooth;
    scroll-padding-top: 70px; /* Account for modern navigation height */
}

body {
    height: 100% !important;
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-top: 70px !important; /* Account for modern navigation height */
    position: relative !important;
}

/* CRITICAL: Force footer to bottom */
footer.footer-responsive {
    flex-shrink: 0 !important;
    margin-top: auto !important;
    position: relative !important;
    width: 100% !important;
    z-index: 10 !important;
    background: var(--onyx-black) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* CRITICAL: Force main content wrapper to expand properly */
div[style*="flex: 1 0 auto"] {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 64px) !important;
    position: relative !important;
}

/* CRITICAL: Force main content area to expand within wrapper */
main[style*="flex: 1 0 auto"] {
    flex: 1 0 auto !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
    min-height: calc(100vh - 200px) !important;
}

/* Legacy support for old classes */
.flex-1 {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 64px) !important;
    position: relative !important;
}

main {
    flex: 1 0 auto !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
}

/* CRITICAL: Fix page-specific content wrappers that break footer positioning */
.directory-content,
.explorer-content,
.dashboard-content,
.auth-content {
    min-height: auto !important;
    height: auto !important;
    flex: none !important;
    display: block !important;
}

/* Ensure page content doesn't interfere with footer positioning */
.directory-content.hero-gradient,
.explorer-content.hero-gradient,
.dashboard-content.hero-gradient,
.auth-content.hero-gradient {
    padding-bottom: 3rem !important;
    margin-bottom: 0 !important;
}

/* CRITICAL: Override any conflicting height/min-height rules on page content */
.hero-gradient {
    min-height: auto !important;
    height: auto !important;
}

/* Ensure cyber-grid doesn't interfere with layout */
.cyber-grid {
    position: relative !important;
    z-index: 1 !important;
}

/* Force proper spacing for all page content sections */
.py-20 {
    padding-top: 5rem !important;
    padding-bottom: 3rem !important;
}

/* CRITICAL: Fix min-h-screen that breaks footer positioning */
.min-h-screen {
    min-height: auto !important;
    height: auto !important;
}

/* Specific fix for auth pages that use min-h-screen */
.min-h-screen.hero-gradient {
    min-height: auto !important;
    height: auto !important;
    display: block !important;
    flex: none !important;
}

/* TARGETED FIX: Only target page wrapper divs, not content containers */
/* Target only the main page content wrappers that interfere with footer */
main > div.directory-content,
main > div.explorer-content,
main > div.dashboard-content,
main > div.auth-content,
main > div.min-h-screen {
    min-height: auto !important;
    height: auto !important;
    flex: none !important;
}

/* Ensure main page wrapper has proper bottom spacing for footer */
main > div:last-child.directory-content,
main > div:last-child.explorer-content,
main > div:last-child.dashboard-content,
main > div:last-child.auth-content {
    margin-bottom: 0 !important;
    padding-bottom: 3rem !important;
}

/* CRITICAL: AGGRESSIVE ANTI-STRETCH SOLUTION FOR GLASS-CARD COMPONENTS */
/* This is the comprehensive fix for vertical stretching issues */

/* STEP 1: Force all glass-card components to natural height */
.glass-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    align-self: start !important;
    justify-self: stretch !important;
    display: block !important;
}

/* STEP 2: Fix CSS Grid containers that cause stretching */
.grid {
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: auto !important;
}

/* STEP 3: Target all grid children that contain glass-cards */
.grid > *,
.grid > .glass-card,
.grid > div > .glass-card,
.grid > * > * > .glass-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* STEP 4: Fix flexbox containers that stretch children */
.flex,
.flex-col,
.flex-row {
    align-items: flex-start !important;
}

.flex > .glass-card,
.flex-col > .glass-card,
.flex-row > .glass-card,
.flex > * > .glass-card,
.flex-col > * > .glass-card {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
    align-self: flex-start !important;
}

/* STEP 5: Target specific component classes */
.validator-card,
.search-panel,
.explorer-search,
.stats-card,
.tech-card,
.network-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    align-self: start !important;
}

/* CRITICAL FIX: Validator Card Vertical Stretching Issue - NUCLEAR OPTION */
/* Specific fix for the Sela Validator Directory page cards */

/* EMERGENCY OVERRIDE: Force all cards in validator grid to natural height */
#validators-grid .card,
#validators-grid .validator-card,
.validator-card.card,
.validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    display: block !important;
    align-self: start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* Force card internal structure to not stretch */
#validators-grid .card .card-header,
#validators-grid .card .card-body,
#validators-grid .card .card-footer,
.validator-card .card-header,
.validator-card .card-body,
.validator-card .card-footer {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    display: block !important;
}

/* Override any Tailwind or other CSS that might be stretching */
#validators-grid > div {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* ULTIMATE NUCLEAR OPTION: Target the exact page and override everything */
/* This is the most aggressive fix possible for the validator directory page */
.directory-content #validators-grid {
    display: grid !important;
    grid-template-columns: repeat(1, 1fr) !important;
    gap: 2rem !important;
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: min-content !important;
    grid-auto-flow: row !important;
}

@media (min-width: 768px) {
    .directory-content #validators-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 1024px) {
    .directory-content #validators-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Force every single element in the validator grid to natural height */
.directory-content #validators-grid > *,
.directory-content #validators-grid > * > *,
.directory-content #validators-grid .card,
.directory-content #validators-grid .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    justify-self: stretch !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
    display: block !important;
}

/* ULTIMATE TAILWIND OVERRIDE - Target specific Tailwind classes */
/* This is the most aggressive fix to override Tailwind CSS grid behavior */
.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8 {
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: min-content !important;
    grid-auto-flow: row !important;
}

.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8 > * {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* Target the exact card classes used in the template */
.card.group.data-stream.validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    display: block !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
}

/* FLEXBOX LAYOUT FIX - Replace problematic grid with flexbox */
/* This is the definitive solution for validator card stretching */
#validators-grid.flex.flex-wrap {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    align-content: flex-start !important;
    justify-content: flex-start !important;
    gap: 2rem !important;
}

#validators-grid.flex.flex-wrap > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* ULTIMATE NUCLEAR OPTION - OVERRIDE ALL POSSIBLE FRAMEWORKS */
/* This targets Bootstrap, Tailwind, and any other CSS that might cause equal heights */

/* Bootstrap Card Override */
.card-deck .card,
.card-group .card,
.row .card,
.d-flex .card,
.flex .card,
.grid .card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    align-self: flex-start !important;
}

/* Bootstrap Grid Override */
.row > [class*="col-"] .card,
.row > [class*="col-"] .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Tailwind Flexbox Override */
.flex > .card,
.flex > .validator-card,
.flex-wrap > .card,
.flex-wrap > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
}

/* CSS Grid Override for any framework */
.grid > .card,
.grid > .validator-card,
[class*="grid"] > .card,
[class*="grid"] > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    grid-row: auto !important;
}

/* SPECIFIC VALIDATOR DIRECTORY OVERRIDE */
/* This is the most specific possible selector */
.directory-content #validators-grid .card.group.data-stream.validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
    display: block !important;
}

/* CRITICAL FIX: Validator Grid Layout - FLEXBOX ONLY */
/* Remove conflicting grid layout that causes equal heights */
#validators-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 2rem !important;
    align-items: flex-start !important;
    align-content: flex-start !important;
    justify-content: flex-start !important;
}

/* Remove grid-based media queries that conflict with flexbox */
/* Cards will use responsive width classes instead */

/* Ensure flexbox children don't stretch */
#validators-grid > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* COMPACT VALIDATOR CARD LAYOUT - REDUCE EXCESSIVE SPACING */
/* This is the key fix for the vertical stretching issue */

/* Reduce card padding for validator cards specifically */
.validator-card.card {
    padding: 1rem !important; /* Reduced from 2rem to 1rem */
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Compact card sections */
.validator-card .card-header {
    margin-bottom: 0.75rem !important; /* Reduced from 1.5rem */
    padding-bottom: 0.75rem !important; /* Reduced from 1.5rem */
}

.validator-card .card-body {
    padding: 0 !important; /* Remove extra padding */
    margin: 0 !important;
}

.validator-card .card-footer {
    margin-top: 0.75rem !important; /* Reduced from 1.5rem */
    padding-top: 0.75rem !important; /* Reduced from 1.5rem */
}

/* Compact spacing for validator card content */
.validator-card .card-body .space-y-4 {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important; /* Reduced from 1rem */
    height: auto !important;
}

.validator-card .card-body .space-y-4 > * {
    flex: none !important;
    height: auto !important;
    min-height: auto !important;
    margin: 0 !important;
}

/* Fix for mining stats grid within validator cards */
.validator-card .grid.grid-cols-2 {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.5rem !important; /* Reduced gap */
    align-items: start !important;
    height: auto !important;
    min-height: auto !important;
    margin-top: 0.5rem !important;
    padding-top: 0.5rem !important;
}

.validator-card .grid.grid-cols-2 > div {
    height: auto !important;
    min-height: auto !important;
    align-self: start !important;
    padding: 0.25rem !important; /* Minimal padding */
}

/* STEP 6: Fix space-y utility classes */
.space-y-4 > *,
.space-y-6 > *,
.space-y-8 > *,
.space-y-12 > * {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
}

/* STEP 7: Target Tailwind CSS grid classes specifically */
.grid-cols-1 > *,
.grid-cols-2 > *,
.grid-cols-3 > *,
.grid-cols-4 > *,
.md\\:grid-cols-2 > *,
.md\\:grid-cols-3 > *,
.lg\\:grid-cols-3 > *,
.lg\\:grid-cols-4 > * {
    height: auto !important;
    min-height: auto !important;
    align-self: start !important;
}

/* STEP 8: Ensure containers don't force height on children */
.container > *,
.max-w-7xl > *,
.mx-auto > * {
    height: auto !important;
    min-height: auto !important;
}

/* STEP 9: NUCLEAR OPTION - Override any remaining stretch behavior */
/* This targets any element that might be causing stretching */
[class*="grid"] > [class*="glass"],
[class*="flex"] > [class*="glass"],
div[class*="grid"] > div,
div[class*="flex"] > div {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
    align-self: start !important;
}

/* STEP 10: Specific fixes for common layout patterns */
.py-20 .grid > *,
.py-16 .grid > *,
.space-y-8 .grid > *,
.space-y-6 .grid > * {
    height: auto !important;
    min-height: auto !important;
}

/* STEP 11: Force block display for glass-card content */
.glass-card > *,
.glass-card .p-6,
.glass-card .p-8,
.glass-card .p-4 {
    display: block !important;
    height: auto !important;
    min-height: auto !important;
}

/* Content spacing adjustments - simplified for new structure */
/* Main content already has proper padding from pt-16 and pb-16 classes */

/* REMOVED: Conflicting min-height rules that cause stretching */
/* These rules were causing the vertical stretching issue */

/* Ensure long content pages scroll properly */
.content-wrapper {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

/* UNIFIED FOOTER SYSTEM - COMPREHENSIVE REDESIGN */
/* Standardized footer across all ONNYX platform pages */

.footer-responsive {
    padding: 3rem 1rem; /* Increased padding */
    background: var(--onyx-black);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 10;
}

.footer-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2.5rem; /* Increased gap */
    max-width: 7xl;
    margin: 0 auto;
}

.footer-section {
    background: var(--glass-bg);
    backdrop-filter: blur(12px); /* Increased blur */
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 16px; /* Increased radius */
    padding: 2.5rem; /* Increased padding */
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.footer-section:hover {
    background: rgba(255, 255, 255, 0.12); /* Increased from 0.1 */
    border-color: rgba(0, 212, 255, 0.4); /* Increased from 0.3 */
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.3); /* Increased from 0.2 */
    transform: translateY(-2px); /* Added lift effect */
    opacity: 1 !important; /* Force full opacity */
}

.footer-section h3 {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    opacity: 1 !important; /* Force full opacity */
}

.footer-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.25rem; /* Increased padding */
    border-radius: 10px; /* Increased radius */
    transition: all 0.3s ease;
    min-height: 48px; /* Increased touch target */
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    opacity: 1 !important; /* Force full opacity */
}

.footer-link:hover {
    background: rgba(0, 212, 255, 0.15); /* Increased from 0.1 */
    color: var(--cyber-cyan);
    transform: translateX(6px); /* Increased from 4px */
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.2);
    opacity: 1 !important; /* Force full opacity */
}

.footer-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0; /* Increased padding */
    min-height: 48px; /* Increased touch target */
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Increased from 0.1 */
    opacity: 1 !important; /* Force full opacity */
}

.footer-stat-item:last-child {
    border-bottom: none;
}

.footer-stat-item .stat-label {
    color: var(--text-tertiary);
    font-weight: 500;
    opacity: 1 !important; /* Force full opacity */
}

.footer-stat-item .stat-value {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
    opacity: 1 !important; /* Force full opacity */
}

.footer-tech-item {
    display: flex;
    align-items: center;
    padding: 1rem 0; /* Increased padding */
    min-height: 48px; /* Increased touch target */
    opacity: 1 !important; /* Force full opacity */
}

.footer-tech-dot {
    width: 10px; /* Increased size */
    height: 10px;
    border-radius: 50%;
    margin-right: 1.25rem; /* Increased margin */
    box-shadow: 0 0 12px currentColor; /* Increased glow */
    opacity: 1 !important; /* Force full opacity */
}

.footer-tech-label {
    color: var(--text-secondary);
    font-weight: 500;
    opacity: 1 !important; /* Force full opacity */
}

/* Footer bottom section */
.footer-bottom {
    margin-top: 3rem; /* Increased margin */
    padding-top: 2rem; /* Increased padding */
    border-top: 1px solid rgba(255, 255, 255, 0.15); /* Increased from 0.1 */
    text-align: center;
}

.footer-bottom p {
    color: var(--text-muted);
    font-size: 0.875rem;
    opacity: 1 !important; /* Force full opacity */
}

.footer-network-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.footer-network-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.875rem;
    opacity: 1 !important; /* Force full opacity */
}

.footer-network-dot {
    width: 8px;
    height: 8px;
    background: #22c55e;
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
    opacity: 1 !important; /* Force full opacity */
}

/* RESPONSIVE DESIGN IMPROVEMENTS - ENHANCED MOBILE EXPERIENCE */
/* Mobile responsive adjustments */
@media (max-width: 768px) {
    /* Main content adjustments for mobile */
    main {
        min-height: calc(100vh - 64px);
    }

    /* REMOVED: Mobile min-height rules that cause stretching */

    /* Mobile footer adjustments */
    .footer-responsive {
        padding: 2.5rem 1rem; /* Increased from 2rem */
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem; /* Increased from 1.5rem */
    }

    .footer-section {
        padding: 2rem; /* Increased from 1.5rem */
        border-radius: 12px;
    }

    .footer-section h3 {
        font-size: 1.125rem;
        margin-bottom: 1.25rem;
    }

    .footer-link {
        padding: 1rem 1.125rem; /* Increased touch targets */
        min-height: 52px; /* Larger touch targets for mobile */
    }

    .footer-stat-item,
    .footer-tech-item {
        min-height: 52px; /* Larger touch targets for mobile */
    }

    /* Mobile navigation improvements */
    .glass-button,
    .glass-button-primary {
        min-height: 52px; /* Larger touch targets */
        padding: 1rem 1.5rem;
    }

    /* Mobile glass card improvements */
    .glass-card {
        margin: 0.5rem;
        padding: 1.5rem;
        border-radius: 12px;
    }
}

/* Tablet responsive adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    .footer-section {
        padding: 2.25rem;
    }

    /* REMOVED: Tablet min-height rules that cause stretching */
}

/* Desktop responsive adjustments */
@media (min-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }

    .footer-section {
        padding: 2.5rem;
    }

    /* REMOVED: Desktop min-height rules that cause stretching */
}

/* Large desktop adjustments */
@media (min-width: 1440px) {
    .footer-grid {
        max-width: 1280px; /* Constrain maximum width */
    }

    .footer-section {
        padding: 3rem; /* Increased padding for large screens */
    }
}

/* ACCESSIBILITY IMPROVEMENTS */
/* Enhanced focus states for better keyboard navigation */
.footer-link:focus,
.glass-button:focus,
.glass-button-primary:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

/* ENHANCED SCROLL EFFECTS SYSTEM - ONNYX PLATFORM */
/* Comprehensive scroll experience with cyber-themed animations */

/* 1. SMOOTH SCROLL TRANSITIONS WITH CUSTOM EASING */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

/* Enhanced smooth scrolling with momentum */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }

    /* Custom easing for scroll transitions */
    * {
        scroll-behavior: smooth;
        scroll-margin-top: 80px;
    }
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }

    * {
        scroll-behavior: auto;
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 2. CYBER-THEMED SCROLL PROGRESS BAR */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    z-index: 9999;
    transition: width 0.1s ease-out;
    box-shadow:
        0 0 10px var(--cyber-cyan),
        0 0 20px rgba(0, 255, 255, 0.5),
        0 0 30px rgba(0, 255, 255, 0.3);
}

.scroll-progress::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--cyber-cyan));
    animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 3. CONTENT FADE-IN ANIMATIONS */
.scroll-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Glass-card specific animations */
.glass-card.scroll-animate {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card.scroll-animate.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Staggered animation for grid items */
.grid .glass-card.scroll-animate {
    transition-delay: calc(var(--animation-order, 0) * 0.1s);
}

/* 4. PROGRESSIVE BLUR EFFECT */
.blur-on-scroll {
    transition: filter 0.3s ease-out;
}

.blur-on-scroll.blurred {
    filter: blur(2px);
    opacity: 0.7;
}

/* 5. SECTION-BASED SCROLL SNAPPING */
.scroll-snap-container {
    scroll-snap-type: y proximity;
    scroll-behavior: smooth;
}

.scroll-snap-section {
    scroll-snap-align: start;
    scroll-snap-stop: normal;
}

/* Major sections get gentle snapping */
.hero-section,
.core-technologies,
.validator-network,
.footer-responsive {
    scroll-snap-align: start;
}

/* 6. ENHANCED PARALLAX EFFECTS (SUBTLE) */
@media (prefers-reduced-motion: no-preference) {
    .parallax-subtle {
        transform: translateZ(0);
        will-change: transform;
    }

    /* Floating particles with enhanced movement */
    .floating-particle {
        animation: floatEnhanced 6s ease-in-out infinite;
    }

    @keyframes floatEnhanced {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
        }
        25% {
            transform: translateY(-10px) rotate(90deg);
            opacity: 1;
        }
        50% {
            transform: translateY(-5px) rotate(180deg);
            opacity: 0.8;
        }
        75% {
            transform: translateY(-15px) rotate(270deg);
            opacity: 0.9;
        }
    }
}

/* 7. SCROLL-TRIGGERED GLOW EFFECTS */
.glow-on-scroll {
    transition: all 0.3s ease;
}

.glow-on-scroll.in-view {
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.3),
        0 0 40px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

/* 8. ENHANCED BUTTON HOVER STATES WITH SCROLL CONTEXT */
.glass-button.scroll-enhanced:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 0 30px var(--cyber-cyan),
        0 0 60px rgba(0, 255, 255, 0.3);
}

/* 9. SCROLL-BASED NAVIGATION ENHANCEMENTS */
.nav-scroll-enhanced {
    transition: all 0.3s ease;
}

.nav-scroll-enhanced.scrolled {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

/* 10. PERFORMANCE OPTIMIZATIONS */
.scroll-optimized {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    .footer-section {
        border: 2px solid var(--cyber-cyan);
        background: rgba(0, 0, 0, 0.8);
    }

    .footer-link {
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .footer-link:hover {
        border-color: var(--cyber-cyan);
        background: rgba(0, 212, 255, 0.2);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .footer-section,
    .footer-link,
    .glass-card,
    .glass-button,
    .glass-button-primary {
        transition: none !important;
        animation: none !important;
    }

    .footer-network-dot {
        animation: none !important;
    }
}
