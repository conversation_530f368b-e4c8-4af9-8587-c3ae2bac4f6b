/* Onnyx Platform - Onyx Stone Theme */

/* CSS Variables for consistent theming - ENHANCED BRIGHTNESS */
:root {
    --onyx-black: #1a1a1a;        /* Optimized for accessibility */
    --onyx-gray: #2a2a2a;         /* Improved contrast base */
    --onyx-light: #3a3a3a;        /* Lighter accent areas */
    --cyber-cyan: #00d4ff;        /* Updated to match specification */
    --cyber-purple: #8b5cf6;      /* Updated to match specification */
    --cyber-blue: #0066ff;        /* Increased visibility */
    --glass-bg: rgba(255, 255, 255, 0.08);    /* Enhanced opacity for better contrast */
    --glass-border: rgba(255, 255, 255, 0.15); /* More visible borders */
    --glass-shadow: rgba(0, 255, 255, 0.1);

    /* Enhanced text colors for better readability */
    --text-primary: #eeeeee;      /* Brighter primary text */
    --text-secondary: #d1d5db;    /* Improved secondary text (gray-300) */
    --text-tertiary: #9ca3af;     /* Enhanced tertiary text (gray-400) */
    --text-muted: #6b7280;        /* Muted text (gray-500) */

    /* Section header theming */
    --section-header-color: var(--cyber-cyan);
    --section-spacing-top: 5rem;
    --section-spacing-bottom: 3rem;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.4), 0 0 40px rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.6), 0 0 60px rgba(0, 255, 255, 0.3);
    }
}

@keyframes purpleGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(154, 0, 255, 0.4), 0 0 40px rgba(154, 0, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(154, 0, 255, 0.6), 0 0 60px rgba(154, 0, 255, 0.3);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes countUp {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes logoGlow {
    0%, 100% {
        filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);
        box-shadow: 0 0 20px rgba(0, 255, 247, 0.3);
    }
    50% {
        filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(120%) contrast(110%);
        box-shadow: 0 0 30px rgba(0, 255, 247, 0.5);
    }
}

@keyframes logoSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes logoPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* Utility classes */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

.purple-glow-effect {
    animation: purpleGlow 2s ease-in-out infinite;
}

.float-effect {
    animation: float 3s ease-in-out infinite;
}

/* Enhanced text utility classes */
.text-primary-bright {
    color: var(--text-primary) !important;
}

.text-secondary-bright {
    color: var(--text-secondary) !important;
}

.text-tertiary-bright {
    color: var(--text-tertiary) !important;
}

.text-muted-bright {
    color: var(--text-muted) !important;
}

/* Enhanced accent colors with glow */
.text-cyber-cyan-glow {
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.text-cyber-purple-glow {
    color: var(--cyber-purple);
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.text-cyber-blue-glow {
    color: var(--cyber-blue);
    text-shadow: 0 0 10px rgba(0, 102, 255, 0.5);
}

/* SECTION TITLE CONSISTENCY - PART 1 SOLUTION */
.section-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--section-header-color);
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
    margin-top: var(--section-spacing-top);
    margin-bottom: var(--section-spacing-bottom);
    letter-spacing: 0.05em;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-cyan), transparent);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Section spacing standardization */
.section-container {
    margin-top: var(--section-spacing-top);
    margin-bottom: var(--section-spacing-bottom);
}

.section-content-buffer {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

.count-up {
    animation: countUp 0.6s ease-out;
}

/* Logo-specific utility classes */
.logo-glow {
    animation: logoGlow 3s ease-in-out infinite;
}

.logo-spin {
    animation: logoSpin 2s linear infinite;
}

.logo-pulse {
    animation: logoPulse 2s ease-in-out infinite;
}

.logo-loading {
    animation: logoSpin 1s linear infinite, logoPulse 2s ease-in-out infinite;
}

/* Logo container styles */
.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.logo-container:hover {
    transform: scale(1.05);
}

.logo-container.loading::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid transparent;
    border-top: 2px solid var(--cyber-cyan);
    border-radius: inherit;
    animation: logoSpin 1s linear infinite;
}

/* Glass Morphism Components - ENHANCED BRIGHTNESS */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.glass-nav {
    background: rgba(26, 26, 26, 0.9);  /* Slightly more opaque for better contrast */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 25px rgba(0, 255, 255, 0.4),
        0 0 50px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.glass-button-primary {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.25), rgba(154, 0, 255, 0.25));
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--cyber-cyan);
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.35), rgba(154, 0, 255, 0.35));
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.5),
        0 0 60px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.02);
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.7);
}

/* Neumorphism Components */
.neuro-card {
    background: var(--onyx-gray);
    border-radius: 20px;
    box-shadow:
        20px 20px 40px rgba(0, 0, 0, 0.5),
        -20px -20px 40px rgba(255, 255, 255, 0.02);
    transition: all 0.3s ease;
}

.neuro-card:hover {
    box-shadow:
        25px 25px 50px rgba(0, 0, 0, 0.6),
        -25px -25px 50px rgba(255, 255, 255, 0.03),
        0 0 30px rgba(0, 255, 255, 0.1);
    transform: translateY(-5px);
}

.neuro-inset {
    background: var(--onyx-gray);
    border-radius: 15px;
    box-shadow:
        inset 10px 10px 20px rgba(0, 0, 0, 0.5),
        inset -10px -10px 20px rgba(255, 255, 255, 0.02);
}

/* Form styles with Onyx theme - IMPROVED CONTRAST */
.form-input {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: #ffffff;                    /* Ensure pure white text */
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

.form-input:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.12);  /* Slightly more opaque for better readability */
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);  /* Improved placeholder visibility */
}

.form-select {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

.form-select:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-textarea {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    transition: all 0.3s ease;
    resize: vertical;
    font-family: 'Montserrat', sans-serif;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-checkbox {
    width: 18px;
    height: 18px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-checkbox:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.form-radio {
    width: 18px;
    height: 18px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    transition: all 0.3s ease;
}

.form-radio:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Special Effects */
.hero-gradient {
    background: linear-gradient(135deg,
        var(--onyx-black) 0%,
        var(--onyx-gray) 25%,
        var(--onyx-black) 50%,
        var(--onyx-light) 75%,
        var(--onyx-black) 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

.cyber-grid {
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
}

.hologram-text {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple), var(--cyber-blue));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

.data-stream {
    position: relative;
    overflow: hidden;
}

.data-stream::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 255, 0.2),
        transparent);
    animation: shimmer 3s infinite;
}

/* Badge styles with Onyx theme */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    font-family: 'Montserrat', sans-serif;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.badge-success {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.badge-warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.badge-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.badge-info {
    background: rgba(0, 255, 255, 0.2);
    color: var(--cyber-cyan);
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.badge-purple {
    background: rgba(154, 0, 255, 0.2);
    color: var(--cyber-purple);
    border: 1px solid rgba(154, 0, 255, 0.3);
}

/* Table styles with Onyx theme */
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    overflow: hidden;
}

.table th {
    padding: 16px 24px;
    background: rgba(0, 255, 255, 0.1);
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--glass-border);
}

.table td {
    padding: 16px 24px;
    color: white;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.table tbody tr:hover {
    background: rgba(0, 255, 255, 0.05);
}

/* Loading spinner with cyber theme */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-top: 2px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hash display with cyber styling */
.hash {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: var(--glass-bg);
    color: var(--cyber-cyan);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.hash-truncate {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--cyber-cyan);
}

/* Status indicators with glow effects */
.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.2);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.status-pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.2);
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
}

/* Navigation active state */
.nav-active {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

/* Custom scrollbar with cyber theme */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--onyx-gray);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--cyber-purple), var(--cyber-cyan));
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }

    .glass-card {
        margin: 8px;
        padding: 16px;
    }

    .hero-gradient {
        background-size: 200% 200%;
    }
}

@media (min-width: 641px) {
    .mobile-only {
        display: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-break {
        page-break-after: always;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-card {
        border: 2px solid white;
    }

    .glass-button {
        border: 2px solid var(--cyber-cyan);
    }
}

/* MOBILE NAVIGATION IMPROVEMENTS - PART 2A SOLUTION */
.mobile-menu-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 60;
    width: 24px;
    height: 24px;
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 55;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-items {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
    text-align: center;
}

.mobile-menu-item {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    min-width: 200px;
}

.mobile-menu-item:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

/* Hamburger animation */
.hamburger {
    width: 18px;
    height: 18px;
    position: relative;
    cursor: pointer;
}

.hamburger span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: var(--text-primary);
    border-radius: 1px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: 0.3s ease-in-out;
}

.hamburger span:nth-child(1) {
    top: 0px;
}

.hamburger span:nth-child(2) {
    top: 7px;
}

.hamburger span:nth-child(3) {
    top: 14px;
}

.hamburger.active span:nth-child(1) {
    top: 7px;
    transform: rotate(135deg);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.hamburger.active span:nth-child(3) {
    top: 7px;
    transform: rotate(-135deg);
}

/* VISUAL CONSISTENCY FIXES - COMPREHENSIVE SOLUTION */
/* Fix opacity inconsistencies and ensure proper footer positioning */

/* OPACITY STANDARDIZATION - Remove reduced opacity from all components */
/* All interactive elements should have full opacity for better visibility */

/* Glass card components - Enhanced visibility */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.12); /* Increased from 0.1 */
    border-color: rgba(0, 255, 255, 0.4); /* Increased from 0.3 */
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 255, 0.3), /* Increased from 0.2 */
        inset 0 1px 0 rgba(255, 255, 255, 0.2); /* Increased from 0.15 */
    opacity: 1 !important; /* Force full opacity */
}

/* Button components - Full opacity */
.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.15); /* Increased from 0.12 */
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 25px rgba(0, 255, 255, 0.5), /* Increased from 0.4 */
        0 0 50px rgba(0, 255, 255, 0.3), /* Increased from 0.2 */
        inset 0 1px 0 rgba(255, 255, 255, 0.25); /* Increased from 0.2 */
    transform: translateY(-2px);
    opacity: 1 !important; /* Force full opacity */
}

.glass-button-primary {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.3), rgba(154, 0, 255, 0.3)); /* Increased from 0.25 */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--cyber-cyan);
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.6); /* Increased from 0.5 */
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3); /* Increased from 0.2 */
    opacity: 1 !important; /* Force full opacity */
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.4), rgba(154, 0, 255, 0.4)); /* Increased from 0.35 */
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.6), /* Increased from 0.5 */
        0 0 60px rgba(0, 255, 255, 0.4), /* Increased from 0.3 */
        inset 0 1px 0 rgba(255, 255, 255, 0.3); /* Increased from 0.2 */
    transform: translateY(-2px) scale(1.02);
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.8); /* Increased from 0.7 */
    opacity: 1 !important; /* Force full opacity */
}

/* Navigation elements - Full opacity */
.glass-nav {
    background: rgba(26, 26, 26, 0.95); /* Increased from 0.9 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Increased from 0.1 */
    opacity: 1 !important; /* Force full opacity */
}

/* Text elements - Enhanced visibility */
.text-text-primary {
    color: var(--text-primary);
    opacity: 1 !important; /* Force full opacity */
}

.text-text-secondary {
    color: var(--text-secondary);
    opacity: 1 !important; /* Force full opacity */
}

.text-text-tertiary {
    color: var(--text-tertiary);
    opacity: 1 !important; /* Force full opacity */
}

/* Remove opacity reductions from decorative elements */
.opacity-70 { opacity: 1 !important; }
.opacity-60 { opacity: 1 !important; }
.opacity-50 { opacity: 0.8 !important; } /* Slightly reduced but still visible */
.opacity-40 { opacity: 0.7 !important; } /* Slightly reduced but still visible */

/* CRITICAL FOOTER POSITIONING FIXES - EMERGENCY OVERRIDE */
/* Force proper page layout with footer that never overlaps content */

/* Root layout setup for proper flexbox structure */
html {
    height: 100% !important;
    scroll-behavior: smooth;
}

body {
    height: 100% !important;
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    padding: 0 !important;
    position: relative !important;
}

/* CRITICAL: Force footer to bottom */
footer.footer-responsive {
    flex-shrink: 0 !important;
    margin-top: auto !important;
    position: relative !important;
    width: 100% !important;
    z-index: 10 !important;
    background: var(--onyx-black) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* CRITICAL: Force main content wrapper to expand properly */
div[style*="flex: 1 0 auto"] {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 64px) !important;
    position: relative !important;
}

/* CRITICAL: Force main content area to expand within wrapper */
main[style*="flex: 1 0 auto"] {
    flex: 1 0 auto !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
    min-height: calc(100vh - 200px) !important;
}

/* Legacy support for old classes */
.flex-1 {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 64px) !important;
    position: relative !important;
}

main {
    flex: 1 0 auto !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
}

/* CRITICAL: Fix page-specific content wrappers that break footer positioning */
.directory-content,
.explorer-content,
.dashboard-content,
.auth-content {
    min-height: auto !important;
    height: auto !important;
    flex: none !important;
    display: block !important;
}

/* Ensure page content doesn't interfere with footer positioning */
.directory-content.hero-gradient,
.explorer-content.hero-gradient,
.dashboard-content.hero-gradient,
.auth-content.hero-gradient {
    padding-bottom: 3rem !important;
    margin-bottom: 0 !important;
}

/* CRITICAL: Override any conflicting height/min-height rules on page content */
.hero-gradient {
    min-height: auto !important;
    height: auto !important;
}

/* Ensure cyber-grid doesn't interfere with layout */
.cyber-grid {
    position: relative !important;
    z-index: 1 !important;
}

/* Force proper spacing for all page content sections */
.py-20 {
    padding-top: 5rem !important;
    padding-bottom: 3rem !important;
}

/* CRITICAL: Fix min-h-screen that breaks footer positioning */
.min-h-screen {
    min-height: auto !important;
    height: auto !important;
}

/* Specific fix for auth pages that use min-h-screen */
.min-h-screen.hero-gradient {
    min-height: auto !important;
    height: auto !important;
    display: block !important;
    flex: none !important;
}

/* TARGETED FIX: Only target page wrapper divs, not content containers */
/* Target only the main page content wrappers that interfere with footer */
main > div.directory-content,
main > div.explorer-content,
main > div.dashboard-content,
main > div.auth-content,
main > div.min-h-screen {
    min-height: auto !important;
    height: auto !important;
    flex: none !important;
}

/* Ensure main page wrapper has proper bottom spacing for footer */
main > div:last-child.directory-content,
main > div:last-child.explorer-content,
main > div:last-child.dashboard-content,
main > div:last-child.auth-content {
    margin-bottom: 0 !important;
    padding-bottom: 3rem !important;
}

/* CRITICAL: Prevent content containers from stretching vertically */
.glass-card {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
    align-self: auto !important;
}

/* CRITICAL: Fix CSS Grid stretching behavior */
.grid {
    align-items: start !important;
    align-content: start !important;
}

.grid > .glass-card {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* Specific fix for validator cards in grid */
.grid .validator-card {
    height: auto !important;
    min-height: auto !important;
    align-self: start !important;
}

/* Fix for any nested content in grid items */
.grid > * > .glass-card {
    height: auto !important;
    min-height: auto !important;
}

/* Fix for validator cards and search panels */
.validator-card,
.search-panel,
.explorer-search {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
}

/* TARGETED: Fix specific flexbox containers that stretch children */
.flex-col > .glass-card {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
}

/* Ensure space-y containers don't stretch */
.space-y-4 > *,
.space-y-6 > *,
.space-y-8 > * {
    height: auto !important;
    min-height: auto !important;
}

/* Content spacing adjustments - simplified for new structure */
/* Main content already has proper padding from pt-16 and pb-16 classes */

/* REMOVED: Conflicting min-height rules that cause stretching */
/* These rules were causing the vertical stretching issue */

/* Ensure long content pages scroll properly */
.content-wrapper {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

/* UNIFIED FOOTER SYSTEM - COMPREHENSIVE REDESIGN */
/* Standardized footer across all ONNYX platform pages */

.footer-responsive {
    padding: 3rem 1rem; /* Increased padding */
    background: var(--onyx-black);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 10;
}

.footer-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2.5rem; /* Increased gap */
    max-width: 7xl;
    margin: 0 auto;
}

.footer-section {
    background: var(--glass-bg);
    backdrop-filter: blur(12px); /* Increased blur */
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 16px; /* Increased radius */
    padding: 2.5rem; /* Increased padding */
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.footer-section:hover {
    background: rgba(255, 255, 255, 0.12); /* Increased from 0.1 */
    border-color: rgba(0, 212, 255, 0.4); /* Increased from 0.3 */
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.3); /* Increased from 0.2 */
    transform: translateY(-2px); /* Added lift effect */
    opacity: 1 !important; /* Force full opacity */
}

.footer-section h3 {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    opacity: 1 !important; /* Force full opacity */
}

.footer-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.25rem; /* Increased padding */
    border-radius: 10px; /* Increased radius */
    transition: all 0.3s ease;
    min-height: 48px; /* Increased touch target */
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    opacity: 1 !important; /* Force full opacity */
}

.footer-link:hover {
    background: rgba(0, 212, 255, 0.15); /* Increased from 0.1 */
    color: var(--cyber-cyan);
    transform: translateX(6px); /* Increased from 4px */
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.2);
    opacity: 1 !important; /* Force full opacity */
}

.footer-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0; /* Increased padding */
    min-height: 48px; /* Increased touch target */
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Increased from 0.1 */
    opacity: 1 !important; /* Force full opacity */
}

.footer-stat-item:last-child {
    border-bottom: none;
}

.footer-stat-item .stat-label {
    color: var(--text-tertiary);
    font-weight: 500;
    opacity: 1 !important; /* Force full opacity */
}

.footer-stat-item .stat-value {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
    opacity: 1 !important; /* Force full opacity */
}

.footer-tech-item {
    display: flex;
    align-items: center;
    padding: 1rem 0; /* Increased padding */
    min-height: 48px; /* Increased touch target */
    opacity: 1 !important; /* Force full opacity */
}

.footer-tech-dot {
    width: 10px; /* Increased size */
    height: 10px;
    border-radius: 50%;
    margin-right: 1.25rem; /* Increased margin */
    box-shadow: 0 0 12px currentColor; /* Increased glow */
    opacity: 1 !important; /* Force full opacity */
}

.footer-tech-label {
    color: var(--text-secondary);
    font-weight: 500;
    opacity: 1 !important; /* Force full opacity */
}

/* Footer bottom section */
.footer-bottom {
    margin-top: 3rem; /* Increased margin */
    padding-top: 2rem; /* Increased padding */
    border-top: 1px solid rgba(255, 255, 255, 0.15); /* Increased from 0.1 */
    text-align: center;
}

.footer-bottom p {
    color: var(--text-muted);
    font-size: 0.875rem;
    opacity: 1 !important; /* Force full opacity */
}

.footer-network-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.footer-network-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.875rem;
    opacity: 1 !important; /* Force full opacity */
}

.footer-network-dot {
    width: 8px;
    height: 8px;
    background: #22c55e;
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
    opacity: 1 !important; /* Force full opacity */
}

/* RESPONSIVE DESIGN IMPROVEMENTS - ENHANCED MOBILE EXPERIENCE */
/* Mobile responsive adjustments */
@media (max-width: 768px) {
    /* Main content adjustments for mobile */
    main {
        min-height: calc(100vh - 64px);
    }

    /* REMOVED: Mobile min-height rules that cause stretching */

    /* Mobile footer adjustments */
    .footer-responsive {
        padding: 2.5rem 1rem; /* Increased from 2rem */
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem; /* Increased from 1.5rem */
    }

    .footer-section {
        padding: 2rem; /* Increased from 1.5rem */
        border-radius: 12px;
    }

    .footer-section h3 {
        font-size: 1.125rem;
        margin-bottom: 1.25rem;
    }

    .footer-link {
        padding: 1rem 1.125rem; /* Increased touch targets */
        min-height: 52px; /* Larger touch targets for mobile */
    }

    .footer-stat-item,
    .footer-tech-item {
        min-height: 52px; /* Larger touch targets for mobile */
    }

    /* Mobile navigation improvements */
    .glass-button,
    .glass-button-primary {
        min-height: 52px; /* Larger touch targets */
        padding: 1rem 1.5rem;
    }

    /* Mobile glass card improvements */
    .glass-card {
        margin: 0.5rem;
        padding: 1.5rem;
        border-radius: 12px;
    }
}

/* Tablet responsive adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    .footer-section {
        padding: 2.25rem;
    }

    /* REMOVED: Tablet min-height rules that cause stretching */
}

/* Desktop responsive adjustments */
@media (min-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }

    .footer-section {
        padding: 2.5rem;
    }

    /* REMOVED: Desktop min-height rules that cause stretching */
}

/* Large desktop adjustments */
@media (min-width: 1440px) {
    .footer-grid {
        max-width: 1280px; /* Constrain maximum width */
    }

    .footer-section {
        padding: 3rem; /* Increased padding for large screens */
    }
}

/* ACCESSIBILITY IMPROVEMENTS */
/* Enhanced focus states for better keyboard navigation */
.footer-link:focus,
.glass-button:focus,
.glass-button-primary:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    .footer-section {
        border: 2px solid var(--cyber-cyan);
        background: rgba(0, 0, 0, 0.8);
    }

    .footer-link {
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .footer-link:hover {
        border-color: var(--cyber-cyan);
        background: rgba(0, 212, 255, 0.2);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .footer-section,
    .footer-link,
    .glass-card,
    .glass-button,
    .glass-button-primary {
        transition: none !important;
        animation: none !important;
    }

    .footer-network-dot {
        animation: none !important;
    }
}
