@echo off
echo 🚀 ONNYX Platform - GitHub Pages Deployment Helper
echo =====================================================
echo.
echo This script will help you deploy to GitHub Pages
echo Repository: https://github.com/Geo222222/onnyx-platform.git
echo.

echo 📁 Files to upload:
echo    ✅ index.html     - Main ONNYX platform interface
echo    ✅ styles.css     - Complete Onyx Stone theme
echo    ✅ script.js      - Frontend logic + backend integration  
echo    ✅ README.md      - Deployment guide
echo.

echo 📂 Source directory: docs/github-pages/
dir docs\github-pages\*.* /b
echo.

echo 🌐 After upload, your site will be live at:
echo    https://geo222222.github.io/onnyx-platform/
echo.

echo 📋 DEPLOYMENT STEPS:
echo    1. Go to: https://github.com/Geo222222/onnyx-platform
echo    2. Click "Add file" → "Upload files"
echo    3. Drag and drop the 4 files from docs/github-pages/
echo    4. Commit message: "Deploy ONNYX Platform frontend"
echo    5. Click "Commit changes"
echo    6. Go to Settings → Pages
echo    7. Source: "Deploy from a branch" → main → / (root)
echo    8. Save settings
echo    9. Wait 5-10 minutes for deployment
echo   10. Access: https://geo222222.github.io/onnyx-platform/
echo.

echo ⚡ QUICK COPY COMMAND (if using Git):
echo    git clone https://github.com/Geo222222/onnyx-platform.git
echo    cd onnyx-platform
echo    copy ..\onnyx\docs\github-pages\*.* .
echo    git add .
echo    git commit -m "Deploy ONNYX Platform frontend"
echo    git push origin main
echo.

echo 🎯 Ready to deploy? Upload the files from docs/github-pages/ to your repository!
echo.
pause
