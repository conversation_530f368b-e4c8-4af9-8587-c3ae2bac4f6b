# src/routes/judah.py

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List
from src.judah.engine import JudahEngine
from src.tokens.ledger import TokenLedger
from src.zeman.ledger import ZemanLedger
from src.etzem.score import EtzemScorer
from src.identity.registry import IdentityRegistry
from src.business.sela import SelaRegistry
from src.chain.txlog import TxLogger
from src.chain.mempool import Mempool
from src.council.registry import CouncilRegistry

# Initialize components
judah_router = APIRouter(prefix="/judah", tags=["judah"])
token_ledger = TokenLedger()
zeman_ledger = ZemanLedger()
etzem_scorer = EtzemScorer()
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()
txlog = TxLogger()
mempool = Mempool()
council_registry = CouncilRegistry()

# Initialize the Judah Engine
judah_engine = JudahEngine(
    token_ledger=token_ledger,
    zeman_ledger=zeman_ledger,
    etzem_scorer=etzem_scorer,
    identity_registry=identity_registry,
    sela_registry=sela_registry,
    txlog=txlog
)

# Request models
class TransactionFeeRequest(BaseModel):
    identity_id: str
    tx_type: str = "standard"
    amount: float = 0.0
    token_id: str = "ONX"
    message: str
    signature: str

class RebateRequest(BaseModel):
    identity_id: str
    role: str
    amount: float
    message: str
    signature: str

class StakeRequest(BaseModel):
    identity_id: str
    amount: float
    lock_period: Optional[int] = None
    message: str
    signature: str

class ConfigUpdateRequest(BaseModel):
    updates: Dict[str, Any]
    identity_id: str
    message: str
    signature: str

# Routes
@judah_router.post("/fee")
def apply_transaction_fee(req: TransactionFeeRequest):
    """
    Apply a transaction fee to an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")
    
    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "tx_type": req.tx_type,
        "amount": req.amount,
        "token_id": req.token_id
    }
    mempool_tx = mempool.add("apply_transaction_fee", payload)
    
    try:
        # Apply the transaction fee
        fee_details = judah_engine.apply_transaction_fee(
            identity_id=req.identity_id,
            tx_type=req.tx_type,
            amount=req.amount,
            token_id=req.token_id
        )
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "fee_details": fee_details
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@judah_router.post("/rebate")
def apply_rebate(req: RebateRequest):
    """
    Apply a rebate to an identity based on their role.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")
    
    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "role": req.role,
        "amount": req.amount
    }
    mempool_tx = mempool.add("apply_rebate", payload)
    
    try:
        # Apply the rebate
        rebate_details = judah_engine.apply_rebate(
            identity_id=req.identity_id,
            role=req.role,
            amount=req.amount
        )
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "rebate_details": rebate_details
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@judah_router.post("/stake")
def stake_onx(req: StakeRequest):
    """
    Stake ONX tokens for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")
    
    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "amount": req.amount,
        "lock_period": req.lock_period
    }
    mempool_tx = mempool.add("stake_onx", payload)
    
    try:
        # Stake ONX
        stake_details = judah_engine.stake_onx(
            identity_id=req.identity_id,
            amount=req.amount,
            lock_period=req.lock_period
        )
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "stake_details": stake_details
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@judah_router.get("/config")
def get_config():
    """
    Get the Judah Engine configuration.
    """
    return {
        "config": judah_engine.get_config()
    }

@judah_router.post("/config/update")
def update_config(req: ConfigUpdateRequest):
    """
    Update the Judah Engine configuration.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")
    
    # Check if the identity is a councilor
    is_councilor = council_registry.is_councilor(req.identity_id)
    if not is_councilor:
        raise HTTPException(status_code=403, detail="Only councilors can update the Judah Engine configuration")
    
    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "updates": req.updates
    }
    mempool_tx = mempool.add("update_judah_config", payload)
    
    try:
        # Update the configuration
        updated_config = judah_engine.update_config(
            updates=req.updates,
            updated_by=req.identity_id
        )
        
        # Remove from mempool
        mempool.remove(mempool_tx["id"])
        
        return {
            "status": "success",
            "config": updated_config
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@judah_router.get("/discount/{identity_id}")
def get_discount(identity_id: str):
    """
    Get the discount for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")
    
    # Calculate discount
    discount_info = judah_engine.calculate_total_discount(identity_id)
    
    return {
        "identity_id": identity_id,
        "discount_info": discount_info
    }

@judah_router.get("/staking/tiers")
def get_staking_tiers():
    """
    Get the staking tiers.
    """
    return {
        "tiers": judah_engine.get_staking_tiers()
    }
