"""
Onnyx Sela Miner Routes

This module provides API routes for Sela miners.
"""

import logging
import time
import uuid
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from blockchain.node.sela_config import SelaConfig
from blockchain.node.activity_ledger import ActivityLedger
from blockchain.node.rotation_registry import RotationRegistry
from blockchain.node.block_signer import BlockSigner
from blockchain.consensus.miner import BlockMiner
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry

from shared.models.sela import Sela
from shared.models.identity import Identity
from shared.models.block import Block
from shared.models.transaction import Transaction
from shared.models.activity import Activity
from shared.models.service import Service
from shared.models.vote import Vote

# Set up logging
logger = logging.getLogger("onnyx.routes.sela_miner")

# Create router
router = APIRouter()

# Create instances
sela_config = SelaConfig()
activity_ledger = ActivityLedger()
rotation_registry = RotationRegistry()
block_signer = BlockSigner()
block_miner = BlockMiner()
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()

@router.get("/sela-miner/status")
def get_status() -> Dict[str, Any]:
    """
    Get the status of the Sela miner.

    Returns:
        The Sela miner status
    """
    logger.info("Getting Sela miner status")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Get the validator status
    validator_status = rotation_registry.get_validator_status(sela_id)

    # Get the mining stats
    mining_stats = block_miner.get_mining_stats()

    # Get activity stats
    activities = Activity.find_by_sela(sela_id)
    services = Service.find_by_sela(sela_id)
    pending_txs = Transaction.find_by_status("PENDING")
    votes = Vote.find_by_sela(sela_id)

    logger.info(f"Retrieved Sela miner status for Sela {sela_id}")
    return {
        "sela_id": sela_id,
        "identity_id": sela_config.get("identity_id"),
        "role": sela_config.get("role"),
        "api_port": sela_config.get("api_port"),
        "auto_mine": sela_config.get("auto_mine"),
        "mine_interval": sela_config.get("mine_interval"),
        "validator_status": validator_status,
        "mining_stats": mining_stats,
        "activity_stats": {
            "activities": len(activities),
            "services": len(services),
            "pending_transactions": len(pending_txs),
            "governance_votes": len(votes)
        }
    }

@router.post("/sela-miner/mine")
def mine_block(force: bool = False) -> Dict[str, Any]:
    """
    Mine a new block.

    Args:
        force: Force mining even if not the current validator

    Returns:
        The mined block
    """
    logger.info(f"Mining block with force={force}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Check if this Sela is the current validator
    current_validator = rotation_registry.get_current_validator()

    if current_validator != sela_id and not force:
        logger.warning(f"This Sela ({sela_id}) is not the current validator ({current_validator})")
        raise HTTPException(
            status_code=403,
            detail=f"This Sela ({sela_id}) is not the current validator ({current_validator})"
        )

    # Load the private key
    try:
        private_key_pem = block_signer.load_private_key(sela_config.get("private_key_path"))
    except Exception as e:
        logger.error(f"Error loading private key: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading private key: {str(e)}")

    # Mine a new block
    try:
        block_data = block_miner.create_block(
            proposer_id=sela_config.get("identity_id"),
            sela_id=sela_id,
            private_key_pem=private_key_pem
        )

        # Create the block in the database
        block = Block.create(
            block_hash=block_data["hash"],
            previous_hash=block_data["previous_hash"],
            index=block_data["index"],
            timestamp=block_data["timestamp"],
            transactions=block_data["transactions"],
            nonce=block_data["nonce"],
            difficulty=block_data.get("difficulty", 4),
            miner=sela_config.get("identity_id")
        )

        # Update transaction statuses
        for tx_data in block_data["transactions"]:
            if isinstance(tx_data, dict) and "tx_id" in tx_data:
                tx = Transaction.get_by_id(tx_data["tx_id"])
                if tx:
                    tx.status = "CONFIRMED"
                    tx.block_hash = block.block_hash
                    tx.block_index = block.index
                    tx.save()
                    logger.info(f"Transaction {tx.tx_id} confirmed in block #{block.index}")

        # Record the mining activity
        activity_id = f"activity_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Activity.create(
            activity_id=activity_id,
            sela_id=sela_id,
            activity_type="MINING",
            description=f"Mined block #{block.index}",
            metadata={
                "block_hash": block.block_hash,
                "block_index": block.index,
                "tx_count": len(block.transactions)
            }
        )

        logger.info(f"Block #{block.index} mined with hash {block.block_hash}")
        return {
            "status": "block_mined",
            "block": {
                "index": block.index,
                "hash": block.block_hash,
                "previous_hash": block.previous_hash,
                "timestamp": block.timestamp,
                "tx_count": len(block.transactions),
                "miner": block.miner,
                "difficulty": block.difficulty,
                "nonce": block.nonce
            }
        }
    except Exception as e:
        logger.error(f"Error mining block: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error mining block: {str(e)}")

@router.post("/sela-miner/service")
def record_service(
    service_type: str = Body(...),
    description: str = Body(...),
    recipient_id: Optional[str] = Body(None),
    duration: Optional[int] = Body(None),
    metadata: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Record a service provided by the Sela.

    Args:
        service_type: The type of service
        description: A description of the service
        recipient_id: The recipient's identity ID (optional)
        duration: The duration of the service in minutes (optional)
        metadata: Additional metadata for the service (optional)

    Returns:
        The recorded service
    """
    logger.info(f"Recording service of type {service_type}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Record the service
    try:
        # Generate a service ID
        service_id = f"service_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Create the service in the database
        service = Service.create(
            service_id=service_id,
            sela_id=sela_id,
            service_type=service_type,
            description=description,
            recipient_id=recipient_id,
            duration=duration,
            metadata=metadata
        )

        # Record the service in the activity ledger
        activity_ledger.record_service(
            service_type=service_type,
            description=description,
            recipient_id=recipient_id,
            duration=duration,
            metadata=metadata
        )

        logger.info(f"Service {service_id} recorded for Sela {sela_id}")
        return {
            "status": "service_recorded",
            "service": service.to_dict()
        }
    except Exception as e:
        logger.error(f"Error recording service: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error recording service: {str(e)}")

@router.post("/sela-miner/vote")
def record_vote(
    scroll_id: str = Body(...),
    vote: str = Body(...),
    reason: Optional[str] = Body(None)
) -> Dict[str, Any]:
    """
    Record a governance vote.

    Args:
        scroll_id: The Voice Scroll ID
        vote: The vote (e.g., "yes", "no", "abstain")
        reason: The reason for the vote (optional)

    Returns:
        The recorded vote
    """
    logger.info(f"Recording vote {vote} for scroll {scroll_id}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID and identity ID
    sela_id = sela_config.get("sela_id")
    identity_id = sela_config.get("identity_id")

    # Validate the vote
    if vote not in ["yes", "no", "abstain"]:
        logger.warning(f"Invalid vote: {vote}")
        raise HTTPException(status_code=400, detail="Invalid vote. Must be 'yes', 'no', or 'abstain'")

    # Record the vote
    try:
        # Generate a vote ID
        vote_id = f"vote_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Create the vote in the database
        vote_obj = Vote.create(
            vote_id=vote_id,
            scroll_id=scroll_id,
            sela_id=sela_id,
            identity_id=identity_id,
            vote=vote,
            reason=reason
        )

        # Record the vote in the activity ledger
        activity_ledger.record_vote(
            scroll_id=scroll_id,
            vote=vote,
            reason=reason
        )

        # Create a vote transaction
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"
        Transaction.create(
            tx_id=tx_id,
            tx_type="VOTE",
            sender=identity_id,
            status="PENDING",
            data={
                "op": "OP_VOTE",
                "scroll_id": scroll_id,
                "vote": vote == "yes",
                "reason": reason
            }
        )

        logger.info(f"Vote {vote_id} recorded for Sela {sela_id} on scroll {scroll_id}")
        return {
            "status": "vote_recorded",
            "vote": vote_obj.to_dict()
        }
    except Exception as e:
        logger.error(f"Error recording vote: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error recording vote: {str(e)}")

@router.get("/sela-miner/activities")
def get_activities(
    activity_type: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get activities.

    Args:
        activity_type: Filter by activity type (optional)
        limit: Maximum number of activities to return

    Returns:
        A list of activities
    """
    logger.info(f"Getting activities with type={activity_type} and limit={limit}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Get activities from the database
    if activity_type:
        activities = Activity.find_by_type(activity_type)
        activities = [a for a in activities if a.sela_id == sela_id][:limit]
    else:
        activities = Activity.find_by_sela(sela_id, limit)

    logger.info(f"Retrieved {len(activities)} activities for Sela {sela_id}")
    return {
        "activities": [activity.to_dict() for activity in activities]
    }

@router.get("/sela-miner/services")
def get_services(
    service_type: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get service logs.

    Args:
        service_type: Filter by service type (optional)
        limit: Maximum number of services to return

    Returns:
        A list of service logs
    """
    logger.info(f"Getting services with type={service_type} and limit={limit}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Get services from the database
    if service_type:
        services = Service.find_by_type(service_type)
        services = [s for s in services if s.sela_id == sela_id][:limit]
    else:
        services = Service.find_by_sela(sela_id, limit)

    logger.info(f"Retrieved {len(services)} services for Sela {sela_id}")
    return {
        "services": [service.to_dict() for service in services]
    }

@router.get("/sela-miner/pending-transactions")
def get_pending_transactions(
    tx_type: Optional[str] = None,
    status: Optional[str] = "PENDING"
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get pending transactions.

    Args:
        tx_type: Filter by transaction type (optional)
        status: Filter by transaction status (default: "PENDING")

    Returns:
        A list of pending transactions
    """
    logger.info(f"Getting pending transactions with type={tx_type} and status={status}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get transactions from the database
    if tx_type:
        transactions = Transaction.find_by_type_and_status(tx_type, status)
    else:
        transactions = Transaction.find_by_status(status)

    logger.info(f"Retrieved {len(transactions)} pending transactions")
    return {
        "transactions": [tx.to_dict() for tx in transactions]
    }

@router.get("/sela-miner/votes")
def get_votes(
    scroll_id: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get governance votes.

    Args:
        scroll_id: Filter by Voice Scroll ID (optional)

    Returns:
        A list of governance votes
    """
    logger.info(f"Getting votes with scroll_id={scroll_id}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Get votes from the database
    if scroll_id:
        votes = Vote.find_by_scroll(scroll_id)
        votes = [v for v in votes if v.sela_id == sela_id]
    else:
        votes = Vote.find_by_sela(sela_id)

    logger.info(f"Retrieved {len(votes)} votes for Sela {sela_id}")
    return {
        "votes": [vote.to_dict() for vote in votes]
    }

@router.get("/sela-miner/validator-status")
def get_validator_status() -> Dict[str, Any]:
    """
    Get the validator status.

    Returns:
        The validator status
    """
    logger.info("Getting validator status")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Get the validator status
    validator_status = rotation_registry.get_validator_status(sela_id)

    logger.info(f"Retrieved validator status for Sela {sela_id}")
    return validator_status

@router.post("/sela-miner/activity")
def record_activity(
    activity_type: str = Body(...),
    description: str = Body(...),
    metadata: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Record an activity.

    Args:
        activity_type: The type of activity
        description: A description of the activity
        metadata: Additional metadata for the activity (optional)

    Returns:
        The recorded activity
    """
    logger.info(f"Recording activity of type {activity_type}")

    # Check if the configuration is valid
    if not sela_config.validate():
        logger.warning("Invalid Sela miner configuration")
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")

    # Get the Sela ID
    sela_id = sela_config.get("sela_id")

    # Record the activity
    try:
        # Generate an activity ID
        activity_id = f"activity_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Create the activity in the database
        activity = Activity.create(
            activity_id=activity_id,
            sela_id=sela_id,
            activity_type=activity_type,
            description=description,
            metadata=metadata
        )

        # Record the activity in the activity ledger
        activity_ledger.record_activity(
            activity_type=activity_type,
            description=description,
            metadata=metadata
        )

        logger.info(f"Activity {activity_id} recorded for Sela {sela_id}")
        return {
            "status": "activity_recorded",
            "activity": activity.to_dict()
        }
    except Exception as e:
        logger.error(f"Error recording activity: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error recording activity: {str(e)}")
