"""
Onnyx Activity Ledger Module

This module provides the ActivityLedger class for tracking Sela activities.
"""

import os
import json
import time
import uuid
import logging
from typing import Dict, Any, List, Optional

# Set up logging
logger = logging.getLogger("onnyx.node.activity_ledger")

class ActivityLedger:
    """
    ActivityLedger tracks activities for a Sela.
    """

    def __init__(self, ledger_path: Optional[str] = None, sela_id: Optional[str] = None):
        """
        Initialize the ActivityLedger.

        Args:
            ledger_path: Path to the activity ledger JSON file. If None, a default path will be used.
            sela_id: The Sela ID. If None, it will be loaded from the configuration.
        """
        # Set up ledger path
        if ledger_path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.ledger_path = os.path.join(data_dir, "activity_ledger.json")
        else:
            self.ledger_path = ledger_path

        # Set Sela ID
        if sela_id is None:
            # Try to load from configuration
            try:
                from blockchain.node.sela_config import SELA_ID
                self.sela_id = SELA_ID
            except ImportError:
                self.sela_id = "unknown"
        else:
            self.sela_id = sela_id

        # Initialize ledger
        self.ledger = {
            "sela_id": self.sela_id,
            "activities": [],
            "pending_transactions": [],
            "governance_votes": [],
            "service_logs": []
        }

        # Load ledger
        self._load_ledger()

    def _load_ledger(self):
        """Load the ledger from the file."""
        try:
            if os.path.exists(self.ledger_path) and os.path.getsize(self.ledger_path) > 0:
                with open(self.ledger_path, "r") as f:
                    loaded_ledger = json.load(f)
                    # Update the ledger with the loaded values
                    self.ledger.update(loaded_ledger)
                    logger.info(f"Loaded activity ledger from {self.ledger_path}")
            else:
                # Save the default ledger
                self._save_ledger()
                logger.info(f"Created default activity ledger at {self.ledger_path}")
        except Exception as e:
            logger.error(f"Error loading activity ledger: {str(e)}")

    def _save_ledger(self):
        """Save the ledger to the file."""
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.ledger_path), exist_ok=True)
            
            with open(self.ledger_path, "w") as f:
                json.dump(self.ledger, f, indent=2)
                logger.info(f"Saved activity ledger to {self.ledger_path}")
        except Exception as e:
            logger.error(f"Error saving activity ledger: {str(e)}")

    def record_activity(self, activity_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Record an activity.

        Args:
            activity_type: The type of activity (e.g., "service", "token_mint", "vote")
            data: The activity data

        Returns:
            The recorded activity
        """
        activity = {
            "id": str(uuid.uuid4()),
            "type": activity_type,
            "timestamp": int(time.time()),
            "sela_id": self.sela_id,
            "data": data
        }

        self.ledger["activities"].append(activity)
        self._save_ledger()

        logger.info(f"Recorded activity: {activity_type}")
        return activity

    def record_service(self, service_type: str, description: str, recipient_id: Optional[str] = None,
                      duration: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Record a service provided by the Sela.

        Args:
            service_type: The type of service
            description: A description of the service
            recipient_id: The recipient's identity ID (optional)
            duration: The duration of the service in minutes (optional)
            metadata: Additional metadata for the service (optional)

        Returns:
            The recorded service
        """
        service = {
            "id": str(uuid.uuid4()),
            "type": service_type,
            "description": description,
            "timestamp": int(time.time()),
            "sela_id": self.sela_id
        }

        if recipient_id:
            service["recipient_id"] = recipient_id

        if duration:
            service["duration"] = duration

        if metadata:
            service["metadata"] = metadata

        self.ledger["service_logs"].append(service)
        
        # Also record as an activity
        self.record_activity("service", service)

        self._save_ledger()

        logger.info(f"Recorded service: {service_type}")
        return service

    def add_pending_transaction(self, tx_type: str, tx_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a pending transaction.

        Args:
            tx_type: The type of transaction
            tx_data: The transaction data

        Returns:
            The pending transaction
        """
        tx = {
            "id": str(uuid.uuid4()),
            "type": tx_type,
            "timestamp": int(time.time()),
            "sela_id": self.sela_id,
            "data": tx_data,
            "status": "pending"
        }

        self.ledger["pending_transactions"].append(tx)
        self._save_ledger()

        logger.info(f"Added pending transaction: {tx_type}")
        return tx

    def update_transaction_status(self, tx_id: str, status: str, tx_hash: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Update the status of a pending transaction.

        Args:
            tx_id: The transaction ID
            status: The new status (e.g., "confirmed", "failed")
            tx_hash: The transaction hash (optional)

        Returns:
            The updated transaction or None if not found
        """
        for tx in self.ledger["pending_transactions"]:
            if tx["id"] == tx_id:
                tx["status"] = status
                if tx_hash:
                    tx["tx_hash"] = tx_hash
                tx["updated_at"] = int(time.time())
                self._save_ledger()
                logger.info(f"Updated transaction {tx_id} status to {status}")
                return tx

        logger.warning(f"Transaction {tx_id} not found")
        return None

    def record_vote(self, scroll_id: str, vote: str, reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Record a governance vote.

        Args:
            scroll_id: The Voice Scroll ID
            vote: The vote (e.g., "yes", "no", "abstain")
            reason: The reason for the vote (optional)

        Returns:
            The recorded vote
        """
        vote_record = {
            "id": str(uuid.uuid4()),
            "scroll_id": scroll_id,
            "vote": vote,
            "timestamp": int(time.time()),
            "sela_id": self.sela_id
        }

        if reason:
            vote_record["reason"] = reason

        self.ledger["governance_votes"].append(vote_record)
        
        # Also record as an activity
        self.record_activity("vote", vote_record)

        self._save_ledger()

        logger.info(f"Recorded vote on scroll {scroll_id}: {vote}")
        return vote_record

    def get_activities(self, activity_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get activities.

        Args:
            activity_type: Filter by activity type (optional)
            limit: Maximum number of activities to return

        Returns:
            A list of activities
        """
        activities = self.ledger["activities"]
        
        if activity_type:
            activities = [a for a in activities if a["type"] == activity_type]
        
        # Sort by timestamp (newest first)
        activities = sorted(activities, key=lambda a: a["timestamp"], reverse=True)
        
        return activities[:limit]

    def get_services(self, service_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get service logs.

        Args:
            service_type: Filter by service type (optional)
            limit: Maximum number of services to return

        Returns:
            A list of service logs
        """
        services = self.ledger["service_logs"]
        
        if service_type:
            services = [s for s in services if s["type"] == service_type]
        
        # Sort by timestamp (newest first)
        services = sorted(services, key=lambda s: s["timestamp"], reverse=True)
        
        return services[:limit]

    def get_pending_transactions(self, tx_type: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get pending transactions.

        Args:
            tx_type: Filter by transaction type (optional)
            status: Filter by transaction status (optional)

        Returns:
            A list of pending transactions
        """
        txs = self.ledger["pending_transactions"]
        
        if tx_type:
            txs = [tx for tx in txs if tx["type"] == tx_type]
        
        if status:
            txs = [tx for tx in txs if tx["status"] == status]
        
        # Sort by timestamp (newest first)
        txs = sorted(txs, key=lambda tx: tx["timestamp"], reverse=True)
        
        return txs

    def get_votes(self, scroll_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get governance votes.

        Args:
            scroll_id: Filter by Voice Scroll ID (optional)

        Returns:
            A list of governance votes
        """
        votes = self.ledger["governance_votes"]
        
        if scroll_id:
            votes = [v for v in votes if v["scroll_id"] == scroll_id]
        
        # Sort by timestamp (newest first)
        votes = sorted(votes, key=lambda v: v["timestamp"], reverse=True)
        
        return votes

# Create a global instance of the ActivityLedger
activity_ledger = ActivityLedger()
