#!/usr/bin/env python3
"""
ONNYX Phase 1 Production Launch - Direct Genesis Identity Creation
Creates the Genesis Identity directly in the database for <PERSON><PERSON><PERSON><PERSON>.
"""

import sqlite3
import json
import time
import uuid
from datetime import datetime

def create_genesis_identity_direct():
    """Create Genesis Identity directly in the database."""
    print("🌟 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("=" * 70)
    print("DIRECT GENESIS IDENTITY CREATION")
    print("Platform Founder: <PERSON><PERSON><PERSON><PERSON>")
    print("Method: Direct Database Creation")
    print("Date:", datetime.now().strftime("%B %d, %Y at %I:%M %p"))
    print("=" * 70)
    
    # Generate cryptographic keys
    print("🔐 Generating cryptographic keys...")
    try:
        from blockchain.wallet.wallet import Wallet
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()
        print("✅ Cryptographic keys generated successfully")
    except Exception as e:
        print(f"❌ Error generating keys: {e}")
        return False
    
    # Genesis Identity data
    identity_id = str(uuid.uuid4())
    current_time = int(time.time())
    
    genesis_metadata = {
        "role": "Platform Founder",
        "purpose": "Establishing a trusted business network for secure commerce and decentralized validation through quantum-resistant cryptographic identities and blockchain technology. Creating the foundational infrastructure for the ONNYX ecosystem to enable transparent, secure, and efficient business interactions.",
        "organization": "ONNYX Foundation",
        "genesis_identity": True,
        "platform_founder": True,
        "registration_type": "genesis",
        "verified": True,
        "admin_privileges": True,
        "registration_timestamp": current_time,
        "genesis_block_creator": True,
        "production_launch": True,
        "launch_date": datetime.now().isoformat()
    }
    
    # Connect to database
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Insert Genesis Identity
        print("📝 Creating Genesis Identity record...")
        cursor.execute("""
            INSERT INTO identities (identity_id, name, email, public_key, metadata, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            "Djuvane Martin",
            "<EMAIL>",
            public_key,
            json.dumps(genesis_metadata),
            current_time,
            current_time
        ))
        
        # Create Genesis Block #0
        print("⛓️ Creating Genesis Block #0...")
        genesis_block_data = {
            "genesis_identity_id": identity_id,
            "founder_name": "Djuvane Martin",
            "founder_email": "<EMAIL>",
            "platform_purpose": genesis_metadata["purpose"],
            "organization": "ONNYX Foundation",
            "created_at": current_time,
            "block_number": 0,
            "genesis_signature": "GENESIS_BLOCK_SIGNATURE",
            "production_launch": True,
            "launch_timestamp": current_time
        }
        
        cursor.execute("""
            INSERT INTO blocks (block_hash, block_number, previous_hash, data, timestamp, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            "GENESIS_BLOCK_HASH_PRODUCTION",
            0,
            "0000000000000000000000000000000000000000000000000000000000000000",
            json.dumps(genesis_block_data),
            current_time,
            current_time
        ))
        
        # Initialize mining record for Platform Founder
        print("⛏️ Initializing mining system...")
        mining_id = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO mining (mining_id, identity_id, mining_tier, mining_power, blocks_mined, total_rewards, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            mining_id,
            identity_id,
            "ONNYX Pro",  # Platform Founder gets highest tier
            10.0,  # 10x mining power
            1,  # Genesis Block counts as first mined block
            100.0,  # Genesis reward
            current_time
        ))
        
        conn.commit()
        conn.close()
        
        print("✅ Genesis Identity created successfully in database")
        print()
        
        # Display Genesis Identity details
        print("📊 OFFICIAL GENESIS IDENTITY CREATED:")
        print(f"   Identity ID: {identity_id}")
        print(f"   Name: Djuvane Martin")
        print(f"   Email: <EMAIL>")
        print(f"   Organization: ONNYX Foundation")
        print(f"   Role: Platform Founder")
        print(f"   Genesis Block: #0")
        print(f"   Mining Tier: ONNYX Pro (10x power)")
        print(f"   Created: {datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Save private key to file
        key_filename = f"credentials/djuvane_martin_genesis_credentials.txt"
        import os
        os.makedirs("credentials", exist_ok=True)
        
        with open(key_filename, "w") as f:
            f.write("ONNYX GENESIS IDENTITY - PLATFORM FOUNDER CREDENTIALS\n")
            f.write("=" * 60 + "\n")
            f.write(f"Name: Djuvane Martin\n")
            f.write(f"Email: <EMAIL>\n")
            f.write(f"Organization: ONNYX Foundation\n")
            f.write(f"Role: Platform Founder\n")
            f.write(f"Identity ID: {identity_id}\n")
            f.write(f"Created: {datetime.fromtimestamp(current_time).isoformat()}\n")
            f.write("\n")
            f.write("CRYPTOGRAPHIC KEYS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Public Key: {public_key}\n")
            f.write(f"Private Key: {private_key}\n")
            f.write("\n")
            f.write("CRITICAL SECURITY WARNING:\n")
            f.write("This private key controls the entire ONNYX platform.\n")
            f.write("Store it securely in multiple locations.\n")
            f.write("This key cannot be recovered if lost.\n")
            f.write("\n")
            f.write("Genesis Block Data:\n")
            f.write(json.dumps(genesis_block_data, indent=2))
        
        print(f"🔐 Private key saved to: {key_filename}")
        print("⚠️  CRITICAL: Secure this file immediately!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating Genesis Identity: {e}")
        return False

def verify_genesis_creation():
    """Verify Genesis Identity was created successfully."""
    print("\n🔍 Verifying Genesis Identity Creation")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Check for Genesis Identity
        cursor.execute("SELECT * FROM identities WHERE metadata LIKE '%Platform Founder%'")
        genesis_identity = cursor.fetchone()
        
        if genesis_identity:
            print("✅ Genesis Identity found in database")
            print(f"   Name: {genesis_identity[1]}")
            print(f"   Email: {genesis_identity[2]}")
            
            # Check for Genesis Block
            cursor.execute("SELECT * FROM blocks WHERE block_number = 0")
            genesis_block = cursor.fetchone()
            
            if genesis_block:
                print("✅ Genesis Block #0 found")
                print(f"   Block Hash: {genesis_block[0]}")
                
                # Check mining record
                cursor.execute("SELECT * FROM mining WHERE identity_id = ?", (genesis_identity[0],))
                mining_record = cursor.fetchone()
                
                if mining_record:
                    print("✅ Mining system initialized")
                    print(f"   Mining Tier: {mining_record[3]}")
                    print(f"   Mining Power: {mining_record[4]}x")
                    
                    conn.close()
                    return True
                else:
                    print("⚠️ Mining record not found")
            else:
                print("❌ Genesis Block not found")
        else:
            print("❌ Genesis Identity not found")
        
        conn.close()
        return False
        
    except Exception as e:
        print(f"❌ Error verifying Genesis creation: {e}")
        return False

def main():
    """Execute direct Genesis Identity creation."""
    print("🚀 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("🌟 DIRECT GENESIS IDENTITY CREATION")
    print()
    
    # Create Genesis Identity
    genesis_success = create_genesis_identity_direct()
    
    if genesis_success:
        # Verify creation
        verification_success = verify_genesis_creation()
        
        if verification_success:
            print("\n🎉 GENESIS IDENTITY CREATION - MISSION ACCOMPLISHED!")
            print("✨ Djuvane Martin is now the official Platform Founder")
            print("⛓️ ONNYX blockchain network is officially launched")
            print("🏢 Ready for business validator registration")
            print()
            print("🚀 PHASE 1 PRODUCTION LAUNCH STATUS:")
            print("   ✅ System Reset Complete")
            print("   ✅ Genesis Identity Created")
            print("   ✅ Genesis Block #0 Established")
            print("   ✅ Mining System Initialized")
            print("   ✅ Platform Founder Privileges Assigned")
            print()
            print("📋 NEXT: Register Business Validators")
            print("   1. ONNYX (Platform company)")
            print("   2. Epinnox (Fintech)")
            print("   3. Investment Club")
            print("   4. Sheryl Williams Hair Replacement")
            print("   5. GetTwisted Hair Studios")
            print()
            print("🌟 ONNYX TRUSTED BUSINESS NETWORK FOUNDATION ESTABLISHED!")
            
            return True
        else:
            print("\n⚠️ Genesis Identity creation needs verification")
            return False
    else:
        print("\n❌ Genesis Identity creation failed")
        return False

if __name__ == "__main__":
    main()
