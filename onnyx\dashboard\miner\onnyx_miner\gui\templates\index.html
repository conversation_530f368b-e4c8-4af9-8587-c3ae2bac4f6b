{% extends "base.html" %}

{% block content %}
<div class="row">
  <div class="col-md-6">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Onnyx Miner Dashboard</h5>
      </div>
      <div class="card-body">
        <h6>Identity</h6>
        <p>
          <strong>ID:</strong> {{ identity_id }}<br>
          <strong>Name:</strong> {{ identity_name }}
        </p>

        <h6>Sela</h6>
        <p>
          <strong>ID:</strong> {{ sela_id }}<br>
          <strong>Name:</strong> {{ sela_name }}
        </p>

        <h6>Chain Status</h6>
        <p>
          <strong>Height:</strong> {{ chain_info.chain_height }}<br>
          <strong>Last Proposer:</strong> {{ chain_info.last_proposer }}<br>
          <strong>Sync Status:</strong> <span class="badge bg-{{ 'success' if chain_info.sync_status == 'synced' else 'warning' }}">{{ chain_info.sync_status }}</span>
        </p>

        <div class="d-grid gap-2">
          <form action="{{ url_for('sync') }}" method="post">
            <button type="submit" class="btn btn-primary btn-block">Sync Chain</button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Mining Status</h5>
      </div>
      <div class="card-body">
        <p>
          <strong>Valid Proposer:</strong> <span class="badge bg-{{ 'success' if mining_status.is_valid_proposer else 'secondary' }}">{{ 'Yes' if mining_status.is_valid_proposer else 'No' }}</span><br>
          <strong>Next Validator:</strong> {{ mining_status.next_validator }}<br>
          <strong>Auto Mining:</strong> <span class="badge bg-{{ 'success' if mining_status.auto_mining else 'secondary' }}">{{ 'Enabled' if mining_status.auto_mining else 'Disabled' }}</span>
        </p>

        <div class="d-grid gap-2">
          <form action="{{ url_for('mine') }}" method="post">
            <button type="submit" class="btn btn-success btn-block mb-2" {{ 'disabled' if not mining_status.is_valid_proposer }}>
              Mine Block
            </button>
          </form>

          <form action="{{ url_for('auto_mine') }}" method="post">
            {% if mining_status.auto_mining %}
              <input type="hidden" name="enable" value="false">
              <button type="submit" class="btn btn-danger btn-block">Disable Auto Mining</button>
            {% else %}
              <input type="hidden" name="enable" value="true">
              <button type="submit" class="btn btn-primary btn-block">Enable Auto Mining</button>
            {% endif %}
          </form>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <h5 class="card-title">Quick Links</h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ url_for('wallet') }}" class="btn btn-outline-primary">Wallet</a>
          <a href="{{ url_for('governance') }}" class="btn btn-outline-primary">Governance</a>
          <a href="{{ url_for('sela') }}" class="btn btn-outline-primary">Sela Info</a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Auto-refresh the page every 30 seconds
  setTimeout(function() {
    location.reload();
  }, 30000);
</script>
{% endblock %}
