# Etzem: Trust Score System

Etzem (עֶצֶם) is the core trust score system, the "bone" or essence of a person's on-chain integrity.

## Overview

Etzem is how the Onnyx chain knows who is trustworthy, who contributes, and who should lead. It combines multiple factors to determine an identity's trustworthiness, contribution, and leadership potential.

Factors include:
- **Zeman Hours** (labor contributed)
- **Reputation Badges**
- **Sela Registration** (operating a verified business)
- **Token Activity** (not just holding — movement, forks, use)
- **Votes Participated In**
- **Governance Proposals Submitted**
- **On-chain age/longevity**

## Components

### EtzemScorer

The `EtzemScorer` class calculates trust scores for identities. It provides methods for:

- Calculating Etzem scores
- Getting top identities by score
- Getting and setting weights for different factors

## Score Calculation

The Etzem score is calculated using the following formula:

```
score = zeman_score + badge_score + sela_score + onx_score + token_activity_score + token_creation_score + age_score + governance_score
```

Where:

- **zeman_score**: <PERSON>eman hours × 2
- **badge_score**: Each badge × 5
- **sela_score**: Having a Sela = 50 points
- **onx_score**: ONX balance × 0.05 (max 50 points)
- **token_activity_score**: Each token transaction × 2
- **token_creation_score**: Each token created × 10
- **age_score**: Each day on-chain × 0.01
- **governance_score**: Each governance action × 5

## Levels

Etzem scores are categorized into levels:

- **Diamond**: 500+ points
- **Platinum**: 300-499 points
- **Gold**: 200-299 points
- **Silver**: 100-199 points
- **Bronze**: 50-99 points
- **Novice**: 0-49 points

## API Endpoints

### Get Etzem Score

```
GET /etzem/score?identity_id=string
```

### Get Top Identities

```
GET /etzem/top?limit=10
```

### Get Weights

```
GET /etzem/weights
```

### Set Weight

```
POST /etzem/set_weight
```

Request body:
```json
{
  "factor": "string",
  "weight": 0,
  "admin_key": "string"
}
```

### Get Levels

```
GET /etzem/levels
```

### Get Factors

```
GET /etzem/factors
```

### Get Score Explanation

```
GET /etzem/score_explanation?identity_id=string
```

## Example Output

```json
{
  "identity_id": "1a2b3c...",
  "name": "Example Identity",
  "score": 145,
  "details": {
    "zeman": {
      "hours": 20,
      "score": 40,
      "weight": 2.0
    },
    "badges": {
      "badges": [
        {
          "name": "trusted",
          "value": 1,
          "score": 5
        },
        {
          "name": "verified",
          "value": 1,
          "score": 5
        },
        {
          "name": "developer",
          "value": 1,
          "score": 5
        }
      ],
      "score": 15,
      "weight": 5.0
    },
    "sela": {
      "has_sela": true,
      "score": 50,
      "weight": 50.0
    },
    "onx_balance": {
      "balance": 400,
      "score": 20,
      "weight": 0.05,
      "max": 50
    },
    "token_activity": {
      "transactions": 5,
      "score": 10,
      "weight": 2.0
    },
    "token_creation": {
      "tokens": 1,
      "score": 10,
      "weight": 10.0
    },
    "age": {
      "days": 30,
      "score": 0.3,
      "weight": 0.01
    },
    "governance": {
      "actions": 0,
      "score": 0,
      "weight": 5.0
    }
  },
  "level": "Silver",
  "percentile": 85
}
```

## Integration with Other Systems

The Etzem score is integrated with other systems in the Onnyx blockchain:

### Yovel Token Mint Limiter

The Etzem score is used by the Yovel token mint limiter to determine how many tokens an identity can mint.

### Sela Business Registry

The Etzem score is used by the Sela business registry to determine the stake required for registering a business.

### Governance

The Etzem score is used by the governance system to determine voting power and proposal rights.

## Future Enhancements

- **Dynamic Weights**: Adjust weights based on network activity
- **Governance**: Allow governance to adjust weights
- **Machine Learning**: Use machine learning to detect patterns of trustworthy behavior
- **Reputation Decay**: Implement reputation decay to encourage continued contribution
- **Integration with External Systems**: Integrate with external reputation systems
