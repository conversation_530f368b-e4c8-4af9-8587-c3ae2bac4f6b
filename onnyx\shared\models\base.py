"""
Onnyx Base Model

This module provides the base model class for Onnyx database models.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union, Type, TypeVar, ClassVar

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.base")

# Type variable for the model class
T = TypeVar('T', bound='BaseModel')

class BaseModel:
    """
    Base model class for Onnyx database models.
    """

    # Table name (to be overridden by subclasses)
    table_name: ClassVar[str] = ""

    # Primary key column (to be overridden by subclasses)
    primary_key: ClassVar[str] = ""

    # JSON fields (to be overridden by subclasses)
    json_fields: ClassVar[List[str]] = []

    def __init__(self, **kwargs):
        """
        Initialize the model with the given attributes.

        Args:
            **kwargs: Model attributes
        """
        for key, value in kwargs.items():
            setattr(self, key, value)

    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """
        Create a model instance from a dictionary.

        Args:
            data: Dictionary containing model attributes

        Returns:
            A model instance
        """
        logger = logging.getLogger(f"onnyx.models.{cls.__name__.lower()}")
        logger.info(f"Creating {cls.__name__} instance from dictionary")
        logger.info(f"Data: {data}")

        try:
            # Parse JSON fields
            for field in cls.json_fields:
                if field in data and isinstance(data[field], str):
                    try:
                        data[field] = json.loads(data[field])
                        logger.info(f"Parsed JSON field {field}")
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse JSON field {field}: {data[field]}")

            # Create the instance
            instance = cls(**data)

            # Log the primary key
            if hasattr(instance, cls.primary_key):
                logger.info(f"Created {cls.__name__} instance with {cls.primary_key}={getattr(instance, cls.primary_key)}")
            else:
                logger.warning(f"Created {cls.__name__} instance without {cls.primary_key}")

            return instance
        except Exception as e:
            logger.error(f"Error creating {cls.__name__} instance from dictionary: {str(e)}")
            logger.error(f"Data: {data}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.

        Returns:
            A dictionary containing model attributes
        """
        data = {}

        for key, value in self.__dict__.items():
            # Skip private attributes
            if key.startswith('_'):
                continue

            # Convert JSON fields to strings
            if key in self.json_fields and value is not None:
                data[key] = json.dumps(value)
            else:
                data[key] = value

        return data

    @classmethod
    def get_by_id(cls: Type[T], id_value: Any) -> Optional[T]:
        """
        Get a model instance by ID.

        Args:
            id_value: The ID value

        Returns:
            A model instance or None if not found
        """
        query = f"SELECT * FROM {cls.table_name} WHERE {cls.primary_key} = ?"
        try:
            row = db.query_one(query, (id_value,))

            if row:
                return cls.from_dict(row)
        except Exception as e:
            logger = logging.getLogger(f"onnyx.models.{cls.__name__.lower()}")
            logger.error(f"Error getting {cls.__name__} by ID {id_value}: {str(e)}")

        return None

    @classmethod
    def get_all(cls: Type[T]) -> List[T]:
        """
        Get all model instances.

        Returns:
            A list of model instances
        """
        logger = logging.getLogger(f"onnyx.models.{cls.__name__.lower()}")
        logger.info(f"Getting all {cls.__name__} instances from table {cls.table_name}")

        try:
            query = f"SELECT * FROM {cls.table_name}"
            rows = db.query(query)

            logger.info(f"Found {len(rows)} {cls.__name__} instances in the database")

            # Create model instances from the rows
            instances = []
            for row in rows:
                try:
                    instance = cls.from_dict(row)
                    instances.append(instance)
                    logger.info(f"Created {cls.__name__} instance: {getattr(instance, cls.primary_key)}")
                except Exception as e:
                    logger.error(f"Error creating {cls.__name__} instance from row: {str(e)}")
                    logger.error(f"Row data: {row}")
                    import traceback
                    logger.error(traceback.format_exc())

            logger.info(f"Returning {len(instances)} {cls.__name__} instances")
            return instances
        except Exception as e:
            logger.error(f"Error getting all {cls.__name__} instances: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    @classmethod
    def filter(cls: Type[T], where: str, params: tuple = ()) -> List[T]:
        """
        Filter model instances.

        Args:
            where: The WHERE clause
            params: The WHERE clause parameters

        Returns:
            A list of model instances
        """
        query = f"SELECT * FROM {cls.table_name} WHERE {where}"
        rows = db.query(query, params)

        return [cls.from_dict(row) for row in rows]

    @classmethod
    def count(cls, where: str = "", params: tuple = ()) -> int:
        """
        Count model instances.

        Args:
            where: The WHERE clause
            params: The WHERE clause parameters

        Returns:
            The count
        """
        query = f"SELECT COUNT(*) as count FROM {cls.table_name}"

        if where:
            query += f" WHERE {where}"

        row = db.query_one(query, params)

        return row["count"] if row else 0

    def save(self) -> None:
        """
        Save the model instance.

        If the primary key is set, update the instance.
        Otherwise, insert a new instance.
        """
        data = self.to_dict()

        # Check if the primary key is set
        if hasattr(self, self.primary_key) and getattr(self, self.primary_key):
            # Update the instance
            primary_key_value = getattr(self, self.primary_key)

            # Remove the primary key from the data
            if self.primary_key in data:
                del data[self.primary_key]

            try:
                # Update the instance
                db.update(
                    self.table_name,
                    data,
                    f"{self.primary_key} = ?",
                    (primary_key_value,)
                )
            except Exception as e:
                logger = logging.getLogger(f"onnyx.models.{self.__class__.__name__.lower()}")
                logger.error(f"Error updating {self.__class__.__name__}: {str(e)}")
                raise
        else:
            try:
                # Insert a new instance
                row_id = db.insert(self.table_name, data)

                # Set the primary key if it's an auto-increment field
                if self.primary_key == "id":
                    setattr(self, self.primary_key, row_id)
            except Exception as e:
                logger = logging.getLogger(f"onnyx.models.{self.__class__.__name__.lower()}")
                logger.error(f"Error inserting {self.__class__.__name__}: {str(e)}")
                raise

    def delete(self) -> None:
        """
        Delete the model instance.
        """
        if not hasattr(self, self.primary_key) or not getattr(self, self.primary_key):
            raise ValueError(f"Cannot delete model instance without {self.primary_key}")

        primary_key_value = getattr(self, self.primary_key)

        try:
            db.delete(
                self.table_name,
                f"{self.primary_key} = ?",
                (primary_key_value,)
            )
        except Exception as e:
            logger = logging.getLogger(f"onnyx.models.{self.__class__.__name__.lower()}")
            logger.error(f"Error deleting {self.__class__.__name__}: {str(e)}")
            raise

    @classmethod
    def delete_by_id(cls, id_value: Any) -> None:
        """
        Delete a model instance by ID.

        Args:
            id_value: The ID value
        """
        try:
            db.delete(
                cls.table_name,
                f"{cls.primary_key} = ?",
                (id_value,)
            )
        except Exception as e:
            logger = logging.getLogger(f"onnyx.models.{cls.__name__.lower()}")
            logger.error(f"Error deleting {cls.__name__} by ID {id_value}: {str(e)}")
            raise

    @classmethod
    def delete_where(cls, where: str, params: tuple = ()) -> None:
        """
        Delete model instances matching a condition.

        Args:
            where: The WHERE clause
            params: The WHERE clause parameters
        """
        try:
            db.delete(
                cls.table_name,
                where,
                params
            )
        except Exception as e:
            logger = logging.getLogger(f"onnyx.models.{cls.__name__.lower()}")
            logger.error(f"Error deleting {cls.__name__} where {where}: {str(e)}")
            raise
