#!/usr/bin/env python3
"""
Test ONNYX Validator Dashboard Data
Verifies that the web dashboard displays correct real-time data.
"""

import sqlite3
import json
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def test_validator_query():
    """Test the validator query used in the web dashboard."""
    print("🔍 TESTING VALIDATOR DASHBOARD QUERY")
    print("=" * 50)

    try:
        # This is the same query used in the sela.py route
        validators = db.query("""
            SELECT s.*, i.name as owner_name, i.email as owner_email,
                   m.mining_tier, m.mining_power, m.mining_id,
                   COALESCE(t.balance, 0) as onx_balance,
                   COALESCE(mining_stats.blocks_mined, 0) as blocks_mined,
                   COALESCE(mining_stats.total_rewards, 0) as total_rewards
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            LEFT JOIN mining m ON s.sela_id = m.sela_id
            LEFT JOIN tokens t ON i.identity_id = t.identity_id AND t.token_id = 'ONX'
            LEFT JOIN (
                SELECT JSON_EXTRACT(data, '$.signed_by') as sela_id,
                       COUNT(*) as blocks_mined,
                       COUNT(*) * 10 as total_rewards
                FROM blocks
                WHERE JSON_EXTRACT(data, '$.signed_by') IS NOT NULL
                GROUP BY JSON_EXTRACT(data, '$.signed_by')
            ) mining_stats ON s.sela_id = mining_stats.sela_id
            WHERE s.status = 'active'
            ORDER BY m.mining_power DESC, s.created_at DESC
        """)

        print(f"✅ Query executed successfully")
        print(f"📊 Found {len(validators)} validators")

        if validators:
            print(f"\n📋 VALIDATOR DETAILS:")
            print("-" * 80)

            for i, validator in enumerate(validators, 1):
                print(f"\n{i}. {validator['name']}")
                print(f"   Owner: {validator['owner_name']} ({validator['owner_email']})")
                print(f"   Category: {validator['category']}")
                print(f"   Status: {validator['status']}")
                print(f"   Mining Tier: {validator.get('mining_tier', 'N/A')}")
                print(f"   Mining Power: {validator.get('mining_power', 'N/A')}x")
                print(f"   ONX Balance: {validator.get('onx_balance', 0)} ONX")
                print(f"   Blocks Mined: {validator.get('blocks_mined', 0)}")
                print(f"   Total Rewards: {validator.get('total_rewards', 0)} ONX")
                print(f"   Sela ID: {validator['sela_id']}")
                print(f"   Identity ID: {validator['identity_id']}")
        else:
            print("⚠️ No validators found")

        return validators

    except Exception as e:
        print(f"❌ Query failed: {e}")
        return []

def test_network_statistics():
    """Test the network statistics query."""
    print(f"\n🌐 TESTING NETWORK STATISTICS")
    print("=" * 40)

    try:
        # Test individual queries
        total_blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
        print(f"✅ Total Blocks: {total_blocks}")

        try:
            mempool_count = db.query_one("SELECT COUNT(*) as count FROM mempool")['count']
            print(f"✅ Mempool Transactions: {mempool_count}")
        except:
            mempool_count = 0
            print(f"⚠️ Mempool table not accessible: {mempool_count}")

        try:
            confirmed_count = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
            print(f"✅ Confirmed Transactions: {confirmed_count}")
        except:
            confirmed_count = 0
            print(f"⚠️ Transactions table not accessible: {confirmed_count}")

        total_transactions = mempool_count + confirmed_count
        print(f"📊 Total Transactions: {total_transactions}")

        # Active validators
        active_validators = db.query_one("""
            SELECT COUNT(*) as count
            FROM selas s
            JOIN mining m ON s.sela_id = m.sela_id
            WHERE s.status = 'active'
        """)['count']
        print(f"✅ Active Validators: {active_validators}")

        # Total mining power
        total_mining_power = db.query_one("""
            SELECT COALESCE(SUM(m.mining_power), 0) as total_power
            FROM selas s
            JOIN mining m ON s.sela_id = m.sela_id
            WHERE s.status = 'active'
        """)['total_power'] or 0
        print(f"✅ Total Mining Power: {total_mining_power}x")

        # Total rewards
        total_rewards = db.query_one("""
            SELECT COALESCE(SUM(balance), 0) as total_rewards
            FROM tokens
            WHERE token_id = 'ONX'
        """)['total_rewards'] or 0
        print(f"✅ Total Rewards Distributed: {total_rewards} ONX")

        return {
            'total_blocks': total_blocks,
            'total_transactions': total_transactions,
            'active_validators': active_validators,
            'total_mining_power': total_mining_power,
            'total_rewards': total_rewards,
            'mempool_count': mempool_count,
            'confirmed_count': confirmed_count
        }

    except Exception as e:
        print(f"❌ Network statistics failed: {e}")
        return {}

def test_individual_tables():
    """Test individual table access."""
    print(f"\n🗄️ TESTING INDIVIDUAL TABLES")
    print("=" * 35)

    tables_to_test = ['identities', 'selas', 'mining', 'tokens', 'blocks', 'mempool', 'transactions']

    for table in tables_to_test:
        try:
            result = db.query_one(f"SELECT COUNT(*) as count FROM {table}")
            count = result['count'] if result else 0
            print(f"✅ {table}: {count} records")
        except Exception as e:
            print(f"❌ {table}: Error - {e}")

def verify_expected_validators():
    """Verify that the expected production validators are present."""
    print(f"\n🏢 VERIFYING EXPECTED PRODUCTION VALIDATORS")
    print("=" * 50)

    expected_validators = [
        ("ONNYX", "Djuvane Martin", "<EMAIL>"),
        ("Sheryl Williams Hair Replacement", "Sheryl Williams", "<EMAIL>"),
        ("GetTwisted Hair Studios", "Michael Williams", "<EMAIL>")
    ]

    found_validators = []

    for business_name, owner_name, email in expected_validators:
        try:
            validator = db.query_one("""
                SELECT s.name, i.name as owner_name, i.email, s.status, m.mining_tier, m.mining_power
                FROM selas s
                JOIN identities i ON s.identity_id = i.identity_id
                LEFT JOIN mining m ON s.sela_id = m.sela_id
                WHERE s.name = ? OR i.name = ? OR i.email = ?
            """, (business_name, owner_name, email))

            if validator:
                print(f"✅ Found: {validator['name']}")
                print(f"   Owner: {validator['owner_name']} ({validator['email']})")
                print(f"   Status: {validator['status']}")
                print(f"   Mining Tier: {validator.get('mining_tier', 'N/A')}")
                print(f"   Mining Power: {validator.get('mining_power', 'N/A')}x")
                found_validators.append(validator)
            else:
                print(f"❌ Missing: {business_name} ({owner_name})")

        except Exception as e:
            print(f"❌ Error checking {business_name}: {e}")

    print(f"\n📊 SUMMARY: Found {len(found_validators)}/3 expected validators")
    return found_validators

def main():
    """Run comprehensive validator dashboard tests."""
    print("🚀 ONNYX VALIDATOR DASHBOARD DATA TEST")
    print("=" * 60)
    print("Testing real-time data queries for the web dashboard")

    # Test individual tables
    test_individual_tables()

    # Test network statistics
    network_stats = test_network_statistics()

    # Test validator query
    validators = test_validator_query()

    # Verify expected validators
    expected_validators = verify_expected_validators()

    # Final assessment
    print(f"\n🎯 DASHBOARD READINESS ASSESSMENT")
    print("=" * 40)

    validators_ready = len(validators) >= 3
    stats_ready = network_stats.get('total_blocks', 0) > 0
    expected_ready = len(expected_validators) == 3

    print(f"{'✅' if validators_ready else '❌'} Validator Query: {'READY' if validators_ready else 'NEEDS ATTENTION'}")
    print(f"{'✅' if stats_ready else '❌'} Network Statistics: {'READY' if stats_ready else 'NEEDS ATTENTION'}")
    print(f"{'✅' if expected_ready else '❌'} Expected Validators: {'READY' if expected_ready else 'NEEDS ATTENTION'}")

    overall_ready = validators_ready and stats_ready and expected_ready

    print(f"\n🏆 OVERALL STATUS: {'✅ DASHBOARD READY' if overall_ready else '⚠️ NEEDS ATTENTION'}")

    if overall_ready:
        print("🎉 The validator dashboard should display real-time data correctly!")
        print("🌐 Web interface at http://127.0.0.1:5000/validators")
        print("📊 All 3 production validators should be visible with mining data")
    else:
        print("⚠️ Some issues need to be resolved before dashboard is fully functional")

    return overall_ready

if __name__ == "__main__":
    main()
