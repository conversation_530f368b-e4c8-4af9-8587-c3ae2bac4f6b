-- Onnyx Initial Database Schema

-- Blockchain table
CREATE TABLE IF NOT EXISTS blocks (
    block_hash TEXT PRIMARY KEY,
    block_height INTEGER NOT NULL,
    previous_hash TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    difficulty INTEGER NOT NULL,
    nonce INTEGER NOT NULL,
    miner TEXT NOT NULL,
    transactions TEXT NOT NULL,  -- JSON array of transaction IDs
    merkle_root TEXT NOT NULL,
    size INTEGER NOT NULL,
    version TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Create index on block height
CREATE INDEX IF NOT EXISTS idx_blocks_height ON blocks(block_height);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    tx_id TEXT PRIMARY KEY,
    block_hash TEXT,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL,  -- <PERSON><PERSON><PERSON> object with transaction data
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    status TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    FOREIGN KEY (block_hash) REFERENCES blocks(block_hash)
);

-- Create index on transaction sender
CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender);

-- Mempool table
CREATE TABLE IF NOT EXISTS mempool (
    tx_id TEXT PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL,  -- JSON object with transaction data
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Identities table
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    public_key TEXT NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with identity metadata
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Create index on identity name
CREATE INDEX IF NOT EXISTS idx_identities_name ON identities(name);

-- Reputation table
CREATE TABLE IF NOT EXISTS reputation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    reputation_type TEXT NOT NULL,
    value INTEGER NOT NULL,
    issuer_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (issuer_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id)
);

-- Create index on reputation identity
CREATE INDEX IF NOT EXISTS idx_reputation_identity ON reputation(identity_id);

-- Badges table
CREATE TABLE IF NOT EXISTS badges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    badge_type TEXT NOT NULL,
    issuer_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (issuer_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id)
);

-- Create index on badges identity
CREATE INDEX IF NOT EXISTS idx_badges_identity ON badges(identity_id);

-- Tokens table
CREATE TABLE IF NOT EXISTS tokens (
    token_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    creator_id TEXT NOT NULL,
    supply INTEGER NOT NULL,
    category TEXT NOT NULL,
    decimals INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with token metadata
    FOREIGN KEY (creator_id) REFERENCES identities(identity_id)
);

-- Create index on token creator
CREATE INDEX IF NOT EXISTS idx_tokens_creator ON tokens(creator_id);

-- Token balances table
CREATE TABLE IF NOT EXISTS token_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    balance INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (token_id) REFERENCES tokens(token_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    UNIQUE(token_id, identity_id)
);

-- Create index on token balances
CREATE INDEX IF NOT EXISTS idx_token_balances_token ON token_balances(token_id);
CREATE INDEX IF NOT EXISTS idx_token_balances_identity ON token_balances(identity_id);

-- Token transactions table
CREATE TABLE IF NOT EXISTS token_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tx_id TEXT NOT NULL,
    token_id TEXT NOT NULL,
    from_id TEXT,
    to_id TEXT,
    amount INTEGER NOT NULL,
    operation TEXT NOT NULL,  -- MINT, SEND, BURN
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id),
    FOREIGN KEY (token_id) REFERENCES tokens(token_id),
    FOREIGN KEY (from_id) REFERENCES identities(identity_id),
    FOREIGN KEY (to_id) REFERENCES identities(identity_id)
);

-- Create index on token transactions
CREATE INDEX IF NOT EXISTS idx_token_transactions_token ON token_transactions(token_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_from ON token_transactions(from_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_to ON token_transactions(to_id);

-- Sela business registry table
CREATE TABLE IF NOT EXISTS selas (
    sela_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    stake_amount INTEGER NOT NULL,
    stake_token_id TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL,  -- JSON object with Sela metadata
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (stake_token_id) REFERENCES tokens(token_id)
);

-- Create index on Sela identity
CREATE INDEX IF NOT EXISTS idx_selas_identity ON selas(identity_id);

-- Zeman time/work credits table
CREATE TABLE IF NOT EXISTS zeman_credits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    hours REAL NOT NULL,
    description TEXT NOT NULL,
    issuer_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (issuer_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id)
);

-- Create index on Zeman credits identity
CREATE INDEX IF NOT EXISTS idx_zeman_credits_identity ON zeman_credits(identity_id);

-- Etzem trust scores table
CREATE TABLE IF NOT EXISTS etzem_scores (
    identity_id TEXT PRIMARY KEY,
    composite_score INTEGER NOT NULL,
    zeman_score INTEGER NOT NULL,
    reputation_score INTEGER NOT NULL,
    sela_score INTEGER NOT NULL,
    token_activity_score INTEGER NOT NULL,
    governance_score INTEGER NOT NULL,
    longevity_score INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Voice Scrolls (governance proposals) table
CREATE TABLE IF NOT EXISTS voice_scrolls (
    scroll_id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    proposer_id TEXT NOT NULL,
    status TEXT NOT NULL,  -- PROPOSED, VOTING, PASSED, REJECTED, IMPLEMENTED
    proposal_type TEXT NOT NULL,
    params TEXT NOT NULL,  -- JSON object with proposal parameters
    voting_start INTEGER NOT NULL,
    voting_end INTEGER NOT NULL,
    implementation_time INTEGER,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (proposer_id) REFERENCES identities(identity_id)
);

-- Create index on Voice Scrolls status
CREATE INDEX IF NOT EXISTS idx_voice_scrolls_status ON voice_scrolls(status);

-- Voice Scroll votes table
CREATE TABLE IF NOT EXISTS scroll_votes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scroll_id TEXT NOT NULL,
    voter_id TEXT NOT NULL,
    vote TEXT NOT NULL,  -- YES, NO, ABSTAIN
    weight INTEGER NOT NULL,
    timestamp INTEGER NOT NULL,
    tx_id TEXT NOT NULL,
    FOREIGN KEY (scroll_id) REFERENCES voice_scrolls(scroll_id),
    FOREIGN KEY (voter_id) REFERENCES identities(identity_id),
    FOREIGN KEY (tx_id) REFERENCES transactions(tx_id),
    UNIQUE(scroll_id, voter_id)
);

-- Create index on Voice Scroll votes
CREATE INDEX IF NOT EXISTS idx_scroll_votes_scroll ON scroll_votes(scroll_id);

-- Event logs table
CREATE TABLE IF NOT EXISTS event_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    data TEXT NOT NULL,  -- JSON object with event data
    timestamp INTEGER NOT NULL
);

-- Create index on event logs
CREATE INDEX IF NOT EXISTS idx_event_logs_type ON event_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_event_logs_entity ON event_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_event_logs_timestamp ON event_logs(timestamp);

-- Activity ledger table
CREATE TABLE IF NOT EXISTS activity_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    activity_type TEXT NOT NULL,
    data TEXT NOT NULL,  -- JSON object with activity data
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Create index on activity ledger
CREATE INDEX IF NOT EXISTS idx_activity_ledger_identity ON activity_ledger(identity_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_timestamp ON activity_ledger(timestamp);

-- Validator rotation registry table
CREATE TABLE IF NOT EXISTS rotation_registry (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sela_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    etzem_score INTEGER NOT NULL,
    eligible BOOLEAN NOT NULL,
    last_block_mined INTEGER,
    next_rotation_time INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (sela_id) REFERENCES selas(sela_id),
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Create index on rotation registry
CREATE INDEX IF NOT EXISTS idx_rotation_registry_eligible ON rotation_registry(eligible);
CREATE INDEX IF NOT EXISTS idx_rotation_registry_next_rotation ON rotation_registry(next_rotation_time);
