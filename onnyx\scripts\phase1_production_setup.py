#!/usr/bin/env python3
"""
ONNYX Phase 1 Production Launch Setup

This script sets up the production database with Genesis Identity and founding validators
for the ONNYX platform Phase 1 launch.

Real Production Identities:
- Platform Founder: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
- Business Validators:
  - GetTwisted Hair Studios (<PERSON>, mi<PERSON><PERSON>@gettwistedhair.com)
  - <PERSON><PERSON> Replacement (<PERSON><PERSON>, <EMAIL>)
"""

import os
import sys
import sqlite3
import json
import time
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from blockchain.wallet.wallet import Wallet
from shared.db.db import db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.phase1_setup")

class Phase1ProductionSetup:
    """Phase 1 Production Launch Setup Manager."""

    def __init__(self):
        """Initialize the setup manager."""
        self.root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        self.data_dir = os.path.join(self.root_dir, 'data')
        self.db_path = os.path.join(self.data_dir, 'onnyx.db')
        self.schema_path = os.path.join(self.root_dir, 'shared', 'schemas', 'production_schema.sql')
        self.wallet = Wallet()

        # Production identities data
        self.genesis_identity = {
            'name': 'Djuvane Martin',
            'email': '<EMAIL>',
            'role': 'Platform Founder',
            'organization': 'ONNYX Foundation'
        }

        self.founding_validators = [
            {
                'owner_name': 'Michael Williams',
                'owner_email': '<EMAIL>',
                'business_name': 'GetTwisted Hair Studios',
                'category': 'services',
                'description': 'Professional hair styling and beauty services',
                'address': '123 Beauty Lane, Hair City, HC 12345',
                'phone': '(*************',
                'website': 'https://gettwistedhair.com',
                'services': 'Hair styling, coloring, treatments, beauty consultations'
            },
            {
                'owner_name': 'Sheryl Williams',
                'owner_email': '<EMAIL>',
                'business_name': 'Sheryl Williams Hair Replacement',
                'category': 'healthcare',
                'description': 'Specialized hair replacement and restoration services',
                'address': '456 Restoration Ave, Hair City, HC 12346',
                'phone': '(*************',
                'website': 'https://sherylwilliamshair.com',
                'services': 'Hair replacement, restoration, medical hair solutions, consultations'
            }
        ]

    def reset_production_database(self) -> None:
        """Reset and initialize the production database."""
        logger.info("🔄 Resetting production database...")

        # Create backup of existing database
        if os.path.exists(self.db_path):
            backup_path = f"{self.db_path}.backup_{int(time.time())}"
            os.rename(self.db_path, backup_path)
            logger.info(f"📦 Backed up existing database to: {backup_path}")

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)

        # Read and execute schema
        with open(self.schema_path, 'r') as f:
            schema = f.read()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.executescript(schema)
            conn.commit()

            # Verify tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [table[0] for table in cursor.fetchall() if table[0] != 'sqlite_sequence']

            logger.info(f"✅ Created {len(tables)} tables: {', '.join(tables)}")

            # Insert default chain parameters
            self._insert_chain_parameters(cursor)
            conn.commit()

        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def _insert_chain_parameters(self, cursor) -> None:
        """Insert default chain parameters."""
        chain_params = [
            ('block_time', '60', '60', 'Target block time in seconds', 'mining'),
            ('difficulty', '1', '1', 'Current mining difficulty', 'mining'),
            ('max_block_size', '1048576', '1048576', 'Maximum block size in bytes', 'blocks'),
            ('genesis_reward', '50', '50', 'Genesis block reward', 'rewards'),
            ('halving_interval', '210000', '210000', 'Block interval for reward halving', 'rewards')
        ]

        for param in chain_params:
            cursor.execute("""
                INSERT OR REPLACE INTO chain_parameters
                (key, value, default_value, description, category, last_updated)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (*param, int(time.time())))

    def create_genesis_identity(self) -> str:
        """Create the Genesis Identity for Platform Founder."""
        logger.info("👑 Creating Genesis Identity...")

        # Generate cryptographic keys
        private_key, public_key = self.wallet.generate_keypair()
        identity_id = str(uuid.uuid4())
        current_time = int(time.time())

        # Genesis metadata
        genesis_metadata = {
            'role': self.genesis_identity['role'],
            'organization': self.genesis_identity['organization'],
            'genesis_identity': True,
            'platform_founder': True,
            'admin_privileges': True,
            'registration_type': 'genesis',
            'verified': True,
            'registration_timestamp': current_time,
            'genesis_block_creator': True,
            'permissions': ['admin', 'validator_approval', 'system_management']
        }

        # Insert Genesis Identity
        identity_data = {
            'identity_id': identity_id,
            'name': self.genesis_identity['name'],
            'email': self.genesis_identity['email'],
            'public_key': public_key,
            'nation_id': 'ONNYX',
            'metadata': json.dumps(genesis_metadata),
            'status': 'active',
            'created_at': current_time,
            'updated_at': current_time
        }

        db.insert('identities', identity_data)

        # Save Genesis credentials securely
        self._save_genesis_credentials(identity_id, private_key, public_key)

        # Create Genesis transaction
        self._create_genesis_transaction(identity_id)

        logger.info(f"✅ Genesis Identity created: {self.genesis_identity['name']}")
        logger.info(f"   Identity ID: {identity_id}")
        logger.info(f"   Email: {self.genesis_identity['email']}")
        logger.info(f"   Role: {self.genesis_identity['role']}")

        return identity_id

    def _save_genesis_credentials(self, identity_id: str, private_key: str, public_key: str) -> None:
        """Save Genesis credentials securely."""
        credentials_dir = os.path.join(self.data_dir, 'credentials')
        os.makedirs(credentials_dir, exist_ok=True)

        credentials = {
            'identity_id': identity_id,
            'name': self.genesis_identity['name'],
            'email': self.genesis_identity['email'],
            'role': self.genesis_identity['role'],
            'organization': self.genesis_identity['organization'],
            'public_key': public_key,
            'private_key': private_key,
            'genesis_identity': True,
            'created_at': datetime.now().isoformat(),
            'warning': 'CRITICAL: This is your Genesis Identity private key. Store it securely!'
        }

        credentials_file = os.path.join(credentials_dir, f'genesis_identity_{identity_id}.json')
        with open(credentials_file, 'w') as f:
            json.dump(credentials, f, indent=2)

        # Set restrictive permissions
        os.chmod(credentials_file, 0o600)

        logger.info(f"🔐 Genesis credentials saved to: {credentials_file}")

    def _create_genesis_transaction(self, identity_id: str) -> None:
        """Create the Genesis transaction."""
        tx_data = {
            'op': 'CREATE_GENESIS_IDENTITY',
            'identity_id': identity_id,
            'name': self.genesis_identity['name'],
            'role': self.genesis_identity['role'],
            'organization': self.genesis_identity['organization']
        }

        transaction_data = {
            'tx_id': str(uuid.uuid4()),
            'block_hash': None,
            'timestamp': int(time.time()),
            'op': 'CREATE_GENESIS_IDENTITY',
            'data': json.dumps(tx_data),
            'sender': identity_id,
            'signature': f'genesis_signature_{int(time.time())}',
            'status': 'confirmed',
            'created_at': int(time.time())
        }

        db.insert('transactions', transaction_data)
        logger.info("📝 Genesis transaction created")

    def create_founding_validators(self, genesis_identity_id: str) -> list:
        """Create the founding business validators."""
        logger.info("🏢 Creating founding business validators...")

        validator_ids = []

        for validator_data in self.founding_validators:
            validator_id = self._create_business_validator(validator_data, genesis_identity_id)
            validator_ids.append(validator_id)

        logger.info(f"✅ Created {len(validator_ids)} founding validators")
        return validator_ids

    def _create_business_validator(self, validator_data: Dict[str, Any], genesis_identity_id: str) -> str:
        """Create a single business validator."""
        logger.info(f"   Creating validator: {validator_data['business_name']}")

        # Generate keys for business owner
        private_key, public_key = self.wallet.generate_keypair()
        owner_identity_id = str(uuid.uuid4())
        sela_id = str(uuid.uuid4())
        current_time = int(time.time())

        # Create owner identity
        owner_metadata = {
            'role': 'Business Owner',
            'business_name': validator_data['business_name'],
            'business_category': validator_data['category'],
            'verified': True,
            'registration_type': 'founding_validator',
            'approved_by': genesis_identity_id,
            'registration_timestamp': current_time
        }

        owner_identity_data = {
            'identity_id': owner_identity_id,
            'name': validator_data['owner_name'],
            'email': validator_data['owner_email'],
            'public_key': public_key,
            'nation_id': 'ONNYX',
            'metadata': json.dumps(owner_metadata),
            'status': 'active',
            'created_at': current_time,
            'updated_at': current_time
        }

        db.insert('identities', owner_identity_data)

        # Create business validator (Sela)
        sela_metadata = {
            'founding_validator': True,
            'approved_by': genesis_identity_id,
            'verification_status': 'verified',
            'business_type': 'service_provider',
            'registration_timestamp': current_time
        }

        sela_data = {
            'sela_id': sela_id,
            'identity_id': owner_identity_id,
            'name': validator_data['business_name'],
            'category': validator_data['category'],
            'description': validator_data['description'],
            'address': validator_data['address'],
            'phone': validator_data['phone'],
            'website': validator_data['website'],
            'services': validator_data['services'],
            'stake_amount': 1000,  # Initial stake
            'stake_token_id': 'ONX',
            'status': 'active',
            'trust_score': 85.0,  # High initial trust for founding validators
            'mining_tier': 'onnyx_optimized',  # Enhanced mining tier
            'mining_power': 3,  # 3x mining power
            'mining_rewards_earned': 0.0,
            'blocks_mined': 0,
            'onx_balance': 1000.0,  # Initial ONX balance
            'created_at': current_time,
            'updated_at': current_time,
            'metadata': json.dumps(sela_metadata)
        }

        db.insert('selas', sela_data)

        # Save validator credentials
        self._save_validator_credentials(owner_identity_id, validator_data, private_key, public_key)

        # Create validator registration transaction
        self._create_validator_transaction(owner_identity_id, sela_id, validator_data)

        logger.info(f"   ✅ Created: {validator_data['business_name']} (Owner: {validator_data['owner_name']})")

        return sela_id

    def _save_validator_credentials(self, identity_id: str, validator_data: Dict[str, Any],
                                  private_key: str, public_key: str) -> None:
        """Save validator credentials securely."""
        credentials_dir = os.path.join(self.data_dir, 'credentials')
        os.makedirs(credentials_dir, exist_ok=True)

        credentials = {
            'identity_id': identity_id,
            'owner_name': validator_data['owner_name'],
            'owner_email': validator_data['owner_email'],
            'business_name': validator_data['business_name'],
            'category': validator_data['category'],
            'public_key': public_key,
            'private_key': private_key,
            'founding_validator': True,
            'created_at': datetime.now().isoformat(),
            'warning': 'SECURE: Store this private key safely. It controls your business identity.'
        }

        safe_name = validator_data['business_name'].replace(' ', '_').replace("'", "").lower()
        credentials_file = os.path.join(credentials_dir, f'validator_{safe_name}_{identity_id}.json')

        with open(credentials_file, 'w') as f:
            json.dump(credentials, f, indent=2)

        os.chmod(credentials_file, 0o600)

        logger.info(f"   🔐 Credentials saved: {credentials_file}")

    def _create_validator_transaction(self, identity_id: str, sela_id: str,
                                    validator_data: Dict[str, Any]) -> None:
        """Create validator registration transaction."""
        tx_data = {
            'op': 'REGISTER_VALIDATOR',
            'identity_id': identity_id,
            'sela_id': sela_id,
            'business_name': validator_data['business_name'],
            'category': validator_data['category'],
            'owner_name': validator_data['owner_name']
        }

        transaction_data = {
            'tx_id': str(uuid.uuid4()),
            'block_hash': None,
            'timestamp': int(time.time()),
            'op': 'REGISTER_VALIDATOR',
            'data': json.dumps(tx_data),
            'sender': identity_id,
            'signature': f'validator_signature_{int(time.time())}',
            'status': 'confirmed',
            'created_at': int(time.time())
        }

        db.insert('transactions', transaction_data)

    def create_initial_onx_tokens(self, genesis_identity_id: str) -> None:
        """Create initial ONX token supply."""
        logger.info("💰 Creating initial ONX token supply...")

        current_time = int(time.time())

        # Create ONX token
        onx_token_data = {
            'token_id': 'ONX',
            'name': 'ONNYX Token',
            'symbol': 'ONX',
            'creator_id': genesis_identity_id,
            'supply': 1000000,  # 1 million initial supply
            'category': 'utility',
            'decimals': 2,
            'created_at': current_time,
            'metadata': json.dumps({
                'description': 'Native utility token of the ONNYX platform',
                'use_cases': ['staking', 'mining', 'governance', 'transaction_fees'],
                'genesis_token': True
            })
        }

        db.insert('tokens', onx_token_data)

        # Create initial token balances
        # Genesis Identity gets 500,000 ONX
        genesis_balance_data = {
            'token_id': 'ONX',
            'identity_id': genesis_identity_id,
            'balance': 500000,
            'updated_at': current_time
        }

        db.insert('token_balances', genesis_balance_data)

        logger.info("✅ ONX token created with initial supply")
        logger.info("   Genesis Identity: 500,000 ONX")
        logger.info("   Founding Validators: 1,000 ONX each")

    def generate_launch_report(self, genesis_identity_id: str, validator_ids: list) -> None:
        """Generate Phase 1 launch report."""
        logger.info("📊 Generating Phase 1 Launch Report...")

        report_data = {
            'launch_timestamp': datetime.now().isoformat(),
            'genesis_identity': {
                'identity_id': genesis_identity_id,
                'name': self.genesis_identity['name'],
                'email': self.genesis_identity['email'],
                'role': self.genesis_identity['role']
            },
            'founding_validators': [],
            'database_path': self.db_path,
            'total_identities': len(self.founding_validators) + 1,
            'total_validators': len(validator_ids),
            'initial_onx_supply': 1000000,
            'phase1_success_criteria': {
                'target_validators': 2,
                'achieved_validators': len(validator_ids),
                'target_uptime': '99.9%',
                'launch_timeline': '6 weeks'
            }
        }

        # Get validator details
        for validator_id in validator_ids:
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (validator_id,))
            if validator:
                owner = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (validator['identity_id'],))
                report_data['founding_validators'].append({
                    'sela_id': validator_id,
                    'business_name': validator['name'],
                    'owner_name': owner['name'] if owner else 'Unknown',
                    'category': validator['category'],
                    'mining_tier': validator['mining_tier'],
                    'trust_score': validator['trust_score']
                })

        # Save report
        report_file = os.path.join(self.data_dir, 'phase1_launch_report.json')
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)

        logger.info(f"📋 Launch report saved: {report_file}")

        # Print summary
        print("\n" + "="*60)
        print("🚀 ONNYX PHASE 1 PRODUCTION LAUNCH COMPLETE!")
        print("="*60)
        print(f"Genesis Identity: {self.genesis_identity['name']}")
        print(f"Founding Validators: {len(validator_ids)}")
        print(f"Database: {self.db_path}")
        print(f"Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)

        for validator in report_data['founding_validators']:
            print(f"✅ {validator['business_name']} ({validator['owner_name']})")

        print("\n🎯 Phase 1 Success Criteria:")
        print(f"   Target Validators: {report_data['phase1_success_criteria']['target_validators']}")
        print(f"   Achieved: {report_data['phase1_success_criteria']['achieved_validators']} ✅")
        print(f"   Target Uptime: {report_data['phase1_success_criteria']['target_uptime']}")
        print(f"   Timeline: {report_data['phase1_success_criteria']['launch_timeline']}")
        print("\n🔐 Credentials saved in: data/credentials/")
        print("📊 Full report: data/phase1_launch_report.json")
        print("="*60)

    def run_phase1_setup(self) -> None:
        """Execute the complete Phase 1 production setup."""
        logger.info("🚀 Starting ONNYX Phase 1 Production Launch Setup...")

        try:
            # Step 1: Reset and initialize production database
            self.reset_production_database()

            # Step 2: Create Genesis Identity
            genesis_identity_id = self.create_genesis_identity()

            # Step 3: Create initial ONX token supply
            self.create_initial_onx_tokens(genesis_identity_id)

            # Step 4: Create founding business validators
            validator_ids = self.create_founding_validators(genesis_identity_id)

            # Step 5: Generate launch report
            self.generate_launch_report(genesis_identity_id, validator_ids)

            # Step 6: Create production launch marker
            self._create_launch_marker()

            logger.info("🎉 Phase 1 Production Launch Setup Complete!")

        except Exception as e:
            logger.error(f"❌ Phase 1 setup failed: {e}")
            raise

    def _create_launch_marker(self) -> None:
        """Create a marker file indicating successful Phase 1 launch."""
        launch_marker = os.path.join(self.data_dir, 'production_launch.txt')

        with open(launch_marker, 'w') as f:
            f.write(f"ONNYX Phase 1 Production Launch\n")
            f.write(f"Launch Date: {datetime.now().isoformat()}\n")
            f.write(f"Genesis Identity: {self.genesis_identity['name']}\n")
            f.write(f"Founding Validators: {len(self.founding_validators)}\n")
            f.write(f"Database: {self.db_path}\n")
            f.write(f"Status: PRODUCTION READY\n")

        logger.info(f"📝 Launch marker created: {launch_marker}")

def main():
    """Main execution function."""
    print("🚀 ONNYX Phase 1 Production Launch Setup")
    print("="*50)
    print("This script will:")
    print("1. Reset the production database")
    print("2. Create Genesis Identity (Djuvane Martin)")
    print("3. Create founding business validators:")
    print("   - GetTwisted Hair Studios (Michael Williams)")
    print("   - Sheryl Williams Hair Replacement (Sheryl Williams)")
    print("4. Initialize ONX token supply")
    print("5. Generate launch report")
    print("="*50)

    # Confirm execution
    confirm = input("\nProceed with Phase 1 production setup? (yes/no): ").strip().lower()
    if confirm not in ['yes', 'y']:
        print("❌ Setup cancelled.")
        return

    try:
        # Execute Phase 1 setup
        setup = Phase1ProductionSetup()
        setup.run_phase1_setup()

        print("\n✅ Phase 1 Production Launch Setup completed successfully!")
        print("🌐 The ONNYX platform is now ready for production use.")
        print("📋 Check data/phase1_launch_report.json for full details.")

    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        logger.error(f"Phase 1 setup failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
