"""
Onnyx Database-Backed Identity Registry Module

This module provides a SQLite-backed identity registry implementation.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.identity.db_registry")

class DBIdentityRegistry:
    """
    DBIdentityRegistry manages identities in the Onnyx ecosystem using a SQLite database.

    An identity is a unique, persistent entity that can participate in the Onnyx ecosystem.
    """
    
    def __init__(self):
        """Initialize the DBIdentityRegistry."""
        # Check if the identities table exists
        if not db.table_exists("identities"):
            logger.warning("Identities table does not exist.")
    
    def register_identity(self, identity_id: str, name: str, public_key: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Register a new identity.
        
        Args:
            identity_id: The identity ID
            name: The identity name
            public_key: The identity's public key
            metadata: Additional metadata for the identity
        
        Returns:
            The newly created identity
        
        Raises:
            Exception: If the identity already exists
        """
        try:
            # Check if the identity already exists
            existing_identity = self.get_identity(identity_id)
            if existing_identity:
                raise Exception(f"Identity with ID '{identity_id}' already exists")
            
            # Create the identity
            now = int(time.time())
            identity = {
                "id": identity_id,
                "name": name,
                "public_key": public_key,
                "metadata": json.dumps(metadata or {}),
                "created_at": now,
                "updated_at": now
            }
            
            # Insert the identity into the database
            db.insert("identities", identity)
            
            # Return the identity
            return self.get_identity(identity_id)
        except Exception as e:
            logger.error(f"Error registering identity: {str(e)}")
            raise
    
    def get_identity(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an identity by ID.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The identity or None if not found
        """
        try:
            # Query the database for the identity
            identity = db.query_one("SELECT * FROM identities WHERE id = ?", (identity_id,))
            
            if identity:
                # Parse the metadata field
                identity["metadata"] = json.loads(identity["metadata"]) if identity["metadata"] else {}
                
                # Get badges
                badges = db.query(
                    "SELECT badge FROM identity_badges WHERE identity_id = ?",
                    (identity_id,)
                )
                identity["badges"] = [badge["badge"] for badge in badges]
                
                # Get Selas
                founded_selas = db.query(
                    """
                    SELECT s.id, s.name, s.type, s.token_type
                    FROM selas s
                    WHERE s.founder = ?
                    """,
                    (identity_id,)
                )
                identity["founded_selas"] = [sela["id"] for sela in founded_selas]
                
                joined_selas = db.query(
                    """
                    SELECT s.id, s.name, s.type, s.token_type, sm.role
                    FROM sela_members sm
                    JOIN selas s ON sm.sela_id = s.id
                    WHERE sm.identity_id = ?
                    """,
                    (identity_id,)
                )
                identity["joined_selas"] = [sela["id"] for sela in joined_selas]
                
                # Get Etzem score
                etzem = db.query_one(
                    "SELECT * FROM etzem_scores WHERE identity_id = ?",
                    (identity_id,)
                )
                if etzem:
                    identity["etzem_score"] = etzem["etzem"]
                else:
                    identity["etzem_score"] = 0
            
            return identity
        except Exception as e:
            logger.error(f"Error getting identity: {str(e)}")
            return None
    
    def get_all_identities(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all identities.
        
        Args:
            limit: The maximum number of identities to return
            offset: The number of identities to skip
        
        Returns:
            A list of identities
        """
        try:
            # Query the database for the identities
            identities = db.query(
                "SELECT * FROM identities ORDER BY created_at DESC LIMIT ? OFFSET ?",
                (limit, offset)
            )
            
            # Process the identities
            for identity in identities:
                # Parse the metadata field
                identity["metadata"] = json.loads(identity["metadata"]) if identity["metadata"] else {}
                
                # Get badges
                badges = db.query(
                    "SELECT badge FROM identity_badges WHERE identity_id = ?",
                    (identity["id"],)
                )
                identity["badges"] = [badge["badge"] for badge in badges]
                
                # Get Etzem score
                etzem = db.query_one(
                    "SELECT * FROM etzem_scores WHERE identity_id = ?",
                    (identity["id"],)
                )
                if etzem:
                    identity["etzem_score"] = etzem["etzem"]
                else:
                    identity["etzem_score"] = 0
            
            return identities
        except Exception as e:
            logger.error(f"Error getting identities: {str(e)}")
            return []
    
    def update_identity(self, identity_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Update an identity.
        
        Args:
            identity_id: The identity ID
            **kwargs: The fields to update
        
        Returns:
            The updated identity or None if not found
        
        Raises:
            Exception: If the identity does not exist
        """
        try:
            # Check if the identity exists
            existing_identity = self.get_identity(identity_id)
            if not existing_identity:
                raise Exception(f"Identity with ID '{identity_id}' not found")
            
            # Prepare the update data
            update_data = {}
            
            # Handle special fields
            if "metadata" in kwargs:
                update_data["metadata"] = json.dumps(kwargs["metadata"])
            
            # Handle regular fields
            for key, value in kwargs.items():
                if key not in ["id", "created_at", "metadata"]:
                    update_data[key] = value
            
            # Update the timestamp
            update_data["updated_at"] = int(time.time())
            
            # Update the identity in the database
            if update_data:
                db.update("identities", update_data, "id = ?", (identity_id,))
            
            # Return the updated identity
            return self.get_identity(identity_id)
        except Exception as e:
            logger.error(f"Error updating identity: {str(e)}")
            raise
    
    def add_badge(self, identity_id: str, badge: str, granted_by: Optional[str] = None) -> bool:
        """
        Add a badge to an identity.
        
        Args:
            identity_id: The identity ID
            badge: The badge to add
            granted_by: The identity ID of the granter
        
        Returns:
            True if the badge was added, False otherwise
        
        Raises:
            Exception: If the identity does not exist or the badge already exists
        """
        try:
            # Check if the identity exists
            existing_identity = self.get_identity(identity_id)
            if not existing_identity:
                raise Exception(f"Identity with ID '{identity_id}' not found")
            
            # Check if the badge already exists
            existing_badge = db.query_one(
                "SELECT * FROM identity_badges WHERE identity_id = ? AND badge = ?",
                (identity_id, badge)
            )
            if existing_badge:
                raise Exception(f"Badge '{badge}' already exists for identity '{identity_id}'")
            
            # Add the badge
            db.insert("identity_badges", {
                "identity_id": identity_id,
                "badge": badge,
                "granted_at": int(time.time()),
                "granted_by": granted_by
            })
            
            return True
        except Exception as e:
            logger.error(f"Error adding badge: {str(e)}")
            raise
    
    def remove_badge(self, identity_id: str, badge: str) -> bool:
        """
        Remove a badge from an identity.
        
        Args:
            identity_id: The identity ID
            badge: The badge to remove
        
        Returns:
            True if the badge was removed, False otherwise
        
        Raises:
            Exception: If the identity does not exist or the badge does not exist
        """
        try:
            # Check if the identity exists
            existing_identity = self.get_identity(identity_id)
            if not existing_identity:
                raise Exception(f"Identity with ID '{identity_id}' not found")
            
            # Check if the badge exists
            existing_badge = db.query_one(
                "SELECT * FROM identity_badges WHERE identity_id = ? AND badge = ?",
                (identity_id, badge)
            )
            if not existing_badge:
                raise Exception(f"Badge '{badge}' does not exist for identity '{identity_id}'")
            
            # Remove the badge
            db.execute(
                "DELETE FROM identity_badges WHERE identity_id = ? AND badge = ?",
                (identity_id, badge)
            )
            
            return True
        except Exception as e:
            logger.error(f"Error removing badge: {str(e)}")
            raise
    
    def link_sela(self, identity_id: str, sela_id: str, role: str) -> bool:
        """
        Link an identity to a Sela.
        
        Args:
            identity_id: The identity ID
            sela_id: The Sela ID
            role: The role of the identity in the Sela
        
        Returns:
            True if the link was created, False otherwise
        
        Raises:
            Exception: If the identity or Sela does not exist
        """
        try:
            # Check if the identity exists
            existing_identity = self.get_identity(identity_id)
            if not existing_identity:
                raise Exception(f"Identity with ID '{identity_id}' not found")
            
            # Check if the Sela exists
            existing_sela = db.query_one("SELECT * FROM selas WHERE id = ?", (sela_id,))
            if not existing_sela:
                raise Exception(f"Sela with ID '{sela_id}' not found")
            
            # Check if the link already exists
            existing_link = db.query_one(
                "SELECT * FROM sela_members WHERE sela_id = ? AND identity_id = ?",
                (sela_id, identity_id)
            )
            
            if existing_link:
                # Update the role
                db.update(
                    "sela_members",
                    {"role": role},
                    "sela_id = ? AND identity_id = ?",
                    (sela_id, identity_id)
                )
            else:
                # Create the link
                db.insert("sela_members", {
                    "sela_id": sela_id,
                    "identity_id": identity_id,
                    "role": role,
                    "joined_at": int(time.time())
                })
            
            # Add the role badge
            badge = f"SELA_{role.upper()}"
            try:
                self.add_badge(identity_id, badge)
            except Exception:
                # Ignore if the badge already exists
                pass
            
            return True
        except Exception as e:
            logger.error(f"Error linking Sela: {str(e)}")
            raise

# Create a global instance of the DBIdentityRegistry
db_identity_registry = DBIdentityRegistry()
