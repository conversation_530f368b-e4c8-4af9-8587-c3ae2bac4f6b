# Onnyx Configuration System

The Onnyx configuration system provides a centralized way to manage configuration parameters for the Onnyx blockchain. It supports default configurations in code and overrides in JSON/YAML files.

## Overview

The configuration system is organized into the following components:

- **Chain Parameters**: Parameters related to the blockchain itself, such as block rewards, voting thresholds, and token supply limits.
- **Network Parameters**: Parameters related to the P2P network, such as port numbers, seed nodes, and protocol versions.
- **Node Parameters**: Parameters related to the node, such as node ID, host, port, and logging level.
- **Sela Parameters**: Parameters related to Sela businesses, such as Sela ID, identity ID, and validator rotation settings.

## Directory Structure

The configuration system uses the following directory structure:

```
onnyx/
├── config/             # Configuration files
│   ├── chain_params.json
│   ├── network_params.json
│   ├── node_params.json
│   └── sela_params.json
├── runtime/            # Runtime data
│   ├── blockchain/     # Blockchain data
│   ├── mempool/        # Mempool data
│   ├── identities/     # Identity data
│   ├── tokens/         # Token data
│   ├── logs/           # Log files
│   └── keys/           # Cryptographic keys
└── schemas/            # Database schemas
```

## Usage

### Initialization

To initialize the configuration system, use the `OnnyxConfig` class:

```python
from config.config import OnnyxConfig

# Initialize with default paths
config = OnnyxConfig()

# Initialize with custom paths
config = OnnyxConfig(config_dir="path/to/config", data_dir="path/to/data")
```

### Accessing Configuration Parameters

To access configuration parameters, use the appropriate getter methods:

```python
# Get a chain parameter
block_reward = config.get_chain_param("block_reward")

# Get a network parameter
max_connections = config.get_network_param("max_connections")

# Get a node parameter
node_id = config.get_node_param("node_id")

# Get a Sela parameter
sela_id = config.get_sela_param("sela_id")
```

### Modifying Configuration Parameters

To modify configuration parameters, use the appropriate setter methods:

```python
# Set a chain parameter
config.set_chain_param("block_reward", 8)

# Set a network parameter
config.set_network_param("max_connections", 100)

# Set a node parameter
config.set_node_param("node_id", "my_node")

# Set a Sela parameter
config.set_sela_param("sela_id", "my_sela")
```

### Loading Configuration from Files

The configuration system automatically loads configuration from files in the specified directories. You can also manually load configuration from specific files:

```python
# Load chain parameters from a file
config.load_chain_params("path/to/chain_params.json")

# Load network parameters from a file
config.load_network_params("path/to/network_params.json")

# Load node parameters from a file
config.load_node_params("path/to/node_params.json")

# Load Sela parameters from a file
config.load_sela_params("path/to/sela_params.json")
```

### Saving Configuration to Files

To save configuration to files, use the appropriate save methods:

```python
# Save chain parameters to a file
config.save_chain_params()

# Save network parameters to a file
config.save_network_params()

# Save node parameters to a file
config.save_node_params()

# Save Sela parameters to a file
config.save_sela_params()
```

## Default Configuration

The configuration system includes default values for all parameters. These defaults are used if no configuration files are found or if a parameter is not specified in the configuration files.

### Chain Parameters

```json
{
  "block_reward": 10,
  "reward_token": "ONX",
  "quorum_percent": 50,
  "vote_pass_ratio": 0.6,
  "mint_cap_factor": 1.0,
  "min_etzem_score": 30,
  "validator_badges": ["VALIDATOR_ELIGIBLE_BADGE"],
  "council_badges": ["COUNCIL_ELIGIBLE_BADGE"],
  "guardian_badges": ["GUARDIAN_ELIGIBLE_BADGE"],
  "proposal_badges": ["PROPOSAL_ELIGIBLE_BADGE"],
  "validator_rotation_interval": 3600,
  "scroll_voting_period": 604800,
  "scroll_implementation_delay": 86400,
  "max_token_supply": 1000000000,
  "min_stake_amount": 100,
  "stake_lock_period": 2592000,
  "max_mempool_size": 1000,
  "max_block_size": 1000000,
  "target_block_time": 60,
  "difficulty_adjustment_period": 100,
  "max_transaction_size": 100000,
  "max_transactions_per_block": 1000
}
```

### Network Parameters

```json
{
  "networks": {
    "mainnet": {
      "name": "Onnyx Mainnet",
      "chain_id": "onnyx-main-1",
      "p2p_port": 8333,
      "rpc_port": 8332,
      "seed_nodes": [
        "seed1.onnyx.chain:8333",
        "seed2.onnyx.chain:8333",
        "seed3.onnyx.chain:8333"
      ],
      "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"
    },
    "testnet": {
      "name": "Onnyx Testnet",
      "chain_id": "onnyx-test-1",
      "p2p_port": 18333,
      "rpc_port": 18332,
      "seed_nodes": [
        "test-seed1.onnyx.chain:18333",
        "test-seed2.onnyx.chain:18333"
      ],
      "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"
    },
    "devnet": {
      "name": "Onnyx Development Network",
      "chain_id": "onnyx-dev-1",
      "p2p_port": 28333,
      "rpc_port": 28332,
      "seed_nodes": [
        "127.0.0.1:28333"
      ],
      "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"
    }
  },
  "protocol_version": "0.1.0",
  "min_peer_version": "0.1.0",
  "max_connections": 125,
  "connection_timeout_seconds": 30,
  "handshake_timeout_seconds": 5
}
```

### Node Parameters

```json
{
  "node_id": "onnyx_node",
  "node_port": 8080,
  "node_host": "localhost",
  "node_peers": [],
  "node_public_key": "",
  "node_private_key": "",
  "data_dir": "data",
  "max_peers": 10,
  "sync_interval": 60,
  "broadcast_interval": 30,
  "mempool_sync_interval": 15,
  "block_sync_interval": 60,
  "peer_discovery_interval": 300,
  "heartbeat_interval": 30,
  "connection_timeout": 10,
  "max_mempool_size": 1000,
  "max_block_size": 1000,
  "max_message_size": 1048576,
  "log_level": "INFO"
}
```

### Sela Parameters

```json
{
  "sela_id": "",
  "identity_id": "",
  "private_key_path": "",
  "api_port": 8888,
  "role": "validator",
  "auto_mine": false,
  "mine_interval": 60,
  "activity_log_path": "data/activity_log.json",
  "validator_rotation": {
    "enabled": true,
    "min_etzem_score": 30,
    "required_badges": [
      "SELA_FOUNDER",
      "VALIDATOR_ELIGIBLE_BADGE"
    ]
  }
}
```
