{% extends "base.html" %}

{% block title %}Access Portal - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative flex items-center justify-center py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
    </div>

    <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full">
        <div class="glass-card p-10 neuro-card">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 rounded-xl flex items-center justify-center mx-auto mb-6 glow-effect bg-white/5 backdrop-blur-sm border border-white/20 shadow-2xl shadow-cyber-cyan/30">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-12 h-12 object-contain"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
                <h1 class="text-3xl font-orbitron font-bold mb-4">
                    <span class="hologram-text">Access Portal</span>
                </h1>
                <p class="text-gray-300 text-lg">
                    Enter your verified identity credentials
                </p>
            </div>

            <!-- Login Form -->
            <form method="POST" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                    </label>
                    <div class="relative">
                        <input type="email"
                               id="email"
                               name="email"
                               required
                               class="form-input w-full pl-12"
                               placeholder="Enter your registered email">
                        <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                        </svg>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="glass-card p-4 border border-cyber-cyan/30 bg-cyber-cyan/5">
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-cyber-cyan mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.012-3.016A9.956 9.956 0 0121 12c0 1.657-.45 3.212-1.237 4.54M16.5 17a9.956 9.956 0 01-4.5 1c-1.657 0-3.212-.45-4.54-1.237M3 12a9.956 9.956 0 011-4.54m5.5-2.5a9.956 9.956 0 014.5-1c1.657 0 3.212.45 4.54 1.237"></path>
                        </svg>
                        <div>
                            <p class="text-sm text-cyber-cyan font-medium">Secure Authentication</p>
                            <p class="text-xs text-gray-400 mt-1">
                                Your identity is verified using quantum-resistant cryptography
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit"
                        class="w-full glass-button-primary px-6 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105 glow-effect">
                    🚀 Access Network
                </button>
            </form>

            <!-- Additional Options -->
            <div class="mt-8 space-y-4">
                <div class="text-center">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-white/10"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-4 bg-onyx-black text-gray-400">New to ONNYX?</span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-3">
                    <a href="{{ url_for('register_choice') }}"
                       class="glass-button px-6 py-3 rounded-xl text-center font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        🔐 Create Identity
                    </a>
                    <a href="{{ url_for('index') }}"
                       class="text-center text-gray-400 hover:text-cyber-cyan transition-colors duration-300 text-sm">
                        ← Back to Home
                    </a>
                </div>
            </div>

            <!-- Features List -->
            <div class="mt-8 pt-6 border-t border-white/10">
                <h3 class="text-sm font-orbitron font-semibold text-cyber-cyan mb-4">Platform Features</h3>
                <ul class="space-y-2 text-sm text-gray-400">
                    <li class="flex items-center space-x-2">
                        <div class="w-1.5 h-1.5 bg-cyber-cyan rounded-full"></div>
                        <span>Quantum-resistant security</span>
                    </li>
                    <li class="flex items-center space-x-2">
                        <div class="w-1.5 h-1.5 bg-cyber-purple rounded-full"></div>
                        <span>Decentralized validation</span>
                    </li>
                    <li class="flex items-center space-x-2">
                        <div class="w-1.5 h-1.5 bg-cyber-blue rounded-full"></div>
                        <span>Trusted business network</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const emailInput = document.getElementById('email');
    const submitButton = form.querySelector('button[type="submit"]');

    // Email validation
    emailInput.addEventListener('input', function() {
        const email = this.value.trim();
        const isValid = email && email.includes('@') && email.includes('.');

        if (isValid) {
            this.classList.remove('border-red-500/50');
            this.classList.add('border-cyber-cyan/50');
        } else if (email) {
            this.classList.remove('border-cyber-cyan/50');
            this.classList.add('border-red-500/50');
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();

        if (!email || !email.includes('@')) {
            e.preventDefault();
            Onnyx.utils.showNotification('Please enter a valid email address', 'error');
            emailInput.focus();
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<div class="spinner inline-block mr-2"></div>Authenticating...';
    });

    // Auto-focus email field
    emailInput.focus();
});
</script>
{% endblock %}
