# src/business/stake.py

from typing import Dict, Any, Optional

from src.db.manager import db_manager
from src.identity.registry import IdentityRegistry
from src.etzem.engine import EtzemEngine

class StakePolicy:
    """
    Policy for determining Sela stake requirements.
    """
    
    def __init__(self, 
                 identity_registry: Optional[IdentityRegistry] = None,
                 etzem_engine: Optional[EtzemEngine] = None):
        """
        Initialize the StakePolicy.
        
        Args:
            identity_registry: The identity registry
            etzem_engine: The Etzem engine
        """
        self.identities = identity_registry or IdentityRegistry()
        self.etzem = etzem_engine or EtzemEngine()
        self.db = db_manager.get_connection()
        
        # Define base stake amounts by Sela type
        self.base_stakes = {
            "BUSINESS": 1000,
            "NONPROFIT": 500,
            "COMMUNITY": 250
        }
        
        # Define sector multipliers
        self.sector_multipliers = {
            "RETAIL": 1.0,
            "TECHNOLOGY": 1.2,
            "EDUCATION": 0.8,
            "HEALTHCARE": 1.1,
            "FINANCE": 1.5,
            "MANUFACTURING": 1.3,
            "AGRICULTURE": 0.9,
            "ENERGY": 1.4,
            "TRANSPORTATION": 1.2,
            "CONSTRUCTION": 1.1,
            "HOSPITALITY": 1.0,
            "MEDIA": 1.2,
            "ENTERTAINMENT": 1.3,
            "REAL_ESTATE": 1.4,
            "LEGAL": 1.3,
            "CONSULTING": 1.2,
            "OTHER": 1.0
        }
        
        # Define Etzem discount tiers
        self.etzem_discounts = {
            0: 0.0,    # 0-29: No discount
            30: 0.1,   # 30-49: 10% discount
            50: 0.2,   # 50-69: 20% discount
            70: 0.3,   # 70-89: 30% discount
            90: 0.4    # 90+: 40% discount
        }
    
    def calculate_stake(self, identity_id: str, sela_type: str, sector: str) -> int:
        """
        Calculate the required stake for a Sela.
        
        Args:
            identity_id: The identity ID
            sela_type: The Sela type
            sector: The Sela sector
        
        Returns:
            The required stake amount
        
        Raises:
            Exception: If the identity does not exist or the Sela type or sector is invalid
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the Sela type is valid
        if sela_type not in self.base_stakes:
            raise Exception(f"Invalid Sela type: {sela_type}")
        
        # Check if the sector is valid
        if sector not in self.sector_multipliers:
            raise Exception(f"Invalid sector: {sector}")
        
        # Get the base stake
        base_stake = self.base_stakes[sela_type]
        
        # Apply the sector multiplier
        sector_multiplier = self.sector_multipliers[sector]
        stake = base_stake * sector_multiplier
        
        # Apply the Etzem discount
        etzem_data = self.etzem.compute_etzem(identity_id)
        if etzem_data:
            etzem_score = etzem_data["etzem"]
            discount = 0.0
            
            for threshold, discount_rate in sorted(self.etzem_discounts.items()):
                if etzem_score >= threshold:
                    discount = discount_rate
            
            stake = stake * (1 - discount)
        
        # Round to the nearest integer
        return round(stake)
    
    def get_stake_info(self, identity_id: str, sela_type: str, sector: str) -> Dict[str, Any]:
        """
        Get detailed information about the stake calculation.
        
        Args:
            identity_id: The identity ID
            sela_type: The Sela type
            sector: The Sela sector
        
        Returns:
            Detailed stake information
        
        Raises:
            Exception: If the identity does not exist or the Sela type or sector is invalid
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the Sela type is valid
        if sela_type not in self.base_stakes:
            raise Exception(f"Invalid Sela type: {sela_type}")
        
        # Check if the sector is valid
        if sector not in self.sector_multipliers:
            raise Exception(f"Invalid sector: {sector}")
        
        # Get the base stake
        base_stake = self.base_stakes[sela_type]
        
        # Apply the sector multiplier
        sector_multiplier = self.sector_multipliers[sector]
        stake_with_multiplier = base_stake * sector_multiplier
        
        # Apply the Etzem discount
        etzem_data = self.etzem.compute_etzem(identity_id)
        etzem_score = etzem_data["etzem"] if etzem_data else 0
        discount = 0.0
        
        for threshold, discount_rate in sorted(self.etzem_discounts.items()):
            if etzem_score >= threshold:
                discount = discount_rate
        
        final_stake = stake_with_multiplier * (1 - discount)
        
        # Return the stake information
        return {
            "identity_id": identity_id,
            "sela_type": sela_type,
            "sector": sector,
            "base_stake": base_stake,
            "sector_multiplier": sector_multiplier,
            "stake_with_multiplier": stake_with_multiplier,
            "etzem_score": etzem_score,
            "etzem_discount": discount,
            "final_stake": round(final_stake)
        }
