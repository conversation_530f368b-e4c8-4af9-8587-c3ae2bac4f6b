"""
Onnyx Token Ledger Module

This module provides the TokenLedger class for managing token balances.
"""

import os
import json
from typing import Dict, List, Any, Optional, Tuple

class TokenLedger:
    """
    TokenLedger manages token balances for identities.
    """

    def __init__(self, ledger_file: str = "token_ledger.json"):
        """
        Initialize the TokenLedger.

        Args:
            ledger_file: Path to the ledger JSON file
        """
        self.ledger_file = ledger_file
        self.balances = self._load()

    def _load(self) -> Dict[str, Dict[str, int]]:
        """
        Load the ledger from the JSON file.

        Returns:
            The ledger as a dictionary of identity balances
        """
        if os.path.exists(self.ledger_file):
            try:
                with open(self.ledger_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}

    def _save(self) -> None:
        """Save the ledger to the JSON file."""
        with open(self.ledger_file, 'w') as f:
            json.dump(self.balances, f, indent=2)

    def get_balance(self, identity_id: str, token_id: str) -> int:
        """
        Get the balance of a token for an identity.

        Args:
            identity_id: The identity ID
            token_id: The token ID

        Returns:
            The balance of the token
        """
        if identity_id not in self.balances:
            return 0

        return self.balances[identity_id].get(token_id, 0)

    def credit(self, identity_id: str, token_id: str, amount: int) -> bool:
        """
        Credit tokens to an identity.

        Args:
            identity_id: The identity ID
            token_id: The token ID
            amount: The amount to credit

        Returns:
            True if the tokens were credited, False otherwise
        """
        if amount <= 0:
            return False

        # Ensure the identity exists in the ledger
        if identity_id not in self.balances:
            self.balances[identity_id] = {}

        # Credit the tokens
        current_balance = self.balances[identity_id].get(token_id, 0)
        self.balances[identity_id][token_id] = current_balance + amount

        # Save the ledger
        self._save()

        return True

    def debit(self, identity_id: str, token_id: str, amount: int) -> bool:
        """
        Debit tokens from an identity.

        Args:
            identity_id: The identity ID
            token_id: The token ID
            amount: The amount to debit

        Returns:
            True if the tokens were debited, False otherwise
        """
        if amount <= 0:
            return False

        # Check if the identity has enough tokens
        current_balance = self.get_balance(identity_id, token_id)
        if current_balance < amount:
            return False

        # Ensure the identity exists in the ledger
        if identity_id not in self.balances:
            self.balances[identity_id] = {}

        # Debit the tokens
        self.balances[identity_id][token_id] = current_balance - amount

        # Save the ledger
        self._save()

        return True

    def transfer(self, from_id: str, to_id: str, token_id: str, amount: int) -> bool:
        """
        Transfer tokens from one identity to another.

        Args:
            from_id: The sender's identity ID
            to_id: The recipient's identity ID
            token_id: The token ID
            amount: The amount to transfer

        Returns:
            True if the tokens were transferred, False otherwise
        """
        # Debit from the sender
        if not self.debit(from_id, token_id, amount):
            return False

        # Credit to the recipient
        if not self.credit(to_id, token_id, amount):
            # Rollback the debit if the credit fails
            self.credit(from_id, token_id, amount)
            return False

        return True

    def get_all_balances(self, identity_id: str) -> Dict[str, int]:
        """
        Get all token balances for an identity.

        Args:
            identity_id: The identity ID

        Returns:
            All token balances for the identity
        """
        return self.balances.get(identity_id, {})

    def get_all_holders(self, token_id: str) -> Dict[str, int]:
        """
        Get all holders of a token.

        Args:
            token_id: The token ID

        Returns:
            All holders of the token and their balances
        """
        holders = {}

        for identity_id, balances in self.balances.items():
            if token_id in balances and balances[token_id] > 0:
                holders[identity_id] = balances[token_id]

        return holders

    def get_total_supply(self, token_id: str) -> int:
        """
        Get the total supply of a token.

        Args:
            token_id: The token ID

        Returns:
            The total supply of the token
        """
        total = 0

        for balances in self.balances.values():
            total += balances.get(token_id, 0)

        return total

    def get_top_holders(self, token_id: str, count: int) -> List[Tuple[str, int]]:
        """
        Get the top holders of a token.

        Args:
            token_id: The token ID
            count: The number of holders to get

        Returns:
            The top holders of the token and their balances
        """
        holders = self.get_all_holders(token_id)

        # Sort holders by balance
        sorted_holders = sorted(
            holders.items(),
            key=lambda x: x[1],
            reverse=True
        )

        return sorted_holders[:count]

    def get_total_volume(self, identity_id: str) -> int:
        """
        Get the total transaction volume for an identity.

        This includes both sent and received transactions.

        Args:
            identity_id: The identity ID

        Returns:
            The total transaction volume
        """
        # In a real implementation, this would query a transaction history database
        # For now, we'll return a mock value based on the identity's balances
        total_volume = 0

        # Add up all token balances as a proxy for transaction volume
        if identity_id in self.balances:
            for token_balance in self.balances[identity_id].values():
                total_volume += token_balance

        # Multiply by a factor to simulate transaction history
        # In a real implementation, this would be the actual transaction volume
        return total_volume * 10
