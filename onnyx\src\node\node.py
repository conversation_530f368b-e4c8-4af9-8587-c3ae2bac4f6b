# src/node/node.py

import os
import json
import time
from src.chain.chain import Blockchain
from src.chain.block import Block

class OnnyxNode:
    def __init__(self, path=None):
        """
        Initialize an Onnyx node.
        
        Args:
            path: Path to the chain data file. If None, a default path in the data directory will be used.
        """
        if path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.path = os.path.join(data_dir, "chain_data.json")
        else:
            self.path = path
            
        self.blockchain = Blockchain()
        self._load()

    def _load(self):
        """Load the blockchain from a file."""
        try:
            if os.path.exists(self.path) and os.path.getsize(self.path) > 0:
                with open(self.path, "r") as f:
                    data = json.load(f)
                    
                # Clear the existing chain (except genesis)
                self.blockchain.chain = [self.blockchain.chain[0]]
                
                # Add blocks from the file (skip genesis)
                for block_data in data[1:]:
                    block = Block(
                        index=block_data["index"],
                        previous_hash=block_data["previous_hash"],
                        timestamp=block_data["timestamp"],
                        transactions=block_data["transactions"],
                        nonce=block_data["nonce"]
                    )
                    # Set the hash directly to avoid recalculating
                    block.hash = block_data["hash"]
                    self.blockchain.chain.append(block)
        except Exception as e:
            print(f"Error loading blockchain: {str(e)}")
            print("Starting with a fresh blockchain...")
            # Keep the default blockchain with genesis block

    def _save(self):
        """Save the blockchain to a file."""
        with open(self.path, "w") as f:
            json.dump(self.blockchain.to_list(), f, indent=2)

    def add_transaction(self, tx):
        """
        Add a transaction to the mempool.
        
        Args:
            tx: The transaction to add
        """
        self.blockchain.add_transaction(tx)
        print(f"[OK] Transaction added to mempool: {tx}")
        return True

    def add_transactions(self, txs):
        """
        Add multiple transactions to the mempool.
        
        Args:
            txs: A list of transactions to add
        """
        for tx in txs:
            self.blockchain.add_transaction(tx)
        print(f"[OK] {len(txs)} transactions added to mempool")
        return True

    def mine_block(self):
        """Mine a new block with transactions from the mempool."""
        if not self.blockchain.mempool:
            print("[WARNING] No transactions in mempool. Mining empty block...")
        
        # In a real implementation, we would do proof-of-work here
        # For now, we just create a block with the pending transactions
        block = self.blockchain.mine_pending_transactions()
        self._save()
        
        print(f"[OK] Block mined: #{block.index} - {block.hash[:8]}")
        return block.to_dict()

    def get_latest_block(self):
        """Get the latest block in the blockchain."""
        return self.blockchain.get_latest_block().to_dict()

    def get_block_by_index(self, idx):
        """
        Get a block by its index.
        
        Args:
            idx: The index of the block to get
            
        Returns:
            The block as a dictionary, or None if the index is out of range
        """
        if idx < 0 or idx >= len(self.blockchain.chain):
            return None
        return self.blockchain.chain[idx].to_dict()

    def get_block_by_hash(self, block_hash):
        """
        Get a block by its hash.
        
        Args:
            block_hash: The hash of the block to get
            
        Returns:
            The block as a dictionary, or None if the hash is not found
        """
        for block in self.blockchain.chain:
            if block.hash == block_hash:
                return block.to_dict()
        return None

    def get_chain(self):
        """Get the entire blockchain as a list of dictionaries."""
        return self.blockchain.to_list()

    def get_mempool(self):
        """Get the current mempool transactions."""
        return self.blockchain.mempool

    def clear_mempool(self):
        """Clear all transactions from the mempool."""
        num_txs = len(self.blockchain.mempool)
        self.blockchain.mempool = []
        print(f"[OK] Cleared {num_txs} transactions from mempool")
        return num_txs

    def status(self):
        """
        Get the current status of the node.
        
        Returns:
            A dictionary with the current status
        """
        return {
            "height": len(self.blockchain.chain) - 1,  # Subtract 1 for genesis block
            "latest_hash": self.blockchain.get_latest_block().hash,
            "latest_index": self.blockchain.get_latest_block().index,
            "mempool_size": len(self.blockchain.mempool),
            "timestamp": int(time.time())
        }

    def validate_chain(self):
        """
        Validate the integrity of the blockchain.
        
        Returns:
            True if the chain is valid, False otherwise
        """
        for i in range(1, len(self.blockchain.chain)):
            current = self.blockchain.chain[i]
            previous = self.blockchain.chain[i-1]
            
            # Check that the current block points to the previous block
            if current.previous_hash != previous.hash:
                print(f"[ERROR] Invalid chain: Block #{current.index} has invalid previous_hash")
                return False
            
            # Check that the block hash is valid
            if current.hash != current.calculate_hash():
                print(f"[ERROR] Invalid chain: Block #{current.index} has invalid hash")
                return False
        
        print("[OK] Blockchain is valid")
        return True
