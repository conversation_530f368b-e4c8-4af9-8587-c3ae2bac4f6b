{% extends "base.html" %}

{% block content %}
<div class="card">
  <div class="card-header">
    <h5 class="card-title">Sela Information</h5>
  </div>
  <div class="card-body">
    {% if sela %}
      <div class="row">
        <div class="col-md-6">
          <h6>Basic Information</h6>
          <p>
            <strong>ID:</strong> {{ sela.id }}<br>
            <strong>Name:</strong> {{ sela.name }}<br>
            <strong>Type:</strong> {{ sela.type }}<br>
            <strong>Token Type:</strong> {{ sela.token_type }}<br>
            <strong>Founder:</strong> {{ sela.founder }}<br>
            <strong>Created At:</strong> {{ sela.created_at }}
          </p>

          <h6>Validator Status</h6>
          <p>
            <strong>Is Validator:</strong> <span class="badge bg-{{ 'success' if sela.is_validator else 'secondary' }}">{{ 'Yes' if sela.is_validator else 'No' }}</span><br>
            <strong>Validator Since:</strong> {{ sela.validator_since or 'N/A' }}<br>
            <strong>Last Block Proposed:</strong> {{ sela.last_block_proposed or 'N/A' }}
          </p>
        </div>

        <div class="col-md-6">
          <h6>Members</h6>
          {% if sela.members %}
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Identity ID</th>
                  <th>Role</th>
                  <th>Joined At</th>
                </tr>
              </thead>
              <tbody>
                {% for member in sela.members %}
                  <tr>
                    <td>{{ member.identity_id }}</td>
                    <td>{{ member.role }}</td>
                    <td>{{ member.joined_at }}</td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% else %}
            <p>No members found.</p>
          {% endif %}

          <h6>Tokens</h6>
          {% if sela.tokens %}
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Token ID</th>
                  <th>Name</th>
                  <th>Symbol</th>
                  <th>Supply</th>
                </tr>
              </thead>
              <tbody>
                {% for token in sela.tokens %}
                  <tr>
                    <td>{{ token.id }}</td>
                    <td>{{ token.name }}</td>
                    <td>{{ token.symbol }}</td>
                    <td>{{ token.supply }}</td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% else %}
            <p>No tokens found.</p>
          {% endif %}
        </div>
      </div>

      <h6>Validator Rotation</h6>
      {% if sela.rotation_schedule %}
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Block Height</th>
              <th>Validator</th>
            </tr>
          </thead>
          <tbody>
            {% for schedule in sela.rotation_schedule %}
              <tr>
                <td>{{ schedule.block_height }}</td>
                <td>{{ schedule.validator }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      {% else %}
        <p>No rotation schedule found.</p>
      {% endif %}
    {% else %}
      <p>No Sela information found.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
