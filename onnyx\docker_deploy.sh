#!/bin/bash

# Onnyx Docker Deployment Script

# Set colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print banner
echo -e "${GREEN}"
echo "  ____  _   _ _   ___   ____  __"
echo " / __ \| \ | | \ | \ \ / /\ \/ /"
echo "| |  | |  \| |  \| |\ V /  \  / "
echo "| |  | | . \` | . \` | > <   /  \ "
echo "| |__| | |\  | |\  |/ . \ / /\ \\"
echo " \____/|_| \_|_| \_/_/ \_/_/  \_\\"
echo -e "${NC}"
echo "Onnyx Docker Deployment Script"
echo "==============================="
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed.${NC}"
    echo "Please install Docker and try again."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed.${NC}"
    echo "Please install Docker Compose and try again."
    exit 1
fi

# Parse arguments
BACKUP=false
REBUILD=false
SERVICES=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --backup)
            BACKUP=true
            shift
            ;;
        --rebuild)
            REBUILD=true
            shift
            ;;
        --services)
            SERVICES=$2
            shift 2
            ;;
        *)
            echo -e "${RED}Error: Unknown option $1${NC}"
            echo "Usage: $0 [--backup] [--rebuild] [--services service1,service2,...]"
            exit 1
            ;;
    esac
done

# Backup data if requested
if [ "$BACKUP" = true ]; then
    echo -e "${YELLOW}Backing up data...${NC}"
    
    # Create backup directory if it doesn't exist
    mkdir -p backups
    
    # Create backup filename with timestamp
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    BACKUP_FILE="backups/onnyx-data-$TIMESTAMP.tar.gz"
    
    # Create backup
    docker run --rm -v onnyx-data:/data -v $(pwd)/backups:/backup alpine tar -czf /backup/$(basename $BACKUP_FILE) /data
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Backup created: $BACKUP_FILE${NC}"
    else
        echo -e "${RED}Error creating backup.${NC}"
        exit 1
    fi
fi

# Rebuild images if requested
if [ "$REBUILD" = true ]; then
    echo -e "${YELLOW}Rebuilding Docker images...${NC}"
    
    if [ -z "$SERVICES" ]; then
        # Rebuild all images
        docker-compose build --no-cache
    else
        # Rebuild specific services
        docker-compose build --no-cache $(echo $SERVICES | tr ',' ' ')
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Docker images rebuilt successfully.${NC}"
    else
        echo -e "${RED}Error rebuilding Docker images.${NC}"
        exit 1
    fi
fi

# Start containers
echo -e "${YELLOW}Starting Onnyx containers...${NC}"

if [ -z "$SERVICES" ]; then
    # Start all services
    docker-compose up -d
else
    # Start specific services
    docker-compose up -d $(echo $SERVICES | tr ',' ' ')
fi

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Onnyx containers started successfully.${NC}"
    
    # Show container status
    echo ""
    echo "Container Status:"
    docker-compose ps
    
    # Show API URL
    API_PORT=$(docker-compose port api 5000 | cut -d: -f2)
    if [ ! -z "$API_PORT" ]; then
        echo ""
        echo -e "${GREEN}Onnyx API is available at:${NC} http://localhost:$API_PORT"
    fi
    
    # Show Transaction Viewer URL
    TX_VIEWER_PORT=$(docker-compose port transaction-viewer 5002 | cut -d: -f2)
    if [ ! -z "$TX_VIEWER_PORT" ]; then
        echo -e "${GREEN}Onnyx Transaction Viewer is available at:${NC} http://localhost:$TX_VIEWER_PORT"
    fi
else
    echo -e "${RED}Error starting Onnyx containers.${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}Deployment completed successfully.${NC}"
echo "Use 'docker-compose logs -f' to view logs."
echo "Use 'docker-compose down' to stop and remove containers."
