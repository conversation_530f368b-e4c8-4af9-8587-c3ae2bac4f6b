/* ONNYX Platform - Onyx Stone Theme for GitHub Pages */

/* CSS Variables */
:root {
    /* Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #000000;
    --text-primary: #eeeeee;
    --text-secondary: #cccccc;
    --text-muted: #999999;

    /* Cyber Colors */
    --cyber-cyan: #00ffff;
    --cyber-purple: #8a2be2;
    --cyber-blue: #0066ff;
    --accent-green: #00ff88;
    --accent-yellow: #ffaa00;
    --accent-red: #ff4444;

    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-shadow: rgba(0, 0, 0, 0.3);

    /* Fonts */
    --font-primary: 'Montserrat', sans-serif;
    --font-heading: 'Orbitron', monospace;

    /* Spacing */
    --section-padding: 5rem 0 3rem 0;
    --container-padding: 0 1.5rem;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--cyber-cyan);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--cyber-purple);
    text-shadow: 0 0 10px currentColor;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

section {
    padding: var(--section-padding);
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--transition-slow);
}

section.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 2rem;
    color: var(--cyber-cyan);
    text-shadow: 0 0 20px currentColor;
}

.logo-text {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
}

.nav-auth {
    display: flex;
    gap: 1rem;
}

.auth-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.9rem;
}

.auth-btn.primary {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    color: white;
}

.auth-btn.secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--glass-border);
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

.mobile-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.hero-grid {
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    width: 100%;
    height: 100%;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 2rem;
    line-height: 1.1;
}

.gradient-text {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.cta-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
}

.cta-btn.primary {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    color: white;
}

.cta-btn.secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 255, 255, 0.4);
}

.btn-icon {
    font-size: 1.2rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--cyber-cyan);
    font-family: var(--font-heading);
    text-shadow: 0 0 20px currentColor;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Glass Cards */
.glass-card,
.validator-card,
.explorer-card,
.dashboard-card,
.tech-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px var(--glass-shadow);
    transition: var(--transition-normal);
}

.glass-card:hover,
.validator-card:hover,
.explorer-card:hover,
.dashboard-card:hover,
.tech-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.3);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--text-primary), var(--cyber-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Validators Section */
.validators-section {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.02) 0%, rgba(138, 43, 226, 0.02) 100%);
}

.validators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.validator-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.validator-name {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.validator-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.category,
.status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.category {
    background: rgba(0, 255, 255, 0.2);
    color: var(--cyber-cyan);
}

.status.active {
    background: rgba(0, 255, 136, 0.2);
    color: var(--accent-green);
}

.mining-badge {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.mining-badge.pro {
    background: linear-gradient(45deg, var(--cyber-purple), var(--cyber-cyan));
    color: white;
}

.mining-badge.optimized {
    background: rgba(0, 255, 255, 0.2);
    color: var(--cyber-cyan);
    border: 1px solid var(--cyber-cyan);
}

.contact-info {
    margin: 1.5rem 0;
}

.contact-item {
    display: flex;
    margin-bottom: 0.5rem;
}

.contact-label {
    min-width: 80px;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.contact-value {
    color: var(--text-secondary);
    font-weight: 500;
}

.services-info {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.services-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.services-list {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.mining-stats {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--glass-border);
}

.stat-row {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.stat-col {
    flex: 1;
    text-align: center;
}

.stat-col .stat-value {
    font-size: 1.5rem;
    color: var(--cyber-cyan);
    font-weight: 700;
}

.stat-col .stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
}

.validators-actions {
    text-align: center;
}

.action-btn {
    padding: 1rem 2rem;
    margin: 0 0.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    color: white;
}

.action-btn.secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--glass-border);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

/* Loading Indicators */
.loading-indicator {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 255, 255, 0.3);
    border-top: 3px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Explorer Section */
.explorer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.explorer-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.refresh-btn {
    background: none;
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.refresh-btn:hover {
    color: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
}

.block-list {
    space-y: 1rem;
}

.block-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.block-number {
    font-weight: 700;
    color: var(--cyber-cyan);
    font-family: var(--font-heading);
}

.block-hash {
    font-family: monospace;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.block-time,
.block-txs {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.network-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.network-stat {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.network-stat .stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.network-stat .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--cyber-cyan);
}

/* Dashboard Section */
.dashboard-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    text-align: center;
    padding: 2rem;
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.card-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.card-content p {
    margin-bottom: 1.5rem;
    color: var(--text-muted);
}

.card-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.card-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

.dashboard-notice {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    backdrop-filter: blur(10px);
}

.notice-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.notice-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.notice-content p {
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

.notice-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.notice-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.notice-btn.primary {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    color: white;
}

.notice-btn.secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--glass-border);
}

.notice-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

/* Technology Section */
.technology-section {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.02) 0%, rgba(0, 255, 255, 0.02) 100%);
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.tech-card {
    text-align: center;
    padding: 2rem;
}

.tech-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.tech-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.tech-card p {
    color: var(--text-muted);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(26, 26, 26, 0.9) 100%);
    border-top: 1px solid var(--glass-border);
    padding: 3rem 0 1rem;
    margin-top: 5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-description {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.footer-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-red);
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: var(--accent-green);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.footer-section a {
    display: block;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.footer-section a:hover {
    color: var(--cyber-cyan);
}

.footer-stat {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid var(--glass-border);
    color: var(--text-muted);
    font-size: 0.8rem;
}

.footer-meta {
    display: flex;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu,
    .nav-auth {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        gap: 2rem;
    }

    .validators-grid,
    .explorer-grid,
    .dashboard-preview,
    .tech-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-links {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stat-row {
        flex-direction: column;
        gap: 1rem;
    }

    .network-stats {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .glass-card,
    .validator-card,
    .explorer-card,
    .dashboard-card,
    .tech-card {
        padding: 1.5rem;
    }
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Additional responsive fixes */
@media (max-width: 1024px) {
    .validators-grid {
        grid-template-columns: 1fr;
    }

    .explorer-grid {
        grid-template-columns: 1fr;
    }
}

/* Performance optimizations */
.hero-background,
.hero-grid {
    will-change: transform;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .hero-grid {
        animation: none;
    }
}

/* Focus states for accessibility */
button:focus,
a:focus,
input:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .navbar,
    .footer,
    .hero-background {
        display: none;
    }

    body {
        background: white;
        color: black;
    }
}
