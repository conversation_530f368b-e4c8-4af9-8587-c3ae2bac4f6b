{% extends "base.html" %}

{% block title %}Tokens - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">All Tokens</h2>
    </div>
    
    <div class="card" style="margin-bottom: 2rem;">
        <p>Onnyx tokens are linked to identities, allowing for a more transparent and accountable token ecosystem. Each token has a creator identity and can be minted, transferred, and burned according to its properties.</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Token ID</th>
                <th>Name</th>
                <th>Symbol</th>
                <th>Creator</th>
                <th>Type</th>
                <th>Supply</th>
            </tr>
        </thead>
        <tbody>
            {% for token in tokens %}
            <tr>
                <td><a href="{{ url_for('token_detail', token_id=token.id) }}">{{ token.id[:10] }}...</a></td>
                <td>{{ token.name }}</td>
                <td>{{ token.symbol }}</td>
                <td>
                    {% if token.creator %}
                    <a href="{{ url_for('identity_detail', identity_id=token.creator) }}">{{ token.creator[:10] }}...</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>{{ token.type }}</td>
                <td>{{ token.supply }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="6" style="text-align: center;">No tokens found</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
