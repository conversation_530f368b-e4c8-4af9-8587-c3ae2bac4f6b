#!/usr/bin/env python3
"""
ONNYX Production Mining System Test
Tests mining operations for all 3 production validators.
"""

import sqlite3
import time
import json
from blockchain.consensus.miner import BlockMiner

def get_production_validators():
    """Get all production validators from database."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.sela_id, s.name, s.category, m.mining_tier, m.mining_power,
                   m.mining_id, s.identity_id, i.name as founder_name
            FROM selas s
            JOIN mining m ON s.sela_id = m.sela_id
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.status = 'active'
            ORDER BY m.mining_power DESC
        """)

        validators = cursor.fetchall()
        conn.close()
        return validators

    except Exception as e:
        print(f"Error getting validators: {e}")
        return []

def test_individual_mining(validator_info):
    """Test mining for a specific validator."""
    sela_id, name, category, mining_tier, mining_power, mining_id, identity_id, founder_name = validator_info

    print(f"\n🔧 Testing Mining for {name}")
    print(f"   Founder: {founder_name}")
    print(f"   Mining Tier: {mining_tier}")
    print(f"   Mining Power: {mining_power}x")
    print(f"   Sela ID: {sela_id}")
    print(f"   Identity ID: {identity_id}")

    try:
        # Initialize miner
        miner = BlockMiner()

        # Get initial stats
        initial_stats = miner.get_mining_stats()
        initial_height = initial_stats["chain_height"]

        print(f"   Initial Chain Height: {initial_height}")
        print(f"   Initial Latest Hash: {initial_stats['latest_block_hash'][:16]}...")

        # Test block creation (without actually mining to avoid conflicts)
        print(f"   🔨 Testing block creation...")

        # Get latest block to verify structure
        latest_block = miner.get_latest_block()
        print(f"   ✅ Latest block retrieved successfully")
        print(f"      Block Index: {latest_block['index']}")
        print(f"      Block Hash: {latest_block['hash'][:16]}...")
        print(f"      Previous Hash: {latest_block['previous_hash'][:16]}...")

        # Test coinbase transaction creation
        coinbase = miner.create_coinbase_tx(identity_id)
        print(f"   ✅ Coinbase transaction created")
        print(f"      Reward: {coinbase['amount']} {coinbase['token_id']}")
        print(f"      Recipient: {coinbase['to']}")

        print(f"   ✅ {name} mining test PASSED")
        return True

    except Exception as e:
        print(f"   ❌ {name} mining test FAILED: {e}")
        return False

def test_actual_block_mining():
    """Test actual block mining with the first validator."""
    print(f"\n⛏️ TESTING ACTUAL BLOCK MINING")
    print("=" * 50)

    validators = get_production_validators()
    if not validators:
        print("❌ No validators found for mining test")
        return False

    # Use the first validator (ONNYX with highest mining power)
    validator = validators[0]
    sela_id, name, category, mining_tier, mining_power, mining_id, identity_id, founder_name = validator

    print(f"Mining with: {name} ({founder_name})")
    print(f"Mining Power: {mining_power}x")

    try:
        # Initialize miner
        miner = BlockMiner()

        # Get initial state
        initial_stats = miner.get_mining_stats()
        initial_height = initial_stats["chain_height"]

        print(f"Initial chain height: {initial_height}")

        # Create a new block
        print("🔨 Creating new block...")
        block = miner.create_block(identity_id, sela_id)

        print(f"✅ Block created successfully!")
        print(f"   Block Index: {block['index']}")
        print(f"   Block Hash: {block['hash'][:16]}...")
        print(f"   Previous Hash: {block['previous_hash'][:16]}...")
        print(f"   Transactions: {len(block['transactions'])}")
        print(f"   Signed By: {block['signed_by']}")
        print(f"   Timestamp: {block['timestamp']}")

        # Verify the block was added to database
        final_stats = miner.get_mining_stats()
        final_height = final_stats["chain_height"]

        print(f"Final chain height: {final_height}")

        if final_height > initial_height:
            print(f"✅ Block successfully added to blockchain!")
            print(f"   Chain height increased from {initial_height} to {final_height}")
            return True
        else:
            print(f"⚠️ Block created but chain height unchanged")
            return False

    except Exception as e:
        print(f"❌ Block mining failed: {e}")
        return False

def verify_mining_database_integration():
    """Verify mining system integrates properly with production database."""
    print(f"\n🔍 VERIFYING DATABASE INTEGRATION")
    print("=" * 40)

    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()

        # Check blocks table
        cursor.execute("SELECT COUNT(*) FROM blocks")
        block_count = cursor.fetchone()[0]
        print(f"✅ Blocks in database: {block_count}")

        # Check latest block
        cursor.execute("SELECT block_hash, block_number, timestamp FROM blocks ORDER BY block_number DESC LIMIT 1")
        latest = cursor.fetchone()
        if latest:
            print(f"✅ Latest block: #{latest[1]} - {latest[0][:16]}...")
            print(f"   Timestamp: {latest[2]}")

        # Check mining records
        cursor.execute("SELECT COUNT(*) FROM mining")
        mining_count = cursor.fetchone()[0]
        print(f"✅ Mining records: {mining_count}")

        # Check if tokens table exists and has data
        try:
            cursor.execute("SELECT COUNT(*) FROM tokens")
            token_count = cursor.fetchone()[0]
            print(f"✅ Token records: {token_count}")
        except:
            print(f"ℹ️ Tokens table not found (will be created when rewards are distributed)")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ Database integration check failed: {e}")
        return False

def main():
    """Run comprehensive production mining tests."""
    print("🚀 ONNYX PRODUCTION MINING SYSTEM TEST")
    print("=" * 60)
    print("Testing mining operations for all production validators")
    print()

    # Get validators
    validators = get_production_validators()

    if not validators:
        print("❌ No production validators found")
        return False

    print(f"✅ Found {len(validators)} production validators")

    # Test individual mining for each validator
    print(f"\n🔧 INDIVIDUAL MINING TESTS")
    print("=" * 40)

    successful_tests = 0
    for validator in validators:
        if test_individual_mining(validator):
            successful_tests += 1

    print(f"\n📊 Individual Test Results: {successful_tests}/{len(validators)} passed")

    # Test actual block mining
    if successful_tests == len(validators):
        mining_success = test_actual_block_mining()
    else:
        print("⚠️ Skipping actual mining test due to individual test failures")
        mining_success = False

    # Verify database integration
    db_integration = verify_mining_database_integration()

    # Final results
    print(f"\n📋 FINAL TEST RESULTS")
    print("=" * 30)
    print(f"✅ Individual Mining Tests: {successful_tests}/{len(validators)}")
    print(f"{'✅' if mining_success else '❌'} Actual Block Mining: {'PASSED' if mining_success else 'FAILED'}")
    print(f"{'✅' if db_integration else '❌'} Database Integration: {'PASSED' if db_integration else 'FAILED'}")

    overall_success = (successful_tests == len(validators)) and mining_success and db_integration

    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")

    if overall_success:
        print("🎉 ONNYX production mining system is fully operational!")
        print("⛏️ All validators can successfully mine blocks")
        print("🔗 Database integration is working correctly")
        print("🚀 Ready for Phase 1 production launch!")
    else:
        print("⚠️ Mining system needs attention before production deployment")

    return overall_success

if __name__ == "__main__":
    main()
