# src/db/schema/identity.py

from src.db.manager import db_manager

def create_identity_tables():
    """
    Create the tables for the identity system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Create identities table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS identities (
        identity_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        public_key TEXT NOT NULL,
        nation TEXT,
        metadata TEXT,
        created_at INTEGER NOT NULL
    )
    ''')
    
    # Create badges table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS badges (
        badge TEXT PRIMARY KEY,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        requirements TEXT
    )
    ''')
    
    # Create titles table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS titles (
        title TEXT PRIMARY KEY,
        description TEXT NOT NULL,
        requirements TEXT
    )
    ''')
    
    # Create identity_badges table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS identity_badges (
        identity_id TEXT NOT NULL,
        badge TEXT NOT NULL,
        awarded_at INTEGER NOT NULL,
        PRIMARY KEY (identity_id, badge),
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (badge) REFERENCES badges (badge) ON DELETE CASCADE
    )
    ''')
    
    # Create identity_titles table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS identity_titles (
        identity_id TEXT NOT NULL,
        title TEXT NOT NULL,
        awarded_at INTEGER NOT NULL,
        PRIMARY KEY (identity_id, title),
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (title) REFERENCES titles (title) ON DELETE CASCADE
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_identities_name ON identities (name)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_identities_nation ON identities (nation)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_badges_type ON badges (type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_identity_badges_badge ON identity_badges (badge)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_identity_titles_title ON identity_titles (title)')
    
    conn.commit()
