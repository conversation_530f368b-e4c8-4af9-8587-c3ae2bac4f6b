{% extends "base.html" %}

{% block title %}Identity Verification Portal - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative flex items-center justify-center py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <!-- Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Identity Verification</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Choose your path to join the most secure blockchain network for verified business operations
            </p>
        </div>

        <!-- Genesis Identity Status (set by backend) -->

        <div class="grid grid-cols-1 {% if not genesis_exists %}lg:grid-cols-3{% else %}lg:grid-cols-2{% endif %} gap-8">

            <!-- Genesis Identity Option (only if none exists) -->
            {% if not genesis_exists %}
            <div class="card group hover:scale-105 transition-all duration-500 border border-cyber-cyan/40 bg-cyber-cyan/5">
                <div class="card-header text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan via-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg group-hover:shadow-cyber-cyan/50 transition-all duration-300">
                        <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <h2 class="card-title text-cyber-cyan">🌟 Genesis Identity</h2>
                    <p class="card-subtitle">Create the foundational Platform Founder identity</p>
                </div>

                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Platform Founder privileges</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Genesis Block #0 creation</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Network administration access</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Master cryptographic authority</span>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <a href="{{ url_for('auth.register_genesis') }}" class="btn btn-primary w-full">
                        <span class="text-lg">🌟</span>
                        <span>Create Genesis</span>
                    </a>
                    <p class="text-xs text-cyber-cyan mt-2 text-center font-mono uppercase tracking-wider">One-time opportunity</p>
                </div>
            </div>
            {% endif %}
            <!-- Register Identity -->
            <div class="card group hover:scale-105 transition-all duration-500">
                <div class="card-header text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg group-hover:shadow-cyber-cyan/40 transition-all duration-300">
                        <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h2 class="card-title text-cyber-cyan">Digital Identity</h2>
                    <p class="card-subtitle">Create your quantum-resistant cryptographic identity for secure business interactions</p>
                </div>

                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">ECDSA cryptographic key pair generation</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Quantum-resistant security protocols</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Full platform access and privileges</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Etzem trust score initialization</span>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <a href="{{ url_for('auth.register_identity') }}" class="btn btn-primary w-full">
                        <span class="text-lg">🔐</span>
                        <span>Verify Identity</span>
                    </a>
                    <p class="text-xs text-text-muted mt-2 text-center font-mono uppercase tracking-wider">Required for all platform activities</p>
                </div>
            </div>

            <!-- Business Validator Registration -->
            <div class="card group hover:scale-105 transition-all duration-500">
                <div class="card-header text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-cyber-purple to-green-400 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg group-hover:shadow-cyber-purple/40 transition-all duration-300">
                        <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                        </svg>
                    </div>
                    <h2 class="card-title text-cyber-purple">Business Validator</h2>
                    <p class="card-subtitle">Register your business as a verified Sela validator on the blockchain network</p>
                </div>

                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-purple to-green-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Verified business identity on blockchain</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-purple to-green-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Mining and validation privileges</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-purple to-green-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Mikvah token minting capabilities</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-5 h-5 bg-gradient-to-br from-cyber-purple to-green-400 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-3 h-3 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-text-secondary text-sm">Public validator profile and metrics</span>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    {% if current_user %}
                    <a href="{{ url_for('auth.register_sela') }}" class="btn btn-secondary w-full">
                        <span class="text-lg">🏢</span>
                        <span>Register Validator</span>
                    </a>
                    <p class="text-xs text-green-400 mt-2 text-center font-mono uppercase tracking-wider">Ready to register your business</p>
                    {% else %}
                    <div class="btn btn-secondary w-full opacity-50 cursor-not-allowed">
                        <span class="text-lg">🏢</span>
                        <span>Register Validator</span>
                    </div>
                    <p class="text-xs text-red-400 mt-2 text-center font-mono uppercase tracking-wider">Requires identity registration first</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Registration Process Flow -->
        <div class="mt-20 glass-card p-12 neuro-card">
            <h3 class="text-4xl font-orbitron font-bold text-center mb-12">
                <span class="hologram-text">Verification Protocol</span>
            </h3>

            <div class="flex flex-col lg:flex-row items-center justify-center space-y-12 lg:space-y-0 lg:space-x-12">
                <!-- Step 1 -->
                <div class="flex flex-col items-center text-center group">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center font-orbitron font-bold text-xl mb-6 group-hover:shadow-lg group-hover:shadow-cyber-cyan/40 transition-all duration-300">
                        <span class="text-onyx-black">01</span>
                    </div>
                    <h4 class="font-orbitron font-bold text-cyber-cyan mb-4 text-xl">Initialize Identity</h4>
                    <p class="text-gray-400 max-w-xs leading-relaxed">
                        Generate quantum-resistant cryptographic identity with ECDSA key pairs
                    </p>
                </div>

                <!-- Arrow -->
                <div class="hidden lg:block">
                    <svg class="w-12 h-12 text-cyber-cyan opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>

                <!-- Step 2 -->
                <div class="flex flex-col items-center text-center group">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-green-400 rounded-xl flex items-center justify-center font-orbitron font-bold text-xl mb-6 group-hover:shadow-lg group-hover:shadow-cyber-purple/40 transition-all duration-300">
                        <span class="text-onyx-black">02</span>
                    </div>
                    <h4 class="font-orbitron font-bold text-cyber-purple mb-4 text-xl">Deploy Validator</h4>
                    <p class="text-gray-400 max-w-xs leading-relaxed">
                        Register business as verified Sela validator on the blockchain network
                    </p>
                </div>

                <!-- Arrow -->
                <div class="hidden lg:block">
                    <svg class="w-12 h-12 text-cyber-purple opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>

                <!-- Step 3 -->
                <div class="flex flex-col items-center text-center group">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-green-400 rounded-xl flex items-center justify-center font-orbitron font-bold text-xl mb-6 group-hover:shadow-lg group-hover:shadow-cyber-blue/40 transition-all duration-300">
                        <span class="text-onyx-black">03</span>
                    </div>
                    <h4 class="font-orbitron font-bold text-cyber-blue mb-4 text-xl">Activate Network</h4>
                    <p class="text-gray-400 max-w-xs leading-relaxed">
                        Begin mining blocks, minting tokens, and building Etzem trust scores
                    </p>
                </div>
            </div>
        </div>

        <!-- Access Portal -->
        <div class="mt-16 text-center">
            <p class="text-gray-400 text-lg mb-4">
                Already verified on the network?
            </p>
            <a href="{{ url_for('auth.login') }}" class="glass-button px-8 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                🚀 Access Portal
            </a>
        </div>
    </div>
</div>
{% endblock %}
