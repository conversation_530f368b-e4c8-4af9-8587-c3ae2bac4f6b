/* Onnyx Blockchain Styles */

/* Variables */
:root {
    --primary-color: #8a2be2; /* Purple */
    --secondary-color: #ffd700; /* Gold */
    --dark-bg: #111;
    --darker-bg: #0a0a0a;
    --light-text: #f8f9fa;
    --card-bg: #222;
    --border-color: #444;
}

/* Animation effects */
@keyframes glow {
    0% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--secondary-color);
    }
    100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
}

.glow-effect {
    animation: glow 3s infinite;
}

/* Base Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--dark-bg);
    color: var(--light-text);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header and Navigation */
header {
    background-color: var(--darker-bg);
    padding: 10px 0;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo {
    font-size: 28px;
    font-weight: bold;
    color: var(--secondary-color);
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    color: var(--light-text);
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 4px;
    transition: all 0.3s;
}

.nav-links a:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    text-decoration: none;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
}

.search-form {
    display: flex;
    gap: 10px;
}

.search-form input {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-bg);
    color: var(--light-text);
}

.search-form button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: var(--light-text);
    cursor: pointer;
    transition: all 0.3s;
}

.search-form button:hover {
    background-color: #7b27c7;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
}

/* Typography */
h1, h2, h3 {
    color: var(--secondary-color);
    margin-top: 0;
}

h1 {
    font-size: 28px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

h2 {
    font-size: 22px;
    margin-bottom: 15px;
}

h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border: 1px solid var(--border-color);
}

th, td {
    padding: 12px;
    border: 1px solid var(--border-color);
    text-align: left;
}

th {
    background-color: var(--darker-bg);
    color: var(--secondary-color);
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: rgba(34, 34, 34, 0.7);
}

tr:hover {
    background-color: var(--card-bg);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
}

.pagination a {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-bg);
    transition: all 0.3s;
}

.pagination a:hover {
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
}

.pagination span {
    color: #aaa;
}

/* Flash Messages */
.flash-messages {
    margin: 20px 0;
}

.flash-message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.flash-message.error {
    background-color: #dc3545;
    color: white;
}

.flash-message.success {
    background-color: #28a745;
    color: white;
}

.flash-message.info {
    background-color: #17a2b8;
    color: white;
}

/* Code and Pre */
pre {
    background-color: var(--darker-bg);
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', Courier, monospace;
    border: 1px solid var(--border-color);
}

code {
    font-family: 'Courier New', Courier, monospace;
    background-color: var(--darker-bg);
    padding: 2px 5px;
    border-radius: 3px;
    color: var(--secondary-color);
}

/* Buttons */
.button {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.button:hover {
    background-color: #7b27c7;
    box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
    text-decoration: none;
}

.button.secondary {
    background-color: var(--card-bg);
    color: var(--light-text);
    border: 1px solid var(--border-color);
}

.button.secondary:hover {
    background-color: var(--darker-bg);
    border-color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--darker-bg);
    padding: 20px 0;
    margin-top: 40px;
    text-align: center;
    color: #aaa;
    font-size: 14px;
    border-top: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        gap: 15px;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .search-form {
        width: 100%;
    }

    .search-form input {
        flex-grow: 1;
    }

    table {
        display: block;
        overflow-x: auto;
    }

    .hero-section {
        padding: 30px 15px;
    }
}

/* Identity Creation Page */
.card-header.bg-dark {
    background-color: var(--darker-bg) !important;
}

.text-gold {
    color: var(--secondary-color) !important;
}

.form-text {
    color: #aaa !important;
}

.btn-highlight {
    display: inline-block;
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s;
}

.btn-highlight:hover {
    background-color: var(--secondary-color);
    color: var(--darker-bg);
    text-decoration: none;
}

.hero-section {
    background-color: var(--darker-bg);
    padding: 60px 20px;
    border-radius: 8px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}
