#!/usr/bin/env python3
"""
Test identity functionality.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_identity_creation():
    """Test identity creation."""
    print("Testing identity creation...")

    try:
        from shared.models.identity import Identity

        # Create a test identity
        identity = Identity.create(
            name="Test User",
            email="<EMAIL>",
            public_key="test_public_key_123"
        )

        print(f"✅ Identity created successfully!")
        print(f"   ID: {identity.identity_id}")
        print(f"   Name: {identity.name}")
        print(f"   Email: {identity.email}")
        print(f"   Status: {identity.status}")

        return True

    except Exception as e:
        print(f"❌ Identity creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_identity_lookup():
    """Test identity lookup."""
    print("\nTesting identity lookup...")

    try:
        from shared.models.identity import Identity

        # Try to find an identity
        identity = Identity.get_by_id("test_user_123")

        if identity:
            print(f"✅ Identity found!")
            print(f"   ID: {identity.identity_id}")
            print(f"   Name: {identity.name}")
        else:
            print(f"⚠️ No identity found (expected if database is empty)")

        return True

    except Exception as e:
        print(f"❌ Identity lookup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_identity_registry():
    """Test identity registry functionality."""
    print("\nTesting identity registry...")

    try:
        from identity.registry import IdentityRegistry

        registry = IdentityRegistry()

        # Test registry methods
        print(f"✅ Identity registry created!")

        return True

    except Exception as e:
        print(f"❌ Identity registry test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Identity Functionality")
    print("=" * 40)

    success = True

    if not test_identity_creation():
        success = False

    if not test_identity_lookup():
        success = False

    if not test_identity_registry():
        success = False

    print("\n" + "=" * 40)
    if success:
        print("🎉 All identity tests passed!")
    else:
        print("⚠️ Some identity tests failed.")

    return success

if __name__ == "__main__":
    main()
