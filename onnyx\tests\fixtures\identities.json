{"test_identity": {"identity_id": "test_identity", "name": "Test Identity", "public_key": "04a5c1a0d0b5e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0", "metadata": {"type": "individual", "description": "Test identity for unit tests"}, "created_at": 1620000000, "updated_at": 1620000000}, "test_identity_2": {"identity_id": "test_identity_2", "name": "Test Identity 2", "public_key": "04b5c1a0d0b5e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0", "metadata": {"type": "individual", "description": "Second test identity for unit tests"}, "created_at": 1620000001, "updated_at": 1620000001}, "test_sela": {"identity_id": "test_sela", "name": "Test Sela", "public_key": "04c5c1a0d0b5e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0", "metadata": {"type": "business", "description": "Test Sela business for unit tests", "sela_id": "test_sela"}, "created_at": 1620000002, "updated_at": 1620000002}}