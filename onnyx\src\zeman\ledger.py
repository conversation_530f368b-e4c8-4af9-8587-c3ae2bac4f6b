# src/zeman/ledger.py

import time
import uuid
from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.identity.registry import IdentityRegistry

class ZemanLedger:
    """
    Ledger for tracking Zeman time credits.
    """
    
    def __init__(self, identity_registry: Optional[IdentityRegistry] = None):
        """
        Initialize the ZemanLedger.
        
        Args:
            identity_registry: The identity registry
        """
        self.identities = identity_registry or IdentityRegistry()
        self.db = db_manager.get_connection()
    
    def add_hours(self, identity_id: str, hours: float, description: str = "", source: str = "manual") -> Dict[str, Any]:
        """
        Add Zeman hours for an identity.
        
        Args:
            identity_id: The identity ID
            hours: The number of hours to add
            description: A description of the work
            source: The source of the hours (e.g., "contract", "manual", "system")
        
        Returns:
            The newly created Zeman entry
        
        Raises:
            Exception: If the identity does not exist or the hours are invalid
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Validate the hours
        if hours <= 0:
            raise Exception("Hours must be greater than zero")
        
        # Generate a unique entry ID
        entry_id = str(uuid.uuid4())
        
        # Create the Zeman entry
        entry = {
            "entry_id": entry_id,
            "identity_id": identity_id,
            "hours": hours,
            "description": description,
            "source": source,
            "created_at": int(time.time()),
            "redeemed": False,
            "redeemed_at": None,
            "redemption_type": None
        }
        
        # Save to database
        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT INTO zeman_entries
            (entry_id, identity_id, hours, description, source, created_at, redeemed, redeemed_at, redemption_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                entry["entry_id"],
                entry["identity_id"],
                entry["hours"],
                entry["description"],
                entry["source"],
                entry["created_at"],
                entry["redeemed"],
                entry["redeemed_at"],
                entry["redemption_type"]
            )
        )
        self.db.commit()
        
        return entry
    
    def get_total_hours(self, identity_id: str, include_redeemed: bool = False) -> float:
        """
        Get the total Zeman hours for an identity.
        
        Args:
            identity_id: The identity ID
            include_redeemed: Whether to include redeemed hours
        
        Returns:
            The total hours
        """
        cursor = self.db.cursor()
        
        if include_redeemed:
            cursor.execute(
                "SELECT SUM(hours) FROM zeman_entries WHERE identity_id = ?",
                (identity_id,)
            )
        else:
            cursor.execute(
                "SELECT SUM(hours) FROM zeman_entries WHERE identity_id = ? AND redeemed = 0",
                (identity_id,)
            )
        
        result = cursor.fetchone()
        
        if result[0] is None:
            return 0.0
        
        return float(result[0])
    
    def get_entries(self, identity_id: str, include_redeemed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all Zeman entries for an identity.
        
        Args:
            identity_id: The identity ID
            include_redeemed: Whether to include redeemed entries
        
        Returns:
            A list of Zeman entries
        """
        cursor = self.db.cursor()
        
        if include_redeemed:
            cursor.execute(
                "SELECT * FROM zeman_entries WHERE identity_id = ? ORDER BY created_at DESC",
                (identity_id,)
            )
        else:
            cursor.execute(
                "SELECT * FROM zeman_entries WHERE identity_id = ? AND redeemed = 0 ORDER BY created_at DESC",
                (identity_id,)
            )
        
        rows = cursor.fetchall()
        
        return [{
            "entry_id": row["entry_id"],
            "identity_id": row["identity_id"],
            "hours": row["hours"],
            "description": row["description"],
            "source": row["source"],
            "created_at": row["created_at"],
            "redeemed": bool(row["redeemed"]),
            "redeemed_at": row["redeemed_at"],
            "redemption_type": row["redemption_type"]
        } for row in rows]
    
    def mark_as_redeemed(self, entry_id: str, redemption_type: str) -> Optional[Dict[str, Any]]:
        """
        Mark a Zeman entry as redeemed.
        
        Args:
            entry_id: The entry ID
            redemption_type: The type of redemption (e.g., "minting", "fee_discount", "reputation", "token")
        
        Returns:
            The updated entry or None if the entry does not exist
        
        Raises:
            Exception: If the entry is already redeemed
        """
        # Get the entry
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM zeman_entries WHERE entry_id = ?",
            (entry_id,)
        )
        row = cursor.fetchone()
        
        if not row:
            return None
        
        # Check if the entry is already redeemed
        if row["redeemed"]:
            raise Exception(f"Zeman entry with ID '{entry_id}' is already redeemed")
        
        # Update the entry
        cursor.execute(
            """
            UPDATE zeman_entries
            SET redeemed = 1, redeemed_at = ?, redemption_type = ?
            WHERE entry_id = ?
            """,
            (
                int(time.time()),
                redemption_type,
                entry_id
            )
        )
        self.db.commit()
        
        # Get the updated entry
        cursor.execute(
            "SELECT * FROM zeman_entries WHERE entry_id = ?",
            (entry_id,)
        )
        row = cursor.fetchone()
        
        return {
            "entry_id": row["entry_id"],
            "identity_id": row["identity_id"],
            "hours": row["hours"],
            "description": row["description"],
            "source": row["source"],
            "created_at": row["created_at"],
            "redeemed": bool(row["redeemed"]),
            "redeemed_at": row["redeemed_at"],
            "redemption_type": row["redemption_type"]
        }
