"""
Onnyx Miner CLI Module

This module provides the command-line interface for the Onnyx Miner.
"""

import os
import sys
import json
import logging
import typer
from typing import Dict, Any, List, Optional

from .config import get_config, save_config, create_default_config
from .keys import generate_identity_keys, load_identity_keys
from .wallet import load_wallet, get_balance, transfer_tokens, stake_tokens, get_transaction_history
from .sync import get_chain_info, get_latest_block, sync_chain, get_peers, add_peer
from .miner import mine_block, start_auto_mining, stop_auto_mining, get_mining_status
from .api import register_identity, register_sela, link_identity_to_sela, create_token, propose_scroll, vote_on_scroll, get_scrolls
from .gui.panel import start_gui

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("onnyx_miner.log")
    ]
)
logger = logging.getLogger("onnyx_miner.cli")

# Create the Typer app
app = typer.Typer()

@app.command()
def setup(
    identity_id: str = typer.Option(..., "--identity-id", help="The identity ID"),
    name: str = typer.Option(..., "--name", help="The identity name"),
    sela_id: str = typer.Option(..., "--sela-id", help="The Sela ID"),
    sela_name: str = typer.Option(..., "--sela-name", help="The Sela name"),
    sela_type: str = typer.Option("BUSINESS", "--sela-type", help="The Sela type"),
    token_type: str = typer.Option("", "--token-type", help="The token type"),
    api_url: str = typer.Option("http://localhost:8000", "--api-url", help="The API URL"),
    config_path: Optional[str] = typer.Option(None, "--config", help="The path to the configuration file")
):
    """Set up the Onnyx Miner."""
    try:
        # Generate keys
        public_key = generate_identity_keys(identity_id)
        
        # Register the identity
        identity = register_identity(identity_id, name, public_key)
        
        # Register the Sela
        sela = register_sela(sela_id, sela_name, identity_id, sela_type, token_type)
        
        # Link the identity to the Sela
        link_identity_to_sela(identity_id, sela_id, "FOUNDER")
        
        # Create the configuration
        config = create_default_config(identity_id, name, sela_id, sela_name, config_path)
        
        # Update the API URL
        config["node"]["api_url"] = api_url
        save_config(config, config_path)
        
        typer.echo(f"Set up Onnyx Miner for {name} ({identity_id}) with Sela {sela_name} ({sela_id})")
    except Exception as e:
        typer.echo(f"Error setting up Onnyx Miner: {str(e)}")
        raise typer.Exit(1)

@app.command()
def sync():
    """Sync the local chain with the network."""
    try:
        # Sync the chain
        success = sync_chain()
        
        if success:
            typer.echo("Synced chain with network")
        else:
            typer.echo("Failed to sync chain with network")
            raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error syncing chain: {str(e)}")
        raise typer.Exit(1)

@app.command()
def mine(
    force: bool = typer.Option(False, "--force", help="Force mining even if not the current validator")
):
    """Mine a new block."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            typer.echo("No identity ID configured")
            raise typer.Exit(1)
        
        # Check if we're the current validator
        if not force:
            mining_status = get_mining_status()
            
            if not mining_status.get("is_valid_proposer", False):
                typer.echo(f"Not the current validator. Current validator: {mining_status.get('next_validator', 'unknown')}")
                raise typer.Exit(1)
        
        # Mine a block
        block = mine_block(identity_id)
        
        if block:
            typer.echo(f"Mined block {block.get('index')}")
        else:
            typer.echo("Failed to mine block")
            raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error mining block: {str(e)}")
        raise typer.Exit(1)

@app.command()
def auto_mine(
    enable: bool = typer.Option(True, "--enable/--disable", help="Enable or disable automatic mining")
):
    """Enable or disable automatic mining."""
    try:
        if enable:
            start_auto_mining()
            typer.echo("Enabled automatic mining")
        else:
            stop_auto_mining()
            typer.echo("Disabled automatic mining")
    except Exception as e:
        typer.echo(f"Error configuring automatic mining: {str(e)}")
        raise typer.Exit(1)

@app.command()
def wallet():
    """Show wallet information."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            typer.echo("No identity ID configured")
            raise typer.Exit(1)
        
        # Get the wallet
        wallet = load_wallet(identity_id)
        
        # Display the wallet information
        typer.echo(f"Identity: {wallet.get('name')} ({wallet.get('identity_id')})")
        typer.echo(f"Etzem Score: {wallet.get('etzem_score')}")
        typer.echo("Balances:")
        
        for token_id, amount in wallet.get("balances", {}).items():
            typer.echo(f"  {token_id}: {amount}")
        
        typer.echo("Badges:")
        
        for badge in wallet.get("badges", []):
            typer.echo(f"  {badge}")
        
        typer.echo("Stakes:")
        
        for stake in wallet.get("stakes", []):
            typer.echo(f"  {stake.get('token_id')}: {stake.get('amount')} (locked until {stake.get('locked_until')})")
    except Exception as e:
        typer.echo(f"Error showing wallet: {str(e)}")
        raise typer.Exit(1)

@app.command()
def transfer(
    to_id: str = typer.Option(..., "--to", help="The recipient's identity ID"),
    token_id: str = typer.Option(..., "--token", help="The token ID"),
    amount: float = typer.Option(..., "--amount", help="The amount to transfer")
):
    """Transfer tokens to another identity."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            typer.echo("No identity ID configured")
            raise typer.Exit(1)
        
        # Transfer the tokens
        success = transfer_tokens(identity_id, to_id, token_id, amount)
        
        if success:
            typer.echo(f"Transferred {amount} {token_id} to {to_id}")
        else:
            typer.echo("Failed to transfer tokens")
            raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error transferring tokens: {str(e)}")
        raise typer.Exit(1)

@app.command()
def stake(
    token_id: str = typer.Option(..., "--token", help="The token ID"),
    amount: float = typer.Option(..., "--amount", help="The amount to stake"),
    duration: int = typer.Option(2592000, "--duration", help="The duration of the stake in seconds")
):
    """Stake tokens."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            typer.echo("No identity ID configured")
            raise typer.Exit(1)
        
        # Stake the tokens
        success = stake_tokens(identity_id, token_id, amount, duration)
        
        if success:
            typer.echo(f"Staked {amount} {token_id} for {duration} seconds")
        else:
            typer.echo("Failed to stake tokens")
            raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error staking tokens: {str(e)}")
        raise typer.Exit(1)

@app.command()
def propose(
    title: str = typer.Option(..., "--title", help="The title of the scroll"),
    description: str = typer.Option(..., "--description", help="The description of the scroll"),
    category: str = typer.Option("economic", "--category", help="The category of the scroll"),
    param: Optional[str] = typer.Option(None, "--param", help="The parameter to change"),
    value: Optional[str] = typer.Option(None, "--value", help="The new value for the parameter")
):
    """Propose a new Voice Scroll."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            typer.echo("No identity ID configured")
            raise typer.Exit(1)
        
        # Create the effect
        effect = None
        
        if param and value:
            effect = {
                "param": param,
                "value": value
            }
        
        # Propose the scroll
        scroll = propose_scroll(identity_id, title, description, category, effect)
        
        typer.echo(f"Proposed scroll: {scroll.get('id')}")
        typer.echo(f"Title: {scroll.get('title')}")
        typer.echo(f"Description: {scroll.get('description')}")
        typer.echo(f"Category: {scroll.get('category')}")
        
        if effect:
            typer.echo(f"Effect: {param} = {value}")
    except Exception as e:
        typer.echo(f"Error proposing scroll: {str(e)}")
        raise typer.Exit(1)

@app.command()
def vote(
    scroll_id: str = typer.Option(..., "--scroll-id", help="The scroll ID"),
    decision: str = typer.Option(..., "--decision", help="The decision (yes, no, or abstain)")
):
    """Vote on a Voice Scroll."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            typer.echo("No identity ID configured")
            raise typer.Exit(1)
        
        # Vote on the scroll
        scroll = vote_on_scroll(identity_id, scroll_id, decision)
        
        typer.echo(f"Voted {decision} on scroll {scroll_id}")
    except Exception as e:
        typer.echo(f"Error voting on scroll: {str(e)}")
        raise typer.Exit(1)

@app.command()
def scrolls(
    status: Optional[str] = typer.Option(None, "--status", help="Filter by status"),
    category: Optional[str] = typer.Option(None, "--category", help="Filter by category")
):
    """List Voice Scrolls."""
    try:
        # Get the scrolls
        scrolls = get_scrolls(status, category)
        
        if not scrolls:
            typer.echo("No scrolls found")
            return
        
        for scroll in scrolls:
            typer.echo(f"ID: {scroll.get('id')}")
            typer.echo(f"Title: {scroll.get('title')}")
            typer.echo(f"Description: {scroll.get('description')}")
            typer.echo(f"Category: {scroll.get('category')}")
            typer.echo(f"Status: {scroll.get('status')}")
            typer.echo(f"Outcome: {scroll.get('outcome')}")
            typer.echo("")
    except Exception as e:
        typer.echo(f"Error listing scrolls: {str(e)}")
        raise typer.Exit(1)

@app.command()
def gui():
    """Start the GUI."""
    try:
        # Start the GUI
        start_gui()
    except Exception as e:
        typer.echo(f"Error starting GUI: {str(e)}")
        raise typer.Exit(1)

if __name__ == "__main__":
    app()
