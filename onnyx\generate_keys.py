#!/usr/bin/env python
# generate_keys.py

import argparse
import logging
import sys
import os
from src.node.crypto import generate_keys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("onnyx.generate_keys")

def main():
    """
    Generate ECDSA keys for a node.
    """
    parser = argparse.ArgumentParser(description="Generate ECDSA keys for an Onnyx node")
    parser.add_argument("--path", type=str, help="Path to save the keys")
    parser.add_argument("--force", action="store_true", help="Overwrite existing keys")
    
    args = parser.parse_args()
    
    # Set the path
    if args.path:
        keys_path = args.path
    else:
        # Use a default path in the data directory
        base_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(base_dir, "data")
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        keys_path = os.path.join(data_dir, "keys")
    
    # Check if keys already exist
    private_key_path = os.path.join(keys_path, "private.pem")
    public_key_path = os.path.join(keys_path, "public.pem")
    
    if os.path.exists(private_key_path) or os.path.exists(public_key_path):
        if not args.force:
            logger.error(f"Keys already exist at {keys_path}. Use --force to overwrite.")
            return
        logger.warning(f"Overwriting existing keys at {keys_path}")
    
    # Generate the keys
    try:
        private_key, public_key = generate_keys(keys_path)
        logger.info(f"Generated and saved keys to {keys_path}")
        
        # Print the public key
        logger.info(f"Public key: {public_key.to_string().hex()}")
    except Exception as e:
        logger.error(f"Error generating keys: {str(e)}")

if __name__ == "__main__":
    main()
