"""
Onnyx API

This module provides the FastAPI application for the Onnyx blockchain.
"""

import sys
import os
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.responses import HTMLResponse

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(__file__))

# Set up templates
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))

from api.routes.miner import miner_router
from api.routes.sela import router as sela_router
from api.routes.identity import router as identity_router
from api.routes.etzem import router as etzem_router
from api.routes.tokens import router as tokens_router
from api.routes.sela_miner import router as sela_miner_router
from api.routes.rotation import router as rotation_router
from api.routes.chain_parameters import router as chain_parameters_router
from api.routes.voice_scroll import router as voice_scroll_router
from api.routes.analytics import router as analytics_router

# Create FastAPI app
app = FastAPI(
    title="Onnyx Blockchain API",
    description="API for interacting with the Onnyx blockchain",
    version="0.1.0",
    docs_url=None,  # Disable default docs
    redoc_url=None  # Disable default redoc
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "static")), name="static")

# Include routers
app.include_router(miner_router, prefix="/api", tags=["Mining"])
app.include_router(sela_router, prefix="/api", tags=["Sela"])
app.include_router(identity_router, prefix="/api", tags=["Identity"])
app.include_router(etzem_router, prefix="/api", tags=["Etzem"])
app.include_router(tokens_router, prefix="/api", tags=["Tokens"])
app.include_router(sela_miner_router, prefix="/api", tags=["Sela Miner"])
app.include_router(rotation_router, prefix="/api", tags=["Validator Rotation"])
app.include_router(chain_parameters_router, prefix="/api", tags=["Chain Parameters"])
app.include_router(voice_scroll_router, prefix="/api", tags=["Governance"])
app.include_router(analytics_router, prefix="/api", tags=["Analytics"])

# Custom docs route
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    return templates.TemplateResponse(
        "custom_docs.html",
        {
            "request": request,
            "title": "Onnyx Blockchain API",
            "openapi_url": app.openapi_url,
            "swagger_ui_css": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
            "oauth2_redirect_url": app.swagger_ui_oauth2_redirect_url,
            "csrf_token": "",
        }
    )

@app.get("/redoc", include_in_schema=False)
async def redoc_html(request: Request):
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - ReDoc",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
    )

@app.get("/")
async def read_root():
    """
    Root endpoint - redirects to docs.

    Returns:
        RedirectResponse: Redirect to docs
    """
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/docs")

@app.get("/health")
def health_check():
    """
    Health check endpoint.

    Returns:
        Health status
    """
    return {
        "status": "healthy"
    }
