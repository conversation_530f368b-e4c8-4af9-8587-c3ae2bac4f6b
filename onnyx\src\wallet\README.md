# Onnyx Wallet

This module handles cryptographic key generation, address creation, and message signing.

## Features

- Uses ECDSA with secp256k1 (same as Bitcoin)
- Derives Onnyx addresses from public key (RIPEMD160(SHA256(pubkey)))
- Signs/Verifies messages for proof-of-ownership and secure actions

## Example

```bash
python cli.py wallet     # View keys and address
python cli.py sign       # Sign a message
python cli.py verify     # Verify a message
```
