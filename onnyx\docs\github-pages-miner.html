<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONNYX Web Miner - GitHub Pages</title>
    <style>
        /* Onyx Stone Theme for GitHub Pages */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #000000 100%);
            color: #eeeeee;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00ffff, #8a2be2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 2rem;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .mining-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 2s infinite;
        }

        .status-dot.active {
            background: #00ff88;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .mining-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: #00ffff;
        }

        .control-group input, .control-group select {
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #eeeeee;
            font-size: 1rem;
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .mining-button {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #00ffff, #8a2be2);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .mining-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
        }

        .mining-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00ffff;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #cccccc;
            font-size: 0.9rem;
        }

        .log-container {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
        }

        .log-entry.info { color: #00ffff; }
        .log-entry.success { color: #00ff88; }
        .log-entry.warning { color: #ffaa00; }
        .log-entry.error { color: #ff4444; }

        .identity-section {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .identity-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .info-label {
            font-size: 0.9rem;
            color: #cccccc;
        }

        .info-value {
            font-weight: 600;
            color: #eeeeee;
            word-break: break-all;
        }

        .warning-banner {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid rgba(255, 170, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .warning-banner h3 {
            color: #ffaa00;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .mining-controls {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">ONNYX</div>
            <div class="subtitle">Web-Based Mining Interface</div>
        </div>

        <!-- Warning Banner -->
        <div class="warning-banner">
            <h3>⚠️ GitHub Pages Limitations</h3>
            <p>This is a demonstration interface. Actual mining requires a backend server or local mining client.</p>
        </div>

        <!-- Identity Section -->
        <div class="glass-card">
            <h2 style="margin-bottom: 1rem; color: #8a2be2;">🔐 Mining Identity</h2>
            <div class="identity-section">
                <div class="identity-info" id="identityInfo">
                    <div class="info-item">
                        <div class="info-label">Identity ID</div>
                        <div class="info-value" id="identityId">Not loaded</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Name</div>
                        <div class="info-value" id="identityName">Not loaded</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value" id="identityEmail">Not loaded</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Public Key</div>
                        <div class="info-value" id="publicKey">Not loaded</div>
                    </div>
                </div>
                <div style="margin-top: 1rem;">
                    <input type="file" id="identityFile" accept=".json" style="margin-bottom: 1rem;">
                    <button onclick="loadIdentity()" class="mining-button" style="padding: 0.5rem 1rem; font-size: 0.9rem;">
                        Load Identity File
                    </button>
                </div>
            </div>
        </div>

        <!-- Mining Controls -->
        <div class="glass-card">
            <h2 style="margin-bottom: 1rem; color: #00ffff;">⚡ Mining Controls</h2>
            
            <div class="mining-status">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Disconnected</span>
                </div>
                <button class="mining-button" id="miningButton" onclick="toggleMining()" disabled>
                    Start Mining
                </button>
            </div>

            <div class="mining-controls">
                <div class="control-group">
                    <label for="poolUrl">Mining Pool URL</label>
                    <input type="url" id="poolUrl" placeholder="wss://pool.onnyx.world" value="ws://127.0.0.1:5000">
                </div>
                <div class="control-group">
                    <label for="miningTier">Mining Tier</label>
                    <select id="miningTier">
                        <option value="basic">Basic CPU (1x)</option>
                        <option value="optimized">ONNYX Optimized (3x)</option>
                        <option value="pro">ONNYX Pro (10x)</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="threads">CPU Threads</label>
                    <input type="number" id="threads" min="1" max="16" value="4">
                </div>
                <div class="control-group">
                    <label for="intensity">Mining Intensity</label>
                    <select id="intensity">
                        <option value="low">Low (25%)</option>
                        <option value="medium">Medium (50%)</option>
                        <option value="high">High (75%)</option>
                        <option value="max">Maximum (100%)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Mining Statistics -->
        <div class="glass-card">
            <h2 style="margin-bottom: 1rem; color: #00ff88;">📊 Mining Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="hashRate">0</div>
                    <div class="stat-label">Hash Rate (H/s)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="blocksFound">0</div>
                    <div class="stat-label">Blocks Found</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="onxEarned">0</div>
                    <div class="stat-label">ONX Earned</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="uptime">00:00:00</div>
                    <div class="stat-label">Mining Uptime</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="difficulty">1</div>
                    <div class="stat-label">Network Difficulty</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="networkHashRate">0</div>
                    <div class="stat-label">Network Hash Rate</div>
                </div>
            </div>
        </div>

        <!-- Mining Log -->
        <div class="glass-card">
            <h2 style="margin-bottom: 1rem; color: #ffaa00;">📝 Mining Log</h2>
            <div class="log-container" id="miningLog">
                <div class="log-entry info">[INFO] ONNYX Web Miner initialized</div>
                <div class="log-entry warning">[WARNING] GitHub Pages limitations apply</div>
                <div class="log-entry info">[INFO] Load your identity file to begin</div>
            </div>
        </div>
    </div>

    <script>
        // ONNYX Web Miner JavaScript
        let miningActive = false;
        let identity = null;
        let miningInterval = null;
        let startTime = null;
        let hashRate = 0;
        let blocksFound = 0;
        let onxEarned = 0;

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('miningLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function loadIdentity() {
            const fileInput = document.getElementById('identityFile');
            const file = fileInput.files[0];
            
            if (!file) {
                addLog('Please select an identity file', 'warning');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    identity = JSON.parse(e.target.result);
                    
                    document.getElementById('identityId').textContent = identity.identity_id || 'Unknown';
                    document.getElementById('identityName').textContent = identity.name || 'Unknown';
                    document.getElementById('identityEmail').textContent = identity.email || 'Unknown';
                    document.getElementById('publicKey').textContent = (identity.public_key || 'Unknown').substring(0, 32) + '...';
                    
                    document.getElementById('miningButton').disabled = false;
                    addLog(`Identity loaded: ${identity.name}`, 'success');
                    
                } catch (error) {
                    addLog('Error parsing identity file: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        function toggleMining() {
            if (!identity) {
                addLog('Please load your identity first', 'warning');
                return;
            }

            if (miningActive) {
                stopMining();
            } else {
                startMining();
            }
        }

        function startMining() {
            miningActive = true;
            startTime = Date.now();
            
            document.getElementById('statusDot').classList.add('active');
            document.getElementById('statusText').textContent = 'Mining Active';
            document.getElementById('miningButton').textContent = 'Stop Mining';
            
            addLog('Mining started', 'success');
            addLog('Note: This is a simulation - no actual mining on GitHub Pages', 'warning');
            
            // Simulate mining activity
            miningInterval = setInterval(updateMiningStats, 1000);
        }

        function stopMining() {
            miningActive = false;
            
            document.getElementById('statusDot').classList.remove('active');
            document.getElementById('statusText').textContent = 'Stopped';
            document.getElementById('miningButton').textContent = 'Start Mining';
            
            if (miningInterval) {
                clearInterval(miningInterval);
                miningInterval = null;
            }
            
            addLog('Mining stopped', 'info');
        }

        function updateMiningStats() {
            if (!miningActive) return;
            
            // Simulate hash rate (this would be real mining in actual implementation)
            const tier = document.getElementById('miningTier').value;
            const threads = parseInt(document.getElementById('threads').value);
            const intensity = document.getElementById('intensity').value;
            
            let baseHashRate = threads * 1000; // Base 1000 H/s per thread
            
            // Apply tier multiplier
            switch(tier) {
                case 'optimized': baseHashRate *= 3; break;
                case 'pro': baseHashRate *= 10; break;
            }
            
            // Apply intensity multiplier
            switch(intensity) {
                case 'low': baseHashRate *= 0.25; break;
                case 'medium': baseHashRate *= 0.5; break;
                case 'high': baseHashRate *= 0.75; break;
            }
            
            // Add some randomness
            hashRate = Math.floor(baseHashRate * (0.8 + Math.random() * 0.4));
            
            // Simulate block finding (very rare)
            if (Math.random() < 0.001) {
                blocksFound++;
                onxEarned += 10;
                addLog(`Block found! Earned 10 ONX`, 'success');
            }
            
            // Update display
            document.getElementById('hashRate').textContent = hashRate.toLocaleString();
            document.getElementById('blocksFound').textContent = blocksFound;
            document.getElementById('onxEarned').textContent = onxEarned;
            
            // Update uptime
            if (startTime) {
                const uptime = Date.now() - startTime;
                const hours = Math.floor(uptime / 3600000);
                const minutes = Math.floor((uptime % 3600000) / 60000);
                const seconds = Math.floor((uptime % 60000) / 1000);
                document.getElementById('uptime').textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            // Simulate network stats
            document.getElementById('difficulty').textContent = (1 + Math.random() * 10).toFixed(2);
            document.getElementById('networkHashRate').textContent = Math.floor(50000 + Math.random() * 100000).toLocaleString();
        }

        // Initialize
        addLog('ONNYX Web Miner ready', 'success');
        addLog('GitHub Pages hosting detected', 'info');
        addLog('For actual mining, deploy to a server with backend support', 'warning');
    </script>
</body>
</html>
