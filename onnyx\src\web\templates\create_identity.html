<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Create Your Identity | Onnyx</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="/static/img/favicon.png" type="image/png">
  <style>
    html, body {
      margin: 0;
      padding: 0;
      overflow-x: hidden;
      font-family: 'Segoe UI', sans-serif;
      background-color: #0a0a0a;
      color: #f2f2f2;
      height: 100%;
      scroll-behavior: smooth;
    }

    #particles-js {
      position: fixed;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
    }

    /* Navigation Styles */
    .navbar {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      background-color: rgba(10, 10, 10, 0.95);
      backdrop-filter: blur(8px);
      z-index: 1000;
      padding: 0.6rem 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.7);
      box-sizing: border-box;
    }

    .navbar-logo {
      display: flex;
      align-items: center;
    }

    .logo-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      transition: transform 0.3s ease;
      position: relative;
    }

    .logo-link:hover {
      transform: scale(1.05);
    }

    .navbar-logo img {
      height: 55px;
      width: auto;
      filter: drop-shadow(0 0 8px rgba(128, 0, 255, 0.6));
    }

    .navbar-links {
      display: flex;
      gap: 1.2rem;
      align-items: center;
      margin-left: auto;
    }

    .navbar-links a {
      color: #f2f2f2;
      text-decoration: none;
      font-size: 1rem;
      transition: all 0.3s ease;
      position: relative;
      padding: 0.5rem 0;
    }

    .navbar-links a:hover {
      color: #a64dff; /* Purple */
    }

    .navbar-links a::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: #a64dff; /* Purple */
      transition: width 0.3s ease;
    }

    .navbar-links a:hover::after {
      width: 100%;
    }

    .mobile-menu-btn {
      display: none;
      background: none;
      border: none;
      color: #f2f2f2;
      font-size: 1.5rem;
      cursor: pointer;
      margin-left: auto;
    }

    .section {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 2rem;
      box-sizing: border-box;
      padding-top: 80px; /* Account for navbar */
      scroll-margin-top: 80px; /* This is crucial for scroll-to-anchor functionality */
    }

    h2 {
      font-size: 2.5rem;
      margin-bottom: 2rem;
      color: #a64dff; /* Purple */
      text-align: center;
      position: relative;
      display: inline-block;
    }

    h2::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 2px;
      background: linear-gradient(90deg, rgba(166,77,255,0), rgba(166,77,255,0.8), rgba(166,77,255,0)); /* Purple gradient */
    }

    .section-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .section-subtitle {
      color: #aaa;
      font-size: 1.1rem;
      max-width: 700px;
      margin: 0 auto;
      margin-top: 1rem;
    }

    .identity-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 40px;
      justify-content: center;
      margin-top: 40px;
      max-width: 1000px;
      margin-left: auto;
      margin-right: auto;
    }

    .identity-form, .identity-preview {
      background: rgba(0, 0, 0, 0.7);
      padding: 25px 10px;
      border: 1px solid #a64dff; /* Purple */
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(166, 77, 255, 0.3);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .identity-form::before, .identity-preview::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #a64dff, #ffd700, #a64dff);
      z-index: 1;
    }

    .identity-form:hover, .identity-preview:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(166, 77, 255, 0.4);
    }

    /* Form layout and styling */
    .form-section {
      margin-bottom: 20px;
    }

    .nation-tribe-section {
      display: grid;
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .identity-form label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #ffd700; /* Gold */
      font-size: 1.1rem;
    }

    .label-with-tooltip {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tooltip-icon {
      color: #a64dff;
      font-size: 0.9rem;
      cursor: help;
      border: 1px solid #a64dff;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .tooltip-icon:hover {
      background-color: #a64dff;
      color: #000;
    }

    .optional-text {
      font-size: 0.8rem;
      color: #aaa;
      font-weight: normal;
      font-style: italic;
    }

    .form-section {
      padding: 0 15px;
    }

    .identity-form input, .identity-form select, .identity-form textarea {
      width: calc(100% - 24px);
      padding: 12px;
      background: rgba(10, 10, 10, 0.8);
      border: 1px solid #444;
      color: #eee;
      border-radius: 5px;
      font-size: 1rem;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
      margin-bottom: 5px;
    }

    .identity-form select optgroup {
      background-color: rgba(10, 10, 10, 0.9);
      color: #a64dff; /* Purple */
      font-weight: bold;
    }

    .identity-form select option {
      background-color: rgba(10, 10, 10, 0.8);
      color: #eee;
      padding: 8px;
    }

    /* Visual signature styling */
    .canvas-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;
    }

    #identityCanvas {
      margin-bottom: 15px;
    }

    .visual-controls {
      margin-bottom: 25px;
    }

    .visual-buttons {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .secondary-button {
      padding: 8px 16px;
      background: rgba(166, 77, 255, 0.2);
      color: #a64dff;
      border: 1px solid #a64dff;
      border-radius: 5px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s ease;
    }

    .secondary-button:hover {
      background: rgba(166, 77, 255, 0.4);
    }

    .or-text {
      color: #aaa;
      font-size: 0.9rem;
    }

    .upload-button {
      padding: 8px 16px;
      background: rgba(255, 215, 0, 0.2);
      color: #ffd700;
      border: 1px solid #ffd700;
      border-radius: 5px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      display: inline-block;
    }

    .upload-button:hover {
      background: rgba(255, 215, 0, 0.4);
    }

    /* Covenant affirmation styling */
    .covenant-affirmation {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      padding: 15px;
      margin: 0 15px;
      background: rgba(166, 77, 255, 0.1);
      border-radius: 5px;
      border-left: 3px solid #a64dff;
    }

    .covenant-affirmation input[type="checkbox"] {
      width: auto;
      margin-top: 3px;
    }

    .covenant-affirmation label {
      margin-top: 0;
      margin-bottom: 0;
      font-size: 0.9rem;
      line-height: 1.4;
      color: #eee;
    }

    /* Highlight effect for tribe selection */
    @keyframes highlight-pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(166, 77, 255, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(166, 77, 255, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(166, 77, 255, 0);
      }
    }

    .highlight-field {
      animation: highlight-pulse 1s;
      border-color: #a64dff;
    }

    .identity-form textarea {
      resize: vertical;
      min-height: 80px;
    }

    .identity-form input:focus, .identity-form select:focus, .identity-form textarea:focus {
      outline: none;
      border-color: #a64dff; /* Purple */
      box-shadow: 0 0 8px rgba(166, 77, 255, 0.5);
    }

    .identity-button {
      margin-top: 25px;
      padding: 12px 24px;
      background: linear-gradient(135deg, #a64dff 0%, #6600cc 100%); /* Purple gradient */
      color: #fff;
      font-weight: bold;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1.1rem;
      transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
      display: block;
      width: calc(100% - 30px);
      margin-left: auto;
      margin-right: auto;
    }

    .identity-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(166, 77, 255, 0.5);
    }

    @keyframes creating-identity-pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(166, 77, 255, 0.7);
      }
      70% {
        box-shadow: 0 0 0 15px rgba(166, 77, 255, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(166, 77, 255, 0);
      }
    }

    .creating-identity {
      animation: creating-identity-pulse 1.5s infinite;
      background: linear-gradient(135deg, #8a2be2 0%, #4b0082 100%); /* Deeper purple gradient */
    }

    .identity-preview h3 {
      text-align: center;
      margin-bottom: 20px;
      color: #ffd700; /* Gold */
      font-size: 1.3rem;
    }

    #identityCanvas {
      display: block;
      margin: 0 auto;
      background: #000;
      border: 2px solid #ffd700; /* Gold border */
      border-radius: 6px;
      box-shadow: 0 0 30px rgba(166, 77, 255, 0.7); /* Purple glow */
      transition: box-shadow 0.3s ease;
    }

    #identityCanvas:hover {
      box-shadow: 0 0 40px rgba(166, 77, 255, 0.9); /* Stronger purple glow on hover */
    }

    .sig-caption {
      text-align: center;
      font-size: 0.9rem;
      margin-top: 15px;
      color: #bbb;
      font-style: italic;
    }

    .identity-tagline {
      text-align: center;
      font-size: 1.2rem;
      color: #aaa;
      margin: 1rem 0;
      letter-spacing: 2px;
      font-style: italic;
    }

    .identity-details {
      margin-top: 20px;
      padding: 15px;
      background: rgba(10, 10, 10, 0.5);
      border-radius: 5px;
      border-left: 3px solid #a64dff; /* Purple */
    }

    .identity-details h4 {
      margin-top: 0;
      color: #ffd700; /* Gold */
    }

    .identity-details p {
      margin: 5px 0;
      font-size: 0.9rem;
    }

    .identity-details .detail-label {
      font-weight: bold;
      color: #aaa;
    }

    .identity-details .detail-value {
      color: #fff;
    }

    .success-message {
      display: none;
      margin: 15px auto;
      padding: 15px;
      background: rgba(0, 128, 0, 0.2);
      border: 1px solid #00ff00;
      border-radius: 5px;
      text-align: center;
      color: #00ff00;
      font-weight: bold;
      width: calc(100% - 30px);
    }

    .error-message {
      display: none;
      margin: 15px auto;
      padding: 15px;
      background: rgba(128, 0, 0, 0.2);
      border: 1px solid #ff0000;
      border-radius: 5px;
      text-align: center;
      color: #ff0000;
      width: calc(100% - 30px);
    }

    footer {
      text-align: center;
      padding: 2rem;
      background-color: rgba(0, 0, 0, 0.5);
      margin-top: 2rem;
    }

    .footer-branding {
      font-size: 0.9rem;
      color: #888;
      max-width: 600px;
      margin: 0 auto;
    }

    /* Animation Classes */
    @keyframes slide-in-up {
      from {
        transform: translateY(30px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @keyframes fade-in {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }

    @keyframes floating {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    .slide-in-up {
      animation: slide-in-up 0.8s forwards;
    }

    .fade-in {
      animation: fade-in 0.8s forwards;
    }

    .pulse {
      animation: pulse 2s infinite;
    }

    .float-element {
      animation: floating 3s ease-in-out infinite;
    }

    .glowing {
      box-shadow: 0 0 20px rgba(166, 77, 255, 0.8), 0 0 30px rgba(166, 77, 255, 0.6), 0 0 40px rgba(166, 77, 255, 0.4);
      transition: box-shadow 0.3s ease;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
      .navbar {
        padding: 0.5rem 1rem;
      }

      .navbar-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        flex-direction: column;
        background-color: rgba(10, 10, 10, 0.95);
        padding: 1rem 0;
        z-index: 1000;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
      }

      .navbar-links.active {
        display: flex;
      }

      .navbar-links a {
        padding: 0.8rem 2rem;
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      }

      .mobile-menu-btn {
        display: block;
      }

      .identity-grid {
        flex-wrap: wrap;
        gap: 30px;
      }

      .identity-form, .identity-preview {
        min-width: 280px;
        max-width: 100%;
      }
    }
  </style>
</head>
<body>

<!-- Particles Background -->
<div id="particles-js"></div>

<!-- Navigation -->
<nav class="navbar">
  <div class="navbar-logo">
    <a href="/" class="logo-link">
      <img src="/static/img/onnyx_logo.png" alt="Onnyx Logo">
    </a>
  </div>
  <div class="navbar-links" id="navLinks">
    <a href="/">Home</a>
    <a href="/#why-onnyx">Why Onnyx</a>
    <a href="/#how-it-works">How It Works</a>
    <a href="/#journey">Your Journey</a>
    <a href="/#sela">Sela</a>
    <a href="/#faq">FAQ</a>
  </div>
  <button class="mobile-menu-btn" id="mobileMenuBtn">☰</button>
</nav>

<!-- Identity Creation Section -->
<section id="identity-creation" class="section">
  <div class="section-header">
    <h2>🧬 Create Your Soulbound Identity</h2>
    <p class="section-subtitle">Your first step into the Onnyx covenant economy</p>
  </div>
  <p class="identity-tagline">Before value can flow, before tokens can transfer — there must be an identity.</p>

  <div class="identity-grid">
    <!-- Left: Inputs -->
    <div class="identity-form">
      <h3>Begin Your Covenant Entry</h3>

      <div class="form-section">
        <label for="fullname">👤 Full Name</label>
        <input type="text" id="fullname" placeholder="e.g. Sarah Ben-Yosef" required>
      </div>

      <div class="form-section nation-tribe-section">
        <div class="nation-selection">
          <div class="label-with-tooltip">
            <label for="nation">🌍 Ancestral Nation</label>
            <span class="tooltip-icon" title="This determines your tribal grouping and governance circle.">❓</span>
          </div>
          <select id="nation" required>
            <option value="">Select your nation...</option>
            <optgroup label="Shem's Lineage">
              <option value="Israel">Israel</option>
              <option value="Ishmael">Ishmael</option>
              <option value="Edom">Edom</option>
              <option value="Midian">Midian</option>
              <option value="Moab">Moab</option>
              <option value="Ammon">Ammon</option>
              <option value="Aram">Aram</option>
            </optgroup>
            <optgroup label="Ham's Lineage">
              <option value="Egypt">Egypt</option>
              <option value="Cush">Cush</option>
              <option value="Canaan">Canaan</option>
              <option value="Put">Put</option>
            </optgroup>
            <optgroup label="Japheth's Lineage">
              <option value="Javan">Javan</option>
              <option value="Gomer">Gomer</option>
              <option value="Magog">Magog</option>
            </optgroup>
          </select>
        </div>

        <div id="tribe-container" class="tribe-selection" style="display: none;">
          <label for="tribe">🏛️ Tribe/Clan</label>
          <select id="tribe">
            <option value="">Select your tribe...</option>
            <!-- Options will be populated by JavaScript -->
          </select>
        </div>
      </div>

      <div class="form-section">
        <label for="birthdate">📅 Date of Birth</label>
        <input type="date" id="birthdate" required>
      </div>

      <div class="form-section">
        <label for="region">📍 Current Region <span class="optional-text">(Optional)</span></label>
        <input type="text" id="region" placeholder="e.g. Nairobi, Kenya">
      </div>

      <div class="form-section">
        <label for="purpose">🔥 Purpose or Calling</label>
        <input type="text" id="purpose" placeholder="e.g. Healer, Farmer, Educator" required>
      </div>

      <div class="form-section">
        <div class="label-with-tooltip">
          <label for="soul-seal">🪪 Soul Seal</label>
          <span class="tooltip-icon" title="This phrase will help secure your identity and form your glyph.">🔐</span>
        </div>
        <input type="text" id="soul-seal" placeholder="e.g. 'She Who Tills and Guards'" required>
      </div>

      <div class="form-section">
        <label for="description">📝 Brief Description</label>
        <textarea id="description" placeholder="Tell us a bit about your vision in Onnyx (max 300 chars)" rows="3" maxlength="300"></textarea>
      </div>

      <div class="form-section covenant-affirmation">
        <input type="checkbox" id="covenant-checkbox" required>
        <label for="covenant-checkbox">I affirm my identity and purpose are real and I accept the principles of the Onnyx covenant.</label>
      </div>

      <div class="form-section">
        <div id="success-message" class="success-message">
          Identity created successfully! Your identity is now part of the Onnyx blockchain.
        </div>

        <div id="error-message" class="error-message">
          Error creating identity. Please try again.
        </div>

        <button id="generate-identity-btn" class="identity-button">🔗 Create My Identity</button>
      </div>
    </div>

    <!-- Right: Visual Output -->
    <div class="identity-preview">
      <h3>Visual Signature</h3>

      <div class="canvas-container">
        <canvas id="identityCanvas" width="250" height="250"></canvas>
        <p class="sig-caption">This visual is your cryptographic symbol in the Onnyx economy.</p>
      </div>

      <div class="visual-controls">
        <div class="visual-buttons">
          <button type="button" id="generate-visual-btn" class="secondary-button">Generate Glyph</button>
          <span class="or-text">or</span>
          <label for="upload-visual" class="upload-button">Upload Image</label>
          <input type="file" id="upload-visual" accept="image/*" style="display: none;">
        </div>
      </div>

      <div id="identity-details" class="identity-details" style="display: none;">
        <h4>Identity Details</h4>
        <p><span class="detail-label">Identity ID:</span> <span id="identity-id" class="detail-value">-</span></p>
        <p><span class="detail-label">Public Key:</span> <span id="public-key" class="detail-value">-</span></p>
        <p><span class="detail-label">Created:</span> <span id="created-at" class="detail-value">-</span></p>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer>
  <div class="footer-branding">© 2025 Onnyx — "Render unto Caesar what is Caesar's, and unto God what is God's."</div>
</footer>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script src="/static/js/dark-animations.js"></script>
<script src="/static/js/identity.js"></script>

<!-- Add animation classes to elements -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to elements
    document.querySelector('.section-header h2').classList.add('hero-animate');
    document.querySelector('.section-header .section-subtitle').classList.add('hero-animate');
    document.querySelector('.identity-tagline').classList.add('hero-animate');

    // Add card animation classes
    document.querySelector('.identity-form').classList.add('card-animate', 'tilt-card');
    document.querySelector('.identity-preview').classList.add('card-animate', 'tilt-card');

    // Add glow effect to the canvas
    document.getElementById('identityCanvas').classList.add('glow-element');

    // Add floating effect to the logo
    const logo = document.querySelector('.navbar-logo img');
    if (logo) {
      logo.classList.add('float-element');
    }
  });
</script>
</body>
</html>
