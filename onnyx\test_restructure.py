#!/usr/bin/env python3
"""
Test script to verify the restructured Onnyx codebase works correctly.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_imports():
    """Test that key modules can be imported from the new structure."""
    print("Testing imports from restructured codebase...")

    try:
        # Test shared components
        print("Testing shared.config...")
        from shared.config.config import OnnyxConfig
        print("✓ shared.config.config imported successfully")

        # Test shared models
        print("Testing shared.models...")
        from shared.models.identity import Identity
        print("✓ shared.models.identity imported successfully")

        # Test blockchain components
        print("Testing blockchain components...")
        from blockchain.wallet.wallet import Wallet
        print("✓ blockchain.wallet.wallet imported successfully")

        # Test governance
        print("Testing governance...")
        from governance.voice_scroll import VoiceScrolls
        print("✓ governance.voice_scroll imported successfully")

        # Test tokens
        print("Testing tokens...")
        from tokens.ledger.ledger import TokenLedger
        print("✓ tokens.ledger.ledger imported successfully")

        print("\n✅ All core imports successful!")
        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config():
    """Test configuration loading."""
    try:
        print("\nTesting configuration...")
        from shared.config.config import OnnyxConfig

        # Try to create a config instance
        config = OnnyxConfig()
        print("✓ Configuration object created successfully")
        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 Testing Onnyx Restructured Codebase")
    print("=" * 50)

    success = True

    # Test imports
    if not test_imports():
        success = False

    # Test configuration
    if not test_config():
        success = False

    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The restructured codebase is working.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
