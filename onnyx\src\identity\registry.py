# src/identity/registry.py

import logging
import time
from typing import List, Optional, Dict, Any

from models.identity import Identity

# Set up logging
logger = logging.getLogger("onnyx.identity.registry")

class IdentityRegistry:
    def __init__(self):
        """
        Initialize the identity registry.
        """
        self.identities_cache = {}
        self._load()

    def _load(self):
        """
        Load identities from the database into the cache.
        """
        try:
            # Get all identities from the database
            identities = Identity.get_all()

            # Log each identity being loaded
            for identity in identities:
                logger.info(f"Loading identity: {identity.identity_id}, name: {identity.name}")

            # Cache the identities
            self.identities_cache = {identity.identity_id: identity for identity in identities}

            # Also check the database directly for identities
            try:
                from data.db import db
                db_identities = db.query("SELECT identity_id, name FROM identities")
                logger.info(f"Direct database query found {len(db_identities)} identities:")
                for db_identity in db_identities:
                    logger.info(f"  DB Identity: {db_identity['identity_id']}, name: {db_identity['name']}")
            except Exception as e:
                logger.error(f"Error querying identities directly from database: {str(e)}")

            logger.info(f"Loaded {len(self.identities_cache)} identities from the database")
        except Exception as e:
            logger.error(f"Error loading identities: {str(e)}")
            # Log the full exception traceback
            import traceback
            logger.error(traceback.format_exc())
            self.identities_cache = {}

    def register_identity(self, name: str, public_key: str, nation: str = None, metadata: Dict[str, Any] = None) -> Identity:
        """
        Register a new identity.

        Args:
            name: The identity name
            public_key: The identity public key
            nation: The nation this identity belongs to
            metadata: The identity metadata

        Returns:
            The created identity
        """
        # Check if an identity with this public key already exists
        existing = Identity.get_by_public_key(public_key)
        if existing:
            raise ValueError(f"Identity with public key {public_key} already exists.")

        # Create the identity
        identity = Identity.create(name=name, public_key=public_key, nation=nation, metadata=metadata)

        # Cache the identity
        self.identities_cache[identity.identity_id] = identity

        logger.info(f"Registered identity {identity.identity_id}: {name}")
        return identity

    def get_identity(self, identity_id: str) -> Optional[Identity]:
        """
        Get an identity by ID.

        Args:
            identity_id: The identity ID

        Returns:
            The identity or None if not found
        """
        # Check the cache first
        if identity_id in self.identities_cache:
            return self.identities_cache[identity_id]

        # If not in cache, try to get from the database
        identity = Identity.get_by_id(identity_id)

        # Update the cache if found
        if identity:
            self.identities_cache[identity_id] = identity

        return identity

    def update_identity(self, identity_id: str, field: str, value: Any) -> Optional[Identity]:
        """
        Update an identity's metadata.

        Args:
            identity_id: The identity ID
            field: The metadata field to update
            value: The new value

        Returns:
            The updated identity or None if not found
        """
        identity = self.get_identity(identity_id)
        if not identity:
            raise ValueError(f"Identity {identity_id} not found")

        # Update the metadata
        identity.metadata[field] = value
        identity.save()

        logger.info(f"Updated identity {identity_id} metadata: {field}={value}")
        return identity

    def update_reputation(self, identity_id: str, reputation_type: str, value: int) -> Optional[Identity]:
        """
        Update the reputation score for an identity.

        Args:
            identity_id: The ID of the identity to update
            reputation_type: The type of reputation to update (score, transactions, minted_tokens, followers)
            value: The value to add to the current reputation score

        Returns:
            The updated identity or None if not found
        """
        identity = self.get_identity(identity_id)
        if not identity:
            raise ValueError(f"Identity {identity_id} not found")

        # Get the current reputation
        reputation = identity.get_reputation()

        # Find the reputation entry for this type
        found = False
        for entry in reputation:
            if entry["type"] == reputation_type:
                # Update the database
                query = "UPDATE reputation SET value = value + ? WHERE identity_id = ? AND type = ?"
                params = (value, identity_id, reputation_type)
                from data.db import db
                db.execute(query, params)
                found = True
                break

        # If not found, create a new entry
        if not found:
            query = "INSERT INTO reputation (identity_id, type, value, timestamp) VALUES (?, ?, ?, ?)"
            params = (identity_id, reputation_type, value, int(time.time()))
            from data.db import db
            db.execute(query, params)

        logger.info(f"Updated identity {identity_id} reputation: {reputation_type}+={value}")
        return identity

    def get_all_identities(self) -> List[Identity]:
        """
        Get all identities in the registry.

        Returns:
            A list of all identities
        """
        # Refresh the cache
        self._load()

        return list(self.identities_cache.values())

    def search_identities(self, query: str) -> List[Identity]:
        """
        Search for identities by name or metadata.

        Args:
            query: The search query

        Returns:
            A list of matching identities
        """
        query = query.lower()

        # Search in the database
        sql_query = f"""
            SELECT * FROM identities
            WHERE LOWER(name) LIKE ?
            OR id IN (
                SELECT id FROM identities
                WHERE json_extract(metadata, '$.*') LIKE ?
            )
        """
        params = (f"%{query}%", f"%{query}%")

        from data.db import db
        rows = db.query(sql_query, params)

        # Convert to Identity objects
        results = []
        for row in rows:
            results.append(Identity.from_dict(row))

        return results
