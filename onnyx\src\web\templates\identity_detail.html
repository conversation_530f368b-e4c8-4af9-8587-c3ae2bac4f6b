{% extends 'base.html' %}

{% block title %}Onnyx Explorer - Identity Details{% endblock %}

{% block content %}
    <h1>Identity Details: {{ identity.name }}</h1>

    <div class="identity-detail">
        <div class="detail-section">
            <h2>Basic Information</h2>
            <table>
                <tr>
                    <th>Identity ID</th>
                    <td>{{ identity.identity_id }}</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>{{ identity.name }}</td>
                </tr>
                <tr>
                    <th>Public Key</th>
                    <td>{{ identity.public_key }}</td>
                </tr>
            </table>
        </div>

        <div class="detail-section">
            <h2>Reputation</h2>
            {% if identity.reputation %}
                <table>
                    {% for key, value in identity.reputation.items() %}
                        <tr>
                            <th>{{ key }}</th>
                            <td>{{ value }}</td>
                        </tr>
                    {% endfor %}
                </table>
            {% else %}
                <p>No reputation data available.</p>
            {% endif %}
        </div>

        <div class="detail-section">
            <h2>Metadata</h2>
            <pre>{{ identity.metadata | tojson(indent=2) }}</pre>
        </div>

        <div class="detail-section">
            <h2>Tokens</h2>
            <p>No tokens associated with this identity.</p>
        </div>
    </div>

    <div class="detail-section">
        <h2>Identity Transactions</h2>
        {% if transactions %}
            <table>
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Time</th>
                        <th>Details</th>
                        <th>Transaction ID</th>
                    </tr>
                </thead>
                <tbody>
                    {% for tx in transactions %}
                        <tr>
                            <td>{{ tx.type }}</td>
                            <td>{{ tx.timestamp | format_timestamp }}</td>
                            <td>
                                {% if tx.type == 'createidentity' %}
                                    Identity created
                                {% elif tx.type == 'updateidentity' %}
                                    Updated field "{{ tx.payload.field }}" to "{{ tx.payload.value }}"
                                {% elif tx.type == 'forktoken' %}
                                    Created token
                                    <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                        {{ tx.payload.name }} ({{ tx.payload.symbol }})
                                    </a>
                                {% elif tx.type == 'minttoken' %}
                                    Minted {{ tx.payload.amount }} tokens of
                                    <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                        {{ tx.payload.token_id }}
                                    </a> to
                                    <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">
                                        {{ tx.payload.to_address }}
                                    </a>
                                {% elif tx.type == 'sendtoken' %}
                                    {% if tx.payload.from_address == identity.identity_id %}
                                        Sent {{ tx.payload.amount }} tokens of
                                        <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                            {{ tx.payload.token_id }}
                                        </a> to
                                        <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">
                                            {{ tx.payload.to_address }}
                                        </a>
                                    {% else %}
                                        Received {{ tx.payload.amount }} tokens of
                                        <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                            {{ tx.payload.token_id }}
                                        </a> from
                                        <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">
                                            {{ tx.payload.from_address }}
                                        </a>
                                    {% endif %}
                                {% elif tx.type == 'burntoken' %}
                                    Burned {{ tx.payload.amount }} tokens of
                                    <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                        {{ tx.payload.token_id }}
                                    </a>
                                {% elif tx.type == 'grantreputation' %}
                                    {% if tx.payload.to_identity == identity.identity_id %}
                                        Received {{ tx.payload.value }} {{ tx.payload.reputation_type }} reputation from
                                        <a href="{{ url_for('identity_detail', identity_id=tx.payload.issuer_identity) }}">
                                            {{ tx.payload.issuer_identity }}
                                        </a>
                                    {% else %}
                                        Granted {{ tx.payload.value }} {{ tx.payload.reputation_type }} reputation to
                                        <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_identity) }}">
                                            {{ tx.payload.to_identity }}
                                        </a>
                                    {% endif %}
                                {% else %}
                                    <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">View details</a>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('transaction_detail', txid=tx.txid) }}">{{ tx.txid }}</a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>No transactions found for this identity.</p>
        {% endif %}
    </div>

    <div class="actions">
        <a href="{{ url_for('identities') }}" class="button">Back to Identities</a>
    </div>
{% endblock %}

{% block head %}
<style>
    .identity-detail {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 20px;
    }

    .detail-section {
        background-color: #222;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .detail-section h2 {
        margin-top: 0;
        border-bottom: 1px solid #444;
        padding-bottom: 10px;
    }

    .actions {
        margin-top: 20px;
        display: flex;
        gap: 10px;
    }

    .button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #0f0;
        color: #111;
        border-radius: 4px;
        font-weight: bold;
        text-decoration: none;
    }

    .button:hover {
        background-color: #00cc00;
        text-decoration: none;
    }

    @media (min-width: 768px) {
        .identity-detail {
            grid-template-columns: 1fr 1fr;
        }

        .detail-section:nth-child(3),
        .detail-section:nth-child(4) {
            grid-column: 1 / span 2;
        }
    }
</style>
{% endblock %}
