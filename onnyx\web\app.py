#!/usr/bin/env python3
"""
Onnyx Web Application

Main Flask application for the Onnyx platform frontend.
"""

import os
import sys
import logging
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from werkzeug.security import generate_password_hash, check_password_hash

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.models.identity import Identity
from shared.db.db import db
from blockchain.wallet.wallet import Wallet

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.web")

def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['DATABASE_URL'] = os.environ.get('DATABASE_URL', 'sqlite:///shared/db/onnyx.db')

    # Register blueprints
    from web.routes.auth import auth_bp
    from web.routes.dashboard import dashboard_bp
    from web.routes.sela import sela_bp
    from web.routes.explorer import explorer_bp
    from web.routes.api import api_bp
    from web.routes.auto_mining import auto_mining_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(sela_bp, url_prefix='/sela')
    app.register_blueprint(explorer_bp, url_prefix='/explorer')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(auto_mining_bp, url_prefix='/auto-mining')

    # Template filters
    @app.template_filter('truncate_hash')
    def truncate_hash(hash_string, length=8):
        """Truncate a hash for display."""
        if not hash_string:
            return ""
        return f"{hash_string[:length]}...{hash_string[-4:]}"

    @app.template_filter('format_timestamp')
    def format_timestamp(timestamp):
        """Format a timestamp for display."""
        import datetime
        if not timestamp:
            return ""

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                # Try to parse ISO 8601 datetime string
                if 'T' in timestamp:
                    # Remove microseconds if present and parse
                    timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                    dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                else:
                    # Try to parse as string representation of Unix timestamp
                    dt = datetime.datetime.fromtimestamp(float(timestamp))
            elif isinstance(timestamp, (int, float)):
                # Handle numeric Unix timestamp
                dt = datetime.datetime.fromtimestamp(timestamp)
            else:
                # Fallback for other types
                return str(timestamp)

            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError, OSError) as e:
            # Return the original value if parsing fails
            return str(timestamp)

    @app.template_filter('timestamp_to_time')
    def timestamp_to_time(timestamp):
        """Convert timestamp to time string."""
        import datetime
        if not timestamp:
            return "Unknown"

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                # Try to parse ISO 8601 datetime string
                if 'T' in timestamp:
                    # Remove microseconds if present and parse
                    timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                    dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                else:
                    # Try to parse as string representation of Unix timestamp
                    dt = datetime.datetime.fromtimestamp(float(timestamp))
            elif isinstance(timestamp, (int, float)):
                # Handle numeric Unix timestamp
                dt = datetime.datetime.fromtimestamp(timestamp)
            else:
                # Fallback for other types
                return "Unknown"

            return dt.strftime('%H:%M:%S')
        except (ValueError, TypeError, OSError) as e:
            # Return "Unknown" if parsing fails
            return "Unknown"

    @app.template_filter('timestamp_to_date')
    def timestamp_to_date(timestamp):
        """Convert timestamp to date string."""
        import datetime
        if not timestamp:
            return ""

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                # Try to parse ISO 8601 datetime string
                if 'T' in timestamp:
                    # Remove microseconds if present and parse
                    timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                    dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                else:
                    # Try to parse as string representation of Unix timestamp
                    dt = datetime.datetime.fromtimestamp(float(timestamp))
            elif isinstance(timestamp, (int, float)):
                # Handle numeric Unix timestamp
                dt = datetime.datetime.fromtimestamp(timestamp)
            else:
                # Fallback for other types
                return str(timestamp)

            return dt.strftime('%Y-%m-%d')
        except (ValueError, TypeError, OSError) as e:
            # Return the original value if parsing fails
            return str(timestamp)

    # Context processors
    @app.context_processor
    def inject_global_data():
        """Inject global data into all templates."""
        # Get platform statistics with proper error handling
        try:
            identity_result = db.query_one("SELECT COUNT(*) as count FROM identities")
            identity_count = identity_result['count'] if isinstance(identity_result, dict) else identity_result[0] if identity_result else 0

            sela_result = db.query_one("SELECT COUNT(*) as count FROM selas")
            sela_count = sela_result['count'] if isinstance(sela_result, dict) else sela_result[0] if sela_result else 0

            transaction_result = db.query_one("SELECT COUNT(*) as count FROM transactions")
            transaction_count = transaction_result['count'] if isinstance(transaction_result, dict) else transaction_result[0] if transaction_result else 0

            block_count = 0
            if db.table_exists('blocks'):
                block_result = db.query_one("SELECT COUNT(*) as count FROM blocks")
                block_count = block_result['count'] if isinstance(block_result, dict) else block_result[0] if block_result else 0
        except Exception as e:
            logger.warning(f"Error getting platform stats: {e}")
            identity_count = sela_count = transaction_count = block_count = 0

        return {
            'platform_stats': {
                'identities': identity_count,
                'selas': sela_count,
                'transactions': transaction_count,
                'blocks': block_count
            },
            'current_user': get_current_user()
        }

    # Routes
    @app.route('/')
    def index():
        """Landing page."""
        # Get recent Selas
        recent_selas = db.query("""
            SELECT s.*, i.name as owner_name
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.status = 'active'
            ORDER BY s.created_at DESC
            LIMIT 6
        """)

        # Get recent transactions
        recent_transactions = db.query("""
            SELECT * FROM transactions
            ORDER BY created_at DESC
            LIMIT 5
        """)

        return render_template('index.html',
                             recent_selas=recent_selas,
                             recent_transactions=recent_transactions)

    @app.route('/register')
    def register_choice():
        """Registration choice page."""
        # Check if Genesis Identity already exists
        genesis_exists = db.query_one("SELECT identity_id FROM identities WHERE metadata LIKE '%Platform Founder%'")
        return render_template('auth/register_choice.html', genesis_exists=bool(genesis_exists))

    @app.route('/test-footer')
    def test_footer():
        """Footer positioning test page."""
        return render_template('test_footer.html')

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500

    return app

def get_current_user():
    """Get the current user from session."""
    if 'identity_id' in session:
        return Identity.get_by_id(session['identity_id'])
    return None

def require_auth(f):
    """Decorator to require authentication."""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'identity_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
