<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Onnyx Blockchain{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        /* Onnyx Custom Theme */
        :root {
            --primary-color: #8a2be2; /* Purple */
            --secondary-color: #ffd700; /* Gold */
            --dark-bg: #111;
            --darker-bg: #0a0a0a;
            --light-text: #f8f9fa;
            --card-bg: #222;
            --border-color: #444;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--dark-bg);
            color: var(--light-text);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: var(--darker-bg);
            padding: 10px 0;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 28px;
            font-weight: bold;
            color: var(--secondary-color);
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: var(--light-text);
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            background-color: var(--primary-color);
            color: var(--light-text);
            text-decoration: none;
            box-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
        }

        .search-form {
            display: flex;
            gap: 10px;
        }

        .search-form input {
            padding: 8px 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--card-bg);
            color: var(--light-text);
        }

        .search-form button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: var(--light-text);
            cursor: pointer;
            transition: all 0.3s;
        }

        .search-form button:hover {
            background-color: #7b27c7;
            box-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
        }

        h1, h2, h3 {
            color: var(--secondary-color);
        }

        .hero-section {
            background-color: var(--darker-bg);
            padding: 60px 20px;
            border-radius: 8px;
            margin-bottom: 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-section h1 {
            color: var(--secondary-color);
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: #7b27c7;
            border-color: #7b27c7;
            box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            border-bottom: 1px solid var(--border-color);
        }

        .text-gold {
            color: var(--secondary-color) !important;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        th, td {
            padding: 12px;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        th {
            background-color: var(--darker-bg);
            color: var(--secondary-color);
        }

        tr:nth-child(even) {
            background-color: rgba(34, 34, 34, 0.7);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s;
        }

        a:hover {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .pagination a {
            padding: 8px 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--card-bg);
            transition: all 0.3s;
        }

        .pagination a:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .flash-messages {
            margin: 20px 0;
        }

        .flash-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .flash-message.error {
            background-color: #dc3545;
            color: white;
        }

        .flash-message.success {
            background-color: #28a745;
            color: white;
        }

        pre {
            background-color: var(--darker-bg);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid var(--border-color);
        }

        .btn-highlight {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-highlight:hover {
            background-color: var(--secondary-color);
            color: var(--darker-bg);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo img {
            filter: drop-shadow(0 0 5px rgba(128, 0, 255, 0.5));
            transition: transform 0.3s ease;
        }

        .logo img:hover {
            transform: scale(1.05);
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
<!-- Particles Background -->
<div id="particles-js"></div>

    <header>
        <nav>
            <div class="logo"><img src="/static/img/onnyx_logo.png" alt="Onnyx Logo" height="40"></div>
            <div class="nav-links">
                <a href="{{ url_for('home') }}">Home</a>
                <a href="{{ url_for('create_identity') }}">Create Identity</a>
                <a href="{{ url_for('explorer') }}" class="btn-highlight">Explore</a>
                <a href="{{ url_for('blockchain_viz') }}">Live View</a>
            </div>
            <form class="search-form" action="{{ url_for('search') }}" method="get">
                <input type="text" name="q" placeholder="Search...">
                <button type="submit">Search</button>
            </form>
        </nav>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="logo"><img src="/static/img/onnyx_logo.png" alt="Onnyx Logo" height="50"></div>
                    <p>A world built on trust</p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Main</h4>
                        <a href="{{ url_for('home') }}">Home</a>
                        <a href="{{ url_for('create_identity') }}">Create Identity</a>
                        <a href="{{ url_for('explorer') }}">Explore</a>
                        <a href="{{ url_for('blockchain_viz') }}">Live View</a>
                    </div>
                    <div class="footer-section">
                        <h4>Explorer</h4>
                        <a href="{{ url_for('transactions') }}">Transactions</a>
                        <a href="{{ url_for('mempool_view') }}">Mempool</a>
                        <a href="{{ url_for('tokens') }}">Tokens</a>
                        <a href="{{ url_for('identities') }}">Identities</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; {{ now.year }} Onnyx Blockchain. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <style>
        footer {
            background-color: var(--darker-bg);
            padding: 3rem 0 1rem;
            margin-top: 4rem;
            border-top: 1px solid var(--border-color);
        }

        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .footer-logo {
            flex: 1;
            min-width: 200px;
            margin-bottom: 1.5rem;
        }

        .footer-logo p {
            color: #aaa;
            margin-top: 0.5rem;
        }

        .footer-links {
            display: flex;
            flex-wrap: wrap;
            gap: 3rem;
        }

        .footer-section {
            min-width: 150px;
        }

        .footer-section h4 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .footer-section a {
            display: block;
            color: #aaa;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: var(--primary-color);
        }

        .footer-bottom {
            border-top: 1px solid var(--border-color);
            padding-top: 1.5rem;
            text-align: center;
            color: #777;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
            }

            .footer-links {
                margin-top: 1.5rem;
            }
        }
    </style>

    {% block scripts %}{% endblock %}
</body>
</html>
