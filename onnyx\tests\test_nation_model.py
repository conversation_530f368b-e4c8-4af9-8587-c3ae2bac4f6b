"""
Onnyx Nation Model Tests

This module provides tests for the Onnyx nation model.
"""

import os
import sys
import unittest
import time
import hashlib
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.nation import Nation
from models.identity import Identity
from data.db import db

class TestNationModel(unittest.TestCase):
    """
    Test the Nation model.
    """
    
    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_nation_model.db")
        
        # Set the database path
        db.db_path = self.db_path
        
        # Create the tables
        conn = sqlite3.connect(self.db_path)
        
        # Create the identities table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            nation_id TEXT,
            metadata TEXT,
            created_at INTEGER,
            updated_at INTEGER,
            FOREIG<PERSON> KEY (nation_id) REFERENCES nations (nation_id)
        )
        """)
        
        # Create the nations table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS nations (
            nation_id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            description TEXT NOT NULL,
            founder_id TEXT NOT NULL,
            metadata TEXT,
            status TEXT NOT NULL,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            FOREIGN KEY (founder_id) REFERENCES identities (identity_id)
        )
        """)
        
        # Create the etzem_scores table for testing total Etzem score
        conn.execute("""
        CREATE TABLE IF NOT EXISTS etzem_scores (
            identity_id TEXT PRIMARY KEY,
            score INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id)
        )
        """)
        
        conn.commit()
        conn.close()
        
        # Create a test identity
        self.founder_id = "founder_identity"
        
        # Insert the identity into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.founder_id,
                "Founder Identity",
                "0x1234567890abcdef",
                json.dumps({"type": "individual", "description": "Founder identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")
        
        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")
    
    def test_create_nation(self):
        """Test creating a nation."""
        # Create a nation directly in the database
        nation_id = "test_nation"
        name = "Test Nation"
        description = "A test nation for testing"
        
        # Insert the nation into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO nations (nation_id, name, description, founder_id, metadata, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                nation_id,
                name,
                description,
                self.founder_id,
                json.dumps({"flag": "🏁", "motto": "Test all the things"}),
                "active",
                int(time.time()),
                int(time.time())
            )
        )
        conn.commit()
        conn.close()
        
        # Check if the nation was saved to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM nations WHERE nation_id = ?", (nation_id,))
        row = cursor.fetchone()
        conn.close()
        
        print(f"Database row: {row}")
        
        # Get the nation by ID
        retrieved_nation = Nation.get_by_id(nation_id)
        
        # Check that the nation was retrieved
        self.assertIsNotNone(retrieved_nation)
        self.assertEqual(retrieved_nation.nation_id, nation_id)
        self.assertEqual(retrieved_nation.name, name)
        self.assertEqual(retrieved_nation.description, description)
        self.assertEqual(retrieved_nation.founder_id, self.founder_id)
        self.assertEqual(retrieved_nation.metadata["flag"], "🏁")
        self.assertEqual(retrieved_nation.metadata["motto"], "Test all the things")
        self.assertEqual(retrieved_nation.status, "active")
    
    def test_nation_members(self):
        """Test nation members."""
        # Create a nation directly in the database
        nation_id = "test_nation"
        
        # Insert the nation into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO nations (nation_id, name, description, founder_id, metadata, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                nation_id,
                "Test Nation",
                "A test nation for testing",
                self.founder_id,
                json.dumps({}),
                "active",
                int(time.time()),
                int(time.time())
            )
        )
        
        # Create some member identities
        member_ids = []
        for i in range(5):
            member_id = f"member_{i}"
            member_ids.append(member_id)
            
            conn.execute(
                "INSERT INTO identities (identity_id, name, public_key, nation_id, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (
                    member_id,
                    f"Member {i}",
                    f"0x{i}",
                    nation_id,
                    json.dumps({}),
                    int(time.time()),
                    int(time.time())
                )
            )
            
            # Add Etzem scores for testing total Etzem score
            conn.execute(
                "INSERT INTO etzem_scores (identity_id, score, updated_at) VALUES (?, ?, ?)",
                (
                    member_id,
                    (i + 1) * 10,  # 10, 20, 30, 40, 50
                    int(time.time())
                )
            )
        
        conn.commit()
        conn.close()
        
        # Get the nation by ID
        nation = Nation.get_by_id(nation_id)
        
        # Check the member count
        self.assertEqual(nation.get_member_count(), 5)
        
        # Check the members
        members = nation.get_members()
        self.assertEqual(len(members), 5)
        
        # Check the total Etzem score (10 + 20 + 30 + 40 + 50 = 150)
        self.assertEqual(nation.get_total_etzem_score(), 150)
        
        # Check getting members by nation
        members_by_nation = Identity.get_by_nation(nation_id)
        self.assertEqual(len(members_by_nation), 5)
        
        # Check member IDs
        member_ids_from_db = [member["identity_id"] for member in members]
        for member_id in member_ids:
            self.assertIn(member_id, member_ids_from_db)

if __name__ == "__main__":
    unittest.main()
