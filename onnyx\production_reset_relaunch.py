#!/usr/bin/env python3
"""
ONNYX Phase 1 Production Reset & Relaunch
Complete database reset and creation of real production identities and businesses.
"""

import sqlite3
import json
import time
import uuid
import os
import shutil
from datetime import datetime

def backup_existing_database():
    """Backup existing database before reset."""
    print("💾 Backing up existing database...")
    
    db_path = "data/onnyx.db"
    if os.path.exists(db_path):
        backup_path = f"data/onnyx_backup_production_reset_{int(time.time())}.db"
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    else:
        print("ℹ️ No existing database found to backup")
        return None

def reset_database():
    """Complete database reset with fresh schema."""
    print("🔄 Resetting database with fresh schema...")
    
    db_path = "data/onnyx.db"
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
        print("✅ Existing database removed")
    
    # Create fresh database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create identities table
    cursor.execute("""
        CREATE TABLE identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL
        )
    """)
    
    # Create selas table
    cursor.execute("""
        CREATE TABLE selas (
            sela_id TEXT PRIMARY KEY,
            identity_id TEXT NOT NULL,
            name TEXT UNIQUE NOT NULL,
            category TEXT NOT NULL,
            description TEXT,
            address TEXT,
            services TEXT,
            status TEXT DEFAULT 'active',
            trust_score REAL DEFAULT 100.0,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id)
        )
    """)
    
    # Create blocks table
    cursor.execute("""
        CREATE TABLE blocks (
            block_hash TEXT PRIMARY KEY,
            block_number INTEGER UNIQUE NOT NULL,
            previous_hash TEXT NOT NULL,
            data TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            created_at INTEGER NOT NULL
        )
    """)
    
    # Create mining table
    cursor.execute("""
        CREATE TABLE mining (
            mining_id TEXT PRIMARY KEY,
            identity_id TEXT NOT NULL,
            sela_id TEXT,
            mining_tier TEXT DEFAULT 'Basic CPU',
            mining_power REAL DEFAULT 1.0,
            blocks_mined INTEGER DEFAULT 0,
            total_rewards REAL DEFAULT 0.0,
            last_mining_at INTEGER,
            created_at INTEGER NOT NULL,
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id),
            FOREIGN KEY (sela_id) REFERENCES selas (sela_id)
        )
    """)
    
    # Create transactions table
    cursor.execute("""
        CREATE TABLE transactions (
            transaction_id TEXT PRIMARY KEY,
            from_identity TEXT,
            to_identity TEXT,
            amount REAL NOT NULL,
            transaction_type TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            block_hash TEXT,
            created_at INTEGER NOT NULL,
            FOREIGN KEY (from_identity) REFERENCES identities (identity_id),
            FOREIGN KEY (to_identity) REFERENCES identities (identity_id),
            FOREIGN KEY (block_hash) REFERENCES blocks (block_hash)
        )
    """)
    
    conn.commit()
    conn.close()
    
    print("✅ Fresh database created with production schema")
    return True

def create_production_identity(name, email, organization, role="Business Owner", is_platform_founder=False):
    """Create a production identity with cryptographic keys."""
    try:
        from blockchain.wallet.wallet import Wallet
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()
        
        identity_id = str(uuid.uuid4())
        current_time = int(time.time())
        
        metadata = {
            "role": role,
            "organization": organization,
            "verified": True,
            "registration_timestamp": current_time,
            "production_identity": True
        }
        
        if is_platform_founder:
            metadata.update({
                "genesis_identity": True,
                "platform_founder": True,
                "admin_privileges": True,
                "genesis_block_creator": True
            })
        
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO identities (identity_id, name, email, public_key, metadata, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            name,
            email,
            public_key,
            json.dumps(metadata),
            current_time,
            current_time
        ))
        
        conn.commit()
        conn.close()
        
        # Save credentials
        os.makedirs("credentials", exist_ok=True)
        safe_name = name.replace(" ", "_").lower()
        credentials_file = f"credentials/{safe_name}_production_credentials.txt"
        
        with open(credentials_file, "w") as f:
            f.write(f"ONNYX PRODUCTION IDENTITY CREDENTIALS\n")
            f.write("=" * 50 + "\n")
            f.write(f"Name: {name}\n")
            f.write(f"Email: {email}\n")
            f.write(f"Organization: {organization}\n")
            f.write(f"Role: {role}\n")
            f.write(f"Identity ID: {identity_id}\n")
            f.write(f"Created: {datetime.fromtimestamp(current_time).isoformat()}\n")
            f.write("\nCRYPTOGRAPHIC KEYS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Public Key: {public_key}\n")
            f.write(f"Private Key: {private_key}\n")
            f.write("\nSECURITY WARNING:\n")
            f.write("Store this private key securely. It cannot be recovered if lost.\n")
        
        return {
            'identity_id': identity_id,
            'name': name,
            'email': email,
            'organization': organization,
            'role': role,
            'credentials_file': credentials_file
        }
        
    except Exception as e:
        print(f"Error creating identity for {name}: {e}")
        return None

def create_business_validator(business_name, category, description, address, services, owner_identity_id, mining_tier="ONNYX Optimized", mining_power=3.0):
    """Create a business validator (Sela)."""
    try:
        sela_id = str(uuid.uuid4())
        mining_id = str(uuid.uuid4())
        current_time = int(time.time())
        
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Insert business validator
        cursor.execute("""
            INSERT INTO selas (sela_id, identity_id, name, category, description, address, services, status, trust_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            sela_id,
            owner_identity_id,
            business_name,
            category,
            description,
            address,
            json.dumps(services),
            'active',
            100.0,
            current_time,
            current_time
        ))
        
        # Initialize mining for this validator
        cursor.execute("""
            INSERT INTO mining (mining_id, identity_id, sela_id, mining_tier, mining_power, blocks_mined, total_rewards, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            mining_id,
            owner_identity_id,
            sela_id,
            mining_tier,
            mining_power,
            0,
            0.0,
            current_time
        ))
        
        conn.commit()
        conn.close()
        
        return {
            'sela_id': sela_id,
            'name': business_name,
            'category': category,
            'mining_tier': mining_tier,
            'mining_power': mining_power
        }
        
    except Exception as e:
        print(f"Error creating business validator {business_name}: {e}")
        return None

def create_genesis_block(platform_founder_identity):
    """Create Genesis Block #0."""
    try:
        current_time = int(time.time())
        
        genesis_block_data = {
            "genesis_identity_id": platform_founder_identity['identity_id'],
            "founder_name": platform_founder_identity['name'],
            "founder_email": platform_founder_identity['email'],
            "platform_purpose": "Establishing a trusted business network for secure commerce and decentralized validation through quantum-resistant cryptographic identities and blockchain technology.",
            "organization": platform_founder_identity['organization'],
            "created_at": current_time,
            "block_number": 0,
            "production_launch": True,
            "launch_timestamp": current_time,
            "network_type": "ONNYX Production"
        }
        
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO blocks (block_hash, block_number, previous_hash, data, timestamp, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            "GENESIS_BLOCK_PRODUCTION_2025",
            0,
            "0000000000000000000000000000000000000000000000000000000000000000",
            json.dumps(genesis_block_data),
            current_time,
            current_time
        ))
        
        conn.commit()
        conn.close()
        
        print("✅ Genesis Block #0 created")
        return True
        
    except Exception as e:
        print(f"Error creating Genesis Block: {e}")
        return False

def main():
    """Execute complete production reset and relaunch."""
    print("🚀 ONNYX PHASE 1 PRODUCTION RESET & RELAUNCH")
    print("=" * 70)
    print("Creating real production identities and businesses")
    print("Date:", datetime.now().strftime("%B %d, %Y at %I:%M %p"))
    print("=" * 70)
    
    # Step 1: Backup and reset database
    backup_path = backup_existing_database()
    if not reset_database():
        print("❌ Database reset failed")
        return False
    
    print()
    print("👥 Creating Production Identities...")
    print("-" * 50)
    
    # Step 2: Create the 3 real production identities
    identities = []
    
    # 1. Djuvane Martin (Platform Founder)
    print("1. Creating Djuvane Martin (Platform Founder)...")
    djuvane = create_production_identity(
        name="Djuvane Martin",
        email="<EMAIL>",
        organization="ONNYX Foundation",
        role="Platform Founder",
        is_platform_founder=True
    )
    if djuvane:
        identities.append(djuvane)
        print(f"   ✅ {djuvane['name']} created successfully")
        print(f"      Email: {djuvane['email']}")
        print(f"      Role: {djuvane['role']}")
        print(f"      Credentials: {djuvane['credentials_file']}")
    else:
        print("   ❌ Failed to create Djuvane Martin")
        return False
    
    # 2. Sheryl Williams
    print("\n2. Creating Sheryl Williams...")
    sheryl = create_production_identity(
        name="Sheryl Williams",
        email="<EMAIL>",
        organization="Sheryl Williams Hair Replacement",
        role="Business Owner"
    )
    if sheryl:
        identities.append(sheryl)
        print(f"   ✅ {sheryl['name']} created successfully")
        print(f"      Email: {sheryl['email']}")
        print(f"      Organization: {sheryl['organization']}")
        print(f"      Credentials: {sheryl['credentials_file']}")
    else:
        print("   ❌ Failed to create Sheryl Williams")
        return False
    
    # 3. Michael Williams
    print("\n3. Creating Michael Williams...")
    michael = create_production_identity(
        name="Michael Williams",
        email="<EMAIL>",
        organization="GetTwisted Hair Studios",
        role="Business Owner"
    )
    if michael:
        identities.append(michael)
        print(f"   ✅ {michael['name']} created successfully")
        print(f"      Email: {michael['email']}")
        print(f"      Organization: {michael['organization']}")
        print(f"      Credentials: {michael['credentials_file']}")
    else:
        print("   ❌ Failed to create Michael Williams")
        return False
    
    # Create Genesis Block
    print("\n⛓️ Creating Genesis Block...")
    if not create_genesis_block(djuvane):
        print("❌ Genesis Block creation failed")
        return False
    
    print()
    print("🏢 Registering Business Validators...")
    print("-" * 50)
    
    # Step 3: Register associated businesses
    businesses = []
    
    # 1. ONNYX (Djuvane Martin)
    print("1. Registering ONNYX...")
    onnyx_business = create_business_validator(
        business_name="ONNYX",
        category="Blockchain Technology",
        description="Trusted business network platform providing quantum-resistant cryptographic identities and decentralized validation for secure commerce.",
        address="Digital Platform - Global Network",
        services=["Blockchain Platform", "Identity Verification", "Business Validation", "Secure Commerce", "Decentralized Mining"],
        owner_identity_id=djuvane['identity_id'],
        mining_tier="ONNYX Pro",
        mining_power=10.0
    )
    if onnyx_business:
        businesses.append(onnyx_business)
        print(f"   ✅ {onnyx_business['name']} registered successfully")
        print(f"      Category: {onnyx_business['category']}")
        print(f"      Mining: {onnyx_business['mining_tier']} ({onnyx_business['mining_power']}x)")
    
    # 2. Sheryl Williams Hair Replacement
    print("\n2. Registering Sheryl Williams Hair Replacement...")
    sheryl_business = create_business_validator(
        business_name="Sheryl Williams Hair Replacement",
        category="Beauty & Hair Services",
        description="Professional hair replacement services providing high-quality hair solutions and personalized styling for clients seeking natural-looking results.",
        address="Professional Hair Studio",
        services=["Hair Replacement", "Hair Restoration", "Custom Styling", "Hair Consultation", "Professional Hair Care"],
        owner_identity_id=sheryl['identity_id'],
        mining_tier="ONNYX Optimized",
        mining_power=3.0
    )
    if sheryl_business:
        businesses.append(sheryl_business)
        print(f"   ✅ {sheryl_business['name']} registered successfully")
        print(f"      Category: {sheryl_business['category']}")
        print(f"      Mining: {sheryl_business['mining_tier']} ({sheryl_business['mining_power']}x)")
    
    # 3. GetTwisted Hair Studios
    print("\n3. Registering GetTwisted Hair Studios...")
    michael_business = create_business_validator(
        business_name="GetTwisted Hair Studios",
        category="Beauty & Hair Services",
        description="Full-service hair salon offering professional hair styling, cutting, coloring, and specialized hair treatments in a modern studio environment.",
        address="Modern Hair Studio & Salon",
        services=["Hair Styling", "Hair Cutting", "Hair Coloring", "Hair Treatments", "Salon Services"],
        owner_identity_id=michael['identity_id'],
        mining_tier="ONNYX Optimized",
        mining_power=3.0
    )
    if michael_business:
        businesses.append(michael_business)
        print(f"   ✅ {michael_business['name']} registered successfully")
        print(f"      Category: {michael_business['category']}")
        print(f"      Mining: {michael_business['mining_tier']} ({michael_business['mining_power']}x)")
    
    # Final verification
    print("\n🔍 Verifying Production Setup...")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM identities")
        identity_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM selas")
        business_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM blocks WHERE block_number = 0")
        genesis_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM mining")
        mining_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ Identities Created: {identity_count}")
        print(f"✅ Businesses Registered: {business_count}")
        print(f"✅ Genesis Block: {genesis_count}")
        print(f"✅ Mining Nodes: {mining_count}")
        
        if identity_count == 3 and business_count == 3 and genesis_count == 1:
            print("\n🎉 PRODUCTION RESET & RELAUNCH COMPLETE!")
            print("✨ All real production identities and businesses created")
            print("⛓️ ONNYX trusted business network is ready")
            print("🌟 Clean production data - no test data remaining")
            
            print("\n📋 Production Network Summary:")
            print("   👑 Platform Founder: Djuvane Martin")
            print("   🏢 Business Owners: Sheryl Williams, Michael Williams")
            print("   🏪 Registered Businesses: 3 active validators")
            print("   ⛏️ Mining Network: Operational")
            print("   🔒 Security: Quantum-resistant cryptography")
            
            return True
        else:
            print("\n⚠️ Production setup incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    main()
