# Onnyx Etzem Trust Growth Engine

The Etzem Trust Growth Engine is a system that rewards identities with increasing trust, reputation, and merit based on real economic activity within Onnyx.

## Overview

Etzem (עֶצֶם) means "essence" or "bone" — it reflects the true inner strength of an identity based on:

| Metric | Source of Reputation |
|--------|---------------------|
| 💼 Sela Operations | Joined or founded a Sela |
| 🔁 Service Volume | Provided services (logs, TXs) |
| 🏆 Reputation Given | Received trust from others |
| 🪙 Token Impact | Minted tokens used by others |
| 🧱 Consistency | Active over time |
| 🔒 Badges Earned | Held roles like FOUNDER, VOTER |

## Components

### EtzemEngine

The EtzemEngine calculates Etzem trust scores for identities. It takes into account:

- **Consistency**: Based on account age and activity consistency
- **Transaction Score**: Based on transaction volume
- **Trust Weight**: Based on reputation received
- **Badge Bonus**: Based on badges earned
- **Sela Participation**: Based on Sela membership and founding
- **Token Impact**: Based on token minting and usage

### Etzem Badges

The Etzem Trust Growth Engine automatically assigns badges to identities based on their Etzem score:

- **ETZEM_TRUSTED**: Etzem score >= 30
- **ETZEM_REPUTABLE**: Etzem score >= 50
- **ETZEM_ESTEEMED**: Etzem score >= 70
- **ETZEM_VENERABLE**: Etzem score >= 90

### Leaderboard

The Etzem Trust Growth Engine provides a leaderboard of identities sorted by Etzem score.

## API Endpoints

### Get Etzem Score

```
GET /api/etzem/{identity_id}
```

Returns the Etzem trust score for an identity.

Example response:

```json
{
  "identity_id": "alice",
  "consistency": 9.33,
  "tx_score": 20.0,
  "trust_weight": 16.0,
  "badge_bonus": 12,
  "sela_participation": 15,
  "token_impact": 10,
  "etzem": 82.33
}
```

### Update Etzem Badges

```
POST /api/etzem/{identity_id}/badges
```

Updates the Etzem badges for an identity.

Example response:

```json
{
  "status": "badges_updated",
  "identity": {
    "identity_id": "alice",
    "name": "Alice",
    "public_key": "0x123456789abcdef",
    "created_at": 1746801307,
    "reputation": 80,
    "etzem_score": 83.83,
    "badges": [
      "SELA_EMPLOYEE",
      "STAKER",
      "PROPOSAL_ELIGIBLE_BADGE",
      "GUARDIAN_ELIGIBLE_BADGE",
      "VALIDATOR_ELIGIBLE_BADGE",
      "SELA_FOUNDER",
      "ETZEM_TRUSTED",
      "ETZEM_REPUTABLE",
      "ETZEM_ESTEEMED"
    ],
    "joined_selas": [],
    "founded_selas": [
      "alices_salon"
    ]
  }
}
```

### Update All Etzem Badges

```
POST /api/etzem/badges/update-all
```

Updates the Etzem badges for all identities.

Example response:

```json
{
  "status": "all_badges_updated",
  "count": 3
}
```

### Get Leaderboard

```
GET /api/leaderboard?limit=10
```

Returns the Etzem leaderboard.

Example response:

```json
{
  "leaderboard": [
    {
      "identity_id": "alice",
      "consistency": 9.33,
      "tx_score": 20.0,
      "trust_weight": 16.0,
      "badge_bonus": 12,
      "sela_participation": 15,
      "token_impact": 10,
      "etzem": 82.33
    },
    {
      "identity_id": "bob",
      "consistency": 6.33,
      "tx_score": 20.0,
      "trust_weight": 10.0,
      "badge_bonus": 4,
      "sela_participation": 10,
      "token_impact": 10,
      "etzem": 60.33
    },
    {
      "identity_id": "charlie",
      "consistency": 3.33,
      "tx_score": 4.2,
      "trust_weight": 4.0,
      "badge_bonus": 0,
      "sela_participation": 0,
      "token_impact": 5.0,
      "etzem": 16.53
    }
  ]
}
```

### Get Etzem Thresholds

```
GET /api/thresholds
```

Returns the Etzem thresholds for badges.

Example response:

```json
{
  "thresholds": {
    "TRUSTED": 30,
    "REPUTABLE": 50,
    "ESTEEMED": 70,
    "VENERABLE": 90
  }
}
```

## Usage

### Running the API Server

```bash
python run_server.py --port 8889
```

### Testing the Etzem Trust Growth Engine

```bash
python test_trust_etzem.py
```

## Integration with Other Systems

The Etzem Trust Growth Engine integrates with the following systems:

- **Identity Registry**: For managing identities and badges
- **Token Ledger**: For tracking transaction volume
- **Sela Registry**: For tracking Sela participation
- **Activity-based Etzem Engine**: For tracking activity consistency and token impact

## Etzem Score Gating

The Etzem score is used to gate access to:

- **Proposal submission**: Requires ETZEM_TRUSTED badge (Etzem score >= 30)
- **Guardian candidacy**: Requires ETZEM_REPUTABLE badge (Etzem score >= 50)
- **Mikvah minting rights**: Requires ETZEM_ESTEEMED badge (Etzem score >= 70)
- **Validator eligibility**: Requires ETZEM_VENERABLE badge (Etzem score >= 90)
