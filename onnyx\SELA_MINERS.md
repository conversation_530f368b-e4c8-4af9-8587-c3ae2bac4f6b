# Onnyx Sela-Owned Miner Nodes

## Overview

Each Sela (business or guild) receives a dedicated Onnyx Miner Node — a secure unit to anchor activity, validate blocks, and serve as a digital root of trust.

- Miner nodes are **non-extractive** — they validate value, not waste energy.
- Each miner holds:
  - A **soulbound identity**
  - A **Sela profile** (services, tokens, roles)
  - A **local ledger** of its on-chain impact
- Blocks are signed using identity keys, proving the work came from a real contributor.

## Components

### Block Signer

The Block Signer provides utilities for signing and verifying blocks. It ensures that blocks are signed by authorized Selas and that the signatures can be verified.

### Sela Configuration

The Sela Configuration manages the configuration for a Sela miner node. It includes:

- Sela ID
- Identity ID
- Private key path
- API port
- Role
- Auto-mine settings
- Validator rotation settings

### Rotation Registry

The Rotation Registry manages the rotation of active validators. It ensures that only eligible Selas can validate blocks and that the validation role is rotated among eligible Selas.

### Activity Ledger

The Activity Ledger tracks activities for a Sela. It records:

- Services provided
- Pending transactions
- Governance votes
- Mining activities

## CLI Commands

The Onnyx Sela Miner CLI provides commands for managing a Sela miner node:

### Setup

```bash
python onnyx-sela-miner.py setup --sela-id <sela_id> --identity-id <identity_id> --private-key <private_key_path> --port <port>
```

### Mine

```bash
python onnyx-sela-miner.py mine [--force]
```

### Record Service

```bash
python onnyx-sela-miner.py service --type <service_type> --description <description> [--recipient <recipient_id>] [--duration <duration>] [--metadata <metadata>]
```

### Vote

```bash
python onnyx-sela-miner.py vote --scroll-id <scroll_id> --vote <yes|no|abstain> [--reason <reason>]
```

### Status

```bash
python onnyx-sela-miner.py status
```

## API Endpoints

The Onnyx Sela Miner API provides endpoints for managing a Sela miner node:

### Get Status

```
GET /api/sela-miner/status
```

### Mine Block

```
POST /api/sela-miner/mine?force=false
```

### Record Service

```
POST /api/sela-miner/service?service_type=<service_type>&description=<description>&recipient_id=<recipient_id>&duration=<duration>
```

### Record Vote

```
POST /api/sela-miner/vote?scroll_id=<scroll_id>&vote=<vote>&reason=<reason>
```

### Get Activities

```
GET /api/sela-miner/activities?activity_type=<activity_type>&limit=<limit>
```

### Get Services

```
GET /api/sela-miner/services?service_type=<service_type>&limit=<limit>
```

### Get Pending Transactions

```
GET /api/sela-miner/pending-transactions?tx_type=<tx_type>&status=<status>
```

### Get Votes

```
GET /api/sela-miner/votes?scroll_id=<scroll_id>
```

### Get Validator Status

```
GET /api/sela-miner/validator-status
```

## Block Structure

Blocks in the Onnyx blockchain include the following fields:

```json
{
  "index": 1,
  "timestamp": 1746801307,
  "transactions": [...],
  "previous_hash": "0x123456789abcdef",
  "nonce": 0,
  "signed_by": "sela_id",
  "signature": "base64(...)",
  "hash": "0x987654321fedcba"
}
```

## Validator Rotation

The validator rotation engine ensures that only eligible Selas can validate blocks and that the validation role is rotated among eligible Selas. Eligibility is based on:

- Minimum Etzem score
- Required badges (e.g., SELA_FOUNDER, VALIDATOR_ELIGIBLE_BADGE)

## Onboarding Flow

1. Register identity
2. Register or join a Sela
3. Set up Onnyx Miner
4. Begin minting tokens, submitting services, and validating blocks

## Implementation Details

### Block Signing

Blocks are signed using ECDSA over the secp256k1 curve (the same as Bitcoin and Ethereum). The signature is created by:

1. Hashing the block content (excluding the signature and hash fields)
2. Signing the hash with the Sela's private key
3. Adding the signature and signer to the block

### Block Verification

Block signatures are verified by:

1. Hashing the block content (excluding the signature and hash fields)
2. Verifying the signature using the Sela founder's public key

### Validator Rotation

Validators are rotated based on:

1. Eligibility (Etzem score and badges)
2. Rotation interval (default: 1 hour)
3. Random selection from eligible validators

## Deployment Options

The Onnyx Sela Miner can be deployed in various ways:

- **Docker container** with FastAPI + SQLite volume
- **ElectronJS or PyQt GUI** (optional)
- **pip install onnyx-miner + CLI**
- **Raspberry Pi image** for edge hardware

## Future Enhancements

- **Proof of Activity**: Replace Proof of Work with a consensus mechanism based on real-world activity
- **Sela Federation**: Allow Selas to form federations for collaborative validation
- **Hardware Security Module**: Enhance security with dedicated hardware for key storage
- **Mobile App**: Provide a mobile interface for Sela miners
