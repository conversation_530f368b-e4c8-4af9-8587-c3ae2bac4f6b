"""
Initialize Test Database

This script initializes the test database with the necessary tables and test data.
"""

import os
import sys
import json
import sqlite3
import logging
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config.config import onnyx_config
from models.identity import Identity
from models.token import Token
from models.transaction import Transaction
from models.block import Block
from models.sela import Sela
from models.etzem import Etzem
from models.activity import Activity
from models.service import Service
from models.vote import Vote
from models.rotation import Rotation
from models.chain_parameter import ChainParameter
from models.voice_scroll import VoiceScroll

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("onnyx.tests.init_test_db")

def create_test_db():
    """Create the test database."""
    # Create the test data directory if it doesn't exist
    test_data_dir = os.path.join(os.path.dirname(__file__), "data")
    os.makedirs(test_data_dir, exist_ok=True)
    
    # Set the database path
    db_path = os.path.join(test_data_dir, "test.db")
    
    # Remove the database if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # Create the database
    conn = sqlite3.connect(db_path)
    conn.close()
    
    logger.info(f"Created test database at {db_path}")
    
    return db_path

def create_tables(db_path):
    """Create the tables in the test database."""
    # Set the database path in the config
    onnyx_config.db_path = db_path
    
    # Create the tables
    Identity.create_table()
    Token.create_table()
    Transaction.create_table()
    Block.create_table()
    Sela.create_table()
    Etzem.create_table()
    Activity.create_table()
    Service.create_table()
    Vote.create_table()
    Rotation.create_table()
    ChainParameter.create_table()
    VoiceScroll.create_table()
    
    logger.info("Created tables in test database")

def create_test_data(db_path):
    """Create test data in the test database."""
    # Set the database path in the config
    onnyx_config.db_path = db_path
    
    # Create a test identity
    identity = Identity.create(
        identity_id="test_identity",
        name="Test Identity",
        public_key="test_public_key",
        metadata={
            "type": "individual",
            "description": "Test identity"
        }
    )
    
    # Create a test token
    token = Token.create(
        token_id="test_token",
        name="Test Token",
        symbol="TST",
        creator_id=identity.identity_id,
        supply=1000,
        category="test",
        decimals=2,
        metadata={
            "description": "Test token"
        }
    )
    
    # Create a test block
    block = Block.create(
        block_hash="test_block_hash",
        previous_hash="0000000000000000000000000000000000000000000000000000000000000000",
        index=0,
        timestamp=0,
        transactions=[],
        nonce=0,
        difficulty=1,
        miner=identity.identity_id
    )
    
    # Create a test transaction
    transaction = Transaction.create(
        tx_id="test_tx",
        tx_type="TEST",
        sender=identity.identity_id,
        recipient=identity.identity_id,
        token_id=token.token_id,
        amount=100,
        status="CONFIRMED",
        block_hash=block.block_hash,
        block_index=block.index,
        data={
            "op": "OP_TEST",
            "description": "Test transaction"
        }
    )
    
    # Create a test Sela
    sela = Sela.create(
        sela_id="test_sela",
        name="Test Sela",
        founder_id=identity.identity_id,
        sela_type="BUSINESS",
        metadata={
            "description": "Test Sela"
        }
    )
    
    # Create a test Etzem score
    etzem = Etzem.create(
        identity_id=identity.identity_id,
        etzem_score=100,
        components={
            "reputation": 50,
            "activity": 50
        },
        badges=["CONTRIBUTOR"]
    )
    
    # Create a test Voice Scroll
    scroll = VoiceScroll.create(
        scroll_id="test_scroll",
        creator_id=identity.identity_id,
        title="Test Scroll",
        description="Test Voice Scroll",
        category="test",
        expiry_days=7,
        effect={
            "param": "test_param",
            "value": "test_value"
        }
    )
    
    # Create a test chain parameter
    parameter = ChainParameter.create(
        key="test_param",
        value=100,
        default_value=50,
        description="Test parameter",
        category="test"
    )
    
    # Create a test rotation
    rotation = Rotation.create(
        id="test_rotation",
        current_validator=sela.sela_id,
        queue=[sela.sela_id],
        update_interval=3600,
        min_etzem_score=100,
        required_badges=["VALIDATOR"]
    )
    
    logger.info("Created test data in test database")

def main():
    """Initialize the test database."""
    # Create the test database
    db_path = create_test_db()
    
    # Create the tables
    create_tables(db_path)
    
    # Create test data
    create_test_data(db_path)
    
    logger.info("Test database initialization complete")

if __name__ == "__main__":
    main()
