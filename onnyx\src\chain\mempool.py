# src/chain/mempool.py

import time
import logging
from typing import List, Optional

from models.mempool import MempoolTransaction

# Set up logging
logger = logging.getLogger("onnyx.chain.mempool")

class Mempool:
    def __init__(self):
        """
        Initialize a mempool.
        """
        self.transactions_cache = []
        self._load()

    def _load(self):
        """
        Load pending transactions from the database.
        """
        try:
            # Get all mempool transactions from the database
            transactions = MempoolTransaction.get_all()

            # Cache the transactions
            self.transactions_cache = transactions

            logger.info(f"Loaded {len(self.transactions_cache)} transactions from the mempool")
        except Exception as e:
            logger.error(f"Error loading mempool: {str(e)}")
            self.transactions_cache = []

    def add(self, op: str, data: dict, sender: str, signature: str) -> MempoolTransaction:
        """
        Add a transaction to the mempool.

        Args:
            op: The operation type (e.g., "OP_MINT", "OP_SEND", "OP_BURN")
            data: The transaction data
            sender: The sender identity ID
            signature: The transaction signature

        Returns:
            The created mempool transaction
        """
        # Generate a unique transaction ID
        tx_id = f"mem_{int(time.time()*1000)}"

        # Create the transaction in the database
        tx = MempoolTransaction.create(
            tx_id=tx_id,
            timestamp=int(time.time()),
            op=op,
            data=data,
            sender=sender,
            signature=signature
        )

        # Add the transaction to the cache
        self.transactions_cache.append(tx)

        logger.info(f"Added transaction {tx_id} to mempool")
        return tx

    def get_all(self, limit: int = 50, offset: int = 0) -> List[MempoolTransaction]:
        """
        Get all pending transactions.

        Args:
            limit: Maximum number of transactions to return
            offset: Number of transactions to skip

        Returns:
            A list of pending transactions
        """
        # Refresh the cache
        self._load()

        # Sort transactions by timestamp (newest first)
        sorted_txs = sorted(self.transactions_cache, key=lambda x: x.timestamp, reverse=True)

        # Apply pagination
        return sorted_txs[offset:offset+limit]

    def get_by_op(self, op: str, limit: int = 50, offset: int = 0) -> List[MempoolTransaction]:
        """
        Get pending transactions of a specific operation type.

        Args:
            op: The operation type
            limit: Maximum number of transactions to return
            offset: Number of transactions to skip

        Returns:
            A list of pending transactions
        """
        # Refresh the cache
        self._load()

        # Find transactions of the specified type
        op_txs = [tx for tx in self.transactions_cache if tx.op == op]

        # Sort transactions by timestamp (newest first)
        sorted_txs = sorted(op_txs, key=lambda x: x.timestamp, reverse=True)

        # Apply pagination
        return sorted_txs[offset:offset+limit]

    def get_by_id(self, tx_id: str) -> Optional[MempoolTransaction]:
        """
        Get a pending transaction by its ID.

        Args:
            tx_id: The transaction ID

        Returns:
            The transaction, or None if not found
        """
        # Try to get from the cache first
        for tx in self.transactions_cache:
            if tx.tx_id == tx_id:
                return tx

        # If not in cache, try to get from the database
        return MempoolTransaction.get_by_id(tx_id)

    def remove(self, tx_id: str) -> bool:
        """
        Remove a transaction from the mempool.

        Args:
            tx_id: The transaction ID

        Returns:
            True if the transaction was removed, False otherwise
        """
        # Get the transaction
        tx = self.get_by_id(tx_id)
        if not tx:
            return False

        # Remove from the database
        tx.remove()

        # Remove from the cache
        self.transactions_cache = [tx for tx in self.transactions_cache if tx.tx_id != tx_id]

        logger.info(f"Removed transaction {tx_id} from mempool")
        return True

    def clear(self) -> None:
        """
        Clear the mempool.
        """
        # Remove all transactions from the database
        for tx in self.transactions_cache:
            tx.remove()

        # Clear the cache
        self.transactions_cache = []

        logger.info("Cleared mempool")
