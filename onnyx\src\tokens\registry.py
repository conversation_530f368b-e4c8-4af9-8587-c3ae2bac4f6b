# src/tokens/registry.py

import os
import hashlib
import time
import logging
from typing import List, Optional, Dict, Any

from models.token import Token
from config.config import onnyx_config

# Set up logging
logger = logging.getLogger("onnyx.tokens.registry")

class TokenRegistry:
    def __init__(self):
        """
        Initialize the token registry.
        """
        self.tokens_cache = {}
        self._load()

    def _load(self):
        """
        Load tokens from the database into the cache.
        """
        try:
            # Get all tokens from the database
            tokens = Token.get_all()

            # Cache the tokens
            self.tokens_cache = {token.token_id: token for token in tokens}

            logger.info(f"Loaded {len(self.tokens_cache)} tokens from the database")
        except Exception as e:
            logger.error(f"Error loading tokens: {str(e)}")
            self.tokens_cache = {}

    def generate_token_id(self, name: str, symbol: str, creator_id: str) -> str:
        """
        Generate a unique token ID based on name, symbol, and creator identity.

        Args:
            name: The token name
            symbol: The token symbol
            creator_id: The creator identity ID

        Returns:
            A unique token ID
        """
        timestamp = int(time.time())
        token_string = f"{name}{symbol}{creator_id}{timestamp}"
        return hashlib.sha256(token_string.encode()).hexdigest()[:16]  # Use first 16 chars of hash

    def fork_token(self, name: str, symbol: str, creator_id: str, token_type: str, supply: float,
                  decimals: int = 2, mintable: bool = False, transferable: bool = True, metadata: Dict[str, Any] = None) -> Token:
        """
        Create a new token (fork) linked to an identity.

        Args:
            name: The token name
            symbol: The token symbol
            creator_id: The creator identity ID
            token_type: The token type
            supply: The initial token supply
            decimals: The number of decimal places
            mintable: Whether the token is mintable
            transferable: Whether the token is transferable
            metadata: Additional token metadata

        Returns:
            The created token
        """
        token_id = self.generate_token_id(name, symbol, creator_id)

        # Check if the token ID already exists
        if Token.get_by_id(token_id):
            raise ValueError(f"Token ID collision: {token_id}. Please try again.")

        # Prepare metadata
        token_metadata = metadata or {}
        token_metadata.update({
            "category": token_type,
            "decimals": decimals,
            "mintable": mintable,
            "transferable": transferable
        })

        # Create the token in the database
        token = Token.create(
            token_id=token_id,
            name=name,
            symbol=symbol,
            creator_id=creator_id,
            supply=supply,
            metadata=token_metadata
        )

        # Cache the token
        self.tokens_cache[token_id] = token

        logger.info(f"Created token {token_id}: {name} ({symbol})")
        return token

    def get_token(self, token_id: str) -> Optional[Token]:
        """
        Get a token by its ID.

        Args:
            token_id: The token ID

        Returns:
            The token or None if not found
        """
        # Check the cache first
        if token_id in self.tokens_cache:
            return self.tokens_cache[token_id]

        # If not in cache, try to get from the database
        token = Token.get_by_id(token_id)

        # Update the cache if found
        if token:
            self.tokens_cache[token_id] = token

        return token

    def list_tokens(self) -> List[Token]:
        """
        List all tokens in the registry.

        Returns:
            A list of tokens
        """
        # Refresh the cache
        self._load()

        return list(self.tokens_cache.values())

    def get_tokens_by_creator(self, creator_id: str) -> List[Token]:
        """
        Get all tokens created by a specific identity.

        Args:
            creator_id: The creator identity ID

        Returns:
            A list of tokens
        """
        return Token.get_by_creator(creator_id)

    def get_tokens_by_category(self, category: str) -> List[Token]:
        """
        Get all tokens of a specific category.

        Args:
            category: The token category

        Returns:
            A list of tokens
        """
        return Token.get_by_category(category)

    def update_token_supply(self, token_id: str, new_supply: float) -> Optional[Token]:
        """
        Update a token's supply (for minting/burning).

        Args:
            token_id: The token ID
            new_supply: The new token supply

        Returns:
            The updated token or None if not found
        """
        token = self.get_token(token_id)
        if not token:
            raise ValueError(f"Token {token_id} not found.")

        # Update the token supply
        token.total_supply = new_supply
        token.save()

        logger.info(f"Updated token {token_id} supply to {new_supply}")
        return token
