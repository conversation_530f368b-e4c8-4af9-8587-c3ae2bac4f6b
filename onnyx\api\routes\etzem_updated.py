"""
Onnyx Etzem Routes

This module provides API routes for Etzem operations.
"""

import logging
import time
from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional

from identity.trust.etzem_engine import EtzemEngine
from identity.registry import IdentityRegistry
from tokens.ledger.ledger.ledger.ledger import TokenLedger
from sela.registry.sela_registry import SelaRegistry
from governance.etzem_engine import EtzemEngine as ActivityEtzemEngine

from shared.models.etzem import Etzem
from shared.models.identity import Identity
from shared.models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.routes.etzem")

# Create router
router = APIRouter()

# Create instances
identities = IdentityRegistry()
ledger = TokenLedger()
selas = SelaRegistry()
activity_etzem = ActivityEtzemEngine()
etzem_engine = EtzemEngine(identities, ledger, selas, activity_etzem)

# Define Etzem thresholds
ETZEM_THRESHOLDS = {
    "GUARDIAN": 1000,
    "COUNCIL_ELIGIBLE": 750,
    "CURATOR": 500,
    "MENTOR": 250,
    "CREATOR": 100,
    "CONTRIBUTOR": 50,
    "MEMBER": 10
}

@router.get("/etzem/{identity_id}")
def get_etzem(identity_id: str) -> Dict[str, Any]:
    """
    Get the Etzem trust score for an identity.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        The Etzem trust score
    """
    logger.info(f"Getting Etzem score for identity: {identity_id}")
    
    # Check if the identity exists
    identity = Identity.get_by_id(identity_id)
    if not identity:
        logger.warning(f"Identity with ID '{identity_id}' not found")
        raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' not found")
    
    # Check if the Etzem score exists in the database
    etzem = Etzem.get_by_id(identity_id)
    
    # If the Etzem score doesn't exist or is outdated, compute it
    if not etzem or (etzem.last_updated < (int(time.time()) - 86400)):  # Older than 1 day
        logger.info(f"Computing new Etzem score for identity: {identity_id}")
        
        # Compute the Etzem score
        result = etzem_engine.compute_etzem(identity_id)
        if not result:
            logger.warning(f"Failed to compute Etzem score for identity: {identity_id}")
            raise HTTPException(status_code=500, detail=f"Failed to compute Etzem score for identity: {identity_id}")
        
        # Create or update the Etzem score in the database
        if etzem:
            etzem.update_score(result["etzem"], result["components"])
        else:
            etzem = Etzem.create(
                identity_id=identity_id,
                etzem_score=result["etzem"],
                components=result["components"],
                badges=result.get("badges", [])
            )
        
        logger.info(f"Computed Etzem score for identity: {identity_id}")
    
    # Return the Etzem score
    return {
        "identity_id": etzem.identity_id,
        "etzem": etzem.etzem_score,
        "components": etzem.components,
        "badges": etzem.badges,
        "last_updated": etzem.last_updated
    }

@router.post("/etzem/{identity_id}/badges")
def update_etzem_badges(identity_id: str) -> Dict[str, Any]:
    """
    Update the Etzem badges for an identity.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        The updated identity
    """
    logger.info(f"Updating Etzem badges for identity: {identity_id}")
    
    # Check if the identity exists
    identity = Identity.get_by_id(identity_id)
    if not identity:
        logger.warning(f"Identity with ID '{identity_id}' not found")
        raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' not found")
    
    # Get the Etzem score
    etzem_data = get_etzem(identity_id)
    etzem_score = etzem_data["etzem"]
    
    # Determine badges based on Etzem score
    badges = []
    for badge, threshold in ETZEM_THRESHOLDS.items():
        if etzem_score >= threshold:
            badges.append(badge)
    
    # Update the Etzem badges in the database
    etzem = Etzem.get_by_id(identity_id)
    if etzem:
        etzem.badges = badges
        etzem.save()
    
    # Update the identity's badges
    if "badges" not in identity.metadata:
        identity.metadata["badges"] = []
    
    # Remove old Etzem badges
    for badge in list(ETZEM_THRESHOLDS.keys()):
        if badge in identity.metadata["badges"]:
            identity.metadata["badges"].remove(badge)
    
    # Add new Etzem badges
    for badge in badges:
        if badge not in identity.metadata["badges"]:
            identity.metadata["badges"].append(badge)
    
    # Save the identity
    identity.save()
    
    # Update the identity in the registry
    result = etzem_engine.update_etzem_badges(identity_id)
    if not result:
        logger.warning(f"Failed to update Etzem badges in registry for identity: {identity_id}")
    
    logger.info(f"Updated Etzem badges for identity: {identity_id}")
    return {
        "status": "badges_updated",
        "identity": {
            "identity_id": identity.identity_id,
            "name": identity.name,
            "badges": identity.metadata.get("badges", []),
            "etzem_score": etzem_score
        }
    }

@router.post("/etzem/badges/update-all")
def update_all_etzem_badges() -> Dict[str, Any]:
    """
    Update the Etzem badges for all identities.
    
    Returns:
        The number of identities updated
    """
    logger.info("Updating Etzem badges for all identities")
    
    # Get all identities
    identities_list = Identity.get_all()
    
    # Update badges for each identity
    updated_identities = []
    for identity in identities_list:
        try:
            update_etzem_badges(identity.identity_id)
            updated_identities.append(identity.identity_id)
        except Exception as e:
            logger.error(f"Error updating Etzem badges for identity {identity.identity_id}: {str(e)}")
    
    # Update all badges in the registry
    result = etzem_engine.update_all_etzem_badges()
    
    logger.info(f"Updated Etzem badges for {len(updated_identities)} identities")
    return {
        "status": "all_badges_updated",
        "count": len(updated_identities),
        "identities": updated_identities
    }

@router.get("/leaderboard")
def get_leaderboard(limit: int = Query(10, ge=1, le=100)) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get the Etzem leaderboard.
    
    Args:
        limit: The maximum number of identities to return
    
    Returns:
        The Etzem leaderboard
    """
    logger.info(f"Getting Etzem leaderboard with limit: {limit}")
    
    # Get the leaderboard from the database
    etzem_scores = Etzem.get_leaderboard(limit)
    
    # Format the leaderboard
    leaderboard = []
    for etzem in etzem_scores:
        # Get the identity
        identity = Identity.get_by_id(etzem.identity_id)
        if identity:
            leaderboard.append({
                "identity_id": etzem.identity_id,
                "name": identity.name,
                "etzem_score": etzem.etzem_score,
                "components": etzem.components,
                "badges": etzem.badges
            })
    
    logger.info(f"Retrieved Etzem leaderboard with {len(leaderboard)} entries")
    return {
        "leaderboard": leaderboard
    }

@router.get("/thresholds")
def get_etzem_thresholds() -> Dict[str, Dict[str, int]]:
    """
    Get the Etzem thresholds for badges.
    
    Returns:
        The Etzem thresholds
    """
    logger.info("Getting Etzem thresholds")
    
    return {
        "thresholds": ETZEM_THRESHOLDS
    }
