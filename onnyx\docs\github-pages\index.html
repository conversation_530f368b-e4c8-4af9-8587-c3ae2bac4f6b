<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONNYX - The Digital Backbone of Trustworthy Commerce</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="logo-container">
                    <div class="logo-icon">⬢</div>
                    <span class="logo-text">ONNYX</span>
                </div>
            </div>

            <div class="nav-menu" id="navMenu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#validators" class="nav-link">Validators</a>
                <a href="#explorer" class="nav-link">Explorer</a>
                <a href="#dashboard" class="nav-link">Dashboard</a>
            </div>

            <div class="nav-auth">
                <button class="auth-btn secondary" onclick="redirectToBackend('/auth/login')">Access Portal</button>
                <button class="auth-btn primary" onclick="redirectToBackend('/auth/register')">Verify Identity</button>
            </div>

            <div class="mobile-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-grid"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    The Digital Backbone of
                    <span class="gradient-text">Trustworthy Commerce</span>
                </h1>
                <p class="hero-subtitle">
                    Blockchain-powered verification platform enabling transparent business operations
                    through cryptographic identity management and decentralized validation networks.
                </p>
                <div class="hero-actions">
                    <button class="cta-btn primary" onclick="redirectToBackend('/auth/register')">
                        <span class="btn-icon">✨</span>
                        <span>Verify Your Identity</span>
                    </button>
                    <button class="cta-btn secondary" onclick="scrollToSection('validators')">
                        <span class="btn-icon">🏢</span>
                        <span>Explore Validators</span>
                    </button>
                </div>
                <div class="hero-stats" id="heroStats">
                    <div class="stat-item">
                        <div class="stat-value" id="validatorCount">-</div>
                        <div class="stat-label">Active Validators</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="blockCount">-</div>
                        <div class="stat-label">Blocks Secured</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="onxSupply">-</div>
                        <div class="stat-label">ONNX Supply</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Validators Section -->
    <section id="validators" class="validators-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Validator Network Directory</h2>
                <p class="section-subtitle">
                    Verified business validators securing the ONNYX network
                </p>
            </div>

            <div class="loading-indicator" id="validatorsLoading">
                <div class="spinner"></div>
                <p>Loading validators from ONNYX network...</p>
            </div>

            <div class="validators-grid" id="validatorsGrid">
                <!-- Validators will be loaded dynamically from Render backend -->
            </div>

            <div class="validators-actions">
                <button class="action-btn" onclick="redirectToBackend('/sela/')">
                    View Full Directory
                </button>
                <button class="action-btn secondary" onclick="redirectToBackend('/auth/register')">
                    Become a Validator
                </button>
            </div>
        </div>
    </section>

    <!-- Blockchain Explorer Section -->
    <section id="explorer" class="explorer-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Blockchain Explorer</h2>
                <p class="section-subtitle">
                    Real-time blockchain data and transaction history
                </p>
            </div>

            <div class="explorer-grid">
                <div class="explorer-card">
                    <div class="card-header">
                        <h3>Latest Blocks</h3>
                        <button class="refresh-btn" onclick="loadBlockchainData()">↻</button>
                    </div>
                    <div class="card-body">
                        <div class="loading-indicator" id="blocksLoading">
                            <div class="spinner"></div>
                            <p>Loading blocks...</p>
                        </div>
                        <div class="block-list" id="blocksList">
                            <!-- Blocks will be loaded from Render backend -->
                        </div>
                    </div>
                </div>

                <div class="explorer-card">
                    <div class="card-header">
                        <h3>Network Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="network-stats" id="networkStats">
                            <div class="network-stat">
                                <div class="stat-label">Total Blocks</div>
                                <div class="stat-value" id="totalBlocks">-</div>
                            </div>
                            <div class="network-stat">
                                <div class="stat-label">Total Transactions</div>
                                <div class="stat-value" id="totalTransactions">-</div>
                            </div>
                            <div class="network-stat">
                                <div class="stat-label">Active Validators</div>
                                <div class="stat-value" id="activeValidators">-</div>
                            </div>
                            <div class="network-stat">
                                <div class="stat-label">Network Hash Rate</div>
                                <div class="stat-value" id="networkHashRate">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="explorer-actions">
                <button class="action-btn" onclick="redirectToBackend('/explorer/')">
                    Full Blockchain Explorer
                </button>
            </div>
        </div>
    </section>

    <!-- Dashboard Section -->
    <section id="dashboard" class="dashboard-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Platform Dashboard</h2>
                <p class="section-subtitle">
                    Access your identity, manage validators, and track performance
                </p>
            </div>

            <div class="dashboard-preview">
                <div class="dashboard-card">
                    <div class="card-icon">👤</div>
                    <div class="card-content">
                        <h3>Identity Management</h3>
                        <p>Secure cryptographic identity with profile management</p>
                        <button class="card-btn" onclick="redirectToBackend('/dashboard/profile')">
                            Manage Profile
                        </button>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">🏢</div>
                    <div class="card-content">
                        <h3>Validator Operations</h3>
                        <p>Register and manage your business validators</p>
                        <button class="card-btn" onclick="redirectToBackend('/dashboard/selas')">
                            My Validators
                        </button>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">⚡</div>
                    <div class="card-content">
                        <h3>Mining Dashboard</h3>
                        <p>Monitor mining performance and earnings</p>
                        <button class="card-btn" onclick="redirectToBackend('/dashboard/mining')">
                            Mining Stats
                        </button>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">💰</div>
                    <div class="card-content">
                        <h3>Token Management</h3>
                        <p>Track ONX balance and transaction history</p>
                        <button class="card-btn" onclick="redirectToBackend('/dashboard/tokens')">
                            My Tokens
                        </button>
                    </div>
                </div>
            </div>

            <div class="dashboard-notice">
                <div class="notice-icon">🔐</div>
                <div class="notice-content">
                    <h3>Secure Access Required</h3>
                    <p>Dashboard features require cryptographic identity verification</p>
                    <div class="notice-actions">
                        <button class="notice-btn primary" onclick="redirectToBackend('/auth/login')">
                            Access Portal
                        </button>
                        <button class="notice-btn secondary" onclick="redirectToBackend('/auth/register')">
                            Verify Identity
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section class="technology-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Quantum-Resistant Technology</h2>
                <p class="section-subtitle">
                    Built on cutting-edge cryptographic protocols for future-proof security
                </p>
            </div>

            <div class="tech-grid">
                <div class="tech-card">
                    <div class="tech-icon">🔐</div>
                    <h3>Etzem Trust Protocol</h3>
                    <p>Advanced cryptographic identity verification with quantum-resistant algorithms</p>
                </div>

                <div class="tech-card">
                    <div class="tech-icon">💎</div>
                    <h3>Mikvah Token Economy</h3>
                    <p>Sophisticated token economics enabling business loyalty programs and equity shares</p>
                </div>

                <div class="tech-card">
                    <div class="tech-icon">⚡</div>
                    <h3>Hybrid Mining System</h3>
                    <p>Proof-of-Trust combined with performance boost mining for optimal security</p>
                </div>

                <div class="tech-card">
                    <div class="tech-icon">🌐</div>
                    <h3>Decentralized Validation</h3>
                    <p>Distributed network of business validators ensuring transparency and trust</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="logo-container">
                        <div class="logo-icon">⬢</div>
                        <span class="logo-text">ONNYX</span>
                    </div>
                    <p class="footer-description">
                        The Digital Backbone of Trustworthy Commerce
                    </p>
                    <div class="footer-status">
                        <div class="status-indicator">
                            <div class="status-dot" id="networkStatus"></div>
                            <span id="networkStatusText">Checking network...</span>
                        </div>
                    </div>
                </div>

                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Platform</h4>
                        <a href="#validators">Validator Network</a>
                        <a href="#explorer">Blockchain Explorer</a>
                        <a href="#dashboard">Dashboard</a>
                        <a href="javascript:redirectToBackend('/auth/register')">Join Network</a>
                    </div>
                    <div class="footer-section">
                        <h4>Technology</h4>
                        <a href="#technology">Quantum-Resistant Security</a>
                        <a href="#technology">Etzem Trust Protocol</a>
                        <a href="#technology">Mikvah Token Economy</a>
                        <a href="#technology">Hybrid Mining</a>
                    </div>
                    <div class="footer-section">
                        <h4>Network Stats</h4>
                        <div class="footer-stat">Validators: <span id="footerValidators">-</span></div>
                        <div class="footer-stat">Blocks: <span id="footerBlocks">-</span></div>
                        <div class="footer-stat">ONNX Supply: <span id="footerSupply">-</span></div>
                        <div class="footer-stat">Uptime: <span id="footerUptime">-</span></div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 ONNYX Platform. Securing the future of digital commerce.</p>
                <div class="footer-meta">
                    <span>Frontend: GitHub Pages</span>
                    <span>Backend: Render</span>
                    <span>v1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Configuration -->
    <script>
        // Backend configuration - Update this when you deploy to Render
        const BACKEND_URL = 'https://your-onnyx-app.onrender.com'; // Replace with your Render URL
        const FALLBACK_URL = 'http://127.0.0.1:5000'; // Local development fallback

        // Auto-detect backend URL
        const API_BASE = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
            ? FALLBACK_URL
            : BACKEND_URL;
    </script>

    <script src="script.js"></script>
</body>
</html>
