#!/usr/bin/env python3

"""
Script to test inserting a transaction directly.
"""

import os
import sys
import time
import logging
import json
import hashlib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.test_insert_transaction")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from data.db import db

def test_insert_transaction():
    """Test inserting a transaction directly."""
    try:
        # Generate a transaction ID
        timestamp = int(time.time())
        tx_data = f"TEST_OP{{'test': 'data'}}SYSTEM{timestamp}"
        tx_id = hashlib.sha256(tx_data.encode()).hexdigest()
        
        # Create the transaction data
        transaction = {
            "tx_id": tx_id,
            "timestamp": timestamp,
            "op": "TEST_OP",
            "data": json.dumps({"test": "data"}),
            "sender": "SYSTEM",
            "signature": f"test_signature_{timestamp}",
            "status": "pending",
            "block_hash": None,
            "created_at": timestamp
        }
        
        logger.info(f"Inserting transaction: {transaction}")
        
        # Insert the transaction
        db.insert("transactions", transaction)
        
        # Verify that the transaction was inserted
        query = "SELECT * FROM transactions WHERE tx_id = ?"
        result = db.query_one(query, (tx_id,))
        
        if result:
            logger.info(f"Transaction found in database: {result}")
            return True
        else:
            logger.error(f"Transaction not found in database after insert: {tx_id}")
            return False
    except Exception as e:
        logger.error(f"Error inserting transaction: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main entry point."""
    logger.info("Testing transaction insertion...")
    
    success = test_insert_transaction()
    
    if success:
        logger.info("Transaction insertion test passed")
    else:
        logger.error("Transaction insertion test failed")

if __name__ == "__main__":
    main()
