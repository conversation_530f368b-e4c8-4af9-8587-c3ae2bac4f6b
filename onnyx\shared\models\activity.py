"""
Onnyx Activity Model

This module defines the Activity model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.activity")

class Activity(BaseModel):
    """
    Activity model for the Onnyx blockchain.
    
    An Activity represents an action performed by a Sela in the Onnyx ecosystem.
    """
    
    TABLE_NAME = "activities"
    PRIMARY_KEY = "activity_id"
    
    def __init__(
        self,
        activity_id: str,
        sela_id: str,
        activity_type: str,
        description: str,
        timestamp: int,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize an Activity.
        
        Args:
            activity_id: The activity ID
            sela_id: The Sela ID
            activity_type: The type of activity
            description: A description of the activity
            timestamp: The timestamp when the activity occurred
            metadata: Additional metadata for the activity (optional)
        """
        self.activity_id = activity_id
        self.sela_id = sela_id
        self.activity_type = activity_type
        self.description = description
        self.timestamp = timestamp
        self.metadata = metadata or {}
    
    @classmethod
    def create_table(cls) -> None:
        """Create the Activity table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            sela_id TEXT NOT NULL,
            activity_type TEXT NOT NULL,
            description TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            metadata TEXT
        )
        """)
        
        conn.commit()
        conn.close()
    
    @classmethod
    def create(
        cls,
        activity_id: str,
        sela_id: str,
        activity_type: str,
        description: str,
        timestamp: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "Activity":
        """
        Create a new Activity.
        
        Args:
            activity_id: The activity ID
            sela_id: The Sela ID
            activity_type: The type of activity
            description: A description of the activity
            timestamp: The timestamp when the activity occurred (optional)
            metadata: Additional metadata for the activity (optional)
        
        Returns:
            The created Activity
        """
        # Create the table if it doesn't exist
        cls.create_table()
        
        # Create the Activity
        activity = cls(
            activity_id=activity_id,
            sela_id=sela_id,
            activity_type=activity_type,
            description=description,
            timestamp=timestamp or int(time.time()),
            metadata=metadata
        )
        
        # Save the Activity to the database
        activity.save()
        
        return activity
    
    def save(self) -> None:
        """Save the Activity to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            sela_id,
            activity_type,
            description,
            timestamp,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            self.activity_id,
            self.sela_id,
            self.activity_type,
            self.description,
            self.timestamp,
            json.dumps(self.metadata)
        ))
        
        conn.commit()
        conn.close()
    
    @classmethod
    def get_by_id(cls, activity_id: str) -> Optional["Activity"]:
        """
        Get an Activity by ID.
        
        Args:
            activity_id: The activity ID
        
        Returns:
            The Activity, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            activity_type,
            description,
            timestamp,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (activity_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return cls(
                activity_id=row[0],
                sela_id=row[1],
                activity_type=row[2],
                description=row[3],
                timestamp=row[4],
                metadata=json.loads(row[5]) if row[5] else {}
            )
        
        return None
    
    @classmethod
    def get_all(cls, limit: int = 100, offset: int = 0) -> List["Activity"]:
        """
        Get all Activities.
        
        Args:
            limit: The maximum number of Activities to return
            offset: The offset for pagination
        
        Returns:
            A list of all Activities
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            activity_type,
            description,
            timestamp,
            metadata
        FROM {cls.TABLE_NAME}
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
        """, (limit, offset))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                activity_id=row[0],
                sela_id=row[1],
                activity_type=row[2],
                description=row[3],
                timestamp=row[4],
                metadata=json.loads(row[5]) if row[5] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_sela(cls, sela_id: str, limit: int = 100) -> List["Activity"]:
        """
        Find Activities by Sela ID.
        
        Args:
            sela_id: The Sela ID
            limit: The maximum number of Activities to return
        
        Returns:
            A list of Activities for the Sela
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            activity_type,
            description,
            timestamp,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE sela_id = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (sela_id, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                activity_id=row[0],
                sela_id=row[1],
                activity_type=row[2],
                description=row[3],
                timestamp=row[4],
                metadata=json.loads(row[5]) if row[5] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_type(cls, activity_type: str, limit: int = 100) -> List["Activity"]:
        """
        Find Activities by type.
        
        Args:
            activity_type: The activity type
            limit: The maximum number of Activities to return
        
        Returns:
            A list of Activities of the specified type
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            activity_type,
            description,
            timestamp,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE activity_type = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (activity_type, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                activity_id=row[0],
                sela_id=row[1],
                activity_type=row[2],
                description=row[3],
                timestamp=row[4],
                metadata=json.loads(row[5]) if row[5] else {}
            )
            for row in rows
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Activity to a dictionary.
        
        Returns:
            The Activity as a dictionary
        """
        return {
            "activity_id": self.activity_id,
            "sela_id": self.sela_id,
            "activity_type": self.activity_type,
            "description": self.description,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }
