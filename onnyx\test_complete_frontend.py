#!/usr/bin/env python3
"""
Complete ONNYX Frontend Test

Comprehensive test of all frontend components with the new Onyx Stone theme.
"""

import requests
import time

def test_complete_frontend():
    """Test all frontend components and functionality."""
    base_url = "http://127.0.0.1:5000"
    
    print("🌟 TESTING COMPLETE ONNYX FRONTEND")
    print("=" * 80)
    
    # Test 1: Core Pages with Onyx Theme
    print("1. Testing Core Pages with Onyx Stone Theme...")
    core_pages = [
        ("/", "Landing Page"),
        ("/register", "Registration Portal"),
        ("/auth/register/identity", "Identity Registration"),
        ("/sela/", "Validator Directory"),
        ("/explorer/", "Blockchain Explorer")
    ]
    
    core_success = 0
    for url, name in core_pages:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"   ✅ {name}: {response.status_code}")
                
                # Check for onyx theme elements
                content = response.text
                theme_elements = ["glass-card", "hologram-text", "cyber-cyan", "font-orbitron"]
                found_elements = sum(1 for element in theme_elements if element in content)
                print(f"      🎨 Theme elements: {found_elements}/{len(theme_elements)}")
                core_success += 1
            else:
                print(f"   ❌ {name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print(f"   📊 Core Pages: {core_success}/{len(core_pages)} working")
    
    # Test 2: API Endpoints
    print("\n2. Testing API Endpoints...")
    api_endpoints = [
        ("/api/stats", "Platform Statistics"),
        ("/api/validate/email", "Email Validation", "POST", {"email": "<EMAIL>"}),
        ("/api/generate-keys", "Key Generation", "POST", {}),
        ("/api/search?q=test", "Search API")
    ]
    
    api_success = 0
    for endpoint_data in api_endpoints:
        url = endpoint_data[0]
        name = endpoint_data[1]
        method = endpoint_data[2] if len(endpoint_data) > 2 else "GET"
        data = endpoint_data[3] if len(endpoint_data) > 3 else None
        
        try:
            if method == "POST":
                response = requests.post(f"{base_url}{url}", json=data)
            else:
                response = requests.get(f"{base_url}{url}")
            
            if response.status_code in [200, 201]:
                print(f"   ✅ {name}: {response.status_code}")
                
                # Check if response is JSON
                try:
                    json_data = response.json()
                    print(f"      📄 Response: {type(json_data).__name__} with {len(json_data)} fields")
                except:
                    print(f"      📄 Response: HTML content")
                
                api_success += 1
            else:
                print(f"   ❌ {name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print(f"   📊 API Endpoints: {api_success}/{len(api_endpoints)} working")
    
    # Test 3: Static Assets
    print("\n3. Testing Static Assets...")
    static_assets = [
        ("/static/css/main.css", "Main CSS"),
        ("/static/js/main.js", "Main JavaScript")
    ]
    
    static_success = 0
    for url, name in static_assets:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"   ✅ {name}: {response.status_code}")
                
                # Check file size
                content_length = len(response.content)
                print(f"      📦 Size: {content_length:,} bytes")
                
                # Check for theme-specific content
                content = response.text
                if "css" in url:
                    theme_checks = ["--onyx-black", "--cyber-cyan", "glass-card", "hologram-text"]
                    found = sum(1 for check in theme_checks if check in content)
                    print(f"      🎨 Theme CSS: {found}/{len(theme_checks)} elements")
                elif "js" in url:
                    js_checks = ["animateCounter", "initGlowEffects", "Onnyx"]
                    found = sum(1 for check in js_checks if check in content)
                    print(f"      ⚡ JavaScript: {found}/{len(js_checks)} features")
                
                static_success += 1
            else:
                print(f"   ❌ {name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print(f"   📊 Static Assets: {static_success}/{len(static_assets)} working")
    
    # Test 4: Form Functionality
    print("\n4. Testing Enhanced Form Functionality...")
    form_tests = [
        ("Email validation", "/api/validate/email", {"email": "<EMAIL>"}),
        ("Sela name validation", "/api/validate/sela-name", {"name": "Test Business"}),
        ("Key generation", "/api/generate-keys", {})
    ]
    
    form_success = 0
    for test_name, endpoint, data in form_tests:
        try:
            response = requests.post(f"{base_url}{endpoint}", json=data)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {test_name}: Working")
                
                # Check response structure
                if 'valid' in result:
                    print(f"      ✓ Validation: {result.get('valid')}")
                elif 'private_key' in result:
                    print(f"      🔑 Keys generated successfully")
                elif 'error' in result:
                    print(f"      ⚠️ Error: {result.get('error')}")
                
                form_success += 1
            else:
                print(f"   ❌ {test_name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {test_name}: Error - {e}")
    
    print(f"   📊 Form Functions: {form_success}/{len(form_tests)} working")
    
    # Test 5: Theme Consistency
    print("\n5. Testing Theme Consistency Across Pages...")
    theme_elements = [
        "glass-card",
        "hologram-text", 
        "cyber-cyan",
        "font-orbitron",
        "neuro-card",
        "hero-gradient"
    ]
    
    consistency_scores = []
    for url, name in core_pages:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                content = response.text
                found = sum(1 for element in theme_elements if element in content)
                percentage = (found / len(theme_elements)) * 100
                consistency_scores.append(percentage)
                print(f"   🎨 {name}: {percentage:.1f}% theme consistency")
        except:
            pass
    
    if consistency_scores:
        avg_consistency = sum(consistency_scores) / len(consistency_scores)
        print(f"   📊 Average Theme Consistency: {avg_consistency:.1f}%")
    
    # Test 6: Error Handling
    print("\n6. Testing Error Handling...")
    error_tests = [
        ("/nonexistent-page", "404 Error"),
        ("/api/nonexistent-endpoint", "API 404"),
    ]
    
    error_success = 0
    for url, name in error_tests:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 404:
                print(f"   ✅ {name}: Properly handled (404)")
                error_success += 1
            else:
                print(f"   ⚠️ {name}: Unexpected status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print(f"   📊 Error Handling: {error_success}/{len(error_tests)} working")
    
    # Final Summary
    print("\n" + "=" * 80)
    print("🎉 ONNYX FRONTEND TESTING COMPLETE!")
    print("\n📋 SUMMARY:")
    print(f"   ✅ Core Pages: {core_success}/{len(core_pages)} working")
    print(f"   ✅ API Endpoints: {api_success}/{len(api_endpoints)} working")
    print(f"   ✅ Static Assets: {static_success}/{len(static_assets)} working")
    print(f"   ✅ Form Functions: {form_success}/{len(form_tests)} working")
    print(f"   ✅ Error Handling: {error_success}/{len(error_tests)} working")
    
    total_tests = len(core_pages) + len(api_endpoints) + len(static_assets) + len(form_tests) + len(error_tests)
    total_success = core_success + api_success + static_success + form_success + error_success
    overall_percentage = (total_success / total_tests) * 100
    
    print(f"\n🎯 OVERALL SUCCESS RATE: {overall_percentage:.1f}% ({total_success}/{total_tests})")
    
    if overall_percentage >= 90:
        print("🌟 EXCELLENT! The ONNYX frontend is production-ready!")
    elif overall_percentage >= 75:
        print("✅ GOOD! The ONNYX frontend is mostly functional!")
    else:
        print("⚠️ NEEDS WORK! Some components require attention!")
    
    print("\n🚀 The ONNYX platform is ready for quantum-resistant business operations!")

if __name__ == "__main__":
    test_complete_frontend()
