#!/usr/bin/env python3
"""
ONNYX Visual Consistency Fixes Verification Test

Tests the comprehensive fixes for:
1. Opacity inconsistencies across all UI components
2. Footer positioning and design uniformity
3. Mobile responsiveness and touch targets
4. Visual consistency across all platform pages

Verifies that all pages maintain the sophisticated ONNYX Onyx Stone theme
while providing optimal user experience across devices.
"""

import os
import sys
import time
import requests
import subprocess
from datetime import datetime

def test_opacity_consistency():
    """Test that opacity inconsistencies have been fixed."""
    print("🎨 TESTING OPACITY CONSISTENCY FIXES")
    print("=" * 45)
    
    results = []
    
    try:
        # Check CSS file for opacity fixes
        css_path = os.path.join('web', 'static', 'css', 'main.css')
        
        if os.path.exists(css_path):
            with open(css_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            print("✅ CSS file found and readable")
            results.append(True)
            
            # Check for opacity standardization
            opacity_fixes = [
                'opacity: 1 !important; /* Force full opacity */',
                '/* OPACITY STANDARDIZATION',
                '/* All interactive elements should have full opacity',
                'opacity-70 { opacity: 1 !important; }',
                'opacity-60 { opacity: 1 !important; }',
                'opacity-50 { opacity: 0.8 !important; }',
                'opacity-40 { opacity: 0.7 !important; }'
            ]
            
            for fix in opacity_fixes:
                if fix in css_content:
                    print(f"✅ Found opacity fix: {fix[:50]}...")
                    results.append(True)
                else:
                    print(f"❌ Missing opacity fix: {fix[:50]}...")
                    results.append(False)
            
            # Check for enhanced glass card visibility
            glass_card_fixes = [
                'background: rgba(255, 255, 255, 0.12); /* Increased from 0.1 */',
                'border-color: rgba(0, 255, 255, 0.4); /* Increased from 0.3 */',
                'background: rgba(255, 255, 255, 0.15); /* Increased from 0.12 */'
            ]
            
            for fix in glass_card_fixes:
                if fix in css_content:
                    print(f"✅ Found glass card enhancement: {fix[:40]}...")
                    results.append(True)
                else:
                    print(f"❌ Missing glass card enhancement: {fix[:40]}...")
                    results.append(False)
        
        else:
            print("❌ CSS file not found")
            results.append(False)
    
    except Exception as e:
        print(f"❌ Error testing opacity consistency: {e}")
        results.append(False)
    
    return results

def test_footer_uniformity():
    """Test that footer has been unified across all pages."""
    print("\n🦶 TESTING FOOTER UNIFORMITY")
    print("=" * 35)
    
    results = []
    
    try:
        # Check base template for unified footer
        base_template_path = os.path.join('web', 'templates', 'base.html')
        
        if os.path.exists(base_template_path):
            with open(base_template_path, 'r', encoding='utf-8') as f:
                base_content = f.read()
            
            print("✅ Base template found and readable")
            results.append(True)
            
            # Check for unified footer elements
            footer_elements = [
                '<!-- Unified Footer - Consistent Across All Pages -->',
                'class="footer-responsive"',
                'class="footer-grid"',
                'class="footer-section"',
                'class="footer-link"',
                'class="footer-stat-item"',
                'class="footer-tech-item"',
                'class="footer-bottom"',
                'class="footer-network-status"'
            ]
            
            for element in footer_elements:
                if element in base_content:
                    print(f"✅ Found footer element: {element}")
                    results.append(True)
                else:
                    print(f"❌ Missing footer element: {element}")
                    results.append(False)
            
            # Check for enhanced footer sections
            footer_sections = [
                'ONNYX Platform Section',
                'Platform Navigation Section',
                'Network Statistics Section',
                'Technology Stack Section',
                'Footer Bottom Section'
            ]
            
            for section in footer_sections:
                if section in base_content:
                    print(f"✅ Found footer section: {section}")
                    results.append(True)
                else:
                    print(f"❌ Missing footer section: {section}")
                    results.append(False)
        
        else:
            print("❌ Base template not found")
            results.append(False)
    
    except Exception as e:
        print(f"❌ Error testing footer uniformity: {e}")
        results.append(False)
    
    return results

def test_responsive_design():
    """Test responsive design improvements."""
    print("\n📱 TESTING RESPONSIVE DESIGN IMPROVEMENTS")
    print("=" * 45)
    
    results = []
    
    try:
        # Check CSS file for responsive improvements
        css_path = os.path.join('web', 'static', 'css', 'main.css')
        
        if os.path.exists(css_path):
            with open(css_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # Check for responsive breakpoints
            responsive_features = [
                '/* RESPONSIVE DESIGN IMPROVEMENTS - ENHANCED MOBILE EXPERIENCE */',
                '@media (max-width: 768px)',
                '@media (min-width: 768px) and (max-width: 1024px)',
                '@media (min-width: 1024px)',
                '@media (min-width: 1440px)',
                'min-height: 52px; /* Larger touch targets for mobile */',
                'padding: 1rem 1.125rem; /* Increased touch targets */',
                'min-height: 48px; /* Increased touch target */'
            ]
            
            for feature in responsive_features:
                if feature in css_content:
                    print(f"✅ Found responsive feature: {feature[:50]}...")
                    results.append(True)
                else:
                    print(f"❌ Missing responsive feature: {feature[:50]}...")
                    results.append(False)
            
            # Check for accessibility improvements
            accessibility_features = [
                '/* ACCESSIBILITY IMPROVEMENTS */',
                '.footer-link:focus,',
                'outline: 2px solid var(--cyber-cyan);',
                '@media (prefers-contrast: high)',
                '@media (prefers-reduced-motion: reduce)'
            ]
            
            for feature in accessibility_features:
                if feature in css_content:
                    print(f"✅ Found accessibility feature: {feature}")
                    results.append(True)
                else:
                    print(f"❌ Missing accessibility feature: {feature}")
                    results.append(False)
        
        else:
            print("❌ CSS file not found")
            results.append(False)
    
    except Exception as e:
        print(f"❌ Error testing responsive design: {e}")
        results.append(False)
    
    return results

def test_page_layout_structure():
    """Test that page layout structure supports proper footer positioning."""
    print("\n🏗️ TESTING PAGE LAYOUT STRUCTURE")
    print("=" * 40)
    
    results = []
    
    try:
        # Check CSS for layout structure
        css_path = os.path.join('web', 'static', 'css', 'main.css')
        
        if os.path.exists(css_path):
            with open(css_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # Check for flexbox layout structure
            layout_features = [
                'display: flex;',
                'flex-direction: column;',
                'flex: 1 0 auto;',
                'flex-shrink: 0;',
                'margin-top: auto;',
                'min-height: calc(100vh - 80px);',
                'position: relative;',
                'z-index: 10;'
            ]
            
            for feature in layout_features:
                if feature in css_content:
                    print(f"✅ Found layout feature: {feature}")
                    results.append(True)
                else:
                    print(f"❌ Missing layout feature: {feature}")
                    results.append(False)
            
            # Check for content spacing
            spacing_features = [
                '.page-content {',
                '.explorer-content,',
                '.directory-content,',
                '.dashboard-content {',
                '.landing-content {',
                'padding-bottom: 3rem;',
                'padding-bottom: 4rem;'
            ]
            
            for feature in spacing_features:
                if feature in css_content:
                    print(f"✅ Found spacing feature: {feature}")
                    results.append(True)
                else:
                    print(f"❌ Missing spacing feature: {feature}")
                    results.append(False)
        
        else:
            print("❌ CSS file not found")
            results.append(False)
    
    except Exception as e:
        print(f"❌ Error testing page layout structure: {e}")
        results.append(False)
    
    return results

def test_visual_theme_consistency():
    """Test that Onyx Stone theme consistency is maintained."""
    print("\n🎭 TESTING VISUAL THEME CONSISTENCY")
    print("=" * 40)
    
    results = []
    
    try:
        # Check index.html for opacity fixes
        index_path = os.path.join('web', 'templates', 'index.html')
        
        if os.path.exists(index_path):
            with open(index_path, 'r', encoding='utf-8') as f:
                index_content = f.read()
            
            print("✅ Index template found and readable")
            results.append(True)
            
            # Check for opacity removals
            opacity_removals = [
                'animate-ping"></div>',  # No opacity-70
                'animate-pulse"></div>', # No opacity-60
                'animate-bounce"></div>', # No opacity-50
                'via-cyber-cyan to-transparent mt-3"></div>', # No opacity-50
                'via-cyber-purple to-transparent mt-3"></div>',
                'via-cyber-blue to-transparent mt-3"></div>',
                'via-green-400 to-transparent mt-3"></div>'
            ]
            
            opacity_count = 0
            for removal in opacity_removals:
                if removal in index_content:
                    print(f"✅ Found opacity fix: {removal[:40]}...")
                    opacity_count += 1
                    results.append(True)
            
            if opacity_count >= 5:
                print(f"✅ Found {opacity_count} opacity fixes in index.html")
                results.append(True)
            else:
                print(f"⚠️  Only found {opacity_count} opacity fixes in index.html")
                results.append(False)
            
            # Check for enhanced background effects
            if 'opacity-30"></div>' in index_content:
                print("✅ Found enhanced background grid opacity")
                results.append(True)
            else:
                print("❌ Missing enhanced background grid opacity")
                results.append(False)
        
        else:
            print("❌ Index template not found")
            results.append(False)
    
    except Exception as e:
        print(f"❌ Error testing visual theme consistency: {e}")
        results.append(False)
    
    return results

def test_live_page_rendering():
    """Test that pages render correctly with the fixes."""
    print("\n🌐 TESTING LIVE PAGE RENDERING")
    print("=" * 35)
    
    results = []
    
    # Start Flask server
    print("🚀 Starting Flask server...")
    server_process = subprocess.Popen([
        sys.executable, 'web/app.py'
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for server to start
    time.sleep(3)
    
    try:
        # Test pages
        test_pages = [
            ('/', 'Home Page'),
            ('/sela/directory', 'Sela Directory'),
            ('/explorer', 'Blockchain Explorer'),
            ('/register', 'Registration Portal')
        ]
        
        for url, page_name in test_pages:
            try:
                response = requests.get(f'http://127.0.0.1:5000{url}', timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ {page_name}: Loads successfully (HTTP 200)")
                    results.append(True)
                    
                    content = response.text
                    
                    # Check for unified footer
                    if 'footer-responsive' in content:
                        print(f"✅ {page_name}: Unified footer found")
                        results.append(True)
                    else:
                        print(f"❌ {page_name}: Unified footer missing")
                        results.append(False)
                    
                    # Check for glass card components
                    if 'glass-card' in content:
                        print(f"✅ {page_name}: Glass card components found")
                        results.append(True)
                    else:
                        print(f"❌ {page_name}: Glass card components missing")
                        results.append(False)
                    
                    # Check for no opacity classes in HTML
                    opacity_classes = ['opacity-70', 'opacity-60', 'opacity-50', 'opacity-40']
                    opacity_found = any(cls in content for cls in opacity_classes)
                    
                    if not opacity_found:
                        print(f"✅ {page_name}: No problematic opacity classes found")
                        results.append(True)
                    else:
                        print(f"⚠️  {page_name}: Some opacity classes still present")
                        results.append(False)
                
                else:
                    print(f"❌ {page_name}: Failed to load (HTTP {response.status_code})")
                    results.extend([False] * 3)
            
            except requests.exceptions.RequestException as e:
                print(f"❌ {page_name}: Request error - {e}")
                results.extend([False] * 3)
    
    finally:
        # Stop Flask server
        server_process.terminate()
        server_process.wait(timeout=5)
        print("⏹️  Flask server stopped")
    
    return results

def main():
    """Main test runner."""
    print("🎨 ONNYX VISUAL CONSISTENCY FIXES VERIFICATION")
    print("=" * 55)
    
    # Run all test categories
    opacity_results = test_opacity_consistency()
    footer_results = test_footer_uniformity()
    responsive_results = test_responsive_design()
    layout_results = test_page_layout_structure()
    theme_results = test_visual_theme_consistency()
    rendering_results = test_live_page_rendering()
    
    # Calculate overall results
    all_results = (opacity_results + footer_results + responsive_results + 
                  layout_results + theme_results + rendering_results)
    total_tests = len(all_results)
    passed_tests = sum(all_results)
    failed_tests = total_tests - passed_tests
    
    # Generate summary
    print("\n📊 VISUAL CONSISTENCY FIXES VERIFICATION SUMMARY")
    print("=" * 55)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed breakdown
    print(f"\nOpacity Consistency: {sum(opacity_results)}/{len(opacity_results)} passed")
    print(f"Footer Uniformity: {sum(footer_results)}/{len(footer_results)} passed")
    print(f"Responsive Design: {sum(responsive_results)}/{len(responsive_results)} passed")
    print(f"Layout Structure: {sum(layout_results)}/{len(layout_results)} passed")
    print(f"Theme Consistency: {sum(theme_results)}/{len(theme_results)} passed")
    print(f"Live Page Rendering: {sum(rendering_results)}/{len(rendering_results)} passed")
    
    # Overall assessment
    if passed_tests >= total_tests * 0.85:  # 85% pass rate
        print("\n🎉 VISUAL CONSISTENCY FIXES SUCCESSFUL!")
        print("✅ Opacity inconsistencies resolved across all components")
        print("✅ Unified footer implemented across all pages")
        print("✅ Enhanced mobile responsiveness and touch targets")
        print("✅ Robust footer positioning prevents content overlap")
        print("✅ Onyx Stone theme consistency maintained")
        print("✅ Accessibility improvements implemented")
        print("\n🌟 ONNYX platform now provides consistent, professional user experience!")
        return True
    else:
        print("\n⚠️  VISUAL CONSISTENCY FIXES NEED ATTENTION")
        print("Some issues may still exist.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
