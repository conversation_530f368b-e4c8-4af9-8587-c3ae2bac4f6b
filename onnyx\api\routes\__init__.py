"""
Onnyx Routes Package

This package provides API routes for the Onnyx blockchain.
"""

from .miner import miner_router
from .sela import router as sela_router
from .identity import router as identity_router
from .etzem import router as etzem_router
from .tokens import router as tokens_router
from .sela_miner import router as sela_miner_router
from .rotation import router as rotation_router
from .chain_parameters import router as chain_parameters_router
from .voice_scroll import router as voice_scroll_router
from .analytics import router as analytics_router

__all__ = [
    'miner_router',
    'sela_router',
    'identity_router',
    'etzem_router',
    'tokens_router',
    'sela_miner_router',
    'rotation_router',
    'chain_parameters_router',
    'voice_scroll_router',
    'analytics_router'
]
