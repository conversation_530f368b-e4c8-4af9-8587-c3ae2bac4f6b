"""
Run the Onnyx API Server

This script runs the Onnyx API Server.
"""

import os
import sys
import uvicorn
import argparse

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set the bypass validation flag for testing
import consensus.miner
consensus.miner.BYPASS_VALIDATION = True

def main():
    """Run the Onnyx API Server."""
    parser = argparse.ArgumentParser(description="Run the Onnyx API Server")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    print(f"Starting Onnyx API Server on {args.host}:{args.port}")
    print("Validation is bypassed for testing")
    
    uvicorn.run(
        "api.api:app",
        host=args.host,
        port=args.port,
        reload=args.reload
    )

if __name__ == "__main__":
    main()
