# Onnyx Mikvah Token Minting

The Mikvah Token Minting system is a trusted, labor-rooted minting engine for Selaholders in the Onnyx blockchain.

## Overview

Mikvah Token Minting ensures that tokens are rooted in real-world economic activity, not speculation. Only verified Selaholders may mint Mikvah tokens.

Each Sela can mint tokens for:
- 🪙 **Loyalty**: Customer rewards
- 🫂 **Community**: Membership or access
- 📊 **Equity**: Co-op or DAO style ownership

Minting limits are governed by the Sela's trust score, activity, and on-chain history.

## Components

### TokenRegistry

The TokenRegistry manages tokens in the Onnyx ecosystem. It provides methods for:
- Registering new tokens
- Getting tokens by ID, symbol, creator, or category
- Updating token supply
- Getting tokens associated with a Sela

### Yovel Limiter

The Yovel Limiter calculates token mint limits based on:
- Etzem score
- Token category
- Badges

### OP_MINT Opcode

The OP_MINT opcode validates token minting transactions. It enforces the following rules:
- Only Selaholders can mint tokens
- Token supply must be within the Yovel mint cap
- Token symbol must be unique

## API Endpoints

### Mint Token

```
POST /api/tokens/mint
```

Mint a new token.

Parameters:
- `token_id`: The token ID
- `name`: The token name
- `symbol`: The token symbol
- `creator_id`: The creator's identity ID
- `supply`: The initial supply
- `category`: The token category (e.g., "loyalty", "equity", "community")
- `decimals`: The number of decimal places
- `metadata`: Additional metadata for the token

Example response:

```json
{
  "status": "token_minted",
  "token": {
    "token_id": "ALICE_LOYALTY",
    "name": "Alice Loyalty Token",
    "symbol": "ALT",
    "creator_id": "alice",
    "supply": 1234,
    "category": "loyalty",
    "decimals": 2,
    "created_at": 1746801307,
    "metadata": {
      "sela_id": "alices_salon"
    }
  }
}
```

### Get Token

```
GET /api/tokens/{token_id}
```

Get a token by ID.

Example response:

```json
{
  "token_id": "ALICE_LOYALTY",
  "name": "Alice Loyalty Token",
  "symbol": "ALT",
  "creator_id": "alice",
  "supply": 1234,
  "category": "loyalty",
  "decimals": 2,
  "created_at": 1746801307,
  "metadata": {
    "sela_id": "alices_salon"
  }
}
```

### Get Token by Symbol

```
GET /api/tokens/symbol/{symbol}
```

Get a token by symbol.

### List Tokens

```
GET /api/tokens?category={category}
```

Get all tokens, optionally filtered by category.

Example response:

```json
{
  "tokens": [
    {
      "token_id": "ALICE_LOYALTY",
      "name": "Alice Loyalty Token",
      "symbol": "ALT",
      "creator_id": "alice",
      "supply": 1234,
      "category": "loyalty",
      "decimals": 2,
      "created_at": 1746801307,
      "metadata": {
        "sela_id": "alices_salon"
      }
    },
    {
      "token_id": "BOB_LOYALTY",
      "name": "Bob Loyalty Token",
      "symbol": "BLT",
      "creator_id": "bob",
      "supply": 904,
      "category": "loyalty",
      "decimals": 2,
      "created_at": 1746801307,
      "metadata": {
        "sela_id": "bobs_barbershop"
      }
    }
  ]
}
```

### Get Tokens by Creator

```
GET /api/tokens/creator/{creator_id}
```

Get all tokens created by an identity.

### Get Tokens by Sela

```
GET /api/sela/{sela_id}/tokens
```

Get all tokens associated with a Sela.

Example response:

```json
{
  "sela_id": "alices_salon",
  "sela_name": "Alice's Salon",
  "tokens": [
    {
      "token_id": "ALICE_LOYALTY",
      "name": "Alice Loyalty Token",
      "symbol": "ALT",
      "creator_id": "alice",
      "supply": 1234,
      "category": "loyalty",
      "decimals": 2,
      "created_at": 1746801307,
      "metadata": {
        "sela_id": "alices_salon"
      }
    }
  ]
}
```

### Get Token Categories

```
GET /api/tokenregistry/categories
```

Get all token categories.

Example response:

```json
{
  "categories": [
    "loyalty",
    "equity",
    "community"
  ]
}
```

## Yovel Mint Cap Calculation

The Yovel mint cap is calculated based on:

1. **Base Cap**: 1000 tokens
2. **Category Multiplier**:
   - Loyalty: 1.5x
   - Equity: 1.2x
   - Community: 1.0x
   - Other: 0.8x
3. **Etzem Score Multiplier**: Etzem score / 100
4. **Badge Multipliers**:
   - STAKER: +0.5x
   - VALIDATOR_ELIGIBLE: +0.5x
   - GUARDIAN_ELIGIBLE: +0.3x
   - SELA_FOUNDER: +0.5x
   - ETZEM_TRUSTED: +0.2x
   - ETZEM_REPUTABLE: +0.3x
   - ETZEM_ESTEEMED: +0.5x
   - ETZEM_VENERABLE: +1.0x

Formula:
```
mint_cap = base_cap * category_multiplier * etzem_multiplier * badge_multiplier
```

## Example Mikvah Mint Transaction

```json
{
  "op": "OP_MINT",
  "from": "bob",
  "data": {
    "name": "BobBux",
    "symbol": "BOB",
    "category": "loyalty",
    "decimals": 2,
    "supply": 1500
  }
}
```

If Bob's Etzem score is 85 and the category is "loyalty", the mint cap would be approximately 1275. The transaction would fail unless the supply is less than or equal to the cap.

## Usage

### Running the API Server

```bash
python run_server.py --port 8889
```

### Testing the Mikvah Token Minting

```bash
python test_mikvah_minting.py
```

## Integration with Other Systems

The Mikvah Token Minting system integrates with the following systems:

- **Identity Registry**: For checking if the creator is a Selaholder
- **Sela Registry**: For getting Sela information
- **Etzem Engine**: For calculating Etzem scores
- **Token Ledger**: For crediting tokens to the creator
