{% extends "base.html" %}

{% block title %}Blockchain Explorer - ONNYX Platform{% endblock %}

{% block content %}
<div class="explorer-content hero-gradient cyber-grid relative py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Header Section -->
        <div class="text-center mb-16">
            <!-- ONNYX Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Blockchain Explorer</span>
            </h1>
            <p class="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed mb-8">
                Real-time exploration of the ONNYX blockchain network and transaction history
            </p>

            <!-- Network Status -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                    <div class="text-3xl font-orbitron font-bold text-cyber-cyan-glow mb-2" id="latest-block">{{ latest_block or 0 }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Latest Block</div>
                </div>
                <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                    <div class="text-3xl font-orbitron font-bold text-cyber-purple-glow mb-2" id="total-transactions">{{ total_transactions or 0 }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Total Transactions</div>
                </div>
                <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                    <div class="text-3xl font-orbitron font-bold text-cyber-blue-glow mb-2" id="network-hashrate">{{ network_hashrate or 'N/A' }}</div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Network Hashrate</div>
                </div>
                <div class="glass-card p-6 text-center neuro-card hover:scale-105 transition-all duration-300">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2 shadow-sm shadow-green-400/50"></div>
                        <span class="text-3xl font-orbitron font-bold text-green-400" style="text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);">LIVE</span>
                    </div>
                    <div class="text-sm text-text-tertiary uppercase tracking-wider">Network Status</div>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="glass-card p-8 mb-12 neuro-card">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">🔍 Search Blockchain</h2>
            <div class="flex flex-col lg:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text"
                               id="blockchain-search"
                               placeholder="Search by block hash, transaction ID, or address..."
                               class="form-input w-full pl-12">
                        <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <button onclick="performSearch()"
                        class="glass-button-primary px-8 py-3 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    Search
                </button>
            </div>
        </div>

        <!-- Recent Blocks Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <!-- Recent Blocks -->
            <div class="glass-card p-8 neuro-card">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan">⛓️ Recent Blocks</h2>
                    <a href="{{ url_for('explorer.blocks') }}" class="text-cyber-cyan hover:text-white transition-colors text-sm">
                        View All →
                    </a>
                </div>

                <div class="space-y-4" id="recent-blocks">
                    {% if recent_blocks %}
                        {% for block in recent_blocks[:5] %}
                        <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300 data-stream">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center space-x-3 mb-2">
                                        <span class="text-lg font-orbitron font-bold text-white">Block #{{ block.block_height }}</span>
                                        <span class="badge-success">CONFIRMED</span>
                                    </div>
                                    <div class="text-sm text-gray-400 space-y-1">
                                        <div>Hash: <span class="font-mono text-cyber-cyan hash-truncate">{{ block.block_hash }}</span></div>
                                        <div>Transactions: <span class="text-cyber-purple">{{ block.transactions|length if block.transactions else 0 }}</span></div>
                                        <div>Miner: <span class="text-gray-300">{{ block.miner or 'Unknown' }}</span></div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm text-gray-500">{{ block.timestamp|timestamp_to_time if block.timestamp else 'Unknown' }}</div>
                                    <div class="text-xs text-gray-600">{{ block.timestamp|timestamp_to_date if block.timestamp else '' }}</div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500">No blocks found</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="glass-card p-8 neuro-card">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-purple">💫 Recent Transactions</h2>
                    <a href="{{ url_for('explorer.transactions') }}" class="text-cyber-purple hover:text-white transition-colors text-sm">
                        View All →
                    </a>
                </div>

                <div class="space-y-4" id="recent-transactions">
                    {% if recent_transactions %}
                        {% for tx in recent_transactions[:5] %}
                        <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300 data-stream">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center space-x-3 mb-2">
                                        <span class="text-sm font-orbitron font-bold text-white">{{ tx.op or 'Transaction' }}</span>
                                        {% if tx.status == 'confirmed' %}
                                        <span class="badge-success">CONFIRMED</span>
                                        {% elif tx.status == 'pending' %}
                                        <span class="badge-warning">PENDING</span>
                                        {% else %}
                                        <span class="badge-error">FAILED</span>
                                        {% endif %}
                                    </div>
                                    <div class="text-sm text-gray-400 space-y-1">
                                        <div>TX: <span class="font-mono text-cyber-purple hash-truncate">{{ tx.tx_id }}</span></div>
                                        <div>From: <span class="text-gray-300">{{ tx.sender or 'System' }}</span></div>
                                        <div>Data: <span class="text-gray-300">{{ tx.data[:50] + '...' if tx.data and tx.data|length > 50 else tx.data or 'N/A' }}</span></div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm text-gray-500">{{ tx.created_at|timestamp_to_time if tx.created_at else 'Unknown' }}</div>
                                    <div class="text-xs text-gray-600">{{ tx.created_at|timestamp_to_date if tx.created_at else '' }}</div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500">No transactions found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Network Analytics -->
        <div class="glass-card p-8 neuro-card mb-12">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-6">📊 Network Analytics</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Transaction Volume Chart Placeholder -->
                <div class="col-span-2">
                    <div class="h-64 bg-gradient-to-br from-cyber-cyan/10 to-cyber-purple/10 rounded-xl flex items-center justify-center">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-400 font-orbitron">Transaction Volume Chart</p>
                            <p class="text-sm text-gray-500">Coming Soon</p>
                        </div>
                    </div>
                </div>

                <!-- Network Stats -->
                <div class="space-y-4">
                    <div class="glass-card p-4">
                        <div class="text-sm text-gray-500 mb-1">Average Block Time</div>
                        <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ avg_block_time or '~10s' }}</div>
                    </div>
                    <div class="glass-card p-4">
                        <div class="text-sm text-gray-500 mb-1">Network Difficulty</div>
                        <div class="text-2xl font-orbitron font-bold text-cyber-purple">{{ network_difficulty or 'Auto' }}</div>
                    </div>
                    <div class="glass-card p-4">
                        <div class="text-sm text-gray-500 mb-1">Active Validators</div>
                        <div class="text-2xl font-orbitron font-bold text-cyber-blue">{{ active_validators or 0 }}</div>
                    </div>
                    <div class="glass-card p-4">
                        <div class="text-sm text-gray-500 mb-1">Total Supply</div>
                        <div class="text-2xl font-orbitron font-bold text-green-400">{{ total_supply or 'N/A' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center">
            <h2 class="text-2xl font-orbitron font-bold text-white mb-8">Quick Actions</h2>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('explorer.blocks') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    🔗 Browse Blocks
                </a>
                <a href="{{ url_for('explorer.transactions') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    💫 View Transactions
                </a>
                <a href="{{ url_for('sela.directory') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    🏢 Validator Network
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('blockchain-search').value.trim();
    if (!searchTerm) {
        Onnyx.utils.showNotification('Please enter a search term', 'warning');
        return;
    }

    // Show loading state
    const searchButton = event.target;
    const originalText = searchButton.textContent;
    searchButton.textContent = 'Searching...';
    searchButton.disabled = true;

    // Simulate search (replace with actual API call)
    setTimeout(() => {
        searchButton.textContent = originalText;
        searchButton.disabled = false;

        // For demo purposes, show a message
        if (searchTerm.length === 64) {
            Onnyx.utils.showNotification('Block/Transaction hash detected', 'info');
        } else if (searchTerm.length === 42) {
            Onnyx.utils.showNotification('Address detected', 'info');
        } else {
            Onnyx.utils.showNotification('Search functionality coming soon', 'info');
        }
    }, 1000);
}

// Auto-refresh data every 30 seconds
setInterval(async () => {
    try {
        // Refresh network stats (replace with actual API calls)
        const stats = await Onnyx.api.get('/api/explorer/stats');
        if (stats) {
            document.getElementById('latest-block').textContent = stats.latest_block || 0;
            document.getElementById('total-transactions').textContent = stats.total_transactions || 0;
        }
    } catch (error) {
        console.log('Auto-refresh failed:', error);
    }
}, 30000);

// Enter key search
document.getElementById('blockchain-search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});
</script>
{% endblock %}
