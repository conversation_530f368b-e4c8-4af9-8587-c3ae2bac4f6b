"""
Onnyx Voice Scroll Routes

This module provides API routes for Voice Scrolls.
"""

from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from governance.voice_scroll import voice_scrolls

# Create router
router = APIRouter()

@router.post("/governance/propose")
def propose_scroll(
    creator_id: str = Body(...),
    title: str = Body(...),
    description: str = Body(...),
    category: str = Body(...),
    expiry_days: int = Body(7),
    effect: Optional[Dict[str, Any]] = Body(None)
) -> Dict[str, Any]:
    """
    Propose a new Voice Scroll.
    
    Args:
        creator_id: The identity ID of the creator
        title: The title of the scroll
        description: The description of the scroll
        category: The category of the scroll
        expiry_days: The number of days until the scroll expires
        effect: The effect of the scroll (optional)
    
    Returns:
        The created scroll
    """
    try:
        scroll = voice_scrolls.create_scroll(
            creator_id=creator_id,
            title=title,
            description=description,
            category=category,
            expiry_days=expiry_days,
            effect=effect
        )
        
        return {
            "scroll": scroll,
            "status": "proposed"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/governance/vote")
def vote_on_scroll(
    identity_id: str = Body(...),
    scroll_id: str = Body(...),
    decision: str = Body(...)
) -> Dict[str, Any]:
    """
    Vote on a Voice Scroll.
    
    Args:
        identity_id: The identity ID of the voter
        scroll_id: The scroll ID
        decision: The decision ("yes", "no", or "abstain")
    
    Returns:
        The updated scroll
    """
    try:
        scroll = voice_scrolls.vote_on_scroll(
            identity_id=identity_id,
            scroll_id=scroll_id,
            decision=decision
        )
        
        return {
            "scroll": scroll,
            "status": "voted"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/scrolls")
def get_scrolls(
    status: Optional[str] = None,
    category: Optional[str] = None,
    creator: Optional[str] = None,
    outcome: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get Voice Scrolls.
    
    Args:
        status: Filter by status (optional)
        category: Filter by category (optional)
        creator: Filter by creator (optional)
        outcome: Filter by outcome (optional)
    
    Returns:
        A list of Voice Scrolls
    """
    try:
        scrolls = voice_scrolls.get_scrolls(
            status=status,
            category=category,
            creator=creator,
            outcome=outcome
        )
        
        return {
            "scrolls": scrolls
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/scrolls/{scroll_id}")
def get_scroll(scroll_id: str) -> Dict[str, Any]:
    """
    Get a Voice Scroll by ID.
    
    Args:
        scroll_id: The scroll ID
    
    Returns:
        The Voice Scroll
    """
    try:
        scroll = voice_scrolls.get_scroll(scroll_id)
        if not scroll:
            raise HTTPException(status_code=404, detail=f"Voice Scroll with ID '{scroll_id}' not found")
        
        return {
            "scroll": scroll
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/scrolls/{scroll_id}/tally")
def tally_scroll(scroll_id: str) -> Dict[str, Any]:
    """
    Tally the votes for a Voice Scroll.
    
    Args:
        scroll_id: The scroll ID
    
    Returns:
        The tally results
    """
    try:
        tally_result = voice_scrolls.tally_scroll(scroll_id)
        
        return {
            "tally": tally_result
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/governance/resolve/{scroll_id}")
def resolve_scroll(scroll_id: str) -> Dict[str, Any]:
    """
    Resolve a Voice Scroll.
    
    Args:
        scroll_id: The scroll ID
    
    Returns:
        The resolved Voice Scroll
    """
    try:
        scroll = voice_scrolls.resolve_scroll(scroll_id)
        
        return {
            "scroll": scroll,
            "status": "resolved"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/governance/categories")
def get_categories() -> Dict[str, List[str]]:
    """
    Get the available Voice Scroll categories.
    
    Returns:
        The available categories
    """
    categories = [
        "protocol",
        "economic",
        "community",
        "membership",
        "other"
    ]
    
    return {
        "categories": categories
    }

@router.get("/governance/outcomes")
def get_outcomes() -> Dict[str, List[str]]:
    """
    Get the available Voice Scroll outcomes.
    
    Returns:
        The available outcomes
    """
    outcomes = [
        "pending",
        "passed",
        "failed",
        "no_votes"
    ]
    
    return {
        "outcomes": outcomes
    }
