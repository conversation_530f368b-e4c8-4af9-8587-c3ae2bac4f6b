# src/db/schema/roles.py

from src.db.manager import db_manager

def create_roles_tables():
    """
    Create the tables for the role system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Create roles table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS roles (
        role TEXT PRIMARY KEY,
        category TEXT NOT NULL,
        description TEXT NOT NULL
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_roles_category ON roles (category)')
    
    conn.commit()
