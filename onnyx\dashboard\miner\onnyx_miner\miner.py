"""
Onnyx Miner Mining Module

This module provides functions for mining blocks.
"""

import os
import json
import logging
import requests
import time
import threading
from typing import Dict, Any, List, Optional

from .config import get_config
from .keys import load_identity_keys, sign_message
from .sync import get_chain_info, get_latest_block

# Set up logging
logger = logging.getLogger("onnyx_miner.miner")

# Global mining thread
mining_thread = None
mining_stop_event = threading.Event()

def mine_block(identity_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Mine a new block.
    
    Args:
        identity_id: The identity ID (optional, uses the configured identity if not provided)
    
    Returns:
        The mined block
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Use the configured identity if none is provided
        if identity_id is None:
            identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            raise ValueError("No identity ID provided or configured")
        
        # Get the Sela ID
        sela_id = config.get("sela", {}).get("id")
        
        if not sela_id:
            raise ValueError("No Sela ID configured")
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Load the keys
        private_key_pem, _ = load_identity_keys(identity_id)
        
        # Create the mining request
        mining_request = {
            "identity_id": identity_id,
            "sela_id": sela_id,
            "private_key_pem": private_key_pem.decode()
        }
        
        # Send the mining request
        mining_url = f"{api_url}/api/mine"
        mining_response = requests.post(mining_url, json=mining_request)
        mining_response.raise_for_status()
        
        # Get the mined block
        block = mining_response.json().get("block", {})
        
        logger.info(f"Mined block {block.get('index')}")
        
        return block
    except Exception as e:
        logger.error(f"Error mining block: {str(e)}")
        
        # Return an empty block if there's an error
        return {}

def start_auto_mining():
    """Start automatic mining."""
    global mining_thread, mining_stop_event
    
    # Stop any existing mining thread
    stop_auto_mining()
    
    # Reset the stop event
    mining_stop_event = threading.Event()
    
    # Start the mining thread
    mining_thread = threading.Thread(target=_auto_mining_thread)
    mining_thread.daemon = True
    mining_thread.start()
    
    logger.info("Started automatic mining")

def stop_auto_mining():
    """Stop automatic mining."""
    global mining_thread, mining_stop_event
    
    # Set the stop event
    if mining_stop_event:
        mining_stop_event.set()
    
    # Wait for the mining thread to stop
    if mining_thread and mining_thread.is_alive():
        mining_thread.join(timeout=5)
    
    mining_thread = None
    
    logger.info("Stopped automatic mining")

def _auto_mining_thread():
    """Automatic mining thread."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            logger.error("No identity ID configured")
            return
        
        # Get the mining interval
        mine_interval = config.get("node", {}).get("mine_interval", 60)
        
        # Mine blocks until stopped
        while not mining_stop_event.is_set():
            try:
                # Check if we're the current validator
                chain_info = get_chain_info()
                current_height = chain_info.get("chain_height", 0)
                
                # Get the validator status
                api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
                sela_id = config.get("sela", {}).get("id")
                
                validator_url = f"{api_url}/api/rotation/is-valid-proposer/{sela_id}/{current_height + 1}"
                validator_response = requests.get(validator_url)
                validator_response.raise_for_status()
                
                is_valid_proposer = validator_response.json().get("is_valid_proposer", False)
                
                if is_valid_proposer:
                    # Mine a block
                    mine_block(identity_id)
                else:
                    logger.info(f"Not the current validator for height {current_height + 1}")
            except Exception as e:
                logger.error(f"Error in mining thread: {str(e)}")
            
            # Wait for the next mining interval
            mining_stop_event.wait(mine_interval)
    except Exception as e:
        logger.error(f"Error in mining thread: {str(e)}")

def get_mining_status() -> Dict[str, Any]:
    """
    Get the mining status.
    
    Returns:
        The mining status
    """
    try:
        # Get the configuration
        config = get_config()
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the mining status
        mining_url = f"{api_url}/api/mining/status"
        mining_response = requests.get(mining_url)
        mining_response.raise_for_status()
        
        # Get the chain info
        chain_info = get_chain_info()
        
        # Get the validator status
        sela_id = config.get("sela", {}).get("id")
        current_height = chain_info.get("chain_height", 0)
        
        validator_url = f"{api_url}/api/rotation/is-valid-proposer/{sela_id}/{current_height + 1}"
        validator_response = requests.get(validator_url)
        validator_response.raise_for_status()
        
        is_valid_proposer = validator_response.json().get("is_valid_proposer", False)
        next_validator = validator_response.json().get("next_validator", "")
        
        # Create the mining status
        mining_status = mining_response.json()
        mining_status["is_valid_proposer"] = is_valid_proposer
        mining_status["next_validator"] = next_validator
        mining_status["auto_mining"] = mining_thread is not None and mining_thread.is_alive()
        
        return mining_status
    except Exception as e:
        logger.error(f"Error getting mining status: {str(e)}")
        
        # Return default status if there's an error
        return {
            "is_valid_proposer": False,
            "next_validator": "",
            "auto_mining": mining_thread is not None and mining_thread.is_alive()
        }
