"""
Onnyx Nations Data

This module provides the data for the nations and tribes in the Onnyx blockchain.
"""

import json
import hashlib
import time
import os
import sys

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from models.nation import Nation
from models.identity import Identity
from data.db import db

# Nations data structure
NATIONS_DATA = {
    "Israel": {
        "father": "<PERSON> (Israel)",
        "description": "The twelve tribes of Israel, descended from <PERSON>, who was renamed Israel after wrestling with <PERSON>. These tribes form the foundation of the biblical nation of Israel and represent different aspects of <PERSON>'s covenant people.",
        "covenant_principles": "The Mosaic covenant, including the Ten Commandments and the Torah. Principles of justice, mercy, and faithfulness to God's law.",
        "economic_strengths": "Agriculture, craftsmanship, trade, and spiritual leadership.",
        "jurisdictional_framework": "Torah-based legal system with judges, elders, and priests administering justice.",
        "tribes": [
            {
                "name": "<PERSON>",
                "description": "The firstborn of <PERSON>, known for strength but unstable character.",
                "symbol": "Water waves",
                "strengths": "Natural leadership, physical strength",
                "economic_focus": "Cattle raising, agriculture"
            },
            {
                "name": "Simeon",
                "description": "Known for zeal and passion, later absorbed into Judah.",
                "symbol": "City gate",
                "strengths": "Determination, protective nature",
                "economic_focus": "Urban crafts, defense"
            },
            {
                "name": "Levi",
                "description": "The priestly tribe, set apart for service in the Tabernacle and Temple.",
                "symbol": "Breastplate",
                "strengths": "Spiritual leadership, teaching",
                "economic_focus": "Religious service, education"
            },
            {
                "name": "Judah",
                "description": "The royal tribe from which King David and ultimately the Messiah came.",
                "symbol": "Lion",
                "strengths": "Leadership, governance, creativity",
                "economic_focus": "Governance, arts, viticulture"
            },
            {
                "name": "Dan",
                "description": "Known for judgment and justice.",
                "symbol": "Scales",
                "strengths": "Discernment, justice",
                "economic_focus": "Judicial services, craftsmanship"
            },
            {
                "name": "Naphtali",
                "description": "Known for eloquence and swift action.",
                "symbol": "Deer",
                "strengths": "Communication, adaptability",
                "economic_focus": "Fishing, olive oil production"
            },
            {
                "name": "Gad",
                "description": "Known for military prowess.",
                "symbol": "Camp/Tent",
                "strengths": "Protection, strategic thinking",
                "economic_focus": "Defense, livestock"
            },
            {
                "name": "Asher",
                "description": "Known for prosperity and abundance.",
                "symbol": "Olive tree",
                "strengths": "Provision, hospitality",
                "economic_focus": "Olive oil, baking, agriculture"
            },
            {
                "name": "Issachar",
                "description": "Known for wisdom, understanding times and seasons.",
                "symbol": "Sun and moon",
                "strengths": "Knowledge, discernment, timing",
                "economic_focus": "Education, agriculture, astronomy"
            },
            {
                "name": "Zebulun",
                "description": "Known for commerce and seafaring.",
                "symbol": "Ship",
                "strengths": "Trade, exploration",
                "economic_focus": "Maritime trade, commerce"
            },
            {
                "name": "Joseph",
                "description": "Known for dreams, provision, and leadership in crisis.",
                "symbol": "Fruitful bough",
                "strengths": "Administration, provision, vision",
                "economic_focus": "Agriculture, storage, distribution",
                "subtribes": [
                    {
                        "name": "Ephraim",
                        "description": "The more prominent of Joseph's sons, known for fruitfulness.",
                        "symbol": "Bull",
                        "strengths": "Multiplication, leadership",
                        "economic_focus": "Agriculture, leadership"
                    },
                    {
                        "name": "Manasseh",
                        "description": "Joseph's firstborn, known for strength and stability.",
                        "symbol": "Olive branch",
                        "strengths": "Steadfastness, endurance",
                        "economic_focus": "Land management, agriculture"
                    }
                ]
            },
            {
                "name": "Benjamin",
                "description": "The youngest tribe, known for fierceness and protection.",
                "symbol": "Wolf",
                "strengths": "Protection, strategy",
                "economic_focus": "Military, craftsmanship"
            }
        ]
    },
    "Ishmael": {
        "father": "Ishmael (son of Abraham & Hagar)",
        "description": "The twelve princes descended from Ishmael, Abraham's son with Hagar. These tribes primarily inhabited the Arabian Peninsula and were known for their nomadic lifestyle and trading prowess.",
        "covenant_principles": "God promised to make Ishmael a great nation with twelve princes. Principles of independence, strength, and survival.",
        "economic_strengths": "Trade, animal husbandry, desert navigation, and caravan protection.",
        "jurisdictional_framework": "Tribal leadership with princes (emirs) governing by consensus and traditional law.",
        "princes": [
            "Nebajoth", "Kedar", "Adbeel", "Mibsam", "Mishma", "Dumah", 
            "Massa", "Hadar", "Tema", "Jetur", "Naphish", "Kedemah"
        ]
    },
    "Edom": {
        "father": "Esau (twin brother of Jacob)",
        "description": "The descendants of Esau, who was also called Edom. They established a kingdom south of the Dead Sea and were known for their mining and metalworking skills.",
        "covenant_principles": "Though outside the Abrahamic covenant through Isaac and Jacob, Edom maintained a relationship with Israel as 'brothers' with their own land inheritance.",
        "economic_strengths": "Mining, metalworking, trade routes control, and hunting.",
        "jurisdictional_framework": "Monarchy with dukes (tribal chiefs) governing regions.",
        "dukes": [
            "Teman", "Omar", "Zepho", "Kenaz", "Korah", "Gatam", "Amalek",
            "Nahath", "Zerah", "Shammah", "Mizzah", "Magdiel", "Iram"
        ]
    },
    "Midian": {
        "father": "Midian (son of Abraham & Keturah)",
        "description": "The descendants of Midian, Abraham's son with Keturah. They were desert dwellers known for their trading caravans and nomadic lifestyle.",
        "covenant_principles": "Connected to Abraham but outside the main covenant line. Principles of trade, hospitality, and desert wisdom.",
        "economic_strengths": "Caravan trade, animal husbandry, and oasis agriculture.",
        "jurisdictional_framework": "Clan-based leadership with elders making decisions.",
        "clans": ["Ephah", "Epher", "Hanoch", "Abida", "Eldaah"]
    },
    "Moab_Ammon": {
        "father": "Lot (via his daughters)",
        "description": "The nations of Moab and Ammon, descended from Lot's sons born through his daughters. They established kingdoms east of the Dead Sea.",
        "covenant_principles": "Related to Abraham through Lot. Principles of territorial sovereignty and family preservation.",
        "economic_strengths": "Agriculture, animal husbandry, and control of trade routes.",
        "jurisdictional_framework": "Monarchy with tribal divisions and city-states.",
        "tribes": ["Moab", "Ben-Ammi (father of the Ammonites)"]
    },
    "Ham": {
        "father": "Ham (son of Noah)",
        "description": "The descendants of Ham, Noah's son. They primarily inhabited Africa, parts of the Middle East, and Canaan.",
        "covenant_principles": "Subject to the Noahic covenant. Principles of cultural development and civilization building.",
        "economic_strengths": "Agriculture, construction, and early civilization development.",
        "jurisdictional_framework": "Varied systems including city-states, kingdoms, and tribal confederations.",
        "sons": {
            "Cush": {
                "description": "Inhabited Ethiopia and surrounding regions.",
                "sons": ["Seba", "Havilah", "Sabtah", "Raamah", "Sabtecha"],
                "grandsons_of_Raamah": ["Sheba", "Dedan"]
            },
            "Mizraim": {
                "description": "Established Egypt and surrounding territories.",
                "sons": ["Ludim", "Anamim", "Lehabim", "Naphtuhim", "Pathrusim", "Casluhim", "Caphtorim"]
            },
            "Phut": {
                "description": "Inhabited Libya and North Africa."
            },
            "Canaan": {
                "description": "Inhabited the land that would later be conquered by Israel.",
                "sons": ["Sidon", "Heth"],
                "clans": ["Jebusite", "Amorite", "Girgasite", "Hivite", "Arkite", "Sinite", "Arvadite", "Zemarite", "Hamathite"]
            }
        }
    },
    "Japheth": {
        "father": "Japheth (son of Noah)",
        "description": "The descendants of Japheth, Noah's son. They primarily inhabited Europe, parts of Asia, and the Mediterranean coastlands.",
        "covenant_principles": "Subject to the Noahic covenant. Principles of expansion, exploration, and intellectual development.",
        "economic_strengths": "Maritime trade, exploration, and diverse resource development.",
        "jurisdictional_framework": "Varied systems including city-states, republics, and eventually empires.",
        "sons": {
            "Gomer": {
                "description": "Inhabited parts of Europe and Asia Minor.",
                "sons": ["Ashkenaz", "Riphath", "Togarmah"]
            },
            "Magog": {
                "description": "Inhabited the far north regions."
            },
            "Madai": {
                "description": "Established the Medes in what is now Iran."
            },
            "Javan": {
                "description": "Established Greece and surrounding islands.",
                "sons": ["Elishah", "Tarshish", "Kittim", "Dodanim"]
            },
            "Tubal": {
                "description": "Inhabited regions in modern Turkey."
            },
            "Meshech": {
                "description": "Inhabited regions between the Black and Caspian Seas."
            },
            "Tiras": {
                "description": "Possibly established Thrace or became the maritime Tyrsenians."
            }
        }
    }
}

def create_nation_id(name):
    """Create a nation ID from the name."""
    return hashlib.sha256(name.encode()).hexdigest()

def initialize_nations():
    """Initialize the nations in the database."""
    # Create a founder identity for each nation
    founders = {}
    
    for nation_name, nation_data in NATIONS_DATA.items():
        # Create a founder identity
        founder_name = nation_data["father"]
        public_key = hashlib.sha256(founder_name.encode()).hexdigest()
        
        # Check if the founder identity already exists
        founder = Identity.get_by_public_key(public_key)
        
        if not founder:
            # Create the founder identity
            founder = Identity.create(
                name=founder_name,
                public_key=public_key,
                metadata={
                    "type": "founder",
                    "description": f"Founder of {nation_name}"
                }
            )
        
        founders[nation_name] = founder.identity_id
    
    # Create the nations
    for nation_name, nation_data in NATIONS_DATA.items():
        nation_id = create_nation_id(nation_name)
        
        # Check if the nation already exists
        existing_nation = Nation.get_by_id(nation_id)
        
        if not existing_nation:
            # Create the nation
            nation = Nation.create(
                name=nation_name,
                description=nation_data.get("description", f"Nation of {nation_name}"),
                founder_id=founders[nation_name],
                metadata={
                    "father": nation_data.get("father", ""),
                    "covenant_principles": nation_data.get("covenant_principles", ""),
                    "economic_strengths": nation_data.get("economic_strengths", ""),
                    "jurisdictional_framework": nation_data.get("jurisdictional_framework", ""),
                    "tribes": nation_data.get("tribes", []),
                    "princes": nation_data.get("princes", []),
                    "dukes": nation_data.get("dukes", []),
                    "clans": nation_data.get("clans", []),
                    "sons": nation_data.get("sons", {})
                }
            )
            
            print(f"Created nation: {nation_name}")
        else:
            print(f"Nation already exists: {nation_name}")

if __name__ == "__main__":
    initialize_nations()
