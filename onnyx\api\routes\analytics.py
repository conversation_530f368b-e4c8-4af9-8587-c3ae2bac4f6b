"""
Onnyx Analytics Routes

This module provides API routes for analytics.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional

from shared.utils.event_logger import event_logger

# Create router
router = APIRouter()

@router.get("/analytics/logs")
def get_logs(
    limit: Optional[int] = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get event logs.
    
    Args:
        limit: Maximum number of logs to return
        offset: Offset for pagination
    
    Returns:
        A list of event logs
    """
    logs = event_logger.get_logs(limit=limit, offset=offset)
    
    return {
        "logs": logs,
        "count": len(logs),
        "limit": limit,
        "offset": offset
    }

@router.get("/analytics/logs/{block_index}")
def get_log_by_block_index(block_index: int) -> Dict[str, Any]:
    """
    Get an event log by block index.
    
    Args:
        block_index: The block index
    
    Returns:
        The event log
    """
    logs = event_logger.get_logs()
    
    for log in logs:
        if log["block_index"] == block_index:
            return {
                "log": log
            }
    
    raise HTTPException(status_code=404, detail=f"Event log for block {block_index} not found")

@router.get("/analytics/summary")
def get_summary(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get a summary of event logs.
    
    Args:
        days: Number of days to include in the summary
    
    Returns:
        A summary of event logs
    """
    summary = event_logger.generate_summary(days=days)
    
    return {
        "summary": summary,
        "days": days
    }

@router.get("/analytics/proposers")
def get_proposers(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get a list of unique proposers.
    
    Args:
        days: Number of days to include
    
    Returns:
        A list of unique proposers
    """
    summary = event_logger.generate_summary(days=days)
    
    return {
        "proposers": summary["unique_proposers"],
        "count": summary["unique_proposer_count"],
        "days": days
    }

@router.get("/analytics/transactions")
def get_transactions(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get transaction statistics.
    
    Args:
        days: Number of days to include
    
    Returns:
        Transaction statistics
    """
    summary = event_logger.generate_summary(days=days)
    
    return {
        "transactions": {
            "total": summary["transactions"],
            "token_mints": summary["token_mints"],
            "token_transfers": summary["token_transfers"],
            "token_burns": summary["token_burns"],
            "proposals": summary["proposals"],
            "votes": summary["votes"],
            "identities": summary["identities"],
            "reputation_grants": summary["reputation_grants"],
            "stakes": summary["stakes"],
            "rewards": summary["rewards"]
        },
        "days": days
    }

@router.get("/analytics/blocks")
def get_blocks(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get block statistics.
    
    Args:
        days: Number of days to include
    
    Returns:
        Block statistics
    """
    summary = event_logger.generate_summary(days=days)
    
    # Calculate average transactions per block
    avg_tx_per_block = 0
    if summary["blocks"] > 0:
        avg_tx_per_block = summary["transactions"] / summary["blocks"]
    
    # Calculate blocks per day
    blocks_per_day = 0
    if summary["start_timestamp"] and summary["end_timestamp"]:
        days_elapsed = (summary["end_timestamp"] - summary["start_timestamp"]) / 86400
        if days_elapsed > 0:
            blocks_per_day = summary["blocks"] / days_elapsed
    
    return {
        "blocks": {
            "total": summary["blocks"],
            "avg_tx_per_block": avg_tx_per_block,
            "blocks_per_day": blocks_per_day
        },
        "days": days
    }
