# Onnyx API Documentation

This document provides documentation for the updated Onnyx API.

## Overview

The Onnyx API provides a RESTful interface for interacting with the Onnyx blockchain. It allows you to:

- Create and manage identities
- Create and manage tokens
- Create and manage Selas (businesses)
- Create and manage Voice Scrolls (governance proposals)
- Mine blocks
- View chain parameters
- View analytics

## Base URL

The API is available at `http://localhost:5000` by default.

## Authentication

The API does not currently require authentication.

## Endpoints

### Root

- `GET /`: Get information about the API.

### Health

- `GET /health`: Get the health status of the API.

### Identity

- `GET /identity/{identity_id}`: Get an identity by ID.
- `GET /identities`: Get all identities.
- `POST /identity/create`: Create a new identity.
- `POST /identity/update`: Update an identity.
- `GET /identity/{identity_id}/tokens`: Get tokens owned by an identity.
- `GET /identity/{identity_id}/transactions`: Get transactions involving an identity.
- `GET /identity/{identity_id}/selas`: Get Selas associated with an identity.

### Token

- `GET /token/{token_id}`: Get a token by ID.
- `GET /tokens`: Get all tokens.
- `POST /token/create`: Create a new token.
- `POST /token/mint`: Mint tokens.
- `POST /token/transfer`: Transfer tokens.
- `POST /token/burn`: Burn tokens.
- `GET /token/{token_id}/transactions`: Get transactions involving a token.
- `GET /token/{token_id}/holders`: Get holders of a token.

### Transaction

- `GET /transaction/{tx_id}`: Get a transaction by ID.
- `GET /transactions`: Get all transactions.
- `GET /transactions/pending`: Get pending transactions.
- `GET /transactions/confirmed`: Get confirmed transactions.

### Sela

- `GET /sela/{sela_id}`: Get a Sela by ID.
- `GET /selas`: Get all Selas.
- `POST /sela/register`: Register a new Sela.
- `POST /sela/join`: Join a Sela.
- `POST /sela/leave`: Leave a Sela.
- `POST /sela/{sela_id}/update-role`: Update a member's role in a Sela.
- `POST /sela/{sela_id}/add-service`: Add a service to a Sela.
- `POST /sela/{sela_id}/remove-service`: Remove a service from a Sela.
- `GET /selas/founder/{founder_id}`: Get Selas founded by an identity.
- `GET /selas/member/{member_id}`: Get Selas that an identity is a member of.

### Etzem

- `GET /etzem/{identity_id}`: Get the Etzem trust score for an identity.
- `POST /etzem/{identity_id}/badges`: Update the Etzem badges for an identity.
- `POST /etzem/badges/update-all`: Update the Etzem badges for all identities.
- `GET /leaderboard`: Get the Etzem leaderboard.
- `GET /thresholds`: Get the Etzem thresholds for badges.

### Miner

- `POST /miner/mine`: Mine a new block.
- `GET /miner/stats`: Get mining statistics.
- `GET /miner/latest-blocks/{count}`: Get the latest blocks.
- `GET /miner/block/{block_id}`: Get a block by hash or index.
- `POST /miner/transactions`: Add a transaction to the mempool.

### Sela Miner

- `GET /sela-miner/status`: Get the status of the Sela miner.
- `POST /sela-miner/mine`: Mine a new block.
- `POST /sela-miner/service`: Record a service provided by the Sela.
- `POST /sela-miner/vote`: Record a governance vote.
- `GET /sela-miner/activities`: Get activities.
- `GET /sela-miner/services`: Get service logs.
- `GET /sela-miner/pending-transactions`: Get pending transactions.
- `GET /sela-miner/votes`: Get governance votes.
- `GET /sela-miner/validator-status`: Get the validator status.
- `POST /sela-miner/activity`: Record an activity.

### Rotation

- `GET /rotation/status`: Get the current rotation status.
- `GET /rotation/next-validator/{height}`: Get the next validator for a given block height.
- `POST /rotation/update-queue`: Update the queue of eligible validators.
- `POST /rotation/set-update-interval`: Set the queue update interval.
- `POST /rotation/set-min-etzem-score`: Set the minimum Etzem score required for eligibility.
- `POST /rotation/set-required-badges`: Set the required badges for eligibility.
- `GET /rotation/is-valid-proposer/{sela_id}/{height}`: Check if a Sela is the valid proposer for a given block height.
- `GET /rotation/eligible-validators`: Get all eligible validators.

### Chain Parameters

- `GET /chain-parameters`: Get all chain parameters.
- `GET /chain-parameters/{key}`: Get a chain parameter.
- `POST /chain-parameters/{key}`: Set a chain parameter.
- `POST /chain-parameters/reset/{key}`: Reset a chain parameter to its default value.
- `POST /chain-parameters/reset-all`: Reset all chain parameters to their default values.
- `POST /chain-parameters/propose`: Propose a chain parameter change.
- `GET /chain-parameters/proposals`: Get chain parameter proposals.
- `GET /chain-parameters/categories`: Get all chain parameter categories.
- `GET /chain-parameters/category/{category}`: Get chain parameters by category.

### Voice Scroll

- `POST /governance/propose`: Propose a new Voice Scroll.
- `POST /governance/vote`: Vote on a Voice Scroll.
- `GET /governance/scrolls`: Get Voice Scrolls.
- `GET /governance/scrolls/{scroll_id}`: Get a Voice Scroll by ID.
- `GET /governance/scrolls/{scroll_id}/tally`: Tally the votes for a Voice Scroll.
- `POST /governance/resolve/{scroll_id}`: Resolve a Voice Scroll.
- `GET /governance/categories`: Get the available Voice Scroll categories.
- `GET /governance/outcomes`: Get the available Voice Scroll outcomes.
- `GET /governance/active-scrolls`: Get all active Voice Scrolls.
- `GET /governance/resolved-scrolls`: Get all resolved Voice Scrolls.

### Analytics

- `GET /analytics/logs`: Get event logs.
- `GET /analytics/logs/{block_index}`: Get event logs by block index.
- `GET /analytics/summary`: Get a summary of event logs.
- `GET /analytics/proposers`: Get a list of unique proposers.
- `GET /analytics/transactions`: Get transaction statistics.
- `GET /analytics/blocks`: Get block statistics.
- `GET /analytics/tokens`: Get token statistics.
- `GET /analytics/identities`: Get identity statistics.

## Models

### Identity

```json
{
  "identity_id": "string",
  "name": "string",
  "public_key": "string",
  "created_at": 0,
  "metadata": {}
}
```

### Token

```json
{
  "token_id": "string",
  "name": "string",
  "symbol": "string",
  "creator_id": "string",
  "supply": 0,
  "category": "string",
  "decimals": 0,
  "created_at": 0,
  "metadata": {}
}
```

### Transaction

```json
{
  "tx_id": "string",
  "tx_type": "string",
  "sender": "string",
  "recipient": "string",
  "token_id": "string",
  "amount": 0,
  "status": "string",
  "timestamp": 0,
  "block_hash": "string",
  "block_index": 0,
  "data": {}
}
```

### Sela

```json
{
  "sela_id": "string",
  "name": "string",
  "founder_id": "string",
  "sela_type": "string",
  "token_id": "string",
  "services": [],
  "roles": {},
  "members": [],
  "created_at": 0,
  "metadata": {}
}
```

### Etzem

```json
{
  "identity_id": "string",
  "etzem_score": 0,
  "components": {},
  "badges": [],
  "last_updated": 0
}
```

### Block

```json
{
  "index": 0,
  "hash": "string",
  "previous_hash": "string",
  "timestamp": 0,
  "transactions": [],
  "nonce": 0,
  "difficulty": 0,
  "miner": "string"
}
```

### Voice Scroll

```json
{
  "scroll_id": "string",
  "creator_id": "string",
  "title": "string",
  "description": "string",
  "category": "string",
  "status": "string",
  "created_at": 0,
  "expires_at": 0,
  "effect": {},
  "votes": {},
  "outcome": "string",
  "resolved_at": 0,
  "metadata": {}
}
```

### Chain Parameter

```json
{
  "key": "string",
  "value": "any",
  "default_value": "any",
  "description": "string",
  "category": "string",
  "last_updated": 0,
  "last_updated_by": "string",
  "metadata": {}
}
```

## Running the API

To run the API, use the following command:

```bash
python run_api_updated.py
```

You can specify the host, port, and other options:

```bash
python run_api_updated.py --host 0.0.0.0 --port 5000 --reload --log-level debug
```

## Testing the API

To run the tests, use the following command:

```bash
python run_tests.py
```

You can specify a specific test file, class, or method:

```bash
python run_tests.py --test-file test_api_updated.py --test-class TestAPIUpdated --test-method test_root
```
