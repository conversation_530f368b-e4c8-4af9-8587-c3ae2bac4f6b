{"TEST_ONX": {"token_id": "TEST_ONX", "name": "Test Onnyx <PERSON>", "symbol": "TEST_ONX", "creator_id": "test_identity", "supply": 1000000, "category": "native", "decimals": 8, "created_at": 1620000000, "metadata": {"description": "Test native token for Onnyx blockchain"}}, "TEST_LOYALTY": {"token_id": "TEST_LOYALTY", "name": "Test Loyalty <PERSON>", "symbol": "TEST_LT", "creator_id": "test_sela", "supply": 10000, "category": "loyalty", "decimals": 2, "created_at": 1620000001, "metadata": {"description": "Test loyalty token for unit tests", "sela_id": "test_sela"}}, "TEST_EQUITY": {"token_id": "TEST_EQUITY", "name": "Test Equity Token", "symbol": "TEST_ET", "creator_id": "test_sela", "supply": 1000, "category": "equity", "decimals": 0, "created_at": 1620000002, "metadata": {"description": "Test equity token for unit tests", "sela_id": "test_sela"}}}