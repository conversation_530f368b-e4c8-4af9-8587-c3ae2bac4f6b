"""
Test script for the new Onnyx VM opcodes.

This script tests the new opcode validation functions:
- OP_BURN: For burning tokens
- OP_GRANT_REPUTATION: For granting reputation to identities
- OP_STAKE: For staking tokens
- OP_VOTE: For voting on governance proposals
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from opcodes import (
    OP_BURN, OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE,
    op_burn, op_grant_reputation, op_stake, op_vote,
    OpcodeBurnError, OpcodeGrantReputationError, OpcodeStakeError, OpcodeVoteError,
    identity, selas, tokens, ledger, scrolls
)

def setup_test_data():
    """Set up test data for the opcode tests."""
    # Set up identities
    identity.identities["alice"] = {"name": "<PERSON>"}
    identity.identities["bob"] = {"name": "<PERSON>"}
    identity.identities["charlie"] = {"name": "Charlie"}
    
    # Set up badges
    identity.badges["alice"] = ["CREATOR", "VALIDATOR"]
    identity.badges["bob"] = ["CREATOR"]
    identity.badges["charlie"] = []
    
    # Set up reputation
    identity.reputation["alice"] = 50
    identity.reputation["bob"] = 20
    identity.reputation["charlie"] = 5
    
    # Set up Selas
    selas.selas["alice"] = {"name": "Alice's Business"}
    
    # Set up tokens
    tokens.tokens["ALICE"] = {"symbol": "ALICE", "supply": 1000, "creator": "alice"}
    tokens.tokens["ONX"] = {"symbol": "ONX", "supply": 10000, "creator": "alice"}
    
    # Set up ledger
    ledger.balances["alice:ALICE"] = 1000
    ledger.balances["alice:ONX"] = 5000
    ledger.balances["bob:ONX"] = 2000
    ledger.balances["charlie:ONX"] = 500
    
    # Set up scrolls
    scrolls.scrolls["scroll1"] = {
        "title": "Test Scroll",
        "description": "This is a test scroll",
        "category": "test",
        "creator": "alice"
    }

def test_op_burn():
    """Test the OP_BURN opcode."""
    print("\nTesting OP_BURN...")
    
    # Test valid burn transaction
    valid_tx = {
        "op": OP_BURN,
        "from": "alice",
        "data": {
            "token_id": "ALICE",
            "amount": 100
        }
    }
    
    try:
        result = op_burn(valid_tx)
        print(f"Valid burn transaction: {result}")
    except OpcodeBurnError as e:
        print(f"Error: {e}")
    
    # Test invalid burn transaction (insufficient balance)
    invalid_tx = {
        "op": OP_BURN,
        "from": "alice",
        "data": {
            "token_id": "ALICE",
            "amount": 2000  # Exceeds balance of 1000
        }
    }
    
    try:
        result = op_burn(invalid_tx)
        print(f"Invalid burn transaction (should not reach here): {result}")
    except OpcodeBurnError as e:
        print(f"Expected error: {e}")
    
    # Test invalid burn transaction (not token creator)
    invalid_tx2 = {
        "op": OP_BURN,
        "from": "bob",
        "data": {
            "token_id": "ALICE",
            "amount": 100
        }
    }
    
    try:
        result = op_burn(invalid_tx2)
        print(f"Invalid burn transaction (should not reach here): {result}")
    except OpcodeBurnError as e:
        print(f"Expected error: {e}")

def test_op_grant_reputation():
    """Test the OP_GRANT_REPUTATION opcode."""
    print("\nTesting OP_GRANT_REPUTATION...")
    
    # Test valid grant reputation transaction
    valid_tx = {
        "op": OP_GRANT_REPUTATION,
        "from": "alice",  # Has VALIDATOR badge
        "data": {
            "to_identity": "charlie",
            "amount": 10
        }
    }
    
    try:
        result = op_grant_reputation(valid_tx)
        print(f"Valid grant reputation transaction: {result}")
    except OpcodeGrantReputationError as e:
        print(f"Error: {e}")
    
    # Test invalid grant reputation transaction (no VALIDATOR badge)
    invalid_tx = {
        "op": OP_GRANT_REPUTATION,
        "from": "bob",  # No VALIDATOR badge
        "data": {
            "to_identity": "charlie",
            "amount": 10
        }
    }
    
    try:
        result = op_grant_reputation(invalid_tx)
        print(f"Invalid grant reputation transaction (should not reach here): {result}")
    except OpcodeGrantReputationError as e:
        print(f"Expected error: {e}")
    
    # Test invalid grant reputation transaction (amount too high)
    invalid_tx2 = {
        "op": OP_GRANT_REPUTATION,
        "from": "alice",
        "data": {
            "to_identity": "charlie",
            "amount": 200  # Exceeds maximum of 100
        }
    }
    
    try:
        result = op_grant_reputation(invalid_tx2)
        print(f"Invalid grant reputation transaction (should not reach here): {result}")
    except OpcodeGrantReputationError as e:
        print(f"Expected error: {e}")

def test_op_stake():
    """Test the OP_STAKE opcode."""
    print("\nTesting OP_STAKE...")
    
    # Test valid stake transaction
    valid_tx = {
        "op": OP_STAKE,
        "from": "alice",
        "data": {
            "token_id": "ONX",
            "amount": 1000,
            "duration": 30  # 30 days
        }
    }
    
    try:
        result = op_stake(valid_tx)
        print(f"Valid stake transaction: {result}")
    except OpcodeStakeError as e:
        print(f"Error: {e}")
    
    # Test invalid stake transaction (insufficient balance)
    invalid_tx = {
        "op": OP_STAKE,
        "from": "alice",
        "data": {
            "token_id": "ONX",
            "amount": 10000,  # Exceeds balance of 5000
            "duration": 30
        }
    }
    
    try:
        result = op_stake(invalid_tx)
        print(f"Invalid stake transaction (should not reach here): {result}")
    except OpcodeStakeError as e:
        print(f"Expected error: {e}")
    
    # Test invalid stake transaction (non-ONX token)
    invalid_tx2 = {
        "op": OP_STAKE,
        "from": "alice",
        "data": {
            "token_id": "ALICE",  # Not ONX
            "amount": 100,
            "duration": 30
        }
    }
    
    try:
        result = op_stake(invalid_tx2)
        print(f"Invalid stake transaction (should not reach here): {result}")
    except OpcodeStakeError as e:
        print(f"Expected error: {e}")

def test_op_vote():
    """Test the OP_VOTE opcode."""
    print("\nTesting OP_VOTE...")
    
    # Test valid vote transaction
    valid_tx = {
        "op": OP_VOTE,
        "from": "alice",
        "data": {
            "scroll_id": "scroll1",
            "vote": True
        }
    }
    
    try:
        result = op_vote(valid_tx)
        print(f"Valid vote transaction: {result}")
    except OpcodeVoteError as e:
        print(f"Error: {e}")
    
    # Test invalid vote transaction (scroll doesn't exist)
    invalid_tx = {
        "op": OP_VOTE,
        "from": "alice",
        "data": {
            "scroll_id": "scroll2",  # Doesn't exist
            "vote": True
        }
    }
    
    try:
        result = op_vote(invalid_tx)
        print(f"Invalid vote transaction (should not reach here): {result}")
    except OpcodeVoteError as e:
        print(f"Expected error: {e}")
    
    # Test invalid vote transaction (insufficient reputation)
    invalid_tx2 = {
        "op": OP_VOTE,
        "from": "charlie",  # Reputation of 5, below minimum of 10
        "data": {
            "scroll_id": "scroll1",
            "vote": True
        }
    }
    
    try:
        result = op_vote(invalid_tx2)
        print(f"Invalid vote transaction (should not reach here): {result}")
    except OpcodeVoteError as e:
        print(f"Expected error: {e}")

def main():
    """Run all tests."""
    print("Testing New Onnyx VM Opcodes")
    print("===========================")
    
    # Set up test data
    setup_test_data()
    
    # Run tests
    test_op_burn()
    test_op_grant_reputation()
    test_op_stake()
    test_op_vote()
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
