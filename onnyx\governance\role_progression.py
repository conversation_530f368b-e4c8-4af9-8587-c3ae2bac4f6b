"""
Onnyx Role Progression Module

This module provides the RoleProgression class for managing role progression.
"""

import os
import json
from typing import Dict, List, Any, Optional

from identity.registry import IdentityRegistry
from governance.etzem_engine import EtzemEngine

class RoleProgression:
    """
    RoleProgression manages role progression for identities.
    
    Roles are assigned based on Etzem scores and other criteria.
    """
    
    def __init__(self, identity_registry: IdentityRegistry, etzem_engine: EtzemEngine):
        """
        Initialize the RoleProgression.
        
        Args:
            identity_registry: The identity registry
            etzem_engine: The Etzem engine
        """
        self.identity_registry = identity_registry
        self.etzem_engine = etzem_engine
        
        # Define role thresholds
        self.role_thresholds = {
            "PROPOSAL_ELIGIBLE": 50,
            "GUARDIAN_ELIGIBLE": 70,
            "VALIDATOR_ELIGIBLE": 85
        }
    
    def check_role_eligibility(self, identity_id: str) -> Dict[str, bool]:
        """
        Check role eligibility for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            A dictionary of role eligibility
        
        Raises:
            Exception: If the identity does not exist
        """
        identity = self.identity_registry.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Calculate the Etzem score
        etzem_data = self.etzem_engine.calculate_etzem_score(identity_id)
        etzem_score = etzem_data["final_etzem"]
        
        # Check role eligibility
        eligibility = {}
        
        # PROPOSAL_ELIGIBLE: Etzem score >= 50
        eligibility["PROPOSAL_ELIGIBLE"] = etzem_score >= self.role_thresholds["PROPOSAL_ELIGIBLE"]
        
        # GUARDIAN_ELIGIBLE: Etzem score >= 70
        eligibility["GUARDIAN_ELIGIBLE"] = etzem_score >= self.role_thresholds["GUARDIAN_ELIGIBLE"]
        
        # VALIDATOR_ELIGIBLE: Etzem score >= 85 and has STAKER badge
        has_staker_badge = "STAKER" in identity.get("badges", [])
        eligibility["VALIDATOR_ELIGIBLE"] = (
            etzem_score >= self.role_thresholds["VALIDATOR_ELIGIBLE"] and has_staker_badge
        )
        
        return eligibility
    
    def update_role_badges(self, identity_id: str) -> Dict[str, Any]:
        """
        Update role badges for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The updated identity
        
        Raises:
            Exception: If the identity does not exist
        """
        identity = self.identity_registry.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check role eligibility
        eligibility = self.check_role_eligibility(identity_id)
        
        # Update badges
        for role, eligible in eligibility.items():
            badge = f"{role}_BADGE"
            
            if eligible and badge not in identity.get("badges", []):
                self.identity_registry.add_badge(identity_id, badge)
            elif not eligible and badge in identity.get("badges", []):
                self.identity_registry.remove_badge(identity_id, badge)
        
        # Return the updated identity
        return self.identity_registry.get_identity(identity_id)
    
    def update_all_role_badges(self) -> Dict[str, Dict[str, Any]]:
        """
        Update role badges for all identities.
        
        Returns:
            A dictionary of updated identities
        """
        updated_identities = {}
        
        for identity_id in self.identity_registry.get_all_identities():
            updated_identities[identity_id] = self.update_role_badges(identity_id)
        
        return updated_identities
