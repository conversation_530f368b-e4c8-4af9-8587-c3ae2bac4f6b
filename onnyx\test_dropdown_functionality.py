#!/usr/bin/env python3
"""
Test Dropdown Functionality

Creates a test identity and tests the user dropdown menu functionality.
"""

import requests
import json
import time
from bs4 import BeautifulSoup

def create_test_identity():
    """Create a test identity for testing dropdown functionality."""
    base_url = "http://127.0.0.1:5000"

    # Test data
    test_data = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'role': 'Business Owner'
    }

    try:
        # Create identity
        response = requests.post(f"{base_url}/auth/register/identity", data=test_data, timeout=10)
        if response.status_code == 200:
            print("✅ Test identity created successfully")
            return True
        else:
            print(f"❌ Failed to create test identity: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error creating test identity: {e}")
        return False

def test_login_functionality():
    """Test login functionality."""
    base_url = "http://127.0.0.1:5000"

    # Create a session to maintain cookies
    session = requests.Session()

    try:
        # Try to login
        login_data = {
            'email': '<EMAIL>'
        }

        response = session.post(f"{base_url}/auth/login", data=login_data, timeout=10)
        if response.status_code == 200:
            print("✅ Login successful")

            # Check if we're redirected to dashboard or if login was successful
            if 'dashboard' in response.url or 'Welcome back' in response.text:
                print("✅ Successfully logged in and redirected")
                return session
            else:
                print("⚠️ Login response received but redirect unclear")
                return session
        else:
            print(f"❌ Login failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def test_logged_in_navigation(session):
    """Test navigation when logged in."""
    base_url = "http://127.0.0.1:5000"
    results = []

    try:
        # Get the main page while logged in
        response = session.get(base_url, timeout=10)
        if response.status_code == 200:
            results.append("✅ Accessed main page while logged in")
        else:
            results.append(f"❌ Failed to access main page: {response.status_code}")
            return results

        soup = BeautifulSoup(response.content, 'html.parser')

        # Test 1: Check if user dropdown button exists
        dropdown_button = soup.find(id='user-dropdown-button')
        if dropdown_button:
            results.append("✅ User dropdown button found when logged in")

            # Check if it has the user's initial
            user_initial = dropdown_button.find('span', class_=lambda x: x and 'font-orbitron' in x)
            if user_initial and user_initial.get_text().strip():
                results.append(f"✅ User initial displayed: '{user_initial.get_text().strip()}'")
            else:
                results.append("❌ User initial not displayed in dropdown button")
        else:
            results.append("❌ User dropdown button not found when logged in")

        # Test 2: Check if dropdown menu exists
        dropdown_menu = soup.find(id='user-dropdown-menu')
        if dropdown_menu:
            results.append("✅ User dropdown menu found when logged in")

            # Check for dropdown menu items
            menu_links = dropdown_menu.find_all('a')
            expected_items = ['Dashboard', 'My Identity', 'My Validators', 'Logout']

            found_items = [link.get_text().strip() for link in menu_links]
            for expected in expected_items:
                if any(expected in item for item in found_items):
                    results.append(f"✅ '{expected}' menu item found")
                else:
                    results.append(f"❌ '{expected}' menu item missing")
        else:
            results.append("❌ User dropdown menu not found when logged in")

        # Test 3: Check if main navigation still exists
        nav_links = soup.find_all('a', class_='nav-link')
        if len(nav_links) >= 5:  # Should include Dashboard and Auto-Mining when logged in
            results.append(f"✅ Found {len(nav_links)} navigation links when logged in")

            nav_texts = [link.get_text().strip() for link in nav_links]
            logged_in_nav = ['Home', 'Validators', 'Explorer', 'Dashboard', 'Auto-Mining']

            for expected in logged_in_nav:
                if expected in nav_texts:
                    results.append(f"✅ '{expected}' navigation link found when logged in")
                else:
                    results.append(f"❌ '{expected}' navigation link missing when logged in")
        else:
            results.append(f"❌ Expected at least 5 nav links when logged in, found {len(nav_links)}")

        # Test 4: Check if guest buttons are hidden in navigation (not footer)
        nav_element = soup.find('nav')
        if nav_element:
            access_portal = nav_element.find('a', string=lambda text: text and 'Access Portal' in text)
            verify_identity = nav_element.find('a', string=lambda text: text and 'Verify Identity' in text)

            if not access_portal and not verify_identity:
                results.append("✅ Guest buttons properly hidden in navigation when logged in")
            else:
                results.append("❌ Guest buttons still visible in navigation when logged in")
        else:
            results.append("❌ Navigation element not found")

    except Exception as e:
        results.append(f"❌ Error testing logged-in navigation: {e}")

    return results

def test_dropdown_css_functionality():
    """Test dropdown CSS and JavaScript functionality."""
    base_url = "http://127.0.0.1:5000"
    results = []

    try:
        # Check CSS file for dropdown styles
        css_response = requests.get(f"{base_url}/static/css/main.css", timeout=5)
        if css_response.status_code == 200:
            css_content = css_response.text

            # Check for specific dropdown CSS
            if '#user-dropdown-button' in css_content:
                results.append("✅ Dropdown button CSS found")
            else:
                results.append("❌ Dropdown button CSS missing")

            if '#user-dropdown-menu' in css_content:
                results.append("✅ Dropdown menu CSS found")
            else:
                results.append("❌ Dropdown menu CSS missing")

            if 'z-index: 9999' in css_content:
                results.append("✅ Proper z-index for dropdown found")
            else:
                results.append("❌ Proper z-index for dropdown missing")

        else:
            results.append(f"❌ CSS file not accessible: {css_response.status_code}")

    except Exception as e:
        results.append(f"❌ Error testing dropdown CSS: {e}")

    return results

def main():
    """Run all dropdown functionality tests."""
    print("🚀 Testing ONNYX Dropdown Functionality")
    print("=" * 50)

    all_results = []

    # Test 1: Create test identity
    print("\n1. Creating test identity...")
    if not create_test_identity():
        print("❌ Cannot proceed without test identity")
        return

    # Test 2: Test login
    print("\n2. Testing login functionality...")
    session = test_login_functionality()
    if not session:
        print("❌ Cannot proceed without successful login")
        return

    # Test 3: Test logged-in navigation
    print("\n3. Testing logged-in navigation...")
    nav_results = test_logged_in_navigation(session)
    all_results.extend(nav_results)

    # Test 4: Test dropdown CSS
    print("\n4. Testing dropdown CSS...")
    css_results = test_dropdown_css_functionality()
    all_results.extend(css_results)

    # Print all results
    print("\n📊 Test Results:")
    print("=" * 50)
    for result in all_results:
        print(result)

    # Summary
    passed = len([r for r in all_results if r.startswith("✅")])
    failed = len([r for r in all_results if r.startswith("❌")])

    print(f"\n📈 Summary: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All dropdown tests passed! User dropdown is working correctly.")
    else:
        print("⚠️ Some dropdown tests failed. Please review the issues above.")

if __name__ == "__main__":
    main()
