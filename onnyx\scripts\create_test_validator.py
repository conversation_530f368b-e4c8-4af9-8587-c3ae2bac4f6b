#!/usr/bin/env python3
"""
Create Test Validator for Hybrid Mining System

Creates a test validator to demonstrate the hybrid mining system.
"""

import os
import sys
import uuid
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def create_test_validator():
    """Create a test validator for hybrid mining demonstration."""
    print("🏢 CREATING TEST VALIDATOR FOR HYBRID MINING")
    print("=" * 50)
    
    try:
        # Check if we already have a test validator
        existing = db.query_one("SELECT * FROM selas WHERE name = 'Test Mining Validator'")
        if existing:
            print("✅ Test validator already exists")
            print(f"   Sela ID: {existing['sela_id']}")
            print(f"   Mining Tier: {existing.get('mining_tier', 'basic')}")
            print(f"   Mining Power: {existing.get('mining_power', 1)}x")
            return existing['sela_id']
        
        # Check if we have an identity to use
        identity = db.query_one("SELECT * FROM identities LIMIT 1")
        if not identity:
            print("❌ No identities found. Please create an identity first.")
            return None
        
        # Create test validator
        sela_id = str(uuid.uuid4())
        
        validator_data = {
            'sela_id': sela_id,
            'identity_id': identity['identity_id'],
            'name': 'Test Mining Validator',
            'category': 'Technology',
            'stake_amount': 100.0,
            'stake_token_id': 'ONNYX',
            'status': 'active',
            'created_at': datetime.now().isoformat(),
            'metadata': '{"description": "Test validator for hybrid mining system", "location": "Test Environment"}',
            'mining_tier': 'optimized',
            'mining_power': 3,
            'mining_rewards_earned': 0.0,
            'blocks_mined': 0,
            'last_mining_activity': datetime.now().isoformat()
        }
        
        db.insert('selas', validator_data)
        
        print("✅ Test validator created successfully!")
        print(f"   Sela ID: {sela_id}")
        print(f"   Name: Test Mining Validator")
        print(f"   Owner: {identity['name']}")
        print(f"   Mining Tier: optimized")
        print(f"   Mining Power: 3x")
        print(f"   Status: active")
        
        # Create a tier history record
        history_data = {
            'sela_id': sela_id,
            'old_tier': 'basic',
            'new_tier': 'optimized',
            'old_mining_power': 1,
            'new_mining_power': 3,
            'changed_by': 'SYSTEM_TEST',
            'reason': 'Test validator creation with optimized tier',
            'timestamp': datetime.now().isoformat()
        }
        
        db.insert('mining_tier_history', history_data)
        print("✅ Mining tier history record created")
        
        return sela_id
        
    except Exception as e:
        print(f"❌ Error creating test validator: {e}")
        return None

def main():
    """Main entry point."""
    sela_id = create_test_validator()
    return 0 if sela_id else 1

if __name__ == "__main__":
    sys.exit(main())
