# tests/test_node.py

import sys
import os
import unittest
import tempfile
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.node.node import OnnyxNode

class TestOnnyxNode(unittest.TestCase):
    def setUp(self):
        # Create a temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.close()
        self.node = OnnyxNode(path=self.temp_file.name)
    
    def tearDown(self):
        # Clean up the temporary file
        os.unlink(self.temp_file.name)
    
    def test_node_initialization(self):
        """Test that a node is initialized with a genesis block."""
        # Check that the blockchain has a genesis block
        self.assertEqual(len(self.node.blockchain.chain), 1)
        self.assertEqual(self.node.blockchain.chain[0].index, 0)
        self.assertEqual(self.node.blockchain.chain[0].previous_hash, "0")
    
    def test_add_transaction(self):
        """Test adding a transaction to the mempool."""
        # Add a transaction
        tx = {"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10}
        self.node.add_transaction(tx)
        
        # Check that the transaction was added to the mempool
        self.assertEqual(len(self.node.blockchain.mempool), 1)
        self.assertEqual(self.node.blockchain.mempool[0], tx)
    
    def test_mine_block(self):
        """Test mining a block."""
        # Add some transactions
        tx1 = {"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10}
        tx2 = {"type": "transfer", "sender": "bob", "recipient": "charlie", "amount": 5}
        self.node.add_transaction(tx1)
        self.node.add_transaction(tx2)
        
        # Mine a block
        block = self.node.mine_block()
        
        # Check that the block was mined
        self.assertEqual(block["index"], 1)
        self.assertEqual(len(block["transactions"]), 2)
        self.assertEqual(block["transactions"][0], tx1)
        self.assertEqual(block["transactions"][1], tx2)
        
        # Check that the mempool was cleared
        self.assertEqual(len(self.node.blockchain.mempool), 0)
        
        # Check that the blockchain has two blocks (genesis + new block)
        self.assertEqual(len(self.node.blockchain.chain), 2)
    
    def test_get_latest_block(self):
        """Test getting the latest block."""
        # Mine a block
        self.node.add_transaction({"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10})
        self.node.mine_block()
        
        # Get the latest block
        latest_block = self.node.get_latest_block()
        
        # Check that it's the block we just mined
        self.assertEqual(latest_block["index"], 1)
        self.assertEqual(len(latest_block["transactions"]), 1)
    
    def test_get_block_by_index(self):
        """Test getting a block by index."""
        # Mine a block
        self.node.add_transaction({"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10})
        self.node.mine_block()
        
        # Get the block by index
        block = self.node.get_block_by_index(1)
        
        # Check that it's the block we just mined
        self.assertEqual(block["index"], 1)
        self.assertEqual(len(block["transactions"]), 1)
        
        # Try to get a non-existent block
        block = self.node.get_block_by_index(2)
        self.assertIsNone(block)
    
    def test_get_block_by_hash(self):
        """Test getting a block by hash."""
        # Mine a block
        self.node.add_transaction({"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10})
        mined_block = self.node.mine_block()
        
        # Get the block by hash
        block = self.node.get_block_by_hash(mined_block["hash"])
        
        # Check that it's the block we just mined
        self.assertEqual(block["index"], 1)
        self.assertEqual(len(block["transactions"]), 1)
        
        # Try to get a non-existent block
        block = self.node.get_block_by_hash("nonexistent")
        self.assertIsNone(block)
    
    def test_get_chain(self):
        """Test getting the entire blockchain."""
        # Mine a few blocks
        self.node.add_transaction({"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10})
        self.node.mine_block()
        self.node.add_transaction({"type": "transfer", "sender": "bob", "recipient": "charlie", "amount": 5})
        self.node.mine_block()
        
        # Get the chain
        chain = self.node.get_chain()
        
        # Check that it has the right number of blocks
        self.assertEqual(len(chain), 3)  # Genesis + 2 mined blocks
        self.assertEqual(chain[0]["index"], 0)  # Genesis block
        self.assertEqual(chain[1]["index"], 1)  # First mined block
        self.assertEqual(chain[2]["index"], 2)  # Second mined block
    
    def test_status(self):
        """Test getting the node status."""
        # Mine a block
        self.node.add_transaction({"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10})
        self.node.mine_block()
        
        # Get the status
        status = self.node.status()
        
        # Check the status
        self.assertEqual(status["height"], 1)  # Genesis block (0) + 1 mined block
        self.assertEqual(status["latest_index"], 1)
        self.assertEqual(status["mempool_size"], 0)
    
    def test_validate_chain(self):
        """Test validating the blockchain."""
        # Mine a few blocks
        self.node.add_transaction({"type": "transfer", "sender": "alice", "recipient": "bob", "amount": 10})
        self.node.mine_block()
        self.node.add_transaction({"type": "transfer", "sender": "bob", "recipient": "charlie", "amount": 5})
        self.node.mine_block()
        
        # Validate the chain
        is_valid = self.node.validate_chain()
        self.assertTrue(is_valid)
        
        # Tamper with the chain
        self.node.blockchain.chain[1].transactions = [{"type": "transfer", "sender": "attacker", "recipient": "attacker", "amount": 1000}]
        
        # Validate again
        is_valid = self.node.validate_chain()
        self.assertFalse(is_valid)

if __name__ == "__main__":
    unittest.main()
