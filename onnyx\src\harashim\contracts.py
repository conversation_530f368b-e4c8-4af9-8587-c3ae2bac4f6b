# src/harashim/contracts.py

import time
import uuid
from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.business.registry import SelaRegistry
from src.harashim.registry import HarashimRegistry
from src.zeman.ledger import ZemanLedger

class ServiceContract:
    """
    Service contract between a Sela and a Harash.
    """

    def __init__(self,
                 sela_registry: Optional[SelaRegistry] = None,
                 harashim_registry: Optional[HarashimRegistry] = None,
                 zeman_ledger: Optional[ZemanLedger] = None):
        """
        Initialize the ServiceContract.

        Args:
            sela_registry: The Sela registry
            harashim_registry: The Harashim registry
            zeman_ledger: The Zeman ledger
        """
        self.selas = sela_registry or SelaRegistry()
        self.harashim = harashim_registry or HarashimRegistry()
        self.zeman = zeman_ledger or ZemanLedger()
        self.db = db_manager.get_connection()

    def create_contract(self,
                        sela_id: str,
                        harash_id: str,
                        title: str,
                        description: str,
                        estimated_hours: int,
                        reward_type: str,
                        reward_amount: float,
                        deadline: Optional[int] = None) -> Dict[str, Any]:
        """
        Create a new service contract.

        Args:
            sela_id: The Sela ID
            harash_id: The Harash ID
            title: The contract title
            description: The contract description
            estimated_hours: The estimated hours to complete
            reward_type: The reward type (ONX, MIKVAH, ZEMAN)
            reward_amount: The reward amount
            deadline: The contract deadline (optional)

        Returns:
            The newly created contract

        Raises:
            Exception: If the Sela or Harash does not exist
        """
        # Check if the Sela exists
        sela = self.selas.get_sela(sela_id)
        if not sela:
            raise Exception(f"Sela with ID '{sela_id}' not found")

        # Check if the Harash exists
        harash = self.harashim.get_harash_role(harash_id)
        if not harash:
            raise Exception(f"Harash with ID '{harash_id}' not found")

        # Generate a unique contract ID
        contract_id = str(uuid.uuid4())

        # Create the contract
        contract = {
            "contract_id": contract_id,
            "sela_id": sela_id,
            "harash_id": harash_id,
            "title": title,
            "description": description,
            "status": "ACTIVE",
            "estimated_hours": estimated_hours,
            "actual_hours": 0,
            "reward_type": reward_type,
            "reward_amount": reward_amount,
            "deadline": deadline,
            "created_at": int(time.time()),
            "completed_at": None,
            "feedback": None,
            "rating": None
        }

        # Save to database
        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT INTO service_contracts
            (contract_id, sela_id, harash_id, title, description, status,
             estimated_hours, actual_hours, reward_type, reward_amount,
             deadline, created_at, completed_at, feedback, rating)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
            (
                contract["contract_id"],
                contract["sela_id"],
                contract["harash_id"],
                contract["title"],
                contract["description"],
                contract["status"],
                contract["estimated_hours"],
                contract["actual_hours"],
                contract["reward_type"],
                contract["reward_amount"],
                contract["deadline"],
                contract["created_at"],
                contract["completed_at"],
                contract["feedback"],
                contract["rating"]
            )
        )
        self.db.commit()

        # Update the Harash's active contracts count
        self.harashim.update_harash_role(
            harash_id,
            {"active_contracts": harash["active_contracts"] + 1}
        )

        return contract

    def get_contract(self, contract_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a contract by ID.

        Args:
            contract_id: The contract ID

        Returns:
            The contract or None if it does not exist
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM service_contracts WHERE contract_id = ?",
            (contract_id,)
        )
        row = cursor.fetchone()

        if not row:
            return None

        return {
            "contract_id": row["contract_id"],
            "sela_id": row["sela_id"],
            "harash_id": row["harash_id"],
            "title": row["title"],
            "description": row["description"],
            "status": row["status"],
            "estimated_hours": row["estimated_hours"],
            "actual_hours": row["actual_hours"],
            "reward_type": row["reward_type"],
            "reward_amount": row["reward_amount"],
            "deadline": row["deadline"],
            "created_at": row["created_at"],
            "completed_at": row["completed_at"],
            "feedback": row["feedback"],
            "rating": row["rating"]
        }

    def get_contracts_by_harash(self, harash_id: str) -> List[Dict[str, Any]]:
        """
        Get all contracts for a Harash.

        Args:
            harash_id: The Harash ID

        Returns:
            A list of contracts
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM service_contracts WHERE harash_id = ?",
            (harash_id,)
        )
        rows = cursor.fetchall()

        return [{
            "contract_id": row["contract_id"],
            "sela_id": row["sela_id"],
            "harash_id": row["harash_id"],
            "title": row["title"],
            "description": row["description"],
            "status": row["status"],
            "estimated_hours": row["estimated_hours"],
            "actual_hours": row["actual_hours"],
            "reward_type": row["reward_type"],
            "reward_amount": row["reward_amount"],
            "deadline": row["deadline"],
            "created_at": row["created_at"],
            "completed_at": row["completed_at"],
            "feedback": row["feedback"],
            "rating": row["rating"]
        } for row in rows]

    def get_contracts_by_sela(self, sela_id: str) -> List[Dict[str, Any]]:
        """
        Get all contracts for a Sela.

        Args:
            sela_id: The Sela ID

        Returns:
            A list of contracts
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM service_contracts WHERE sela_id = ?",
            (sela_id,)
        )
        rows = cursor.fetchall()

        return [{
            "contract_id": row["contract_id"],
            "sela_id": row["sela_id"],
            "harash_id": row["harash_id"],
            "title": row["title"],
            "description": row["description"],
            "status": row["status"],
            "estimated_hours": row["estimated_hours"],
            "actual_hours": row["actual_hours"],
            "reward_type": row["reward_type"],
            "reward_amount": row["reward_amount"],
            "deadline": row["deadline"],
            "created_at": row["created_at"],
            "completed_at": row["completed_at"],
            "feedback": row["feedback"],
            "rating": row["rating"]
        } for row in rows]

    def complete_contract(self,
                          contract_id: str,
                          actual_hours: int,
                          feedback: Optional[str] = None,
                          rating: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        Complete a contract.

        Args:
            contract_id: The contract ID
            actual_hours: The actual hours spent
            feedback: The feedback (optional)
            rating: The rating (1-5, optional)

        Returns:
            The updated contract or None if it does not exist

        Raises:
            Exception: If the contract is already completed
        """
        # Get the contract
        contract = self.get_contract(contract_id)
        if not contract:
            return None

        # Check if the contract is already completed
        if contract["status"] != "ACTIVE":
            raise Exception(f"Contract with ID '{contract_id}' is already {contract['status']}")

        # Update the contract
        cursor = self.db.cursor()
        cursor.execute(
            """
            UPDATE service_contracts
            SET status = ?, actual_hours = ?, completed_at = ?, feedback = ?, rating = ?
            WHERE contract_id = ?
            """,
            (
                "COMPLETED",
                actual_hours,
                int(time.time()),
                feedback,
                rating,
                contract_id
            )
        )
        self.db.commit()

        # Get the updated contract
        updated_contract = self.get_contract(contract_id)

        # Update the Harash's contract counts
        harash = self.harashim.get_harash_role(contract["harash_id"])
        if harash:
            self.harashim.update_harash_role(
                contract["harash_id"],
                {
                    "active_contracts": harash["active_contracts"] - 1,
                    "completed_contracts": harash["completed_contracts"] + 1
                }
            )

        # Credit Zeman hours
        if contract["reward_type"] == "ZEMAN" or contract["reward_type"] == "ALL":
            # If reward type is ZEMAN or ALL, credit the actual hours
            self.zeman.add_hours(
                contract["harash_id"],
                actual_hours,
                description=f"Contract completion: {contract['title']}"
            )

        return updated_contract
