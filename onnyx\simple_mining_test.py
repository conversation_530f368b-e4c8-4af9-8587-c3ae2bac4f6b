"""
Simple test for the Onnyx Mining System

This script tests the Onnyx Mining System by creating a new block without VM validation.
"""

import os
import json
import time
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from consensus.miner import BlockMiner
from node.blockchain import LocalBlockchain
from node.mempool import Mempool
from tokens.ledger import TokenLedger

# Set the bypass validation flag
import consensus.miner
consensus.miner.BYPASS_VALIDATION = True

def test_mining():
    """Test the mining functionality."""
    # Create a miner
    miner = BlockMiner()

    # Get the initial chain length
    initial_length = miner.chain.get_chain_length()
    print(f"Initial chain length: {initial_length}")

    # Create a test identity
    identity_id = "test_miner"

    # Get the initial balance
    initial_balance = miner.ledger.get_balance(identity_id, "ONX")
    print(f"Initial balance of {identity_id}: {initial_balance} ONX")

    # Mine a block
    print(f"Mining a block with proposer {identity_id}...")
    block = miner.create_block(identity_id)

    # Print block information
    print(f"Block {block['index']} mined with hash {block['hash']}")
    print(f"Transactions: {len(block['transactions'])}")
    print(f"Timestamp: {block['timestamp']}")

    # Verify the chain length increased
    new_length = miner.chain.get_chain_length()
    print(f"New chain length: {new_length}")
    assert new_length == initial_length + 1, "Chain length did not increase"

    # Verify the miner was rewarded
    new_balance = miner.ledger.get_balance(identity_id, "ONX")
    print(f"New balance of {identity_id}: {new_balance} ONX")
    assert new_balance == initial_balance + 10, "Miner was not rewarded correctly"  # BLOCK_REWARD is 10

    # Verify the mempool was cleared
    assert miner.mempool.get_count() == 0, "Mempool was not cleared"

    print("Mining test passed!")

if __name__ == "__main__":
    test_mining()
