#!/usr/bin/env python3
"""
Test wallet functionality.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_wallet_creation():
    """Test wallet creation."""
    print("Testing wallet creation...")

    try:
        from blockchain.wallet.wallet import Wallet

        # Create a new wallet
        wallet = Wallet()

        print(f"✅ Wallet created successfully!")

        return True

    except Exception as e:
        print(f"❌ Wallet creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_key_generation():
    """Test key generation."""
    print("\nTesting key generation...")

    try:
        from blockchain.wallet.wallet import Wallet

        # Generate keys
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()

        print(f"✅ Keys generated successfully!")
        print(f"   Private Key: {private_key[:50]}...")
        print(f"   Public Key: {public_key[:50]}...")

        return True

    except Exception as e:
        print(f"❌ Key generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signing():
    """Test transaction signing."""
    print("\nTesting transaction signing...")

    try:
        from blockchain.wallet.wallet import Wallet

        wallet = Wallet()

        # Generate a keypair for testing
        private_key, public_key = wallet.generate_keypair()

        # Test data to sign
        test_data = "test transaction data"

        # Sign the data
        signature = wallet.sign_message(test_data, private_key)

        print(f"✅ Data signed successfully!")
        print(f"   Signature: {signature[:50]}...")

        # Verify the signature
        is_valid = wallet.verify_signature(test_data, signature, public_key)

        if is_valid:
            print(f"✅ Signature verification successful!")
        else:
            print(f"❌ Signature verification failed!")
            return False

        return True

    except Exception as e:
        print(f"❌ Signing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔧 Testing Wallet Functionality")
    print("=" * 40)

    success = True

    if not test_wallet_creation():
        success = False

    if not test_key_generation():
        success = False

    if not test_signing():
        success = False

    print("\n" + "=" * 40)
    if success:
        print("🎉 All wallet tests passed!")
    else:
        print("⚠️ Some wallet tests failed.")

    return success

if __name__ == "__main__":
    main()
