"""
Onnyx Voice Scroll Module

This module provides the VoiceScrolls class for managing governance proposals.
"""

import os
import json
import time
import uuid
import logging
from typing import Dict, Any, List, Optional

from shared.config.chain_parameters import ChainParameters, chain_parameters
from identity.registry import IdentityRegistry
from identity.trust.etzem_engine import EtzemEngine

# Set up logging
logger = logging.getLogger("onnyx.governance.voice_scroll")

class VoiceScrolls:
    """
    VoiceScrolls manages governance proposals (scrolls) for the Onnyx blockchain.
    """
    
    def __init__(self, scrolls_path: str = "data/scrolls.json",
                 identity_registry_path: str = "data/identities.json"):
        """
        Initialize the VoiceScrolls.
        
        Args:
            scrolls_path: Path to the scrolls JSON file
            identity_registry_path: Path to the identity registry JSON file
        """
        self.scrolls_path = scrolls_path
        self.identity_registry = IdentityRegistry(identity_registry_path)
        self.etzem_engine = EtzemEngine()
        
        # Initialize scrolls
        self.scrolls = {}
        
        # Load scrolls
        self._load_scrolls()
    
    def _load_scrolls(self):
        """Load the scrolls from the file."""
        try:
            if os.path.exists(self.scrolls_path) and os.path.getsize(self.scrolls_path) > 0:
                with open(self.scrolls_path, "r") as f:
                    self.scrolls = json.load(f)
                    logger.info(f"Loaded scrolls from {self.scrolls_path}")
            else:
                # Save the default scrolls
                self._save_scrolls()
                logger.info(f"Created default scrolls at {self.scrolls_path}")
        except Exception as e:
            logger.error(f"Error loading scrolls: {str(e)}")
    
    def _save_scrolls(self):
        """Save the scrolls to the file."""
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.scrolls_path), exist_ok=True)
            
            with open(self.scrolls_path, "w") as f:
                json.dump(self.scrolls, f, indent=2)
                logger.info(f"Saved scrolls to {self.scrolls_path}")
        except Exception as e:
            logger.error(f"Error saving scrolls: {str(e)}")
    
    def create_scroll(self, creator_id: str, title: str, description: str, category: str,
                     expiry_days: int = 7, effect: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a new scroll.
        
        Args:
            creator_id: The identity ID of the creator
            title: The title of the scroll
            description: The description of the scroll
            category: The category of the scroll
            expiry_days: The number of days until the scroll expires
            effect: The effect of the scroll (optional)
        
        Returns:
            The created scroll
        
        Raises:
            Exception: If the creator does not exist or does not have sufficient Etzem score
        """
        # Check if the creator exists
        creator = self.identity_registry.get_identity(creator_id)
        if not creator:
            raise Exception(f"Identity with ID '{creator_id}' not found")
        
        # Check if the creator has sufficient Etzem score
        etzem_data = self.etzem_engine.compute_etzem(creator_id)
        etzem_score = etzem_data.get("etzem", 0)
        
        min_etzem_score = chain_parameters.get("min_etzem_score")
        if etzem_score < min_etzem_score:
            raise Exception(f"Insufficient Etzem score: {etzem_score} < {min_etzem_score}")
        
        # Generate a unique scroll ID
        scroll_id = f"scroll_{uuid.uuid4().hex[:8]}_{int(time.time())}"
        
        # Create the scroll
        scroll = {
            "id": scroll_id,
            "creator": creator_id,
            "title": title,
            "description": description,
            "category": category,
            "created_at": int(time.time()),
            "expires_at": int(time.time()) + (expiry_days * 86400),  # Convert days to seconds
            "status": "open",
            "votes": {},
            "tally": {
                "yes": 0,
                "no": 0,
                "abstain": 0
            },
            "outcome": "pending"
        }
        
        # Add effect if provided
        if effect:
            scroll["effect"] = effect
        
        # Add the scroll to the scrolls
        self.scrolls[scroll_id] = scroll
        self._save_scrolls()
        
        logger.info(f"Created scroll: {scroll_id}")
        return scroll
    
    def vote_on_scroll(self, identity_id: str, scroll_id: str, decision: str) -> Dict[str, Any]:
        """
        Vote on a scroll.
        
        Args:
            identity_id: The identity ID of the voter
            scroll_id: The scroll ID
            decision: The decision ("yes", "no", or "abstain")
        
        Returns:
            The updated scroll
        
        Raises:
            Exception: If the identity does not exist, the scroll does not exist, or the scroll is not open
        """
        # Check if the identity exists
        identity = self.identity_registry.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the scroll exists
        if scroll_id not in self.scrolls:
            raise Exception(f"Scroll with ID '{scroll_id}' not found")
        
        scroll = self.scrolls[scroll_id]
        
        # Check if the scroll is open
        if scroll["status"] != "open":
            raise Exception(f"Scroll with ID '{scroll_id}' is not open")
        
        # Check if the scroll has expired
        if scroll["expires_at"] < int(time.time()):
            scroll["status"] = "closed"
            self._save_scrolls()
            raise Exception(f"Scroll with ID '{scroll_id}' has expired")
        
        # Calculate the voter's weight
        etzem_data = self.etzem_engine.compute_etzem(identity_id)
        etzem_score = etzem_data.get("etzem", 0)
        
        # Check if the voter is a council member
        is_council_member = "COUNCIL_MEMBER_BADGE" in identity.get("badges", [])
        
        # Council members get double voting power
        weight = etzem_score * 2 if is_council_member else etzem_score
        
        # Record the vote
        scroll["votes"][identity_id] = {
            "decision": decision,
            "weight": weight,
            "timestamp": int(time.time()),
            "is_council_member": is_council_member
        }
        
        # Update the tally
        self._update_tally(scroll)
        
        # Save the scrolls
        self._save_scrolls()
        
        logger.info(f"Recorded vote on scroll {scroll_id} by {identity_id}: {decision}")
        return scroll
    
    def _update_tally(self, scroll: Dict[str, Any]):
        """
        Update the tally for a scroll.
        
        Args:
            scroll: The scroll to update
        """
        # Reset the tally
        scroll["tally"] = {
            "yes": 0,
            "no": 0,
            "abstain": 0
        }
        
        # Calculate the tally
        for vote in scroll["votes"].values():
            decision = vote["decision"]
            weight = vote["weight"]
            
            if decision in scroll["tally"]:
                scroll["tally"][decision] += weight
    
    def tally_scroll(self, scroll_id: str) -> Dict[str, Any]:
        """
        Tally the votes for a scroll.
        
        Args:
            scroll_id: The scroll ID
        
        Returns:
            The tally results
        
        Raises:
            Exception: If the scroll does not exist
        """
        # Check if the scroll exists
        if scroll_id not in self.scrolls:
            raise Exception(f"Scroll with ID '{scroll_id}' not found")
        
        scroll = self.scrolls[scroll_id]
        
        # Update the tally
        self._update_tally(scroll)
        
        # Calculate the total weight
        total_weight = sum(scroll["tally"].values())
        
        # Calculate the quorum percentage
        quorum_percent = chain_parameters.get("quorum_percent")
        
        # Calculate the pass ratio
        pass_ratio = chain_parameters.get("vote_pass_ratio")
        
        # Calculate the yes ratio
        yes_ratio = scroll["tally"]["yes"] / total_weight if total_weight > 0 else 0
        
        # Determine the outcome
        if total_weight == 0:
            outcome = "no_votes"
        elif yes_ratio >= pass_ratio:
            outcome = "passed"
        else:
            outcome = "failed"
        
        # Update the scroll
        scroll["outcome"] = outcome
        self._save_scrolls()
        
        logger.info(f"Tallied scroll {scroll_id}: {outcome}")
        
        return {
            "scroll_id": scroll_id,
            "tally": scroll["tally"],
            "total_weight": total_weight,
            "quorum_percent": quorum_percent,
            "pass_ratio": pass_ratio,
            "yes_ratio": yes_ratio,
            "outcome": outcome
        }
    
    def resolve_scroll(self, scroll_id: str) -> Dict[str, Any]:
        """
        Resolve a scroll.
        
        Args:
            scroll_id: The scroll ID
        
        Returns:
            The resolved scroll
        
        Raises:
            Exception: If the scroll does not exist or is not closed
        """
        # Check if the scroll exists
        if scroll_id not in self.scrolls:
            raise Exception(f"Scroll with ID '{scroll_id}' not found")
        
        scroll = self.scrolls[scroll_id]
        
        # Check if the scroll is closed
        if scroll["status"] != "closed":
            # Check if the scroll has expired
            if scroll["expires_at"] < int(time.time()):
                scroll["status"] = "closed"
            else:
                raise Exception(f"Scroll with ID '{scroll_id}' is not closed")
        
        # Tally the scroll
        tally_result = self.tally_scroll(scroll_id)
        
        # Apply the scroll effect if it passed
        if tally_result["outcome"] == "passed" and "effect" in scroll:
            self._apply_scroll_effect(scroll)
        
        # Update the scroll
        scroll["status"] = "resolved"
        scroll["resolved_at"] = int(time.time())
        self._save_scrolls()
        
        logger.info(f"Resolved scroll {scroll_id}: {tally_result['outcome']}")
        
        return scroll
    
    def _apply_scroll_effect(self, scroll: Dict[str, Any]):
        """
        Apply the effect of a scroll.
        
        Args:
            scroll: The scroll to apply
        """
        if "effect" not in scroll:
            logger.warning(f"Scroll {scroll['id']} has no effect to apply")
            return
        
        effect = scroll["effect"]
        
        # Check if the effect is a chain parameter update
        if "param" in effect and "value" in effect:
            param = effect["param"]
            value = effect["value"]
            
            # Update the chain parameter
            chain_parameters.set(param, value)
            
            logger.info(f"Applied scroll effect: updated chain parameter {param} to {value}")
        else:
            logger.warning(f"Scroll {scroll['id']} has an unknown effect: {effect}")
    
    def get_scroll(self, scroll_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a scroll by ID.
        
        Args:
            scroll_id: The scroll ID
        
        Returns:
            The scroll or None if not found
        """
        return self.scrolls.get(scroll_id)
    
    def get_scrolls(self, status: Optional[str] = None, category: Optional[str] = None,
                   creator: Optional[str] = None, outcome: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get scrolls.
        
        Args:
            status: Filter by status (optional)
            category: Filter by category (optional)
            creator: Filter by creator (optional)
            outcome: Filter by outcome (optional)
        
        Returns:
            A list of scrolls
        """
        scrolls = list(self.scrolls.values())
        
        # Filter by status
        if status:
            scrolls = [s for s in scrolls if s["status"] == status]
        
        # Filter by category
        if category:
            scrolls = [s for s in scrolls if s["category"] == category]
        
        # Filter by creator
        if creator:
            scrolls = [s for s in scrolls if s["creator"] == creator]
        
        # Filter by outcome
        if outcome:
            scrolls = [s for s in scrolls if s["outcome"] == outcome]
        
        # Sort by created_at (newest first)
        scrolls = sorted(scrolls, key=lambda s: s["created_at"], reverse=True)
        
        return scrolls

# Create a global instance of the VoiceScrolls
voice_scrolls = VoiceScrolls()
