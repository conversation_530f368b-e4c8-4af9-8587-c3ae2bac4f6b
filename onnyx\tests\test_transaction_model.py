"""
Onnyx Transaction Model Tests

This module provides tests for the Onnyx transaction model.
"""

import os
import sys
import unittest
import time
import hashlib
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.transaction import Transaction
from models.identity import Identity
from data.db import db

class TestTransactionModel(unittest.TestCase):
    """
    Test the Transaction model.
    """
    
    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_transaction_model.db")
        
        # Set the database path
        db.db_path = self.db_path
        
        # Create the tables
        conn = sqlite3.connect(self.db_path)
        
        # Create the identities table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER,
            updated_at INTEGER
        )
        """)
        
        # Create the blocks table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS blocks (
            block_hash TEXT PRIMARY KEY,
            previous_hash TEXT NOT NULL,
            block_height INTEGER NOT NULL,
            timestamp INTEGER NOT NULL,
            difficulty INTEGER NOT NULL,
            nonce INTEGER NOT NULL,
            miner TEXT NOT NULL,
            transactions TEXT,
            merkle_root TEXT,
            size INTEGER,
            version TEXT,
            created_at INTEGER,
            FOREIGN KEY (miner) REFERENCES identities (identity_id)
        )
        """)
        
        # Create the transactions table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS transactions (
            tx_id TEXT PRIMARY KEY,
            block_hash TEXT,
            timestamp INTEGER NOT NULL,
            op TEXT NOT NULL,
            data TEXT NOT NULL,
            sender TEXT NOT NULL,
            signature TEXT,
            status TEXT NOT NULL,
            created_at INTEGER,
            FOREIGN KEY (sender) REFERENCES identities (identity_id),
            FOREIGN KEY (block_hash) REFERENCES blocks (block_hash)
        )
        """)
        
        conn.commit()
        conn.close()
        
        # Create test identities
        self.sender_id = "sender_identity"
        self.recipient_id = "recipient_identity"
        
        # Insert the identities into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.sender_id,
                "Sender Identity",
                "0x1234567890abcdef",
                json.dumps({"type": "individual", "description": "Sender identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.recipient_id,
                "Recipient Identity",
                "0x0987654321fedcba",
                json.dumps({"type": "individual", "description": "Recipient identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        
        # Create a test block
        self.block_hash = "test_block_hash"
        conn.execute(
            "INSERT INTO blocks (block_hash, previous_hash, block_height, timestamp, difficulty, nonce, miner, transactions, merkle_root, size, version, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                self.block_hash,
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                int(time.time()),
                1,
                0,
                self.sender_id,
                json.dumps([]),
                "0000000000000000000000000000000000000000000000000000000000000000",
                0,
                "1.0",
                int(time.time())
            )
        )
        
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")
        
        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")
    
    def test_create_transaction(self):
        """Test creating a transaction."""
        # Create a transaction directly in the database
        tx_id = "test_tx_id"
        timestamp = int(time.time())
        
        # Insert the transaction into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                tx_id,
                self.block_hash,
                timestamp,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 100,
                    "token_id": "onx",
                    "description": "Test transaction"
                }),
                self.sender_id,
                "test_signature",
                "CONFIRMED",
                timestamp
            )
        )
        conn.commit()
        conn.close()
        
        # Check if the transaction was saved to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM transactions WHERE tx_id = ?", (tx_id,))
        row = cursor.fetchone()
        conn.close()
        
        print(f"Database row: {row}")
        
        # Get the transaction by ID
        retrieved_tx = Transaction.get_by_id(tx_id)
        
        # Check that the transaction was retrieved
        self.assertIsNotNone(retrieved_tx)
        self.assertEqual(retrieved_tx.tx_id, tx_id)
        self.assertEqual(retrieved_tx.block_hash, self.block_hash)
        self.assertEqual(retrieved_tx.timestamp, timestamp)
        self.assertEqual(retrieved_tx.op, "OP_SEND")
        self.assertEqual(retrieved_tx.data["recipient"], self.recipient_id)
        self.assertEqual(retrieved_tx.data["amount"], 100)
        self.assertEqual(retrieved_tx.data["token_id"], "onx")
        self.assertEqual(retrieved_tx.data["description"], "Test transaction")
        self.assertEqual(retrieved_tx.sender, self.sender_id)
        self.assertEqual(retrieved_tx.signature, "test_signature")
        self.assertEqual(retrieved_tx.status, "CONFIRMED")
        self.assertEqual(retrieved_tx.created_at, timestamp)
    
    def test_get_transactions_by_sender(self):
        """Test getting transactions by sender."""
        # Create multiple transactions directly in the database
        timestamp = int(time.time())
        
        # Insert the transactions into the database
        conn = sqlite3.connect(self.db_path)
        
        # Insert a transaction from the sender
        conn.execute(
            "INSERT INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "tx_from_sender_1",
                self.block_hash,
                timestamp,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 100,
                    "token_id": "onx",
                    "description": "Transaction from sender 1"
                }),
                self.sender_id,
                "signature_1",
                "CONFIRMED",
                timestamp
            )
        )
        
        # Insert another transaction from the sender
        conn.execute(
            "INSERT INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "tx_from_sender_2",
                self.block_hash,
                timestamp + 60,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 200,
                    "token_id": "onx",
                    "description": "Transaction from sender 2"
                }),
                self.sender_id,
                "signature_2",
                "CONFIRMED",
                timestamp + 60
            )
        )
        
        # Insert a transaction from the recipient
        conn.execute(
            "INSERT INTO transactions (tx_id, block_hash, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "tx_from_recipient",
                self.block_hash,
                timestamp + 120,
                "OP_SEND",
                json.dumps({
                    "recipient": self.sender_id,
                    "amount": 50,
                    "token_id": "onx",
                    "description": "Transaction from recipient"
                }),
                self.recipient_id,
                "signature_3",
                "CONFIRMED",
                timestamp + 120
            )
        )
        
        conn.commit()
        conn.close()
        
        # Get transactions by sender
        sender_txs = Transaction.filter("sender = ?", (self.sender_id,))
        
        # Check that the transactions were retrieved
        self.assertEqual(len(sender_txs), 2)
        self.assertEqual(sender_txs[0].tx_id, "tx_from_sender_1")
        self.assertEqual(sender_txs[1].tx_id, "tx_from_sender_2")

if __name__ == "__main__":
    unittest.main()
