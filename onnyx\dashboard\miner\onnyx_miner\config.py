"""
Onnyx Miner Configuration Module

This module provides functions for managing configuration.
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional

# Set up logging
logger = logging.getLogger("onnyx_miner.config")

# Default configuration
DEFAULT_CONFIG = {
    "identity": {
        "id": "",
        "name": "",
        "keys_dir": "keys"
    },
    "sela": {
        "id": "",
        "name": "",
        "type": "BUSINESS"
    },
    "node": {
        "api_url": "http://localhost:8000",
        "p2p_port": 9000,
        "data_dir": "data",
        "auto_mine": False,
        "mine_interval": 60
    },
    "gui": {
        "enabled": True,
        "port": 5005,
        "host": "127.0.0.1"
    }
}

def get_config_path() -> str:
    """
    Get the path to the configuration file.
    
    Returns:
        The path to the configuration file
    """
    # Check if the ONNYX_CONFIG environment variable is set
    if "ONNYX_CONFIG" in os.environ:
        return os.environ["ONNYX_CONFIG"]
    
    # Check if the configuration file exists in the current directory
    if os.path.exists("config.yaml"):
        return "config.yaml"
    
    # Check if the configuration file exists in the user's home directory
    home_config = os.path.join(os.path.expanduser("~"), ".onnyx", "config.yaml")
    if os.path.exists(home_config):
        return home_config
    
    # Return the default path
    return "config.yaml"

def get_config() -> Dict[str, Any]:
    """
    Get the configuration.
    
    Returns:
        The configuration
    """
    try:
        # Get the path to the configuration file
        config_path = get_config_path()
        
        # Check if the configuration file exists
        if not os.path.exists(config_path):
            logger.warning(f"Configuration file not found: {config_path}")
            return DEFAULT_CONFIG.copy()
        
        # Read the configuration file
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        
        # Merge with the default configuration
        merged_config = DEFAULT_CONFIG.copy()
        merged_config.update(config)
        
        return merged_config
    except Exception as e:
        logger.error(f"Error loading configuration: {str(e)}")
        return DEFAULT_CONFIG.copy()

def save_config(config: Dict[str, Any], config_path: Optional[str] = None) -> bool:
    """
    Save the configuration.
    
    Args:
        config: The configuration to save
        config_path: The path to the configuration file (optional)
    
    Returns:
        True if the configuration was saved, False otherwise
    """
    try:
        # Get the path to the configuration file
        if config_path is None:
            config_path = get_config_path()
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(config_path)), exist_ok=True)
        
        # Write the configuration file
        with open(config_path, "w") as f:
            yaml.dump(config, f, default_flow_style=False)
        
        logger.info(f"Saved configuration to {config_path}")
        
        return True
    except Exception as e:
        logger.error(f"Error saving configuration: {str(e)}")
        return False

def create_default_config(identity_id: str, identity_name: str, sela_id: str, sela_name: str, config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Create a default configuration.
    
    Args:
        identity_id: The identity ID
        identity_name: The identity name
        sela_id: The Sela ID
        sela_name: The Sela name
        config_path: The path to the configuration file (optional)
    
    Returns:
        The created configuration
    """
    try:
        # Create the configuration
        config = DEFAULT_CONFIG.copy()
        config["identity"]["id"] = identity_id
        config["identity"]["name"] = identity_name
        config["sela"]["id"] = sela_id
        config["sela"]["name"] = sela_name
        
        # Save the configuration
        save_config(config, config_path)
        
        return config
    except Exception as e:
        logger.error(f"Error creating default configuration: {str(e)}")
        raise
