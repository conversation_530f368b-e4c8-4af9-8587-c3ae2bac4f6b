# Harashim Role System

The Harashim Role System represents identities who serve within or outside Selas — non-owner laborers, contract workers, or contributors. It is tied to <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and contract performance.

## Components

### HarashimRegistry

The `HarashimRegistry` class manages Harashim roles. It provides methods for:

- Registering an identity as a Harash
- Getting a Harash role for an identity
- Updating a Harash role
- Getting all Harashim affiliated with a Sela

### ServiceContract

The `ServiceContract` class manages service contracts between Selas and Harashim. It provides methods for:

- Creating a new service contract
- Getting a contract by ID
- Getting all contracts for a Harash
- Getting all contracts for a Sela
- Completing a contract

### HarashimPerformance

The `HarashimPerformance` class manages performance metrics and Etzem integration for Harashim. It provides methods for:

- Applying contract results to Etzem score and badges
- Getting performance metrics for a Harash

### ContractPayout

The `ContractPayout` class handles token payouts for completed contracts. It provides methods for:

- Processing a token payout for a completed contract
- Getting the payout for a contract
- Getting all payouts for a Harash

## API Endpoints

### Register a Harash

```
POST /harashim/register
```

Register an identity as a Harash.

### Create a Service Contract

```
POST /harashim/contract
```

Create a new service contract between a Sela and a Harash.

### Get Contracts

```
GET /harashim/contracts/{identity_id}
```

Get all contracts for an identity (Harash or Sela).

### Complete a Contract

```
PATCH /harashim/contract/{contract_id}/complete
```

Complete a service contract, apply Etzem boost, and process token payout.

### Get Performance Metrics

```
GET /harashim/performance/{identity_id}
```

Get performance metrics for a Harash.

### Get Contract

```
GET /harashim/contract/{contract_id}
```

Get a contract by ID.

### Get Sela Harashim

```
GET /harashim/sela/{sela_id}/harashim
```

Get all Harashim affiliated with a Sela.

## Integration with Other Systems

The Harashim Role System integrates with several other systems in the Onnyx blockchain:

### Zeman Time/Work Credit System

Completed contracts automatically credit Zeman hours to Harashim, which can be redeemed for various benefits.

### Etzem Trust Score System

Contract performance feeds into Etzem scores, which affects an identity's trustworthiness and reputation.

### Sela Business Registry

Service contracts are created between Selas and Harashim, allowing businesses to hire workers and track their performance.

### Token System

Completed contracts can result in token payouts to Harashim, providing a way to reward workers for their contributions.

## Example Identity with Harash Role

```json
{
  "identity_id": "abc123",
  "name": "Elijah Ben-Yosef",
  "role": "Harash",
  "nation": "Judah",
  "affiliated_sela": "Kingdom Builders",
  "active_contracts": 2,
  "completed_contracts": 14,
  "zeman_hours": 178,
  "etzem_score": 61
}
```

## Badges

The Harashim Role System awards badges based on contract performance:

- **HARASH**: Basic badge for all registered Harashim
- **HARASH_10_CONTRACTS**: Awarded for completing 10 contracts
- **HARASH_25_CONTRACTS**: Awarded for completing 25 contracts
- **HARASH_50_CONTRACTS**: Awarded for completing 50 contracts
- **HARASH_3_FIVE_STARS**: Awarded for receiving 3 five-star ratings
- **HARASH_10_FIVE_STARS**: Awarded for receiving 10 five-star ratings
