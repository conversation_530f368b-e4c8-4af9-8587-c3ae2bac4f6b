"""
Onnyx Miner Key Generation Module

This module provides functions for generating and managing cryptographic keys.
"""

import os
import logging
from typing import Dict, Any, Tuple
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives import hashes

# Set up logging
logger = logging.getLogger("onnyx_miner.keys")

def generate_identity_keys(identity_name: str, keys_dir: str = "keys") -> str:
    """
    Generate a new key pair for an identity.
    
    Args:
        identity_name: The name of the identity
        keys_dir: The directory to store the keys
    
    Returns:
        The public key in PEM format
    """
    try:
        # Generate a new private key
        private_key = ec.generate_private_key(ec.SECP256K1())
        
        # Get the public key
        public_key = private_key.public_key()
        
        # Serialize the keys to PEM format
        pem_priv = private_key.private_bytes(
            serialization.Encoding.PEM,
            serialization.PrivateFormat.PKCS8,
            serialization.NoEncryption()
        )
        
        pem_pub = public_key.public_bytes(
            serialization.Encoding.PEM,
            serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # Create the keys directory if it doesn't exist
        identity_dir = os.path.join(keys_dir, identity_name)
        os.makedirs(identity_dir, exist_ok=True)
        
        # Write the keys to files
        with open(os.path.join(identity_dir, "private.pem"), "wb") as f:
            f.write(pem_priv)
        
        with open(os.path.join(identity_dir, "public.pem"), "wb") as f:
            f.write(pem_pub)
        
        logger.info(f"Generated keys for identity: {identity_name}")
        
        return pem_pub.decode()
    except Exception as e:
        logger.error(f"Error generating keys: {str(e)}")
        raise

def load_identity_keys(identity_name: str, keys_dir: str = "keys") -> Tuple[bytes, bytes]:
    """
    Load the keys for an identity.
    
    Args:
        identity_name: The name of the identity
        keys_dir: The directory where the keys are stored
    
    Returns:
        A tuple of (private_key_pem, public_key_pem)
    
    Raises:
        FileNotFoundError: If the keys don't exist
    """
    try:
        # Get the paths to the key files
        identity_dir = os.path.join(keys_dir, identity_name)
        private_key_path = os.path.join(identity_dir, "private.pem")
        public_key_path = os.path.join(identity_dir, "public.pem")
        
        # Check if the keys exist
        if not os.path.exists(private_key_path) or not os.path.exists(public_key_path):
            raise FileNotFoundError(f"Keys for identity '{identity_name}' not found")
        
        # Read the keys
        with open(private_key_path, "rb") as f:
            private_key_pem = f.read()
        
        with open(public_key_path, "rb") as f:
            public_key_pem = f.read()
        
        return private_key_pem, public_key_pem
    except Exception as e:
        logger.error(f"Error loading keys: {str(e)}")
        raise

def sign_message(message: str, private_key_pem: bytes) -> str:
    """
    Sign a message with a private key.
    
    Args:
        message: The message to sign
        private_key_pem: The private key in PEM format
    
    Returns:
        The signature in hex format
    """
    try:
        # Load the private key
        private_key = serialization.load_pem_private_key(
            private_key_pem,
            password=None
        )
        
        # Sign the message
        signature = private_key.sign(
            message.encode(),
            ec.ECDSA(hashes.SHA256())
        )
        
        # Return the signature as a hex string
        return signature.hex()
    except Exception as e:
        logger.error(f"Error signing message: {str(e)}")
        raise

def verify_signature(message: str, signature: str, public_key_pem: bytes) -> bool:
    """
    Verify a signature with a public key.
    
    Args:
        message: The message that was signed
        signature: The signature in hex format
        public_key_pem: The public key in PEM format
    
    Returns:
        True if the signature is valid, False otherwise
    """
    try:
        # Load the public key
        public_key = serialization.load_pem_public_key(
            public_key_pem
        )
        
        # Convert the signature from hex to bytes
        signature_bytes = bytes.fromhex(signature)
        
        # Verify the signature
        try:
            public_key.verify(
                signature_bytes,
                message.encode(),
                ec.ECDSA(hashes.SHA256())
            )
            return True
        except Exception:
            return False
    except Exception as e:
        logger.error(f"Error verifying signature: {str(e)}")
        return False
