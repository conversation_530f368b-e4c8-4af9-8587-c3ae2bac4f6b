#!/usr/bin/env python3
"""
ONNYX Frontend Fixes Test Script

Tests all the critical fixes implemented:
1. Footer positioning issues
2. Visual accessibility improvements
3. Access portal implementation
4. Overall theme consistency
"""

import requests
import time
import sys
from bs4 import BeautifulSoup

def test_page_response(url, page_name):
    """Test if a page loads successfully."""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(f"✅ {page_name}: Page loads successfully")
            return True, response.text
        else:
            print(f"❌ {page_name}: HTTP {response.status_code}")
            return False, None
    except Exception as e:
        print(f"❌ {page_name}: Error - {e}")
        return False, None

def test_css_variables(base_url):
    """Test if the improved CSS variables are present."""
    try:
        css_response = requests.get(f"{base_url}/static/css/main.css", timeout=10)
        if css_response.status_code == 200:
            css_content = css_response.text
            if '--onyx-black: #1a1a1a' in css_content:
                print("✅ CSS Variables: Improved brightness colors detected")
                return True
            else:
                print("❌ CSS Variables: Old dark colors still present")
                return False
        else:
            print("❌ CSS Variables: Could not load CSS file")
            return False
    except Exception as e:
        print(f"❌ CSS Variables: Error loading CSS - {e}")
        return False

def test_footer_structure(html_content, page_name):
    """Test if footer has proper structure for positioning."""
    soup = BeautifulSoup(html_content, 'html.parser')

    # Check for proper layout structure
    main_content = soup.find('main')
    footer = soup.find('footer')

    if main_content and footer:
        print(f"✅ {page_name}: Proper main/footer structure found")

        # Check for flex layout classes
        body_classes = soup.find('body').get('class', [])
        if any('flex' in str(cls) for cls in body_classes):
            print(f"✅ {page_name}: Flex layout classes detected")

        return True
    else:
        print(f"❌ {page_name}: Missing main or footer elements")
        return False

def test_content_spacing(html_content, page_name):
    """Test if content has proper spacing classes."""
    if 'page-content' in html_content or 'explorer-content' in html_content or 'directory-content' in html_content:
        print(f"✅ {page_name}: Proper content spacing classes found")
        return True
    else:
        print(f"❌ {page_name}: Missing content spacing classes")
        return False

def test_access_portal_features(html_content):
    """Test if access portal has all required features."""
    soup = BeautifulSoup(html_content, 'html.parser')

    features = {
        'Email input': soup.find('input', {'type': 'email'}),
        'Security notice': soup.find('div', class_='glass-card'),
        'Submit button': soup.find('button', {'type': 'submit'}),
        'Registration link': soup.find('a', href=lambda x: x and 'register' in x),
        'Form validation script': soup.find('script')
    }

    passed = 0
    for feature, element in features.items():
        if element:
            print(f"✅ Access Portal: {feature} present")
            passed += 1
        else:
            print(f"❌ Access Portal: {feature} missing")

    return passed == len(features)

def main():
    """Run all frontend tests."""
    print("🚀 ONNYX Frontend Fixes Test Suite")
    print("=" * 50)

    base_url = "http://127.0.0.1:5000"

    # Test pages
    pages = [
        (f"{base_url}/", "Home Page"),
        (f"{base_url}/explorer", "Explorer Page"),
        (f"{base_url}/sela", "Validator Directory"),
        (f"{base_url}/auth/login", "Access Portal"),
        (f"{base_url}/register", "Registration Choice")
    ]

    results = {}

    print("\n📋 Testing Page Responses...")
    print("-" * 30)

    for url, name in pages:
        success, content = test_page_response(url, name)
        results[name] = {'loads': success, 'content': content}
        time.sleep(0.5)  # Be nice to the server

    print("\n🎨 Testing CSS Improvements...")
    print("-" * 30)

    # Test CSS variables directly from CSS file
    css_ok = test_css_variables(base_url)

    # Add CSS result to first loaded page for reporting
    for name, data in results.items():
        if data['loads']:
            results[name]['css'] = css_ok
            break

    print("\n🦶 Testing Footer Structure...")
    print("-" * 30)

    for name, data in results.items():
        if data['loads'] and data['content']:
            footer_ok = test_footer_structure(data['content'], name)
            spacing_ok = test_content_spacing(data['content'], name)
            results[name]['footer'] = footer_ok
            results[name]['spacing'] = spacing_ok

    print("\n🔐 Testing Access Portal Features...")
    print("-" * 30)

    if results.get('Access Portal', {}).get('loads'):
        portal_ok = test_access_portal_features(results['Access Portal']['content'])
        results['Access Portal']['features'] = portal_ok

    print("\n📊 Test Summary")
    print("=" * 50)

    total_tests = 0
    passed_tests = 0

    for name, data in results.items():
        print(f"\n{name}:")
        for test_type, result in data.items():
            if test_type != 'content':
                total_tests += 1
                if result:
                    passed_tests += 1
                    print(f"  ✅ {test_type.title()}")
                else:
                    print(f"  ❌ {test_type.title()}")

    print(f"\n🎯 Overall Results: {passed_tests}/{total_tests} tests passed")

    if passed_tests == total_tests:
        print("🎉 All tests passed! Frontend fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
