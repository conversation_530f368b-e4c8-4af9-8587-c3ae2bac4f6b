# src/zeman/redeemer.py

from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.zeman.ledger import ZemanLedger
from src.identity.registry import IdentityRegistry
from src.tokens.ledger import TokenLedger

class ZemanRedeemer:
    """
    Redeemer for converting Zeman time credits into rewards.
    """

    def __init__(self,
                 zeman_ledger: Optional[ZemanLedger] = None,
                 identity_registry: Optional[IdentityRegistry] = None,
                 token_ledger: Optional[TokenLedger] = None):
        """
        Initialize the ZemanRedeemer.

        Args:
            zeman_ledger: The Zeman ledger
            identity_registry: The identity registry
            token_ledger: The token ledger
        """
        self.zeman = zeman_ledger or ZemanLedger()
        self.identities = identity_registry or IdentityRegistry()
        self.token_ledger = token_ledger or TokenLedger()
        self.db = db_manager.get_connection()

        # Define redemption rates
        self.redemption_rates = {
            "minting": 10,  # 10 Zeman hours = 1,000 tokens
            "fee_discount": 100,  # 100 Zeman hours = 25% fee discount
            "reputation": 50,  # 50 Zeman hours = +10 reputation points
            "token": 5  # 5 Zeman hours = 1 ONX token
        }

    def redeem_for_minting_power(self, identity_id: str, hours: float) -> Dict[str, Any]:
        """
        Redeem Zeman hours for token minting power.

        Args:
            identity_id: The identity ID
            hours: The number of hours to redeem

        Returns:
            The redemption result

        Raises:
            Exception: If the identity does not exist or has insufficient hours
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the identity has enough hours
        available_hours = self.zeman.get_total_hours(identity_id)
        if available_hours < hours:
            raise Exception(f"Insufficient Zeman hours: {available_hours} available, {hours} requested")

        # Calculate the minting power
        minting_power = hours * (1000 / self.redemption_rates["minting"])

        # Redeem the hours
        entries = self.zeman.get_entries(identity_id)
        remaining_hours = hours
        redeemed_entries = []

        for entry in entries:
            if remaining_hours <= 0:
                break

            entry_hours = min(entry["hours"], remaining_hours)
            remaining_hours -= entry_hours

            if entry_hours == entry["hours"]:
                # Redeem the entire entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "minting")
                redeemed_entries.append(entry)
            else:
                # Split the entry
                new_entry = self.zeman.add_hours(
                    identity_id,
                    entry["hours"] - entry_hours,
                    entry["description"],
                    entry["source"]
                )

                # Redeem the original entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "minting")
                redeemed_entries.append(entry)

        # Return the result
        return {
            "identity_id": identity_id,
            "hours_redeemed": hours,
            "minting_power": minting_power,
            "redeemed_entries": redeemed_entries
        }

    def redeem_for_fee_discount(self, identity_id: str, hours: float) -> Dict[str, Any]:
        """
        Redeem Zeman hours for fee discounts.

        Args:
            identity_id: The identity ID
            hours: The number of hours to redeem

        Returns:
            The redemption result

        Raises:
            Exception: If the identity does not exist or has insufficient hours
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the identity has enough hours
        available_hours = self.zeman.get_total_hours(identity_id)
        if available_hours < hours:
            raise Exception(f"Insufficient Zeman hours: {available_hours} available, {hours} requested")

        # Calculate the fee discount
        discount_percentage = hours * (25 / self.redemption_rates["fee_discount"])

        # Cap the discount at 75%
        discount_percentage = min(discount_percentage, 75)

        # Redeem the hours
        entries = self.zeman.get_entries(identity_id)
        remaining_hours = hours
        redeemed_entries = []

        for entry in entries:
            if remaining_hours <= 0:
                break

            entry_hours = min(entry["hours"], remaining_hours)
            remaining_hours -= entry_hours

            if entry_hours == entry["hours"]:
                # Redeem the entire entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "fee_discount")
                redeemed_entries.append(entry)
            else:
                # Split the entry
                new_entry = self.zeman.add_hours(
                    identity_id,
                    entry["hours"] - entry_hours,
                    entry["description"],
                    entry["source"]
                )

                # Redeem the original entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "fee_discount")
                redeemed_entries.append(entry)

        # Return the result
        return {
            "identity_id": identity_id,
            "hours_redeemed": hours,
            "discount_percentage": discount_percentage,
            "redeemed_entries": redeemed_entries
        }

    def redeem_for_reputation(self, identity_id: str, hours: float) -> Dict[str, Any]:
        """
        Redeem Zeman hours for reputation boosts.

        Args:
            identity_id: The identity ID
            hours: The number of hours to redeem

        Returns:
            The redemption result

        Raises:
            Exception: If the identity does not exist or has insufficient hours
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the identity has enough hours
        available_hours = self.zeman.get_total_hours(identity_id)
        if available_hours < hours:
            raise Exception(f"Insufficient Zeman hours: {available_hours} available, {hours} requested")

        # Calculate the reputation boost
        reputation_boost = hours * (10 / self.redemption_rates["reputation"])

        # Redeem the hours
        entries = self.zeman.get_entries(identity_id)
        remaining_hours = hours
        redeemed_entries = []

        for entry in entries:
            if remaining_hours <= 0:
                break

            entry_hours = min(entry["hours"], remaining_hours)
            remaining_hours -= entry_hours

            if entry_hours == entry["hours"]:
                # Redeem the entire entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "reputation")
                redeemed_entries.append(entry)
            else:
                # Split the entry
                new_entry = self.zeman.add_hours(
                    identity_id,
                    entry["hours"] - entry_hours,
                    entry["description"],
                    entry["source"]
                )

                # Redeem the original entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "reputation")
                redeemed_entries.append(entry)

        # Update the identity's reputation
        # This is a placeholder - the actual implementation would depend on the identity system
        # self.identities.update_reputation(identity_id, reputation_boost)

        # Return the result
        return {
            "identity_id": identity_id,
            "hours_redeemed": hours,
            "reputation_boost": reputation_boost,
            "redeemed_entries": redeemed_entries
        }

    def redeem_for_tokens(self, identity_id: str, hours: float) -> Dict[str, Any]:
        """
        Redeem Zeman hours for ONX tokens.

        Args:
            identity_id: The identity ID
            hours: The number of hours to redeem

        Returns:
            The redemption result

        Raises:
            Exception: If the identity does not exist or has insufficient hours
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")

        # Check if the identity has enough hours
        available_hours = self.zeman.get_total_hours(identity_id)
        if available_hours < hours:
            raise Exception(f"Insufficient Zeman hours: {available_hours} available, {hours} requested")

        # Calculate the token amount
        token_amount = hours * (1 / self.redemption_rates["token"])

        # Redeem the hours
        entries = self.zeman.get_entries(identity_id)
        remaining_hours = hours
        redeemed_entries = []

        for entry in entries:
            if remaining_hours <= 0:
                break

            entry_hours = min(entry["hours"], remaining_hours)
            remaining_hours -= entry_hours

            if entry_hours == entry["hours"]:
                # Redeem the entire entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "token")
                redeemed_entries.append(entry)
            else:
                # Split the entry
                new_entry = self.zeman.add_hours(
                    identity_id,
                    entry["hours"] - entry_hours,
                    entry["description"],
                    entry["source"]
                )

                # Redeem the original entry
                self.zeman.mark_as_redeemed(entry["entry_id"], "token")
                redeemed_entries.append(entry)

        # Credit the tokens to the identity
        self.token_ledger.credit(identity_id, "ONX", token_amount)

        # Return the result
        return {
            "identity_id": identity_id,
            "hours_redeemed": hours,
            "token_amount": token_amount,
            "token_id": "ONX",
            "redeemed_entries": redeemed_entries
        }
