{"alice_salon": {"sela_id": "alice_salon", "name": "Alice's Beauty Salon", "founder": "alice_salon", "type": "BEAUTY", "token_id": "alice_salon_TOKEN", "created_at": 1748892499, "members": ["alice_salon"], "roles": {"alice_salon": "FOUNDER"}, "services_offered": ["Service 1", "Service 2", "Service 3"], "is_validator": true, "validator_since": 1748892499, "last_block_proposed": 0}, "bob_barber": {"sela_id": "bob_barber", "name": "Bob's Barbershop", "founder": "bob_barber", "type": "BARBER", "token_id": "bob_barber_TOKEN", "created_at": 1748892499, "members": ["bob_barber"], "roles": {"bob_barber": "FOUNDER"}, "services_offered": ["Service 1", "Service 2", "Service 3"], "is_validator": true, "validator_since": 1748892499, "last_block_proposed": 0}, "charlie_cafe": {"sela_id": "charlie_cafe", "name": "Charlie's Cafe", "founder": "charlie_cafe", "type": "CAFE", "token_id": "charlie_cafe_TOKEN", "created_at": 1748892499, "members": ["charlie_cafe"], "roles": {"charlie_cafe": "FOUNDER"}, "services_offered": ["Service 1", "Service 2", "Service 3"], "is_validator": true, "validator_since": 1748892499, "last_block_proposed": 0}}