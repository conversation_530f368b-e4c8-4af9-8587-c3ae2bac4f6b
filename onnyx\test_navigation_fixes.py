#!/usr/bin/env python3
"""
ONNYX Navigation Fixes Test Script

Tests both logged-in and logged-out navigation states to verify:
1. Main navigation bar is visible in both states
2. User dropdown functionality works properly when logged in
3. Proper responsive behavior on mobile and desktop
4. CSS styling and theme consistency
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class NavigationTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.driver = None
        self.results = []
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options."""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            print(f"❌ Failed to setup WebDriver: {e}")
            return False
    
    def test_server_running(self):
        """Test if the Flask server is running."""
        try:
            response = requests.get(self.base_url, timeout=5)
            if response.status_code == 200:
                self.results.append("✅ Flask server is running and accessible")
                return True
            else:
                self.results.append(f"❌ Server returned status code: {response.status_code}")
                return False
        except Exception as e:
            self.results.append(f"❌ Cannot connect to server: {e}")
            return False
    
    def test_logged_out_navigation(self):
        """Test navigation visibility when logged out."""
        try:
            self.driver.get(self.base_url)
            time.sleep(2)  # Allow page to load
            
            # Check if main navigation is visible
            nav_links = self.driver.find_elements(By.CSS_SELECTOR, ".nav-link")
            if len(nav_links) >= 3:  # Home, Validators, Explorer
                self.results.append("✅ Main navigation links are visible when logged out")
                
                # Check specific links
                expected_links = ["Home", "Validators", "Explorer"]
                found_links = [link.text.strip() for link in nav_links]
                
                for expected in expected_links:
                    if expected in found_links:
                        self.results.append(f"✅ '{expected}' link found in navigation")
                    else:
                        self.results.append(f"❌ '{expected}' link missing from navigation")
            else:
                self.results.append(f"❌ Expected at least 3 nav links, found {len(nav_links)}")
            
            # Check guest user menu (Access Portal, Verify Identity)
            guest_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), 'Access Portal') or contains(text(), 'Verify Identity')]")
            if len(guest_buttons) >= 2:
                self.results.append("✅ Guest user buttons (Access Portal, Verify Identity) are visible")
            else:
                self.results.append(f"❌ Expected 2 guest buttons, found {len(guest_buttons)}")
                
        except Exception as e:
            self.results.append(f"❌ Error testing logged-out navigation: {e}")
    
    def test_navigation_styling(self):
        """Test navigation styling and theme consistency."""
        try:
            # Check if navigation has proper styling
            nav_element = self.driver.find_element(By.TAG_NAME, "nav")
            nav_classes = nav_element.get_attribute("class")
            
            if "glass-nav" in nav_classes:
                self.results.append("✅ Navigation has proper glass-nav styling")
            else:
                self.results.append("❌ Navigation missing glass-nav styling")
            
            # Check if nav links have proper styling
            nav_links = self.driver.find_elements(By.CSS_SELECTOR, ".nav-link")
            if nav_links:
                first_link = nav_links[0]
                link_color = first_link.value_of_css_property("color")
                if link_color:
                    self.results.append("✅ Navigation links have color styling applied")
                else:
                    self.results.append("❌ Navigation links missing color styling")
            
        except Exception as e:
            self.results.append(f"❌ Error testing navigation styling: {e}")
    
    def test_responsive_behavior(self):
        """Test responsive navigation behavior."""
        try:
            # Test desktop view
            self.driver.set_window_size(1920, 1080)
            time.sleep(1)
            
            desktop_nav = self.driver.find_elements(By.CSS_SELECTOR, ".hidden.md\\:ml-12.md\\:flex.md\\:space-x-1")
            if desktop_nav and desktop_nav[0].is_displayed():
                self.results.append("✅ Desktop navigation is visible on large screens")
            else:
                self.results.append("❌ Desktop navigation not visible on large screens")
            
            # Test mobile view
            self.driver.set_window_size(375, 667)
            time.sleep(1)
            
            mobile_toggle = self.driver.find_elements(By.CSS_SELECTOR, ".mobile-menu-toggle")
            if mobile_toggle:
                self.results.append("✅ Mobile menu toggle is present")
            else:
                self.results.append("❌ Mobile menu toggle not found")
                
        except Exception as e:
            self.results.append(f"❌ Error testing responsive behavior: {e}")
    
    def test_dropdown_elements(self):
        """Test dropdown menu elements (even if not logged in)."""
        try:
            # Check if dropdown elements exist in DOM (they should be hidden when logged out)
            dropdown_button = self.driver.find_elements(By.ID, "user-dropdown-button")
            dropdown_menu = self.driver.find_elements(By.ID, "user-dropdown-menu")
            
            if not dropdown_button and not dropdown_menu:
                self.results.append("✅ User dropdown elements properly hidden when logged out")
            else:
                self.results.append("❌ User dropdown elements visible when should be hidden")
                
        except Exception as e:
            self.results.append(f"❌ Error testing dropdown elements: {e}")
    
    def test_javascript_functionality(self):
        """Test JavaScript functionality and Alpine.js loading."""
        try:
            # Check if Alpine.js is loaded
            alpine_loaded = self.driver.execute_script("return typeof Alpine !== 'undefined'")
            if alpine_loaded:
                self.results.append("✅ Alpine.js is loaded successfully")
            else:
                self.results.append("⚠️ Alpine.js not detected (fallback JavaScript should work)")
            
            # Check if main.js is loaded
            main_js_loaded = self.driver.execute_script("return typeof Onnyx !== 'undefined'")
            if main_js_loaded:
                self.results.append("✅ Main.js (Onnyx object) is loaded successfully")
            else:
                self.results.append("❌ Main.js not loaded properly")
                
        except Exception as e:
            self.results.append(f"❌ Error testing JavaScript functionality: {e}")
    
    def run_all_tests(self):
        """Run all navigation tests."""
        print("🚀 Starting ONNYX Navigation Fixes Test Suite")
        print("=" * 60)
        
        # Test server
        if not self.test_server_running():
            print("❌ Server not running. Please start the Flask app first.")
            return
        
        # Setup WebDriver
        if not self.setup_driver():
            print("❌ WebDriver setup failed. Please install ChromeDriver.")
            return
        
        try:
            # Run all tests
            self.test_logged_out_navigation()
            self.test_navigation_styling()
            self.test_responsive_behavior()
            self.test_dropdown_elements()
            self.test_javascript_functionality()
            
        finally:
            if self.driver:
                self.driver.quit()
        
        # Print results
        print("\n📊 Test Results:")
        print("=" * 60)
        for result in self.results:
            print(result)
        
        # Summary
        passed = len([r for r in self.results if r.startswith("✅")])
        warnings = len([r for r in self.results if r.startswith("⚠️")])
        failed = len([r for r in self.results if r.startswith("❌")])
        
        print(f"\n📈 Summary: {passed} passed, {warnings} warnings, {failed} failed")
        
        if failed == 0:
            print("🎉 All critical tests passed! Navigation fixes are working.")
        else:
            print("⚠️ Some tests failed. Please review the issues above.")

if __name__ == "__main__":
    tester = NavigationTester()
    tester.run_all_tests()
