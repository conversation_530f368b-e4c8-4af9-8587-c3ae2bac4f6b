# src/node/peer.py

import asyncio
import json
import time
import logging
from typing import Dict, List, Set, Any, Optional, Callable
import websockets
from websockets.exceptions import ConnectionClosed

from src.node.config import node_config, NODE_ID
from src.node.crypto import key_manager

# Configure logging
logger = logging.getLogger("onnyx.node.peer")

class Peer:
    """
    Represents a peer node in the Onnyx network.
    """

    def __init__(self, url: str, websocket=None, node_id: Optional[str] = None):
        """
        Initialize a peer.

        Args:
            url: The URL of the peer.
            websocket: The WebSocket connection to the peer.
            node_id: The ID of the peer node.
        """
        self.url = url
        self.websocket = websocket
        self.node_id = node_id
        self.connected = websocket is not None
        self.last_seen = time.time() if self.connected else 0
        self.last_message = 0
        self.message_count = 0
        self.error_count = 0
        self.latency = 0
        self.metadata = {}

    def __str__(self):
        return f"Peer(url={self.url}, node_id={self.node_id}, connected={self.connected})"

    def __repr__(self):
        return self.__str__()

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the peer to a dictionary.

        Returns:
            A dictionary representation of the peer.
        """
        return {
            "url": self.url,
            "node_id": self.node_id,
            "connected": self.connected,
            "last_seen": self.last_seen,
            "last_message": self.last_message,
            "message_count": self.message_count,
            "error_count": self.error_count,
            "latency": self.latency,
            "metadata": self.metadata
        }

    def update_stats(self, message_received: bool = False, error: bool = False):
        """
        Update the peer statistics.

        Args:
            message_received: Whether a message was received from the peer.
            error: Whether an error occurred with the peer.
        """
        self.last_seen = time.time()
        if message_received:
            self.last_message = time.time()
            self.message_count += 1
        if error:
            self.error_count += 1


class PeerManager:
    """
    Manages peer connections in the Onnyx network.
    """

    def __init__(self):
        """
        Initialize the peer manager.
        """
        self.peers: Dict[str, Peer] = {}
        self.connected_peers: Dict[str, Peer] = {}
        self.message_handlers: Dict[str, List[Callable]] = {}
        self.running = False
        self.tasks = []

    async def start(self):
        """
        Start the peer manager.
        """
        self.running = True

        # Initialize peers from configuration
        for peer_url in node_config.get_peers():
            self.add_peer(peer_url)

        # Start background tasks
        self.tasks = [
            asyncio.create_task(self._connect_to_peers()),
            asyncio.create_task(self._heartbeat()),
            asyncio.create_task(self._peer_discovery())
        ]

        logger.info(f"Peer manager started with {len(self.peers)} peers")

    async def stop(self):
        """
        Stop the peer manager.
        """
        self.running = False

        # Cancel all tasks
        for task in self.tasks:
            task.cancel()

        # Close all connections
        for peer in list(self.connected_peers.values()):
            await self._disconnect_peer(peer)

        logger.info("Peer manager stopped")

    def add_peer(self, url: str) -> Peer:
        """
        Add a peer to the peer manager.

        Args:
            url: The URL of the peer to add.

        Returns:
            The added peer.
        """
        if url not in self.peers:
            peer = Peer(url)
            self.peers[url] = peer
            node_config.add_peer(url)
            logger.info(f"Added peer: {url}")
            return peer
        return self.peers[url]

    def remove_peer(self, url: str):
        """
        Remove a peer from the peer manager.

        Args:
            url: The URL of the peer to remove.
        """
        if url in self.peers:
            peer = self.peers.pop(url)
            if url in self.connected_peers:
                del self.connected_peers[url]
            node_config.remove_peer(url)
            logger.info(f"Removed peer: {url}")

    def get_peer(self, url: str) -> Optional[Peer]:
        """
        Get a peer by URL.

        Args:
            url: The URL of the peer to get.

        Returns:
            The peer, or None if not found.
        """
        return self.peers.get(url)

    def get_peers(self) -> List[Peer]:
        """
        Get all peers.

        Returns:
            A list of all peers.
        """
        return list(self.peers.values())

    def get_connected_peers(self) -> List[Peer]:
        """
        Get all connected peers.

        Returns:
            A list of all connected peers.
        """
        return list(self.connected_peers.values())

    def register_message_handler(self, message_type: str, handler: Callable):
        """
        Register a message handler.

        Args:
            message_type: The type of message to handle.
            handler: The handler function.
        """
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
        logger.debug(f"Registered message handler for {message_type}")

    async def broadcast(self, message: Dict[str, Any]):
        """
        Broadcast a message to all connected peers.

        Args:
            message: The message to broadcast.
        """
        # Add the sender ID if not already present
        if "from" not in message:
            message["from"] = NODE_ID

        # Add a timestamp if not already present
        if "timestamp" not in message:
            message["timestamp"] = time.time()

        # Sign the message
        try:
            signature = key_manager.sign_message(message)
            message_with_signature = message.copy()
            message_with_signature["signature"] = signature
            message_json = json.dumps(message_with_signature)

            logger.debug(f"Signed message of type {message['type']}")
        except Exception as e:
            logger.error(f"Error signing message: {str(e)}")
            message_json = json.dumps(message)

        # Send the message to all connected peers
        for peer in self.connected_peers.values():
            try:
                await peer.websocket.send(message_json)
                logger.debug(f"Broadcast message to {peer.url}: {message['type']}")
            except Exception as e:
                logger.error(f"Error broadcasting message to {peer.url}: {str(e)}")
                peer.update_stats(error=True)

    async def send_to_peer(self, peer_url: str, message: Dict[str, Any]) -> bool:
        """
        Send a message to a specific peer.

        Args:
            peer_url: The URL of the peer to send the message to.
            message: The message to send.

        Returns:
            True if the message was sent successfully, False otherwise.
        """
        if peer_url in self.connected_peers:
            peer = self.connected_peers[peer_url]

            # Add the sender ID if not already present
            if "from" not in message:
                message["from"] = NODE_ID

            # Add a timestamp if not already present
            if "timestamp" not in message:
                message["timestamp"] = time.time()

            # Sign the message
            try:
                signature = key_manager.sign_message(message)
                message_with_signature = message.copy()
                message_with_signature["signature"] = signature
                message_json = json.dumps(message_with_signature)

                logger.debug(f"Signed message of type {message['type']}")
            except Exception as e:
                logger.error(f"Error signing message: {str(e)}")
                message_json = json.dumps(message)

            try:
                await peer.websocket.send(message_json)
                logger.debug(f"Sent message to {peer_url}: {message['type']}")
                return True
            except Exception as e:
                logger.error(f"Error sending message to {peer_url}: {str(e)}")
                peer.update_stats(error=True)
        else:
            logger.warning(f"Cannot send message to disconnected peer: {peer_url}")
        return False

    async def _connect_to_peers(self):
        """
        Connect to all peers.
        """
        while self.running:
            for url, peer in list(self.peers.items()):
                if not peer.connected and len(self.connected_peers) < node_config.get("max_peers"):
                    try:
                        await self._connect_peer(peer)
                    except Exception as e:
                        logger.error(f"Error connecting to peer {url}: {str(e)}")
                        peer.update_stats(error=True)

            # Wait before trying again
            await asyncio.sleep(node_config.get("sync_interval"))

    async def _connect_peer(self, peer: Peer):
        """
        Connect to a peer.

        Args:
            peer: The peer to connect to.
        """
        if peer.connected:
            return

        try:
            # Connect to the peer
            websocket = await asyncio.wait_for(
                websockets.connect(peer.url),
                timeout=node_config.get("connection_timeout")
            )

            # Update peer information
            peer.websocket = websocket
            peer.connected = True
            peer.update_stats()
            self.connected_peers[peer.url] = peer

            # Send hello message with public key
            hello_message = {
                "type": "hello",
                "from": NODE_ID,
                "timestamp": time.time(),
                "public_key": key_manager.get_public_key_base64()
            }
            await websocket.send(json.dumps(hello_message))

            # Start listening for messages
            asyncio.create_task(self._listen_to_peer(peer))

            logger.info(f"Connected to peer: {peer.url}")
        except Exception as e:
            logger.error(f"Error connecting to peer {peer.url}: {str(e)}")
            peer.update_stats(error=True)
            await self._disconnect_peer(peer)

    async def _disconnect_peer(self, peer: Peer):
        """
        Disconnect from a peer.

        Args:
            peer: The peer to disconnect from.
        """
        if not peer.connected:
            return

        try:
            # Close the WebSocket connection
            if peer.websocket:
                await peer.websocket.close()

            # Update peer information
            peer.websocket = None
            peer.connected = False

            # Remove from connected peers
            if peer.url in self.connected_peers:
                del self.connected_peers[peer.url]

            logger.info(f"Disconnected from peer: {peer.url}")
        except Exception as e:
            logger.error(f"Error disconnecting from peer {peer.url}: {str(e)}")

    async def _listen_to_peer(self, peer: Peer):
        """
        Listen for messages from a peer.

        Args:
            peer: The peer to listen to.
        """
        try:
            async for message in peer.websocket:
                try:
                    # Parse the message
                    data = json.loads(message)

                    # Update peer statistics
                    peer.update_stats(message_received=True)

                    # Check if the message has a signature
                    if "signature" in data:
                        # Extract the signature and payload
                        signature = data.pop("signature")
                        sender = data.get("from")

                        # Verify the signature if we have the sender's public key
                        if sender and sender != NODE_ID:
                            try:
                                # Create a copy of the data for verification
                                verification_data = data.copy()

                                # Verify the signature
                                if not key_manager.verify_message(verification_data, signature, sender):
                                    logger.warning(f"Invalid signature from {sender} at {peer.url}")
                                    peer.update_stats(error=True)
                                    continue

                                logger.debug(f"Verified signature from {sender} at {peer.url}")
                            except ValueError as e:
                                # If we don't have the public key, log a warning but still process the message
                                logger.warning(f"Could not verify signature from {sender} at {peer.url}: {str(e)}")

                    # Process the message
                    await self._process_message(peer, data)
                except json.JSONDecodeError:
                    logger.error(f"Received invalid JSON from {peer.url}")
                    peer.update_stats(error=True)
                except Exception as e:
                    logger.error(f"Error processing message from {peer.url}: {str(e)}")
                    peer.update_stats(error=True)
        except ConnectionClosed:
            logger.info(f"Connection closed by peer: {peer.url}")
        except Exception as e:
            logger.error(f"Error listening to peer {peer.url}: {str(e)}")
            peer.update_stats(error=True)
        finally:
            await self._disconnect_peer(peer)

    async def _process_message(self, peer: Peer, message: Dict[str, Any]):
        """
        Process a message from a peer.

        Args:
            peer: The peer that sent the message.
            message: The message to process.
        """
        message_type = message.get("type")
        if not message_type:
            logger.warning(f"Received message without type from {peer.url}")
            return

        # Handle hello message
        if message_type == "hello":
            peer.node_id = message.get("from")

            # Store the peer's public key if provided
            public_key_base64 = message.get("public_key")
            if public_key_base64:
                try:
                    from src.node.crypto import public_key_from_base64
                    public_key = public_key_from_base64(public_key_base64)
                    key_manager.add_peer_key(peer.node_id, public_key)
                    logger.info(f"Stored public key for peer {peer.node_id}")
                except Exception as e:
                    logger.error(f"Error storing public key for peer {peer.node_id}: {str(e)}")

            logger.info(f"Received hello from {peer.node_id} at {peer.url}")
            return

        # Call registered handlers
        if message_type in self.message_handlers:
            for handler in self.message_handlers[message_type]:
                try:
                    await handler(peer, message)
                except Exception as e:
                    logger.error(f"Error in message handler for {message_type}: {str(e)}")
        else:
            logger.debug(f"No handler for message type: {message_type}")

    async def _heartbeat(self):
        """
        Send heartbeat messages to all connected peers.
        """
        while self.running:
            try:
                # Send heartbeat message to all connected peers
                heartbeat_message = {
                    "type": "heartbeat",
                    "from": NODE_ID,
                    "timestamp": time.time()
                }
                await self.broadcast(heartbeat_message)

                # Check for inactive peers
                current_time = time.time()
                for peer in list(self.connected_peers.values()):
                    if current_time - peer.last_seen > node_config.get("heartbeat_interval") * 3:
                        logger.warning(f"Peer {peer.url} has not been seen for a while, disconnecting")
                        await self._disconnect_peer(peer)
            except Exception as e:
                logger.error(f"Error in heartbeat: {str(e)}")

            # Wait before sending the next heartbeat
            await asyncio.sleep(node_config.get("heartbeat_interval"))

    async def _peer_discovery(self):
        """
        Discover new peers from connected peers.
        """
        while self.running:
            try:
                # Request peers from all connected peers
                peer_request_message = {
                    "type": "get_peers",
                    "from": NODE_ID,
                    "timestamp": time.time()
                }
                await self.broadcast(peer_request_message)
            except Exception as e:
                logger.error(f"Error in peer discovery: {str(e)}")

            # Wait before the next discovery
            await asyncio.sleep(node_config.get("peer_discovery_interval"))


# Create a global instance of the peer manager
peer_manager = PeerManager()
