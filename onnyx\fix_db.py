#!/usr/bin/env python3
"""
Fix the Onnyx database schema.
"""

import os
import sqlite3
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("onnyx.fix_db")

def main():
    """Main function to fix the database schema."""
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), "data", "onnyx.db")

    # Check if the database file exists
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return

    logger.info(f"Fixing database schema at {db_path}")

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check if the identities table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='identities'")
    if cursor.fetchone() is None:
        logger.info("Creating identities table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            nation TEXT,
            metadata TEXT,
            created_at INTEGER NOT NULL
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_identities_name ON identities (name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_identities_nation ON identities (nation)')

    # Check if the badges table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='badges'")
    if cursor.fetchone() is None:
        logger.info("Creating badges table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS badges (
            badge TEXT PRIMARY KEY,
            description TEXT NOT NULL,
            type TEXT NOT NULL,
            requirements TEXT
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_badges_type ON badges (type)')

    # Check if the identity_badges table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='identity_badges'")
    if cursor.fetchone() is None:
        logger.info("Creating identity_badges table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS identity_badges (
            identity_id TEXT NOT NULL,
            badge TEXT NOT NULL,
            awarded_at INTEGER NOT NULL,
            PRIMARY KEY (identity_id, badge),
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
            FOREIGN KEY (badge) REFERENCES badges (badge) ON DELETE CASCADE
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_identity_badges_badge ON identity_badges (badge)')

    # Drop the blocks table if it exists
    cursor.execute("DROP TABLE IF EXISTS blocks")
    logger.info("Creating blocks table...")
    cursor.execute('''
    CREATE TABLE blocks (
        block_id TEXT PRIMARY KEY,
        block_height INTEGER NOT NULL,
        previous_block_id TEXT,
        timestamp INTEGER NOT NULL,
        miner_identity_id TEXT,
        transactions TEXT,
        nonce TEXT,
        difficulty INTEGER,
        hash TEXT NOT NULL,
        metadata TEXT
    )
    ''')

    # Create indexes for performance
    cursor.execute('CREATE INDEX idx_blocks_height ON blocks (block_height)')
    cursor.execute('CREATE INDEX idx_blocks_timestamp ON blocks (timestamp)')

    # Check if the transactions table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")
    if cursor.fetchone() is None:
        logger.info("Creating transactions table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            tx_id TEXT PRIMARY KEY,
            type TEXT NOT NULL,
            sender TEXT,
            recipient TEXT,
            amount REAL,
            fee REAL,
            timestamp INTEGER NOT NULL,
            block_id TEXT,
            status TEXT NOT NULL,
            payload TEXT,
            signature TEXT,
            metadata TEXT
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_block_id ON transactions (block_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions (sender)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_recipient ON transactions (recipient)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions (timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions (status)')

    # Drop the tokens table if it exists
    cursor.execute("DROP TABLE IF EXISTS tokens")
    logger.info("Creating tokens table...")
    cursor.execute('''
    CREATE TABLE tokens (
        token_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        creator_id TEXT NOT NULL,
        total_supply REAL NOT NULL,
        supply REAL NOT NULL,
        metadata TEXT,
        created_at INTEGER NOT NULL
    )
    ''')

    # Create indexes for performance
    cursor.execute('CREATE INDEX idx_tokens_creator ON tokens (creator_id)')
    cursor.execute('CREATE INDEX idx_tokens_name ON tokens (name)')
    cursor.execute('CREATE INDEX idx_tokens_symbol ON tokens (symbol)')

    # Check if the token_balances table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='token_balances'")
    if cursor.fetchone() is None:
        logger.info("Creating token_balances table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS token_balances (
            identity_id TEXT NOT NULL,
            token_id TEXT NOT NULL,
            balance REAL NOT NULL,
            updated_at INTEGER NOT NULL,
            PRIMARY KEY (identity_id, token_id),
            FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
            FOREIGN KEY (token_id) REFERENCES tokens (token_id) ON DELETE CASCADE
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_balances_token ON token_balances (token_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_balances_identity ON token_balances (identity_id)')

    # Check if the token_transactions table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='token_transactions'")
    if cursor.fetchone() is None:
        logger.info("Creating token_transactions table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS token_transactions (
            tx_id TEXT PRIMARY KEY,
            token_id TEXT NOT NULL,
            from_id TEXT,
            to_id TEXT,
            amount REAL NOT NULL,
            operation TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            FOREIGN KEY (token_id) REFERENCES tokens (token_id) ON DELETE CASCADE
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_token ON token_transactions (token_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_from ON token_transactions (from_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_to ON token_transactions (to_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_timestamp ON token_transactions (timestamp)')

    # Check if the mempool table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='mempool'")
    if cursor.fetchone() is None:
        logger.info("Creating mempool table...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS mempool (
            tx_id TEXT PRIMARY KEY,
            type TEXT NOT NULL,
            sender TEXT,
            recipient TEXT,
            amount REAL,
            fee REAL,
            timestamp INTEGER NOT NULL,
            payload TEXT,
            signature TEXT,
            metadata TEXT,
            FOREIGN KEY (tx_id) REFERENCES transactions (tx_id) ON DELETE CASCADE
        )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mempool_timestamp ON mempool (timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mempool_sender ON mempool (sender)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mempool_recipient ON mempool (recipient)')

    # Commit the changes
    conn.commit()

    # Close the connection
    conn.close()

    logger.info("Database schema fixed successfully.")

if __name__ == "__main__":
    main()
