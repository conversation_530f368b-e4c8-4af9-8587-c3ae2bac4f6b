"""
Authentication Routes

Handles identity registration, login, and authentication.
"""

import os
import sys
import json
import time
import logging
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.identity import Identity
from blockchain.wallet.wallet import Wallet
from shared.db.db import db

logger = logging.getLogger("onnyx.web.auth")

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register/identity')
def register_identity():
    """Identity registration page."""
    return render_template('auth/register_identity.html')

@auth_bp.route('/register/identity', methods=['POST'])
def register_identity_post():
    """Handle identity registration."""
    try:
        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        role = request.form.get('role', 'Community')
        
        # Validation
        if not name or not email:
            flash('Name and email are required.', 'error')
            return render_template('auth/register_identity.html')
        
        # Check if email already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", (email,))
        if existing:
            flash('An identity with this email already exists.', 'error')
            return render_template('auth/register_identity.html')
        
        # Generate cryptographic keys
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()
        
        # Create identity
        identity = Identity.create(
            name=name,
            email=email,
            public_key=public_key,
            metadata={
                "purpose": role,
                "registration_type": "web",
                "verified": True,
                "registration_timestamp": int(time.time())
            }
        )
        
        # Store in session
        session['identity_id'] = identity.identity_id
        session['identity_name'] = identity.name
        
        # Prepare key data for download
        key_data = {
            "identity_id": identity.identity_id,
            "name": identity.name,
            "email": identity.email,
            "public_key": public_key,
            "private_key": private_key,
            "created_at": int(time.time()),
            "warning": "KEEP THIS FILE SECURE! Your private key controls your identity and assets."
        }
        
        logger.info(f"Identity registered successfully: {identity.identity_id}")
        
        return render_template('auth/registration_success.html', 
                             identity=identity,
                             key_data=json.dumps(key_data, indent=2))
        
    except Exception as e:
        logger.error(f"Error registering identity: {e}")
        flash('Registration failed. Please try again.', 'error')
        return render_template('auth/register_identity.html')

@auth_bp.route('/register/sela')
def register_sela():
    """Sela registration page."""
    if 'identity_id' not in session:
        flash('Please register an identity first.', 'warning')
        return redirect(url_for('auth.register_identity'))
    
    # Get business categories
    categories = [
        'Technology', 'Consulting', 'Retail', 'Manufacturing', 
        'Healthcare', 'Education', 'Finance', 'Real Estate',
        'Food & Beverage', 'Transportation', 'Entertainment', 'Other'
    ]
    
    return render_template('auth/register_sela.html', categories=categories)

@auth_bp.route('/register/sela', methods=['POST'])
def register_sela_post():
    """Handle Sela registration."""
    try:
        if 'identity_id' not in session:
            flash('Please log in first.', 'error')
            return redirect(url_for('auth.register_identity'))
        
        # Get form data
        sela_name = request.form.get('sela_name', '').strip()
        category = request.form.get('category', '').strip()
        description = request.form.get('description', '').strip()
        address = request.form.get('address', '').strip()
        services = request.form.getlist('services')
        
        # Validation
        if not sela_name or not category or not description:
            flash('Business name, category, and description are required.', 'error')
            return redirect(url_for('auth.register_sela'))
        
        # Check if Sela name already exists
        existing = db.query_one("SELECT sela_id FROM selas WHERE name = ?", (sela_name,))
        if existing:
            flash('A business with this name already exists.', 'error')
            return redirect(url_for('auth.register_sela'))
        
        # Generate Sela ID
        import hashlib
        sela_id = hashlib.sha256(f"{sela_name}_{session['identity_id']}_{int(time.time())}".encode()).hexdigest()[:16]
        
        # Create Sela data
        sela_data = {
            'sela_id': sela_id,
            'identity_id': session['identity_id'],
            'name': sela_name,
            'category': category,
            'stake_amount': 0,
            'stake_token_id': None,
            'status': 'active',
            'created_at': int(time.time()),
            'metadata': json.dumps({
                "description": description,
                "address": address,
                "services": services,
                "registration_type": "web",
                "verified": True
            })
        }
        
        # Insert into database
        db.insert('selas', sela_data)
        
        logger.info(f"Sela registered successfully: {sela_id}")
        flash(f'Business "{sela_name}" registered successfully!', 'success')
        
        return redirect(url_for('dashboard.overview'))
        
    except Exception as e:
        logger.error(f"Error registering Sela: {e}")
        flash('Sela registration failed. Please try again.', 'error')
        return redirect(url_for('auth.register_sela'))

@auth_bp.route('/login')
def login():
    """Login page."""
    return render_template('auth/login.html')

@auth_bp.route('/login', methods=['POST'])
def login_post():
    """Handle login."""
    try:
        email = request.form.get('email', '').strip()
        
        if not email:
            flash('Email is required.', 'error')
            return render_template('auth/login.html')
        
        # Find identity by email
        identity = db.query_one("SELECT * FROM identities WHERE email = ?", (email,))
        
        if not identity:
            flash('No identity found with this email.', 'error')
            return render_template('auth/login.html')
        
        # Create identity object
        identity_obj = Identity.from_dict(identity)
        
        # Store in session
        session['identity_id'] = identity_obj.identity_id
        session['identity_name'] = identity_obj.name
        
        flash(f'Welcome back, {identity_obj.name}!', 'success')
        return redirect(url_for('dashboard.overview'))
        
    except Exception as e:
        logger.error(f"Error during login: {e}")
        flash('Login failed. Please try again.', 'error')
        return render_template('auth/login.html')

@auth_bp.route('/logout')
def logout():
    """Logout user."""
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))
