"""
Onnyx Mempool Transaction Model Tests

This module provides tests for the Onnyx mempool transaction model.
"""

import os
import sys
import unittest
import time
import hashlib
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.mempool import MempoolTransaction
from models.identity import Identity
from data.db import db

class TestMempoolModel(unittest.TestCase):
    """
    Test the MempoolTransaction model.
    """
    
    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_mempool_model.db")
        
        # Set the database path
        db.db_path = self.db_path
        
        # Create the tables
        conn = sqlite3.connect(self.db_path)
        
        # Create the identities table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER,
            updated_at INTEGER
        )
        """)
        
        # Create the mempool table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS mempool (
            tx_id TEXT PRIMARY KEY,
            timestamp INTEGER NOT NULL,
            op TEXT NOT NULL,
            data TEXT NOT NULL,
            sender TEXT NOT NULL,
            signature TEXT,
            status TEXT NOT NULL,
            created_at INTEGER,
            FOREIGN KEY (sender) REFERENCES identities (identity_id)
        )
        """)
        
        conn.commit()
        conn.close()
        
        # Create test identities
        self.sender_id = "sender_identity"
        self.recipient_id = "recipient_identity"
        
        # Insert the identities into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.sender_id,
                "Sender Identity",
                "0x1234567890abcdef",
                json.dumps({"type": "individual", "description": "Sender identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                self.recipient_id,
                "Recipient Identity",
                "0x0987654321fedcba",
                json.dumps({"type": "individual", "description": "Recipient identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")
        
        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")
    
    def test_create_mempool_tx(self):
        """Test creating a mempool transaction."""
        # Create a mempool transaction directly in the database
        tx_id = "test_mempool_tx_id"
        timestamp = int(time.time())
        
        # Insert the mempool transaction into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO mempool (tx_id, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                tx_id,
                timestamp,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 100,
                    "token_id": "onx",
                    "description": "Test mempool transaction"
                }),
                self.sender_id,
                "test_signature",
                "PENDING",
                timestamp
            )
        )
        conn.commit()
        conn.close()
        
        # Check if the mempool transaction was saved to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM mempool WHERE tx_id = ?", (tx_id,))
        row = cursor.fetchone()
        conn.close()
        
        print(f"Database row: {row}")
        
        # Get the mempool transaction by ID
        retrieved_tx = MempoolTransaction.get_by_id(tx_id)
        
        # Check that the mempool transaction was retrieved
        self.assertIsNotNone(retrieved_tx)
        self.assertEqual(retrieved_tx.tx_id, tx_id)
        self.assertEqual(retrieved_tx.timestamp, timestamp)
        self.assertEqual(retrieved_tx.op, "OP_SEND")
        self.assertEqual(retrieved_tx.data["recipient"], self.recipient_id)
        self.assertEqual(retrieved_tx.data["amount"], 100)
        self.assertEqual(retrieved_tx.data["token_id"], "onx")
        self.assertEqual(retrieved_tx.data["description"], "Test mempool transaction")
        self.assertEqual(retrieved_tx.sender, self.sender_id)
        self.assertEqual(retrieved_tx.signature, "test_signature")
        self.assertEqual(retrieved_tx.status, "PENDING")
        self.assertEqual(retrieved_tx.created_at, timestamp)
    
    def test_get_pending_transactions(self):
        """Test getting pending transactions."""
        # Create multiple mempool transactions directly in the database
        timestamp = int(time.time())
        
        # Insert the mempool transactions into the database
        conn = sqlite3.connect(self.db_path)
        
        # Insert a pending transaction
        conn.execute(
            "INSERT INTO mempool (tx_id, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "pending_tx_1",
                timestamp,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 100,
                    "token_id": "onx",
                    "description": "Pending transaction 1"
                }),
                self.sender_id,
                "signature_1",
                "PENDING",
                timestamp
            )
        )
        
        # Insert another pending transaction
        conn.execute(
            "INSERT INTO mempool (tx_id, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "pending_tx_2",
                timestamp + 60,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 200,
                    "token_id": "onx",
                    "description": "Pending transaction 2"
                }),
                self.sender_id,
                "signature_2",
                "PENDING",
                timestamp + 60
            )
        )
        
        # Insert a processing transaction
        conn.execute(
            "INSERT INTO mempool (tx_id, timestamp, op, data, sender, signature, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (
                "processing_tx",
                timestamp + 120,
                "OP_SEND",
                json.dumps({
                    "recipient": self.recipient_id,
                    "amount": 300,
                    "token_id": "onx",
                    "description": "Processing transaction"
                }),
                self.sender_id,
                "signature_3",
                "PROCESSING",
                timestamp + 120
            )
        )
        
        conn.commit()
        conn.close()
        
        # Get pending transactions
        pending_txs = MempoolTransaction.filter("status = ?", ("PENDING",))
        
        # Check that the pending transactions were retrieved
        self.assertEqual(len(pending_txs), 2)
        self.assertEqual(pending_txs[0].tx_id, "pending_tx_1")
        self.assertEqual(pending_txs[1].tx_id, "pending_tx_2")

if __name__ == "__main__":
    unittest.main()
