#!/usr/bin/env python3
"""
ONNYX Timestamp Filter Fix Verification Test

Tests the fix for the TypeError in the format_timestamp template filter that was preventing
the home page from loading. Verifies that the filter now correctly handles both string
datetime formats and numeric timestamps.
"""

import os
import sys
import time
import requests
import subprocess
from datetime import datetime

def test_timestamp_filter_functions():
    """Test the timestamp filter functions directly."""
    print("🕒 TESTING TIMESTAMP FILTER FUNCTIONS")
    print("=" * 45)
    
    results = []
    
    try:
        # Add the current directory to Python path
        sys.path.insert(0, os.path.abspath('.'))
        
        # Import the Flask app
        from web.app import create_app
        
        app = create_app()
        
        with app.app_context():
            # Get the template filters
            format_timestamp = app.jinja_env.filters.get('format_timestamp')
            timestamp_to_time = app.jinja_env.filters.get('timestamp_to_time')
            timestamp_to_date = app.jinja_env.filters.get('timestamp_to_date')
            
            if format_timestamp:
                print("✅ format_timestamp filter found")
                results.append(True)
            else:
                print("❌ format_timestamp filter not found")
                results.append(False)
                return results
            
            # Test cases for different timestamp formats
            test_cases = [
                # ISO 8601 datetime strings (from database)
                ('2025-06-02T16:40:56.329731', 'ISO 8601 with microseconds'),
                ('2025-06-02T16:40:56', 'ISO 8601 without microseconds'),
                ('2025-06-02T16:40:56Z', 'ISO 8601 with Z timezone'),
                
                # Unix timestamps
                (1733155256, 'Unix timestamp (int)'),
                (1733155256.329731, 'Unix timestamp (float)'),
                ('1733155256', 'Unix timestamp as string'),
                
                # Edge cases
                (None, 'None value'),
                ('', 'Empty string'),
                ('invalid', 'Invalid string'),
            ]
            
            print("\n📋 Testing format_timestamp filter:")
            for test_input, description in test_cases:
                try:
                    result = format_timestamp(test_input)
                    print(f"✅ {description}: {repr(test_input)} -> {repr(result)}")
                    results.append(True)
                except Exception as e:
                    print(f"❌ {description}: {repr(test_input)} -> Error: {e}")
                    results.append(False)
            
            print("\n📋 Testing timestamp_to_time filter:")
            for test_input, description in test_cases[:3]:  # Test first 3 cases
                try:
                    result = timestamp_to_time(test_input)
                    print(f"✅ {description}: {repr(test_input)} -> {repr(result)}")
                    results.append(True)
                except Exception as e:
                    print(f"❌ {description}: {repr(test_input)} -> Error: {e}")
                    results.append(False)
            
            print("\n📋 Testing timestamp_to_date filter:")
            for test_input, description in test_cases[:3]:  # Test first 3 cases
                try:
                    result = timestamp_to_date(test_input)
                    print(f"✅ {description}: {repr(test_input)} -> {repr(result)}")
                    results.append(True)
                except Exception as e:
                    print(f"❌ {description}: {repr(test_input)} -> Error: {e}")
                    results.append(False)
    
    except Exception as e:
        print(f"❌ Error testing timestamp filters: {e}")
        results.append(False)
    
    return results

def test_database_timestamp_format():
    """Test the actual timestamp format from the database."""
    print("\n🗄️ TESTING DATABASE TIMESTAMP FORMAT")
    print("=" * 40)
    
    results = []
    
    try:
        # Add the current directory to Python path
        sys.path.insert(0, os.path.abspath('.'))
        
        from shared.db.db import db
        
        # Check selas table timestamps
        selas = db.query("SELECT sela_id, name, created_at FROM selas LIMIT 3")
        if selas:
            print("✅ Found selas data in database")
            results.append(True)
            
            for sela in selas:
                created_at = sela.get('created_at')
                print(f"  Sela: {sela.get('name', 'Unknown')}")
                print(f"    created_at: {repr(created_at)} (type: {type(created_at).__name__})")
                
                # Verify it's a string in ISO 8601 format
                if isinstance(created_at, str) and 'T' in created_at:
                    print(f"    ✅ Correct ISO 8601 string format")
                    results.append(True)
                else:
                    print(f"    ❌ Unexpected format")
                    results.append(False)
        else:
            print("⚠️  No selas data found in database")
            results.append(False)
        
        # Check transactions table timestamps
        transactions = db.query("SELECT tx_id, created_at FROM transactions LIMIT 3")
        if transactions:
            print("✅ Found transaction data in database")
            results.append(True)
            
            for tx in transactions:
                created_at = tx.get('created_at')
                print(f"  Transaction: {tx.get('tx_id', 'Unknown')[:8]}...")
                print(f"    created_at: {repr(created_at)} (type: {type(created_at).__name__})")
                
                # Verify it's a string in ISO 8601 format
                if isinstance(created_at, str) and 'T' in created_at:
                    print(f"    ✅ Correct ISO 8601 string format")
                    results.append(True)
                else:
                    print(f"    ❌ Unexpected format")
                    results.append(False)
        else:
            print("⚠️  No transaction data found in database")
            results.append(False)
    
    except Exception as e:
        print(f"❌ Error checking database timestamps: {e}")
        results.append(False)
    
    return results

def test_home_page_loading():
    """Test that the home page loads without errors."""
    print("\n🌐 TESTING HOME PAGE LOADING")
    print("=" * 35)
    
    results = []
    
    # Start Flask server
    print("🚀 Starting Flask server...")
    server_process = subprocess.Popen([
        sys.executable, 'web/app.py'
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for server to start
    time.sleep(3)
    
    try:
        # Test home page
        response = requests.get('http://127.0.0.1:5000/', timeout=10)
        
        if response.status_code == 200:
            print("✅ Home page loads successfully (HTTP 200)")
            results.append(True)
            
            content = response.text
            
            # Check for Recent Validators section
            if 'Recent Validators' in content:
                print("✅ Recent Validators section found")
                results.append(True)
            else:
                print("❌ Recent Validators section not found")
                results.append(False)
            
            # Check for formatted timestamps
            import re
            timestamp_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
            timestamps = re.findall(timestamp_pattern, content)
            
            if timestamps:
                print(f"✅ Found {len(timestamps)} formatted timestamps")
                for i, ts in enumerate(timestamps[:3]):
                    print(f"    {i+1}. {ts}")
                results.append(True)
            else:
                print("❌ No formatted timestamps found")
                results.append(False)
            
            # Check for template errors
            error_indicators = ['format_timestamp', 'TypeError', 'TemplateSyntaxError']
            has_errors = any(indicator in content for indicator in error_indicators)
            
            if not has_errors:
                print("✅ No template errors detected")
                results.append(True)
            else:
                print("❌ Template errors detected")
                results.append(False)
            
            # Check for Live Transaction Stream
            if 'Live Transaction Stream' in content:
                print("✅ Live Transaction Stream section found")
                results.append(True)
            else:
                print("❌ Live Transaction Stream section not found")
                results.append(False)
        
        else:
            print(f"❌ Home page failed to load (HTTP {response.status_code})")
            results.extend([False] * 5)
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Error accessing home page: {e}")
        results.extend([False] * 5)
    
    finally:
        # Stop Flask server
        server_process.terminate()
        server_process.wait(timeout=5)
        print("⏹️  Flask server stopped")
    
    return results

def test_specific_timestamp_scenarios():
    """Test specific timestamp scenarios that were causing issues."""
    print("\n🎯 TESTING SPECIFIC TIMESTAMP SCENARIOS")
    print("=" * 45)
    
    results = []
    
    try:
        # Test the exact scenario that was failing
        import datetime
        
        def format_timestamp(timestamp):
            """The fixed format_timestamp function."""
            if not timestamp:
                return ""
            
            try:
                # Handle different timestamp formats
                if isinstance(timestamp, str):
                    # Try to parse ISO 8601 datetime string
                    if 'T' in timestamp:
                        # Remove microseconds if present and parse
                        timestamp_clean = timestamp.split('.')[0] if '.' in timestamp else timestamp
                        dt = datetime.datetime.fromisoformat(timestamp_clean.replace('Z', '+00:00'))
                    else:
                        # Try to parse as string representation of Unix timestamp
                        dt = datetime.datetime.fromtimestamp(float(timestamp))
                elif isinstance(timestamp, (int, float)):
                    # Handle numeric Unix timestamp
                    dt = datetime.datetime.fromtimestamp(timestamp)
                else:
                    # Fallback for other types
                    return str(timestamp)
                
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError, OSError) as e:
                # Return the original value if parsing fails
                return str(timestamp)
        
        # Test the exact timestamp format from the error
        problematic_timestamp = '2025-06-02T16:40:56.329731'
        
        try:
            result = format_timestamp(problematic_timestamp)
            expected_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
            
            if re.match(expected_pattern, result):
                print(f"✅ Problematic timestamp fixed: {problematic_timestamp} -> {result}")
                results.append(True)
            else:
                print(f"❌ Unexpected result format: {result}")
                results.append(False)
        except Exception as e:
            print(f"❌ Still failing on problematic timestamp: {e}")
            results.append(False)
        
        # Test edge cases that could cause similar issues
        edge_cases = [
            '2025-06-02T16:40:56',  # Without microseconds
            '2025-06-02T16:40:56Z',  # With Z timezone
            '2025-06-02T16:40:56+00:00',  # With timezone offset
        ]
        
        for edge_case in edge_cases:
            try:
                result = format_timestamp(edge_case)
                print(f"✅ Edge case handled: {edge_case} -> {result}")
                results.append(True)
            except Exception as e:
                print(f"❌ Edge case failed: {edge_case} -> {e}")
                results.append(False)
    
    except Exception as e:
        print(f"❌ Error in timestamp scenario testing: {e}")
        results.append(False)
    
    return results

def main():
    """Main test runner."""
    print("🔧 ONNYX TIMESTAMP FILTER FIX VERIFICATION")
    print("=" * 50)
    
    # Run all test categories
    filter_results = test_timestamp_filter_functions()
    database_results = test_database_timestamp_format()
    homepage_results = test_home_page_loading()
    scenario_results = test_specific_timestamp_scenarios()
    
    # Calculate overall results
    all_results = filter_results + database_results + homepage_results + scenario_results
    total_tests = len(all_results)
    passed_tests = sum(all_results)
    failed_tests = total_tests - passed_tests
    
    # Generate summary
    print("\n📊 TIMESTAMP FIX VERIFICATION SUMMARY")
    print("=" * 45)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed breakdown
    print(f"\nFilter Functions: {sum(filter_results)}/{len(filter_results)} passed")
    print(f"Database Format: {sum(database_results)}/{len(database_results)} passed")
    print(f"Home Page Loading: {sum(homepage_results)}/{len(homepage_results)} passed")
    print(f"Specific Scenarios: {sum(scenario_results)}/{len(scenario_results)} passed")
    
    # Overall assessment
    if passed_tests >= total_tests * 0.85:  # 85% pass rate
        print("\n🎉 TIMESTAMP FILTER FIX SUCCESSFUL!")
        print("✅ TypeError in format_timestamp resolved")
        print("✅ Home page loads without errors")
        print("✅ Timestamps display correctly")
        print("✅ All timestamp formats handled")
        print("\n🌐 ONNYX home page is now fully functional!")
        return True
    else:
        print("\n⚠️  TIMESTAMP FIX NEEDS ATTENTION")
        print("Some issues may still exist.")
        return False

if __name__ == "__main__":
    import re
    success = main()
    sys.exit(0 if success else 1)
