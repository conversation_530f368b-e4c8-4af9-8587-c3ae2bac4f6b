# src/db/schema/governance.py

from src.db.manager import db_manager

def create_governance_tables():
    """
    Create the tables for the governance system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Create voice_scrolls table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS voice_scrolls (
        scroll_id TEXT PRIMARY KEY,
        proposer_id TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        scroll_type TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        expires_at INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'PENDING',
        FOREIGN KEY (proposer_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create voice_votes table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS voice_votes (
        scroll_id TEXT NOT NULL,
        voter_id TEXT NOT NULL,
        vote TEXT NOT NULL,
        weight REAL NOT NULL,
        cast_at INTEGER NOT NULL,
        <PERSON>IMAR<PERSON> KEY (scroll_id, voter_id),
        <PERSON>OREIG<PERSON> KEY (scroll_id) REFERENCES voice_scrolls (scroll_id) ON DELETE CASCADE,
        FOREIGN KEY (voter_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create council_members table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS council_members (
        identity_id TEXT NOT NULL,
        tribe TEXT NOT NULL,
        appointed_at INTEGER NOT NULL,
        term_end INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'ACTIVE',
        PRIMARY KEY (identity_id),
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create council_votes table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS council_votes (
        scroll_id TEXT NOT NULL,
        council_member_id TEXT NOT NULL,
        vote TEXT NOT NULL,
        cast_at INTEGER NOT NULL,
        PRIMARY KEY (scroll_id, council_member_id),
        FOREIGN KEY (scroll_id) REFERENCES voice_scrolls (scroll_id) ON DELETE CASCADE,
        FOREIGN KEY (council_member_id) REFERENCES council_members (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_voice_scrolls_proposer_id ON voice_scrolls (proposer_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_voice_scrolls_status ON voice_scrolls (status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_voice_scrolls_scroll_type ON voice_scrolls (scroll_type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_voice_votes_voter_id ON voice_votes (voter_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_council_members_tribe ON council_members (tribe)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_council_members_status ON council_members (status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_council_votes_council_member_id ON council_votes (council_member_id)')
    
    conn.commit()
