# Onnyx Core Chain Module

This module defines the blockchain's structure and core logic.

## Files

- `block.py`: Defines the `Block` class — the fundamental unit of the chain.
- `chain.py`: Defines the `Blockchain` class — manages blocks, mempool, and mining.

## Features

- In-memory blockchain
- Genesis block creation
- Hash-verified block linking
- Basic mempool handling
- Proof-of-work and UTXO integration coming next

## Next Steps

- Add transaction validation rules
- Add UTXO model (optional if using account-based)
- Add persistence layer (to disk or DB)
