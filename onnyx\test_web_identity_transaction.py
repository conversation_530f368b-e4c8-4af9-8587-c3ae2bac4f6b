#!/usr/bin/env python3

"""
Script to test creating an identity through the web app and verifying the transaction.
"""

import os
import sys
import time
import logging
import requests
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.test_web_identity_transaction")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from data.db import db

def test_web_identity_transaction():
    """Test creating an identity through the web app and verifying the transaction."""
    try:
        # Create a unique name for the test identity
        test_name = f"Web Test Identity {int(time.time())}"
        
        # Create the identity data
        identity_data = {
            "name": test_name,
            "nation": "Israel",
            "tribe": "Judah",
            "dob": "01/01/2000",
            "region": "North America",
            "purpose": "Testing",
            "soul_seal": "test_seal"
        }
        
        logger.info(f"Creating identity through web app: {identity_data}")
        
        # Send the request to create the identity
        response = requests.post("http://localhost:8082/create-identity", data=identity_data)
        
        # Check if the request was successful
        if response.status_code != 200:
            logger.error(f"Error creating identity: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
        
        logger.info("Identity created successfully through web app")
        
        # Wait for the transaction to be created
        time.sleep(2)
        
        # Query the database for CREATE_IDENTITY transactions
        query = "SELECT * FROM transactions WHERE op = 'CREATE_IDENTITY' ORDER BY timestamp DESC LIMIT 1"
        transaction = db.query_one(query)
        
        if transaction:
            logger.info(f"Found transaction: {transaction}")
            
            # Parse the transaction data
            data = json.loads(transaction['data'])
            
            # Check if the transaction data matches the identity data
            if data.get('name') == test_name:
                logger.info(f"Transaction data matches identity data: {data}")
                return True
            else:
                logger.error(f"Transaction data does not match identity data: {data}")
                return False
        else:
            logger.error("No CREATE_IDENTITY transaction found")
            return False
    except Exception as e:
        logger.error(f"Error testing web identity transaction: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main entry point."""
    logger.info("Testing web identity transaction...")
    
    success = test_web_identity_transaction()
    
    if success:
        logger.info("Web identity transaction test passed")
    else:
        logger.error("Web identity transaction test failed")

if __name__ == "__main__":
    main()
