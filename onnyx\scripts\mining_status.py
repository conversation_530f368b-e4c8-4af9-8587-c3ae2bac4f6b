#!/usr/bin/env python3
"""
Mining Status Check

Quick check of the current mining status and blockchain health.
"""

import os
import sys
import time
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def check_mining_status():
    """Check the current mining status."""
    print("⛏️  ONNYX MINING STATUS CHECK")
    print("=" * 50)
    
    try:
        # Get latest block info
        latest_block = db.query_one('SELECT * FROM blocks ORDER BY block_height DESC LIMIT 1')
        if latest_block:
            print("📦 Latest Block:")
            print(f"   Height: #{latest_block['block_height']}")
            print(f"   Hash: {latest_block['block_hash'][:20]}...")
            print(f"   Miner: {latest_block['miner']}")
            
            # Convert timestamp to readable format
            block_time = datetime.fromtimestamp(latest_block['timestamp'])
            print(f"   Time: {block_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Check how recent the last block is
            time_since_last = time.time() - latest_block['timestamp']
            print(f"   Age: {int(time_since_last)} seconds ago")
            
            if time_since_last < 30:
                print("   Status: ✅ ACTIVE (recent block)")
            elif time_since_last < 60:
                print("   Status: ⚠️  SLOW (block is getting old)")
            else:
                print("   Status: ❌ INACTIVE (no recent blocks)")
        else:
            print("❌ No blocks found in database")
            return False
        
        # Get network statistics
        print("\n📊 Network Statistics:")
        block_count = db.query_one('SELECT COUNT(*) as count FROM blocks')['count']
        tx_count = db.query_one('SELECT COUNT(*) as count FROM transactions')['count']
        identity_count = db.query_one('SELECT COUNT(*) as count FROM identities')['count']
        sela_count = db.query_one('SELECT COUNT(*) as count FROM selas')['count']
        
        print(f"   Total Blocks: {block_count}")
        print(f"   Total Transactions: {tx_count}")
        print(f"   Total Identities: {identity_count}")
        print(f"   Total Selas: {sela_count}")
        
        # Check recent mining activity (last 5 blocks)
        print("\n🔗 Recent Blocks:")
        recent_blocks = db.query('''
            SELECT block_height, block_hash, miner, timestamp 
            FROM blocks 
            ORDER BY block_height DESC 
            LIMIT 5
        ''')
        
        for block in recent_blocks:
            block_time = datetime.fromtimestamp(block['timestamp'])
            print(f"   #{block['block_height']}: {block['block_hash'][:16]}... by {block['miner']} at {block_time.strftime('%H:%M:%S')}")
        
        # Check if mining is consistent
        if len(recent_blocks) >= 2:
            time_diff = recent_blocks[0]['timestamp'] - recent_blocks[1]['timestamp']
            print(f"\n⏱️  Average block time: ~{int(time_diff)} seconds")
            
            if time_diff < 15:
                print("   Mining rate: ✅ OPTIMAL")
            elif time_diff < 30:
                print("   Mining rate: ⚠️  ACCEPTABLE")
            else:
                print("   Mining rate: ❌ SLOW")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking mining status: {e}")
        return False

def main():
    """Main entry point."""
    success = check_mining_status()
    
    if success:
        print("\n🎉 MINING STATUS CHECK COMPLETE")
        print("✅ ONNYX blockchain is operational")
        return 0
    else:
        print("\n⚠️  MINING STATUS CHECK FAILED")
        print("❌ Issues detected with blockchain")
        return 1

if __name__ == "__main__":
    sys.exit(main())
