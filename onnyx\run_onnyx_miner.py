#!/usr/bin/env python3
"""
Run Onnyx Miner

This script runs the Onnyx Miner locally with multiple terminals.
"""

import os
import sys
import time
import json
import subprocess
import argparse
import threading
import signal
import atexit

# Demo identities
IDENTITIES = [
    {"id": "alice_salon", "name": "Alice's Beauty Salon", "type": "BEAUTY"},
    {"id": "bob_barber", "name": "Bob's Barbershop", "type": "BARBER"},
    {"id": "charlie_cafe", "name": "Charlie's Cafe", "type": "CAFE"}
]

# Global variables
processes = []

def cleanup():
    """Clean up processes on exit."""
    for process in processes:
        try:
            process.terminate()
        except:
            pass

def run_api_server():
    """Run the mock API server."""
    print("Starting mock API server...")
    
    # Create the demo directory
    os.makedirs("demo", exist_ok=True)
    os.makedirs("demo/data", exist_ok=True)
    
    # Create mock data
    create_mock_data()
    
    # Run the API server
    api_process = subprocess.Popen([sys.executable, "onnyx/mock_api_server.py"])
    processes.append(api_process)
    
    # Wait for the API server to start
    time.sleep(2)
    
    return api_process

def create_mock_data():
    """Create mock data for the demo."""
    # Create mock blocks
    blocks_file = os.path.join("demo/data", "blocks.json")
    blocks = {
        "blocks": [
            {
                "index": 0,
                "timestamp": int(time.time()) - 3600,
                "proposer": "genesis",
                "previous_hash": "0",
                "hash": "000000000000000000000000000000000000000000000000000000000000000",
                "nonce": 0,
                "signature": "",
                "signed_by": "genesis",
                "transactions": []
            }
        ]
    }
    
    with open(blocks_file, "w") as f:
        json.dump(blocks, f, indent=2)
    
    # Create mock identities
    identities_file = os.path.join("demo/data", "identities.json")
    identities = {}
    
    for identity in IDENTITIES:
        identities[identity["id"]] = {
            "identity_id": identity["id"],
            "name": identity["name"],
            "public_key": f"0x{identity['id']}",
            "created_at": int(time.time()) - 3600,
            "reputation": 100,
            "etzem_score": 50,
            "badges": ["VALIDATOR_ELIGIBLE_BADGE"],
            "joined_selas": [],
            "founded_selas": [identity["id"]]
        }
    
    with open(identities_file, "w") as f:
        json.dump(identities, f, indent=2)
    
    # Create mock selas
    selas_file = os.path.join("demo/data", "selas.json")
    selas = {}
    
    for identity in IDENTITIES:
        selas[identity["id"]] = {
            "sela_id": identity["id"],
            "name": identity["name"],
            "founder": identity["id"],
            "type": identity["type"],
            "token_id": f"{identity['id']}_TOKEN",
            "created_at": int(time.time()) - 3600,
            "members": [identity["id"]],
            "roles": {identity["id"]: "FOUNDER"},
            "services_offered": ["Service 1", "Service 2", "Service 3"],
            "is_validator": True,
            "validator_since": int(time.time()) - 3600,
            "last_block_proposed": 0
        }
    
    with open(selas_file, "w") as f:
        json.dump(selas, f, indent=2)
    
    # Create mock tokens
    tokens_file = os.path.join("demo/data", "tokens.json")
    tokens = {}
    
    for identity in IDENTITIES:
        tokens[f"{identity['id']}_TOKEN"] = {
            "token_id": f"{identity['id']}_TOKEN",
            "name": f"{identity['name']} Token",
            "symbol": f"{identity['id'][:3].upper()}",
            "creator_id": identity["id"],
            "supply": 1000,
            "category": "loyalty",
            "decimals": 2,
            "created_at": int(time.time()) - 3600,
            "metadata": {
                "sela_id": identity["id"]
            }
        }
    
    with open(tokens_file, "w") as f:
        json.dump(tokens, f, indent=2)
    
    # Create mock balances
    balances_file = os.path.join("demo/data", "balances.json")
    balances = {}
    
    for identity in IDENTITIES:
        balances[identity["id"]] = {
            f"{identity['id']}_TOKEN": 1000,
            "ONX": 100
        }
    
    with open(balances_file, "w") as f:
        json.dump(balances, f, indent=2)
    
    # Create mock rotation
    rotation_file = os.path.join("demo/data", "rotation.json")
    rotation = {
        "queue": [identity["id"] for identity in IDENTITIES],
        "last_proposer": "genesis",
        "height": 0
    }
    
    with open(rotation_file, "w") as f:
        json.dump(rotation, f, indent=2)

def create_config(identity):
    """Create a configuration file for an identity."""
    config = {
        "identity": {
            "id": identity["id"],
            "name": identity["name"],
            "keys_dir": "demo/keys"
        },
        "sela": {
            "id": identity["id"],
            "name": identity["name"],
            "type": identity["type"]
        },
        "node": {
            "api_url": "http://localhost:8000",
            "p2p_port": 9000 + IDENTITIES.index(identity),
            "data_dir": "demo/data",
            "auto_mine": False,
            "mine_interval": 60
        },
        "gui": {
            "enabled": True,
            "port": 5005 + IDENTITIES.index(identity),
            "host": "127.0.0.1"
        }
    }
    
    config_file = os.path.join("demo", f"config_{identity['id']}.yaml")
    
    with open(config_file, "w") as f:
        import yaml
        yaml.dump(config, f, default_flow_style=False)
    
    # Create keys for the identity
    identity_dir = os.path.join("demo/keys", identity["id"])
    os.makedirs(identity_dir, exist_ok=True)
    
    # Create a mock private key
    with open(os.path.join(identity_dir, "private.pem"), "w") as f:
        f.write(f"-----BEGIN PRIVATE KEY-----\n{identity['id']}\n-----END PRIVATE KEY-----")
    
    # Create a mock public key
    with open(os.path.join(identity_dir, "public.pem"), "w") as f:
        f.write(f"-----BEGIN PUBLIC KEY-----\n{identity['id']}\n-----END PUBLIC KEY-----")

def run_miner(identity):
    """Run the Onnyx Miner for an identity."""
    config_file = os.path.join("demo", f"config_{identity['id']}.yaml")
    
    # Set the environment variable for the configuration file
    env = os.environ.copy()
    env["ONNYX_CONFIG"] = config_file
    
    # Run the miner
    print(f"Starting Onnyx Miner for {identity['name']}...")
    
    # Use subprocess to run the miner
    try:
        # Run the onnyx-sela-miner.py script
        miner_process = subprocess.Popen(
            [sys.executable, "onnyx/onnyx-sela-miner.py", "status", "--config", config_file],
            env=env
        )
        processes.append(miner_process)
        
        # Wait for the miner to start
        miner_process.wait()
        
        # Run the onnyx-explorer.py script
        explorer_process = subprocess.Popen(
            [sys.executable, "onnyx/onnyx-explorer.py"],
            env=env
        )
        processes.append(explorer_process)
        
        return miner_process, explorer_process
    except Exception as e:
        print(f"Error starting miner for {identity['name']}: {str(e)}")
        return None, None

def run_demo():
    """Run the demo."""
    # Register cleanup function
    atexit.register(cleanup)
    
    # Create the demo directory
    os.makedirs("demo", exist_ok=True)
    os.makedirs("demo/keys", exist_ok=True)
    os.makedirs("demo/data", exist_ok=True)
    
    # Create configurations for each identity
    for identity in IDENTITIES:
        create_config(identity)
    
    # Run the API server
    api_process = run_api_server()
    
    # Run the miners
    miner_processes = []
    
    for identity in IDENTITIES:
        miner_process, explorer_process = run_miner(identity)
        
        if miner_process and explorer_process:
            miner_processes.append((miner_process, explorer_process))
        
        # Wait a bit between starting miners
        time.sleep(1)
    
    print("\nDemo is running!")
    print("You can access the miners at:")
    
    for i, identity in enumerate(IDENTITIES):
        port = 5005 + i
        print(f"  {identity['name']}: http://localhost:{port}")
    
    print("\nPress Ctrl+C to stop the demo.")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping demo...")
        cleanup()

if __name__ == "__main__":
    run_demo()
