# src/harashim/payout.py

from typing import Dict, Any, Optional, List
import time

from src.db.manager import db_manager
from src.tokens.ledger import TokenLedger
from src.harashim.contracts import ServiceContract

class ContractPayout:
    """
    Handles token payouts for completed contracts.
    """
    
    def __init__(self, 
                 token_ledger: Optional[TokenLedger] = None,
                 service_contract: Optional[ServiceContract] = None):
        """
        Initialize the ContractPayout.
        
        Args:
            token_ledger: The token ledger
            service_contract: The service contract manager
        """
        self.ledger = token_ledger or TokenLedger()
        self.contracts = service_contract or ServiceContract()
        self.db = db_manager.get_connection()
    
    def process_payout(self, contract_id: str) -> Dict[str, Any]:
        """
        Process a token payout for a completed contract.
        
        Args:
            contract_id: The contract ID
        
        Returns:
            The payout result
        
        Raises:
            Exception: If the contract does not exist, is not completed, or has already been paid out
        """
        # Get the contract
        contract = self.contracts.get_contract(contract_id)
        if not contract:
            raise Exception(f"Contract with ID '{contract_id}' not found")
        
        # Check if the contract is completed
        if contract["status"] != "COMPLETED":
            raise Exception(f"Contract with ID '{contract_id}' is not completed")
        
        # Check if the contract has already been paid out
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM contract_payouts WHERE contract_id = ?",
            (contract_id,)
        )
        if cursor.fetchone():
            raise Exception(f"Contract with ID '{contract_id}' has already been paid out")
        
        # Check if the reward type is eligible for token payout
        if contract["reward_type"] not in ["ONX", "MIKVAH", "ALL"]:
            raise Exception(f"Contract with ID '{contract_id}' has reward type '{contract['reward_type']}' which is not eligible for token payout")
        
        # Get the token ID based on reward type
        token_id = "ONX" if contract["reward_type"] in ["ONX", "ALL"] else contract["token_id"]
        
        # Process the payout
        harash_id = contract["harash_id"]
        sela_id = contract["sela_id"]
        amount = contract["reward_amount"]
        
        # Credit the tokens to the Harash
        self.ledger.credit(harash_id, token_id, amount)
        
        # Record the payout
        payout = {
            "contract_id": contract_id,
            "harash_id": harash_id,
            "sela_id": sela_id,
            "token_id": token_id,
            "amount": amount,
            "paid_at": int(time.time())
        }
        
        cursor.execute(
            """
            INSERT INTO contract_payouts
            (contract_id, harash_id, sela_id, token_id, amount, paid_at)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            (
                payout["contract_id"],
                payout["harash_id"],
                payout["sela_id"],
                payout["token_id"],
                payout["amount"],
                payout["paid_at"]
            )
        )
        self.db.commit()
        
        return payout
    
    def get_payout(self, contract_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the payout for a contract.
        
        Args:
            contract_id: The contract ID
        
        Returns:
            The payout or None if it does not exist
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM contract_payouts WHERE contract_id = ?",
            (contract_id,)
        )
        row = cursor.fetchone()
        
        if not row:
            return None
        
        return {
            "contract_id": row["contract_id"],
            "harash_id": row["harash_id"],
            "sela_id": row["sela_id"],
            "token_id": row["token_id"],
            "amount": row["amount"],
            "paid_at": row["paid_at"]
        }
    
    def get_payouts_by_harash(self, harash_id: str) -> List[Dict[str, Any]]:
        """
        Get all payouts for a Harash.
        
        Args:
            harash_id: The Harash ID
        
        Returns:
            A list of payouts
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM contract_payouts WHERE harash_id = ?",
            (harash_id,)
        )
        rows = cursor.fetchall()
        
        return [{
            "contract_id": row["contract_id"],
            "harash_id": row["harash_id"],
            "sela_id": row["sela_id"],
            "token_id": row["token_id"],
            "amount": row["amount"],
            "paid_at": row["paid_at"]
        } for row in rows]
