#!/usr/bin/env python3

"""
Test script to verify that identity creation generates a blockchain transaction.
"""

import os
import sys
import time
import logging
import uuid
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.test_identity_transaction")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from models.identity import Identity
from models.transaction import Transaction
from data.db import db

def test_identity_creation_transaction():
    """Test that creating an identity generates a blockchain transaction."""
    # Generate a unique name for the test identity
    test_name = f"Test Identity {int(time.time())}"
    test_public_key = f"test_key_{uuid.uuid4().hex}"

    logger.info(f"Creating test identity: {test_name}")

    # Create the identity
    identity = Identity.create(
        name=test_name,
        public_key=test_public_key,
        nation="Israel",
        metadata={
            "purpose": "Testing",
            "tribe": "Judah",
            "dob": "01/01/2000",
            "region": "North America"
        }
    )

    logger.info(f"Created identity: {identity.identity_id}")

    # Check if a transaction was created
    time.sleep(1)  # Give the system time to create the transaction

    # Query the database for all transactions
    query = "SELECT * FROM transactions"
    transactions = db.query(query)

    logger.info(f"Found {len(transactions)} total transactions in the database")
    for tx in transactions:
        logger.info(f"Transaction: {tx}")

    # Query the database for CREATE_IDENTITY transactions
    query = "SELECT * FROM transactions WHERE op = 'CREATE_IDENTITY'"
    create_identity_txs = db.query(query)

    logger.info(f"Found {len(create_identity_txs)} CREATE_IDENTITY transactions in the database")
    for tx in create_identity_txs:
        logger.info(f"CREATE_IDENTITY Transaction: {tx}")

    transactions = create_identity_txs

    # Filter transactions manually since SQLite JSON extraction might not work as expected
    matching_transactions = []
    for tx in transactions:
        try:
            data = json.loads(tx['data'])
            if data.get('identity_id') == identity.identity_id:
                matching_transactions.append(tx)
        except (json.JSONDecodeError, TypeError):
            continue

    transactions = matching_transactions

    if transactions:
        logger.info(f"Found {len(transactions)} transactions for identity {identity.identity_id}")
        for tx in transactions:
            logger.info(f"Transaction ID: {tx['tx_id']}")
            logger.info(f"Operation: {tx['op']}")
            logger.info(f"Sender: {tx['sender']}")
            logger.info(f"Data: {tx['data']}")
        return True
    else:
        logger.error(f"No transactions found for identity {identity.identity_id}")
        return False

def main():
    """Main entry point."""
    logger.info("Testing identity creation transaction...")

    success = test_identity_creation_transaction()

    if success:
        logger.info("Identity creation transaction test passed")
    else:
        logger.error("Identity creation transaction test failed")

if __name__ == "__main__":
    main()
