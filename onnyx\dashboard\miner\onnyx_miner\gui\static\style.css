/* Onnyx Miner GUI Stylesheet */

/* Custom colors */
:root {
  --onnyx-primary: #3498db;
  --onnyx-secondary: #2c3e50;
  --onnyx-success: #2ecc71;
  --onnyx-danger: #e74c3c;
  --onnyx-warning: #f39c12;
  --onnyx-info: #1abc9c;
  --onnyx-light: #ecf0f1;
  --onnyx-dark: #2c3e50;
}

/* Override Bootstrap colors */
.bg-primary {
  background-color: var(--onnyx-primary) !important;
}

.bg-secondary {
  background-color: var(--onnyx-secondary) !important;
}

.bg-success {
  background-color: var(--onnyx-success) !important;
}

.bg-danger {
  background-color: var(--onnyx-danger) !important;
}

.bg-warning {
  background-color: var(--onnyx-warning) !important;
}

.bg-info {
  background-color: var(--onnyx-info) !important;
}

.bg-light {
  background-color: var(--onnyx-light) !important;
}

.bg-dark {
  background-color: var(--onnyx-dark) !important;
}

.btn-primary {
  background-color: var(--onnyx-primary);
  border-color: var(--onnyx-primary);
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.btn-success {
  background-color: var(--onnyx-success);
  border-color: var(--onnyx-success);
}

.btn-success:hover {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-danger {
  background-color: var(--onnyx-danger);
  border-color: var(--onnyx-danger);
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

/* Navbar */
.navbar-dark {
  background-color: var(--onnyx-dark) !important;
}

.navbar-brand {
  font-weight: bold;
}

/* Cards */
.card {
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
}

.card-header {
  background-color: var(--onnyx-light);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-title {
  margin-bottom: 0;
  font-weight: 600;
}

/* Tables */
.table {
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  background-color: var(--onnyx-light);
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
}

/* Footer */
.footer {
  background-color: var(--onnyx-light);
  padding: 1rem 0;
  margin-top: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }
}

/* Log display */
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
