"""
Onnyx Identity Model Tests

This module provides tests for the Onnyx identity model.
"""

import os
import sys
import unittest
import time
import hashlib
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.identity import Identity
from data.db import db

class TestIdentityModel(unittest.TestCase):
    """
    Test the Identity model.
    """

    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_identity_model.db")

        # Set the database path
        db.db_path = self.db_path

        # Create the tables
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
        CREATE TABLE IF NOT EXISTS identities (
            identity_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            public_key TEXT NOT NULL,
            metadata TEXT,
            created_at INTEGER,
            updated_at INTEGER
        )
        """)
        conn.commit()
        conn.close()

    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")

        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")

    def test_create_identity(self):
        """Test creating an identity."""
        # Create an identity directly in the database
        public_key = "0x1234567890abcdef"
        identity_id = hashlib.sha256(public_key.encode()).hexdigest()

        # Insert the identity into the database
        conn = sqlite3.connect(self.db_path)
        conn.execute(
            "INSERT INTO identities (identity_id, name, public_key, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
            (
                identity_id,
                "Test Identity",
                public_key,
                json.dumps({"type": "individual", "description": "Test identity"}),
                int(time.time()),
                int(time.time())
            )
        )
        conn.commit()
        conn.close()

        # Check if the identity was saved to the database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))
        row = cursor.fetchone()
        conn.close()

        print(f"Database row: {row}")

        # Get the identity by ID
        retrieved_identity = Identity.get_by_id(identity_id)

        # Check that the identity was retrieved
        self.assertIsNotNone(retrieved_identity)
        self.assertEqual(retrieved_identity.identity_id, identity_id)
        self.assertEqual(retrieved_identity.name, "Test Identity")
        self.assertEqual(retrieved_identity.public_key, public_key)
        self.assertEqual(retrieved_identity.metadata["type"], "individual")
        self.assertEqual(retrieved_identity.metadata["description"], "Test identity")

if __name__ == "__main__":
    unittest.main()
