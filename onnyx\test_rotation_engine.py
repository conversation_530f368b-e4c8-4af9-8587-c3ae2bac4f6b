#!/usr/bin/env python3
"""
Test the Onnyx Validator Rotation Engine

This script tests the Onnyx Validator Rotation Engine by simulating block mining with multiple Selas.
"""

import os
import sys
import time
import json
import random
from typing import Dict, Any, List

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from consensus.rotation_engine import ValidatorRotationEngine
from identity.registry import IdentityRegistry
from business.sela_registry import SelaRegistry
from trust.etzem_engine import EtzemEngine
from wallet.wallet import Wallet
from consensus.miner import BlockMiner

def setup_test_data():
    """Set up test data for the rotation engine test."""
    # Create identity registry
    identity_registry = IdentityRegistry()

    # Create Sela registry
    sela_registry = SelaRegistry()

    # Create Etzem engine
    etzem_engine = EtzemEngine()

    # Create wallet
    wallet = Wallet()

    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "Alice", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: Alice")

    try:
        bob = identity_registry.register_identity("bob", "Bob", "0x987654321fedcba")
        print(f"Created identity: Bob")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: Bob")

    try:
        charlie = identity_registry.register_identity("charlie", "Charlie", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")

    # Add VALIDATOR_ELIGIBLE_BADGE to identities
    for identity_id in ["alice", "bob", "charlie"]:
        identity = identity_registry.get_identity(identity_id)
        badges = identity.get("badges", [])
        if "VALIDATOR_ELIGIBLE_BADGE" not in badges:
            badges.append("VALIDATOR_ELIGIBLE_BADGE")
            identity["badges"] = badges
            identity_registry.identities[identity_id] = identity
            identity_registry._save()
            print(f"Added VALIDATOR_ELIGIBLE_BADGE to {identity_id}")

    # Create or get test Selas
    try:
        alice_sela = sela_registry.register_sela(
            "alices_salon",
            "Alice's Salon",
            "alice",
            "BUSINESS",
            "SALON_TOKEN"
        )
        identity_registry.link_sela("alice", "alices_salon", "FOUNDER")
        print(f"Created Sela: Alice's Salon")
    except Exception:
        alice_sela = sela_registry.get_sela("alices_salon")
        print(f"Using existing Sela: Alice's Salon")

    try:
        bob_sela = sela_registry.register_sela(
            "bobs_barbershop",
            "Bob's Barbershop",
            "bob",
            "BUSINESS",
            "BARBER_TOKEN"
        )
        identity_registry.link_sela("bob", "bobs_barbershop", "FOUNDER")
        print(f"Created Sela: Bob's Barbershop")
    except Exception:
        bob_sela = sela_registry.get_sela("bobs_barbershop")
        print(f"Using existing Sela: Bob's Barbershop")

    try:
        charlie_sela = sela_registry.register_sela(
            "charlies_cafe",
            "Charlie's Cafe",
            "charlie",
            "BUSINESS",
            "CAFE_TOKEN"
        )
        identity_registry.link_sela("charlie", "charlies_cafe", "FOUNDER")
        print(f"Created Sela: Charlie's Cafe")
    except Exception:
        charlie_sela = sela_registry.get_sela("charlies_cafe")
        print(f"Using existing Sela: Charlie's Cafe")

    # Mock the Etzem engine
    def mock_compute_etzem(identity_id):
        if identity_id == "alice":
            return {
                "identity_id": "alice",
                "consistency": 9.33,
                "tx_score": 20.0,
                "trust_weight": 16.0,
                "badge_bonus": 12,
                "sela_participation": 15,
                "token_impact": 10,
                "etzem": 82.33
            }
        elif identity_id == "bob":
            return {
                "identity_id": "bob",
                "consistency": 6.33,
                "tx_score": 20.0,
                "trust_weight": 10.0,
                "badge_bonus": 4,
                "sela_participation": 10,
                "token_impact": 10,
                "etzem": 60.33
            }
        else:
            return {
                "identity_id": "charlie",
                "consistency": 3.33,
                "tx_score": 4.2,
                "trust_weight": 4.0,
                "badge_bonus": 0,
                "sela_participation": 0,
                "token_impact": 5.0,
                "etzem": 16.53
            }

    etzem_engine.compute_etzem = mock_compute_etzem

    return identity_registry, sela_registry, etzem_engine, wallet

def test_rotation_engine():
    """Test the validator rotation engine."""
    print("\nTesting Validator Rotation Engine...")

    # Set up test data
    identity_registry, sela_registry, etzem_engine, wallet = setup_test_data()

    # Create rotation engine
    rotation_engine = ValidatorRotationEngine("data/test_rotation.json")

    # Set required badges
    rotation_engine.set_required_badges(["VALIDATOR_ELIGIBLE_BADGE"])

    # Set minimum Etzem score
    rotation_engine.set_min_etzem_score(10)

    # Update the queue
    queue = rotation_engine.update_queue()

    print("\nValidator Queue:")
    for i, sela_id in enumerate(queue):
        sela = sela_registry.get_sela(sela_id)
        founder_id = sela.get("founder")
        founder = identity_registry.get_identity(founder_id)
        etzem_score = etzem_engine.compute_etzem(founder_id)["etzem"]
        print(f"  {i+1}. {sela_id} (Founder: {founder_id}, Etzem: {etzem_score:.2f})")

    # Test get_next_validator
    print("\nNext Validators:")
    for height in range(1, 10):
        next_validator = rotation_engine.get_next_validator(height)
        print(f"  Height {height}: {next_validator}")

    # Test is_valid_proposer
    print("\nValid Proposers:")
    for height in range(1, 10):
        next_validator = rotation_engine.get_next_validator(height)
        for sela_id in queue:
            is_valid = rotation_engine.is_valid_proposer(sela_id, height)
            if is_valid:
                print(f"  Height {height}: {sela_id} (Valid)")
            else:
                print(f"  Height {height}: {sela_id} (Invalid - should be {next_validator})")

    # Test mark_proposed
    print("\nMarking Blocks as Proposed:")
    for height in range(1, 10):
        next_validator = rotation_engine.get_next_validator(height)
        rotation_engine.mark_proposed(next_validator, height)
        print(f"  Height {height}: Marked as proposed by {next_validator}")

    # Test rotation status
    status = rotation_engine.get_rotation_status()
    print("\nRotation Status:")
    print(f"  Queue: {status['queue']}")
    print(f"  Last Proposer: {status['last_proposer']}")
    print(f"  Height: {status['height']}")
    print(f"  Last Update: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(status['last_update']))}")
    print(f"  Update Interval: {status['update_interval']} seconds")
    print(f"  Min Etzem Score: {status['min_etzem_score']}")
    print(f"  Required Badges: {status['required_badges']}")

    print("\nValidator Rotation Engine test completed successfully!")

def test_mining_with_rotation():
    """Test mining with validator rotation."""
    print("\nTesting Mining with Validator Rotation...")

    # Set up test data
    identity_registry, sela_registry, etzem_engine, wallet = setup_test_data()

    # Create rotation engine
    rotation_engine = ValidatorRotationEngine("data/test_rotation.json")

    # Set required badges
    rotation_engine.set_required_badges(["VALIDATOR_ELIGIBLE_BADGE"])

    # Set minimum Etzem score
    rotation_engine.set_min_etzem_score(10)

    # Update the queue
    queue = rotation_engine.update_queue()

    # Create block miner
    miner = BlockMiner()

    # Mine blocks
    print("\nMining Blocks:")
    for height in range(1, 10):
        next_validator = rotation_engine.get_next_validator(height)
        sela = sela_registry.get_sela(next_validator)
        founder_id = sela.get("founder")

        print(f"  Height {height}: Mining with {next_validator} (Founder: {founder_id})")

        try:
            # Generate a private key for testing
            private_key = wallet.generate_private_key()

            # Mine a block
            block = miner.mine_block(
                identity_id=founder_id,
                sela_id=next_validator,
                private_key_pem=private_key,
                enforce_rotation=True
            )

            print(f"    Block {block['index']} mined successfully by {next_validator}")
            print(f"    Block hash: {block['hash']}")
            print(f"    Transactions: {len(block['transactions'])}")
        except Exception as e:
            print(f"    Error mining block: {str(e)}")

    print("\nMining with Validator Rotation test completed successfully!")

if __name__ == "__main__":
    test_rotation_engine()
    test_mining_with_rotation()
