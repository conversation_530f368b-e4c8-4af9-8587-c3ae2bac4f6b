# Onnyx Mining System

The Onnyx Mining System is responsible for creating new blocks and adding them to the blockchain. It includes the following components:

## Components

### Block Builder

The Block Builder gathers transactions from the mempool, validates them, and builds a block. It is implemented in the `consensus/miner.py` file.

```python
def create_block(self, proposer_id):
    # Get the latest block
    latest = self.chain.get_latest()
    
    # Get transactions from the mempool
    mempool_txs = self.mempool.get_all()
    
    # Create the coinbase transaction
    coinbase = self.create_coinbase_tx(proposer_id)
    
    # Create the block
    block = {
        "index": latest["index"] + 1,
        "timestamp": int(time.time()),
        "transactions": [coinbase] + mempool_txs,
        "previous_hash": latest["hash"],
        "nonce": 0  # For future PoW
    }
    
    # Calculate the block hash
    block["hash"] = self.hash_block(block)
    
    # Validate the block
    validate_block(block, latest)
    
    # Add the block to the chain
    self.chain.add_block(block)
    
    # Clear the mempool
    self.mempool.clear()
    
    # Credit the block reward to the proposer
    self.ledger.credit(proposer_id, TOKEN_ID, BLOCK_REWARD)
    
    return block
```

### Coinbase Transaction

The Coinbase Transaction is the miner reward for block creation. It is sent to the proposer's identity and is implemented as an `OP_REWARD` transaction.

```python
def create_coinbase_tx(self, proposer_id):
    return {
        "type": "reward",
        "op": "OP_REWARD",
        "txid": f"coinbase-{int(time.time())}-{proposer_id}",
        "to": proposer_id,
        "amount": BLOCK_REWARD,
        "token_id": TOKEN_ID,
        "timestamp": int(time.time())
    }
```

### Chain Advancer

The Chain Advancer adds new blocks to the blockchain after validation. It is implemented in the `node/blockchain.py` file.

```python
def add_block(self, block):
    # Verify that the block's previous_hash matches the latest block's hash
    latest_block = self.get_latest()
    if block["previous_hash"] != latest_block["hash"]:
        return False
    
    # Verify that the block's index is one more than the latest block's index
    if block["index"] != latest_block["index"] + 1:
        return False
    
    # Add the block to the chain
    self.chain.append(block)
    self._save()
    
    return True
```

### CLI / API Support

The Mining System includes CLI and API support for triggering block creation manually. This is useful for development and testing.

#### CLI

```python
def main():
    parser = argparse.ArgumentParser(description="Mine a block on the Onnyx blockchain")
    parser.add_argument("--proposer", type=str, required=True, help="The identity ID of the block proposer")
    parser.add_argument("--pretty", action="store_true", help="Pretty-print the block")
    
    args = parser.parse_args()
    
    # Mine block
    mine_block(args.proposer, args.pretty)
```

#### API

```python
@miner_router.post("/mine")
def mine_block(proposer_id: str = Query(..., description="The identity ID of the block proposer")):
    try:
        block = miner.create_block(proposer_id)
        
        return {
            "status": "block created",
            "index": block["index"],
            "hash": block["hash"],
            "tx_count": len(block["transactions"]),
            "timestamp": block["timestamp"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### VM Validator

The Mining System uses the VM Validator to validate blocks before sealing them. This ensures that only valid blocks are added to the blockchain.

```python
def validate_block(block, previous_block):
    # Validate block structure
    validate_block_structure(block)
    
    # Validate block hash
    validate_block_hash(block)
    
    # Validate block transactions
    validate_block_transactions(block)
    
    # Validate miner reward
    validate_block_miner_reward(block)
    
    # Validate against previous block
    validate_block_against_chain(block, previous_block)
    
    return True
```

## Usage

### Running the API Server

```bash
python run_server.py --port 8888
```

### Adding Test Transactions

```bash
python add_test_tx.py --count 5 --type send --sender alice --recipient bob --token ONX --amount 10
```

### Mining a Block

```bash
python mine_cli.py --proposer alice
```

### API Endpoints

- `GET /api/stats`: Get mining statistics
- `GET /api/latest-blocks/{count}`: Get the latest blocks
- `POST /api/mine?proposer_id=<identity_id>`: Mine a new block

## Testing

### Simple Mining Test

```bash
python simple_mining_test.py
```

### Adding Test Transactions

```bash
python add_test_tx.py --count 5
```

### Mining a Block

```bash
python mine_cli.py --proposer alice
```
