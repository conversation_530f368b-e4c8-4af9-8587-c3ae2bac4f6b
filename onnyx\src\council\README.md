# Council of Twelve Tribes: Core Authority Layer

The Council of Twelve Tribes is a core layer of righteous authority within Onnyx.

> "No one person controls Onnyx. Twelve trusted identities guide its evolution — not through money, but through merit and trust."

## Overview

The Council of Twelve Tribes is a rotating set of respected identities that guide the evolution of the Onnyx blockchain. Each tribe has a seat on the council, and each seat is assigned to an identity.

The twelve tribes are:
- <PERSON>
<PERSON> <PERSON><PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON><PERSON>
<PERSON> <PERSON><PERSON><PERSON>
<PERSON>

## Components

### CouncilRegistry

The `CouncilRegistry` class manages the Council of Twelve Tribes. It provides methods for:

- Assigning seats to identities
- Removing seats from identities
- Getting council information
- Checking if an identity is a councilor

## Councilor Selection

Councilors are selected based on their merit and trust, not their wealth or token holdings. When an identity is assigned a seat on the council, their metadata is updated to include:

```json
{
  "role": "Councilor",
  "tribe": "Judah",
  "soulbound": true
}
```

## Council Authority

The Council of Twelve Tribes has special authority within the Onnyx blockchain:

- **Critical Proposals**: Critical proposals require approval from at least 7 out of 12 councilors to pass.
- **Council-Only Proposals**: Some proposals can be restricted to councilors only, meaning only councilors can vote on them.
- **Council-Only Voting**: Councilors can create proposals that only other councilors can vote on.

## API Endpoints

### Council Endpoints

- `GET /council` - Get the council registry
- `GET /council/councilors` - Get all councilors
- `GET /council/tribes` - Get all tribes
- `GET /council/vacant` - Get vacant seats on the council
- `POST /council/assign` - Assign a seat on the council to an identity
- `POST /council/remove` - Remove a seat from the council
- `GET /council/seat/{tribe}` - Get a seat by tribe
- `GET /council/identity/{identity_id}` - Get a seat by identity ID
- `GET /council/is_councilor/{identity_id}` - Check if an identity is a councilor
- `GET /council/stats` - Get statistics about the council

## Example Usage

### Assigning a Seat

```json
POST /council/assign
{
  "tribe": "Judah",
  "identity_id": "marcus_id",
  "appointed_by": "admin_id",
  "message": "...",
  "signature": "..."
}
```

### Removing a Seat

```json
POST /council/remove
{
  "tribe": "Judah",
  "removed_by": "admin_id",
  "message": "...",
  "signature": "..."
}
```

### Getting Council Information

```
GET /council
```

## Example Output

### Council Registry

```json
{
  "council": {
    "Judah": {
      "identity_id": "marcus_id",
      "appointed_by": "admin_id",
      "appointed_at": 1712341234,
      "soulbound": true
    },
    "Levi": {
      "identity_id": "alice_id",
      "appointed_by": "admin_id",
      "appointed_at": 1712341235,
      "soulbound": true
    }
  },
  "stats": {
    "total_seats": 12,
    "filled_seats": 2,
    "vacant_seats": 10,
    "vacant_tribes": [
      "Reuben",
      "Simeon",
      "Zebulun",
      "Issachar",
      "Dan",
      "Gad",
      "Asher",
      "Naphtali",
      "Joseph",
      "Benjamin"
    ]
  }
}
```

### Councilors

```json
{
  "councilors": [
    {
      "tribe": "Judah",
      "identity_id": "marcus_id",
      "appointed_by": "admin_id",
      "appointed_at": 1712341234,
      "soulbound": true,
      "name": "Marcus Garvey"
    },
    {
      "tribe": "Levi",
      "identity_id": "alice_id",
      "appointed_by": "admin_id",
      "appointed_at": 1712341235,
      "soulbound": true,
      "name": "Alice Johnson"
    }
  ],
  "stats": {
    "total_seats": 12,
    "filled_seats": 2,
    "vacant_seats": 10,
    "vacant_tribes": [
      "Reuben",
      "Simeon",
      "Zebulun",
      "Issachar",
      "Dan",
      "Gad",
      "Asher",
      "Naphtali",
      "Joseph",
      "Benjamin"
    ]
  }
}
```

## Integration with Governance

The Council of Twelve Tribes is integrated with the governance system:

- Councilors can create critical proposals that require council approval
- Councilors can create council-only proposals that only councilors can vote on
- Critical proposals require approval from at least 7 out of 12 councilors to pass

This ensures that important decisions are made by trusted identities, not just those with the most tokens.
