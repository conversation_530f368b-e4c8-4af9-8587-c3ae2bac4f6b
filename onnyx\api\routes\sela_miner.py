"""
Onnyx Sela Miner Routes

This module provides API routes for Sela miners.
"""

from fastapi import APIRouter, Query, HTTPException, Body, Depends
from typing import Dict, Any, List, Optional
import time
import json

from blockchain.node.sela_config import SelaConfig
from blockchain.node.activity_ledger import ActivityLedger
from blockchain.node.rotation_registry import RotationRegistry
from blockchain.node.block_signer import BlockSigner
from blockchain.consensus.miner import BlockMiner
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry

# Create router
router = APIRouter()

# Create instances
sela_config = SelaConfig()
activity_ledger = ActivityLedger()
rotation_registry = RotationRegistry()
block_signer = BlockSigner()
block_miner = BlockMiner()
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()

@router.get("/sela-miner/status")
def get_status() -> Dict[str, Any]:
    """
    Get the status of the Sela miner.
    
    Returns:
        The Sela miner status
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    # Get the validator status
    validator_status = rotation_registry.get_validator_status(sela_config.get("sela_id"))
    
    # Get the mining stats
    mining_stats = block_miner.get_mining_stats()
    
    # Get activity stats
    activities = activity_ledger.get_activities()
    services = activity_ledger.get_services()
    pending_txs = activity_ledger.get_pending_transactions()
    votes = activity_ledger.get_votes()
    
    return {
        "sela_id": sela_config.get("sela_id"),
        "identity_id": sela_config.get("identity_id"),
        "role": sela_config.get("role"),
        "api_port": sela_config.get("api_port"),
        "auto_mine": sela_config.get("auto_mine"),
        "mine_interval": sela_config.get("mine_interval"),
        "validator_status": validator_status,
        "mining_stats": mining_stats,
        "activity_stats": {
            "activities": len(activities),
            "services": len(services),
            "pending_transactions": len(pending_txs),
            "governance_votes": len(votes)
        }
    }

@router.post("/sela-miner/mine")
def mine_block(force: bool = False) -> Dict[str, Any]:
    """
    Mine a new block.
    
    Args:
        force: Force mining even if not the current validator
    
    Returns:
        The mined block
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    # Check if this Sela is the current validator
    current_validator = rotation_registry.get_current_validator()
    
    if current_validator != sela_config.get("sela_id") and not force:
        raise HTTPException(
            status_code=403,
            detail=f"This Sela ({sela_config.get('sela_id')}) is not the current validator ({current_validator})"
        )
    
    # Load the private key
    try:
        private_key_pem = block_signer.load_private_key(sela_config.get("private_key_path"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading private key: {str(e)}")
    
    # Mine a new block
    try:
        block = block_miner.create_block(
            proposer_id=sela_config.get("identity_id"),
            sela_id=sela_config.get("sela_id"),
            private_key_pem=private_key_pem
        )
        
        return {
            "status": "block_mined",
            "block": block
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error mining block: {str(e)}")

@router.post("/sela-miner/service")
def record_service(
    service_type: str,
    description: str,
    recipient_id: Optional[str] = None,
    duration: Optional[int] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Record a service provided by the Sela.
    
    Args:
        service_type: The type of service
        description: A description of the service
        recipient_id: The recipient's identity ID (optional)
        duration: The duration of the service in minutes (optional)
        metadata: Additional metadata for the service (optional)
    
    Returns:
        The recorded service
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    # Record the service
    try:
        service = activity_ledger.record_service(
            service_type=service_type,
            description=description,
            recipient_id=recipient_id,
            duration=duration,
            metadata=metadata
        )
        
        return {
            "status": "service_recorded",
            "service": service
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error recording service: {str(e)}")

@router.post("/sela-miner/vote")
def record_vote(
    scroll_id: str,
    vote: str,
    reason: Optional[str] = None
) -> Dict[str, Any]:
    """
    Record a governance vote.
    
    Args:
        scroll_id: The Voice Scroll ID
        vote: The vote (e.g., "yes", "no", "abstain")
        reason: The reason for the vote (optional)
    
    Returns:
        The recorded vote
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    # Validate the vote
    if vote not in ["yes", "no", "abstain"]:
        raise HTTPException(status_code=400, detail="Invalid vote. Must be 'yes', 'no', or 'abstain'")
    
    # Record the vote
    try:
        vote_record = activity_ledger.record_vote(
            scroll_id=scroll_id,
            vote=vote,
            reason=reason
        )
        
        return {
            "status": "vote_recorded",
            "vote": vote_record
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error recording vote: {str(e)}")

@router.get("/sela-miner/activities")
def get_activities(
    activity_type: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get activities.
    
    Args:
        activity_type: Filter by activity type (optional)
        limit: Maximum number of activities to return
    
    Returns:
        A list of activities
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    activities = activity_ledger.get_activities(activity_type, limit)
    
    return {
        "activities": activities
    }

@router.get("/sela-miner/services")
def get_services(
    service_type: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get service logs.
    
    Args:
        service_type: Filter by service type (optional)
        limit: Maximum number of services to return
    
    Returns:
        A list of service logs
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    services = activity_ledger.get_services(service_type, limit)
    
    return {
        "services": services
    }

@router.get("/sela-miner/pending-transactions")
def get_pending_transactions(
    tx_type: Optional[str] = None,
    status: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get pending transactions.
    
    Args:
        tx_type: Filter by transaction type (optional)
        status: Filter by transaction status (optional)
    
    Returns:
        A list of pending transactions
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    txs = activity_ledger.get_pending_transactions(tx_type, status)
    
    return {
        "transactions": txs
    }

@router.get("/sela-miner/votes")
def get_votes(
    scroll_id: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get governance votes.
    
    Args:
        scroll_id: Filter by Voice Scroll ID (optional)
    
    Returns:
        A list of governance votes
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    votes = activity_ledger.get_votes(scroll_id)
    
    return {
        "votes": votes
    }

@router.get("/sela-miner/validator-status")
def get_validator_status() -> Dict[str, Any]:
    """
    Get the validator status.
    
    Returns:
        The validator status
    """
    # Check if the configuration is valid
    if not sela_config.validate():
        raise HTTPException(status_code=400, detail="Invalid Sela miner configuration")
    
    validator_status = rotation_registry.get_validator_status(sela_config.get("sela_id"))
    
    return validator_status
