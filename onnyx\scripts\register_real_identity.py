#!/usr/bin/env python3
"""
Register Real Identity and Sela

This script registers a real identity and associated Sela business for production use.
"""

import os
import sys
import time
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.models.identity import Identity
from blockchain.wallet.wallet import Wallet

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.register_real_identity")

def register_identity(name: str, email: str) -> tuple:
    """
    Register a real identity with generated keys.
    
    Args:
        name: The person's name
        email: The person's email
        
    Returns:
        Tuple of (identity, private_key, public_key)
    """
    logger.info(f"Registering identity for: {name} ({email})")
    
    # Generate cryptographic keys
    wallet = Wallet()
    private_key, public_key = wallet.generate_keypair()
    
    logger.info(f"Generated keys for {name}")
    logger.info(f"Public key: {public_key[:50]}...")
    
    # Create the identity
    identity = Identity.create(
        name=name,
        email=email,
        public_key=public_key,
        metadata={
            "purpose": "Business Owner",
            "registration_type": "production",
            "verified": True
        }
    )
    
    logger.info(f"✅ Identity registered successfully!")
    logger.info(f"   Identity ID: {identity.identity_id}")
    logger.info(f"   Name: {identity.name}")
    logger.info(f"   Email: {identity.email}")
    
    return identity, private_key, public_key

def register_sela(identity_id: str, sela_name: str, category: str, description: str) -> dict:
    """
    Register a Sela business.
    
    Args:
        identity_id: The owner's identity ID
        sela_name: The business name
        category: The business category
        description: The business description
        
    Returns:
        Sela registration data
    """
    logger.info(f"Registering Sela: {sela_name}")
    
    # Import here to avoid circular imports
    from shared.db.db import db
    
    # Generate Sela ID
    import hashlib
    sela_id = hashlib.sha256(f"{sela_name}_{identity_id}_{int(time.time())}".encode()).hexdigest()[:16]
    
    # Create Sela data
    sela_data = {
        'sela_id': sela_id,
        'identity_id': identity_id,
        'name': sela_name,
        'category': category,
        'stake_amount': 0,  # Will be set when staking is implemented
        'stake_token_id': None,
        'status': 'active',
        'created_at': int(time.time()),
        'metadata': f'{{"description": "{description}", "registration_type": "production"}}'
    }
    
    # Insert into database
    db.insert('selas', sela_data)
    
    logger.info(f"✅ Sela registered successfully!")
    logger.info(f"   Sela ID: {sela_id}")
    logger.info(f"   Name: {sela_name}")
    logger.info(f"   Category: {category}")
    logger.info(f"   Owner: {identity_id}")
    
    return sela_data

def save_credentials(identity_id: str, name: str, private_key: str, public_key: str, sela_id: str = None):
    """
    Save credentials to a secure file.
    
    Args:
        identity_id: The identity ID
        name: The person's name
        private_key: The private key
        public_key: The public key
        sela_id: The Sela ID (optional)
    """
    # Create credentials directory
    creds_dir = os.path.join(os.path.dirname(__file__), '..', 'credentials')
    os.makedirs(creds_dir, exist_ok=True)
    
    # Create filename based on name
    safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_name = safe_name.replace(' ', '_').lower()
    filename = f"{safe_name}_credentials.txt"
    filepath = os.path.join(creds_dir, filename)
    
    # Write credentials
    with open(filepath, 'w') as f:
        f.write(f"ONNYX PRODUCTION CREDENTIALS\n")
        f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"Name: {name}\n")
        f.write(f"Identity ID: {identity_id}\n")
        f.write(f"Public Key: {public_key}\n")
        f.write(f"Private Key: {private_key}\n")
        if sela_id:
            f.write(f"Sela ID: {sela_id}\n")
        f.write(f"\n" + "=" * 50 + "\n")
        f.write(f"KEEP THIS FILE SECURE!\n")
        f.write(f"Your private key controls your identity and assets.\n")
    
    logger.info(f"✅ Credentials saved to: {filepath}")
    logger.warning(f"🔒 IMPORTANT: Keep your credentials file secure!")

def main():
    """Main registration function."""
    print("🚀 ONNYX PRODUCTION IDENTITY & SELA REGISTRATION")
    print("=" * 60)
    
    # Get user input
    name = input("Enter your full name: ").strip()
    email = input("Enter your email address: ").strip()
    
    if not name or not email:
        print("❌ Name and email are required!")
        return
    
    # Register identity
    try:
        identity, private_key, public_key = register_identity(name, email)
    except Exception as e:
        logger.error(f"Failed to register identity: {e}")
        return
    
    # Ask about Sela registration
    register_sela_choice = input("\nWould you like to register a Sela business? (y/n): ").strip().lower()
    
    sela_id = None
    if register_sela_choice in ['y', 'yes']:
        sela_name = input("Enter your business name: ").strip()
        sela_category = input("Enter business category (e.g., Technology, Consulting, Retail): ").strip()
        sela_description = input("Enter business description: ").strip()
        
        if sela_name and sela_category:
            try:
                sela_data = register_sela(identity.identity_id, sela_name, sela_category, sela_description)
                sela_id = sela_data['sela_id']
            except Exception as e:
                logger.error(f"Failed to register Sela: {e}")
    
    # Save credentials
    try:
        save_credentials(identity.identity_id, name, private_key, public_key, sela_id)
    except Exception as e:
        logger.error(f"Failed to save credentials: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 REGISTRATION COMPLETE!")
    print(f"   Identity ID: {identity.identity_id}")
    if sela_id:
        print(f"   Sela ID: {sela_id}")
    print("   Credentials saved securely.")
    print("\n🔒 IMPORTANT: Keep your credentials file safe!")

if __name__ == "__main__":
    main()
