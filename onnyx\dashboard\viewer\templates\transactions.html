{% extends "base.html" %}

{% block title %}Transactions - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Confirmed Transactions</h2>
        <div class="auto-refresh-controls">
            <button id="manual-refresh" class="refresh-button" title="Refresh now">
                <i class="fas fa-sync-alt"></i>
            </button>
            <div class="auto-refresh-toggle">
                <input type="checkbox" id="auto-refresh-toggle">
                <label for="auto-refresh-toggle">Auto</label>
            </div>
            <div class="auto-refresh-interval">
                <input type="number" id="auto-refresh-interval" min="5" max="300" step="5" value="30">
                <span>sec</span>
            </div>
            <div class="auto-refresh-info">
                <span id="auto-refresh-status">Auto-refresh disabled</span>
                <span id="auto-refresh-countdown"></span>
            </div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Transaction ID</th>
                <th>Block</th>
                <th>Type</th>
                <th>From</th>
                <th>To</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            {% for tx in txs %}
            <tr>
                <td><a href="{{ url_for('tx_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a></td>
                <td>{{ tx.block }}</td>
                <td>
                    <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
                </td>
                <td>
                    {% if tx.data.from %}
                    <a href="{{ url_for('identity_detail', identity_id=tx.data.from) }}">{{ tx.data.from[:10] }}...</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>
                    {% if tx.data.to %}
                    <a href="{{ url_for('identity_detail', identity_id=tx.data.to) }}">{{ tx.data.to[:10] }}...</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td>{{ format_timestamp(tx.timestamp) }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="6" style="text-align: center;">No transactions found</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="pagination">
        {% if page > 1 %}
        <a href="{{ url_for('transactions', page=page-1, per_page=per_page) }}"><i class="fas fa-chevron-left"></i> Previous</a>
        {% endif %}

        {% for p in range(max(1, page-2), min(total_pages+1, page+3)) %}
        <a href="{{ url_for('transactions', page=p, per_page=per_page) }}" class="{% if p == page %}active{% endif %}">{{ p }}</a>
        {% endfor %}

        {% if page < total_pages %}
        <a href="{{ url_for('transactions', page=page+1, per_page=per_page) }}">Next <i class="fas fa-chevron-right"></i></a>
        {% endif %}
    </div>
</div>
{% endblock %}
