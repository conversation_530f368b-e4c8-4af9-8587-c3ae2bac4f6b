# src/db/schema/token.py

from src.db.manager import db_manager

def create_token_tables():
    """
    Create the tables for the token system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Create tokens table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS tokens (
        token_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        creator_id TEXT NOT NULL,
        total_supply REAL NOT NULL,
        metadata TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (creator_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create token_balances table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS token_balances (
        identity_id TEXT NOT NULL,
        token_id TEXT NOT NULL,
        balance REAL NOT NULL,
        PRIMARY KEY (identity_id, token_id),
        <PERSON>OREI<PERSON><PERSON> KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIG<PERSON> KEY (token_id) REFERENCES tokens (token_id) ON DELETE CASCADE
    )
    ''')
    
    # Create token_transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS token_transactions (
        tx_id TEXT PRIMARY KEY,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        token_id TEXT NOT NULL,
        amount REAL NOT NULL,
        timestamp INTEGER NOT NULL,
        metadata TEXT,
        FOREIGN KEY (sender_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (receiver_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (token_id) REFERENCES tokens (token_id) ON DELETE CASCADE
    )
    ''')
    
    # Create token_mint_log table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS token_mint_log (
        log_id TEXT PRIMARY KEY,
        token_id TEXT NOT NULL,
        creator_id TEXT NOT NULL,
        approved INTEGER NOT NULL,
        reason TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (token_id) REFERENCES tokens (token_id) ON DELETE CASCADE,
        FOREIGN KEY (creator_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_tokens_creator_id ON tokens (creator_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens (symbol)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_balances_token_id ON token_balances (token_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_sender_id ON token_transactions (sender_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_receiver_id ON token_transactions (receiver_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_token_id ON token_transactions (token_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_transactions_timestamp ON token_transactions (timestamp)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_mint_log_token_id ON token_mint_log (token_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_mint_log_creator_id ON token_mint_log (creator_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_mint_log_approved ON token_mint_log (approved)')
    
    conn.commit()
