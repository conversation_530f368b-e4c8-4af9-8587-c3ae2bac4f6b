#!/usr/bin/env python3
"""
Onnyx Mock API Server

This script provides a mock API server for the Onnyx blockchain.
"""

import os
import sys
import json
import time
import random
from flask import Flask, jsonify, request

app = Flask(__name__)

# Demo directory
DEMO_DIR = "demo"
DATA_DIR = os.path.join(DEMO_DIR, "data")

# Global variables
current_block_height = 0
last_proposer = "genesis"

@app.route("/api/chain/info", methods=["GET"])
def get_chain_info():
    """Get information about the blockchain."""
    global current_block_height, last_proposer
    
    # Load blocks
    blocks_file = os.path.join(DATA_DIR, "blocks.json")
    
    if os.path.exists(blocks_file):
        with open(blocks_file, "r") as f:
            blocks = json.load(f)
        
        if blocks.get("blocks"):
            current_block_height = len(blocks["blocks"]) - 1
            last_proposer = blocks["blocks"][-1].get("proposer", "genesis")
    
    return jsonify({
        "chain_height": current_block_height,
        "last_block_hash": "0x123456789abcdef",
        "last_proposer": last_proposer,
        "sync_status": "synced"
    })

@app.route("/api/blocks/latest", methods=["GET"])
def get_latest_block():
    """Get the latest block."""
    # Load blocks
    blocks_file = os.path.join(DATA_DIR, "blocks.json")
    
    if os.path.exists(blocks_file):
        with open(blocks_file, "r") as f:
            blocks = json.load(f)
        
        if blocks.get("blocks"):
            return jsonify({"block": blocks["blocks"][-1]})
    
    return jsonify({"block": {}})

@app.route("/api/blocks/<int:block_index>", methods=["GET"])
def get_block(block_index):
    """Get a block by index."""
    # Load blocks
    blocks_file = os.path.join(DATA_DIR, "blocks.json")
    
    if os.path.exists(blocks_file):
        with open(blocks_file, "r") as f:
            blocks = json.load(f)
        
        if blocks.get("blocks") and 0 <= block_index < len(blocks["blocks"]):
            return jsonify({"block": blocks["blocks"][block_index]})
    
    return jsonify({"block": {}})

@app.route("/api/blocks/<int:block_index>/transactions", methods=["GET"])
def get_transactions(block_index):
    """Get transactions for a block."""
    # Load blocks
    blocks_file = os.path.join(DATA_DIR, "blocks.json")
    
    if os.path.exists(blocks_file):
        with open(blocks_file, "r") as f:
            blocks = json.load(f)
        
        if blocks.get("blocks") and 0 <= block_index < len(blocks["blocks"]):
            return jsonify({"transactions": blocks["blocks"][block_index].get("transactions", [])})
    
    return jsonify({"transactions": []})

@app.route("/api/identity/<identity_id>", methods=["GET"])
def get_identity(identity_id):
    """Get an identity by ID."""
    # Load identities
    identities_file = os.path.join(DATA_DIR, "identities.json")
    
    if os.path.exists(identities_file):
        with open(identities_file, "r") as f:
            identities = json.load(f)
        
        if identity_id in identities:
            return jsonify({"identity": identities[identity_id]})
    
    return jsonify({"identity": {}})

@app.route("/api/identity/register", methods=["POST"])
def register_identity():
    """Register a new identity."""
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    identity_id = data.get("identity_id")
    name = data.get("name")
    public_key = data.get("public_key")
    
    if not identity_id or not name or not public_key:
        return jsonify({"error": "Missing required fields"}), 400
    
    # Load identities
    identities_file = os.path.join(DATA_DIR, "identities.json")
    identities = {}
    
    if os.path.exists(identities_file):
        with open(identities_file, "r") as f:
            identities = json.load(f)
    
    # Check if the identity already exists
    if identity_id in identities:
        return jsonify({"error": f"Identity with ID '{identity_id}' already exists"}), 400
    
    # Create the identity
    identity = {
        "identity_id": identity_id,
        "name": name,
        "public_key": public_key,
        "created_at": int(time.time()),
        "reputation": 0,
        "etzem_score": 0,
        "badges": [],
        "joined_selas": [],
        "founded_selas": []
    }
    
    # Add the identity
    identities[identity_id] = identity
    
    # Save the identities
    with open(identities_file, "w") as f:
        json.dump(identities, f, indent=2)
    
    return jsonify({"identity": identity})

@app.route("/api/sela/<sela_id>", methods=["GET"])
def get_sela(sela_id):
    """Get a Sela by ID."""
    # Load selas
    selas_file = os.path.join(DATA_DIR, "selas.json")
    
    if os.path.exists(selas_file):
        with open(selas_file, "r") as f:
            selas = json.load(f)
        
        if sela_id in selas:
            return jsonify({"sela": selas[sela_id]})
    
    return jsonify({"sela": {}})

@app.route("/api/sela/register", methods=["POST"])
def register_sela():
    """Register a new Sela."""
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    sela_id = data.get("sela_id")
    name = data.get("name")
    founder = data.get("founder")
    sela_type = data.get("type")
    token_type = data.get("token_type")
    
    if not sela_id or not name or not founder or not sela_type:
        return jsonify({"error": "Missing required fields"}), 400
    
    # Load selas
    selas_file = os.path.join(DATA_DIR, "selas.json")
    selas = {}
    
    if os.path.exists(selas_file):
        with open(selas_file, "r") as f:
            selas = json.load(f)
    
    # Check if the Sela already exists
    if sela_id in selas:
        return jsonify({"error": f"Sela with ID '{sela_id}' already exists"}), 400
    
    # Create the Sela
    sela = {
        "sela_id": sela_id,
        "name": name,
        "founder": founder,
        "type": sela_type,
        "token_id": token_type or f"{sela_id}_TOKEN",
        "created_at": int(time.time()),
        "members": [founder],
        "roles": {founder: "FOUNDER"},
        "services_offered": [],
        "is_validator": True,
        "validator_since": int(time.time()),
        "last_block_proposed": 0
    }
    
    # Add the Sela
    selas[sela_id] = sela
    
    # Save the selas
    with open(selas_file, "w") as f:
        json.dump(selas, f, indent=2)
    
    # Update the founder's identity
    identities_file = os.path.join(DATA_DIR, "identities.json")
    
    if os.path.exists(identities_file):
        with open(identities_file, "r") as f:
            identities = json.load(f)
        
        if founder in identities:
            if "founded_selas" not in identities[founder]:
                identities[founder]["founded_selas"] = []
            
            identities[founder]["founded_selas"].append(sela_id)
            
            # Save the identities
            with open(identities_file, "w") as f:
                json.dump(identities, f, indent=2)
    
    return jsonify({"sela": sela})

@app.route("/api/balances/<identity_id>", methods=["GET"])
def get_balances(identity_id):
    """Get balances for an identity."""
    # Load balances
    balances_file = os.path.join(DATA_DIR, "balances.json")
    
    if os.path.exists(balances_file):
        with open(balances_file, "r") as f:
            balances = json.load(f)
        
        if identity_id in balances:
            return jsonify({"balances": balances[identity_id]})
    
    return jsonify({"balances": {}})

@app.route("/api/stakes/<identity_id>", methods=["GET"])
def get_stakes(identity_id):
    """Get stakes for an identity."""
    # For demo purposes, we'll return some mock stakes
    stakes = [
        {
            "token_id": "ONX",
            "amount": 100,
            "locked_until": int(time.time()) + 86400 * 30
        }
    ]
    
    return jsonify({"stakes": stakes})

@app.route("/api/transactions", methods=["GET"])
def get_transaction_history():
    """Get transaction history."""
    identity_id = request.args.get("identity_id")
    limit = int(request.args.get("limit", 10))
    
    # For demo purposes, we'll return some mock transactions
    transactions = [
        {
            "txid": f"tx_{int(time.time())}_{i}",
            "timestamp": int(time.time()) - i * 3600,
            "op": random.choice(["OP_MINT", "OP_SEND", "OP_BURN"]),
            "from": identity_id,
            "to": f"recipient_{i}",
            "token_id": "ONX",
            "amount": random.randint(1, 100)
        }
        for i in range(limit)
    ]
    
    return jsonify({"transactions": transactions})

@app.route("/api/transactions", methods=["POST"])
def create_transaction():
    """Create a new transaction."""
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    # For demo purposes, we'll just return the transaction with a txid
    tx = data.copy()
    tx["txid"] = f"tx_{int(time.time())}_{random.randint(1000, 9999)}"
    tx["timestamp"] = int(time.time())
    
    return jsonify({"transaction": tx})

@app.route("/api/mine", methods=["POST"])
def mine_block():
    """Mine a new block."""
    global current_block_height, last_proposer
    
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    identity_id = data.get("identity_id")
    sela_id = data.get("sela_id")
    
    if not identity_id or not sela_id:
        return jsonify({"error": "Missing required fields"}), 400
    
    # Load blocks
    blocks_file = os.path.join(DATA_DIR, "blocks.json")
    blocks = {"blocks": []}
    
    if os.path.exists(blocks_file):
        with open(blocks_file, "r") as f:
            blocks = json.load(f)
    
    # Get the latest block
    latest_block = blocks["blocks"][-1] if blocks["blocks"] else {
        "index": -1,
        "hash": "0"
    }
    
    # Create a new block
    block = {
        "index": latest_block["index"] + 1,
        "timestamp": int(time.time()),
        "proposer": identity_id,
        "previous_hash": latest_block["hash"],
        "hash": f"0x{random.randint(1000000000000000, 9999999999999999):x}",
        "nonce": random.randint(1000000, 9999999),
        "signature": f"0x{random.randint(1000000000000000, 9999999999999999):x}",
        "signed_by": identity_id,
        "transactions": []
    }
    
    # Add the block to the chain
    blocks["blocks"].append(block)
    
    # Save the blocks
    with open(blocks_file, "w") as f:
        json.dump(blocks, f, indent=2)
    
    # Update the Sela
    selas_file = os.path.join(DATA_DIR, "selas.json")
    
    if os.path.exists(selas_file):
        with open(selas_file, "r") as f:
            selas = json.load(f)
        
        if sela_id in selas:
            selas[sela_id]["last_block_proposed"] = block["index"]
            
            # Save the selas
            with open(selas_file, "w") as f:
                json.dump(selas, f, indent=2)
    
    # Update global variables
    current_block_height = block["index"]
    last_proposer = identity_id
    
    return jsonify({"block": block})

@app.route("/api/rotation/is-valid-proposer/<sela_id>/<int:block_height>", methods=["GET"])
def is_valid_proposer(sela_id, block_height):
    """Check if a Sela is a valid proposer for a block height."""
    # For demo purposes, we'll return True for all Selas
    return jsonify({
        "is_valid_proposer": True,
        "next_validator": sela_id
    })

@app.route("/api/mining/status", methods=["GET"])
def get_mining_status():
    """Get the mining status."""
    return jsonify({
        "is_mining": False,
        "auto_mining": False,
        "last_mined_block": current_block_height
    })

@app.route("/api/governance/scrolls", methods=["GET"])
def get_scrolls():
    """Get Voice Scrolls."""
    # For demo purposes, we'll return some mock scrolls
    scrolls = [
        {
            "id": f"scroll_{i}",
            "title": f"Voice Scroll {i}",
            "description": f"This is Voice Scroll {i}",
            "category": random.choice(["economic", "technical", "social", "governance"]),
            "status": random.choice(["active", "passed", "rejected"]),
            "outcome": random.choice(["implemented", "pending", "rejected", ""])
        }
        for i in range(1, 6)
    ]
    
    return jsonify({"scrolls": scrolls})

@app.route("/api/governance/propose", methods=["POST"])
def propose_scroll():
    """Propose a new Voice Scroll."""
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    # For demo purposes, we'll just return the scroll with an id
    scroll = data.copy()
    scroll["id"] = f"scroll_{int(time.time())}"
    scroll["status"] = "active"
    scroll["outcome"] = ""
    
    return jsonify({"scroll": scroll})

@app.route("/api/governance/vote", methods=["POST"])
def vote_on_scroll():
    """Vote on a Voice Scroll."""
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    # For demo purposes, we'll just return a success message
    return jsonify({"success": True})

@app.route("/api/peers", methods=["GET"])
def get_peers():
    """Get the list of peers."""
    # For demo purposes, we'll return some mock peers
    peers = [
        {
            "url": f"http://peer{i}.onnyx.io:9000",
            "identity_id": f"peer{i}",
            "last_seen": int(time.time()) - i * 60
        }
        for i in range(1, 6)
    ]
    
    return jsonify({"peers": peers})

@app.route("/api/peers", methods=["POST"])
def add_peer():
    """Add a peer."""
    data = request.json
    
    if not data:
        return jsonify({"error": "No data provided"}), 400
    
    # For demo purposes, we'll just return a success message
    return jsonify({"success": True})

if __name__ == "__main__":
    # Create the demo directory
    os.makedirs(DEMO_DIR, exist_ok=True)
    os.makedirs(DATA_DIR, exist_ok=True)
    
    # Run the server
    app.run(host="127.0.0.1", port=8000, debug=True)
