# src/etzem/engine.py

import time
from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.identity.registry import IdentityRegistry
from src.tokens.ledger import TokenLedger
from src.business.registry import SelaRegistry

class EtzemEngine:
    """
    Engine for calculating Etzem trust scores.
    """
    
    def __init__(self, 
                 identity_registry: Optional[IdentityRegistry] = None,
                 token_ledger: Optional[TokenLedger] = None,
                 sela_registry: Optional[SelaRegistry] = None):
        """
        Initialize the EtzemEngine.
        
        Args:
            identity_registry: The identity registry
            token_ledger: The token ledger
            sela_registry: The Sela registry
        """
        self.identities = identity_registry or IdentityRegistry()
        self.ledger = token_ledger or TokenLedger()
        self.selas = sela_registry or SelaRegistry()
        self.db = db_manager.get_connection()
        
        # Define Etzem thresholds for badges
        self.etzem_thresholds = {
            "TRUSTED": 30,
            "REPUTABLE": 50,
            "ESTEEMED": 70,
            "VENERABLE": 90
        }
    
    def compute_etzem(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Compute the Etzem score for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The Etzem score data or None if the identity does not exist
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            return None
        
        # Get the identity's age in days
        created_at = identity.get("created_at", int(time.time()))
        age_days = (int(time.time()) - created_at) / 86400
        
        # Get the identity's transaction volume
        tx_volume = self._get_transaction_volume(identity_id)
        
        # Get the identity's reputation score
        reputation_score = identity.get("reputation", {}).get("score", 0)
        
        # Get the identity's badges
        badges = identity.get("badges", [])
        
        # Get the identity's Sela count
        sela_count = self._get_sela_count(identity_id)
        
        # Get the identity's founded Selas
        founded_selas = self._get_founded_selas(identity_id)
        
        # Get the identity's activity Etzem
        activity_etzem = self._get_activity_etzem(identity_id)
        
        # Calculate Etzem components
        
        # 1. Consistency: Based on account age and activity consistency
        consistency_base = min(age_days / 30, 1.0) * 10  # Max 10 points from age
        consistency_activity = activity_etzem.get("consistency_score", 0) / 10  # Max 10 points from activity
        consistency = consistency_base + consistency_activity
        
        # 2. Transaction Score: Based on transaction volume
        tx_score = min(tx_volume / 10000, 1.0) * 20  # Max 20 points
        
        # 3. Trust Weight: Based on reputation received
        trust_weight = min(reputation_score / 100, 1.0) * 20  # Max 20 points
        
        # 4. Badge Bonus: Based on badges earned
        badge_bonus = min(len(badges) * 2, 20)  # Max 20 points
        
        # 5. Sela Participation: Based on Sela membership and founding
        sela_bonus = min(sela_count * 5, 10)  # Max 10 points
        founder_bonus = len(founded_selas) * 5  # 5 points per founded Sela
        sela_participation = min(sela_bonus + founder_bonus, 20)  # Max 20 points
        
        # 6. Token Impact: Based on token minting and usage
        token_impact = min(activity_etzem.get("token_impact", 0) / 5, 10)  # Max 10 points
        
        # Calculate total Etzem score
        total = (
            consistency +
            tx_score +
            trust_weight +
            badge_bonus +
            sela_participation +
            token_impact
        )
        
        # Return the Etzem score
        return {
            "identity_id": identity_id,
            "components": {
                "consistency": round(consistency, 2),
                "tx_score": round(tx_score, 2),
                "trust_weight": round(trust_weight, 2),
                "badge_bonus": round(badge_bonus, 2),
                "sela_participation": round(sela_participation, 2),
                "token_impact": round(token_impact, 2)
            },
            "etzem": round(total, 2)
        }
    
    def _get_transaction_volume(self, identity_id: str) -> int:
        """
        Get the transaction volume for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The transaction volume
        """
        # This is a placeholder - the actual implementation would query the transaction history
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT COUNT(*) FROM transactions WHERE sender_id = ? OR receiver_id = ?",
            (identity_id, identity_id)
        )
        result = cursor.fetchone()
        
        if result is None or result[0] is None:
            return 0
        
        return result[0]
    
    def _get_sela_count(self, identity_id: str) -> int:
        """
        Get the number of Selas an identity is a member of.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The Sela count
        """
        # This is a placeholder - the actual implementation would query the Sela registry
        return len(self.selas.get_selas_by_member(identity_id))
    
    def _get_founded_selas(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get the Selas founded by an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            A list of founded Selas
        """
        # This is a placeholder - the actual implementation would query the Sela registry
        return self.selas.get_selas_by_founder(identity_id)
    
    def _get_activity_etzem(self, identity_id: str) -> Dict[str, Any]:
        """
        Get the activity-based Etzem components for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The activity Etzem components
        """
        # This is a placeholder - the actual implementation would query various activity metrics
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT * FROM activity_etzem WHERE identity_id = ?",
            (identity_id,)
        )
        row = cursor.fetchone()
        
        if row is None:
            return {
                "consistency_score": 0,
                "token_impact": 0
            }
        
        return {
            "consistency_score": row["consistency_score"],
            "token_impact": row["token_impact"]
        }
    
    def apply_activity_boost(self, identity_id: str, activity_type: str, boost_value: float) -> Optional[Dict[str, Any]]:
        """
        Apply an activity boost to an identity's Etzem score.
        
        Args:
            identity_id: The identity ID
            activity_type: The type of activity (e.g., "contract_completion", "token_usage", "governance")
            boost_value: The boost value
        
        Returns:
            The updated activity Etzem or None if the identity does not exist
        """
        # Check if the identity exists
        identity = self.identities.get_identity(identity_id)
        if not identity:
            return None
        
        # Get the current activity Etzem
        activity_etzem = self._get_activity_etzem(identity_id)
        
        # Apply the boost based on activity type
        if activity_type == "contract_completion":
            # Contract completion boosts consistency score and token impact
            activity_etzem["consistency_score"] += boost_value * 0.7
            activity_etzem["token_impact"] += boost_value * 0.3
        elif activity_type == "token_usage":
            # Token usage boosts token impact
            activity_etzem["token_impact"] += boost_value
        elif activity_type == "governance":
            # Governance participation boosts consistency score
            activity_etzem["consistency_score"] += boost_value
        else:
            # Unknown activity type - apply a general boost
            activity_etzem["consistency_score"] += boost_value * 0.5
            activity_etzem["token_impact"] += boost_value * 0.5
        
        # Update the activity Etzem in the database
        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT OR REPLACE INTO activity_etzem
            (identity_id, consistency_score, token_impact, updated_at)
            VALUES (?, ?, ?, ?)
            """,
            (
                identity_id,
                activity_etzem["consistency_score"],
                activity_etzem["token_impact"],
                int(time.time())
            )
        )
        self.db.commit()
        
        # Return the updated activity Etzem
        return activity_etzem
