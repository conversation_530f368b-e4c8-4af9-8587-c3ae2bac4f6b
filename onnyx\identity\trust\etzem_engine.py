"""
Onnyx Etzem Trust Growth Engine

This module provides the EtzemEngine class for calculating Etzem trust scores.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional

from identity.registry import IdentityRegistry
from tokens.ledger.ledger import TokenLedger
from sela.registry.sela_registry import SelaRegistry
from governance.etzem_engine import EtzemEngine as ActivityEtzemEngine

class EtzemEngine:
    """
    EtzemEngine calculates Etzem trust scores for identities.
    
    The Etzem trust score is a measure of an identity's trustworthiness and contribution
    to the Onnyx ecosystem based on real economic activity.
    """
    
    def __init__(self, 
                 identity_registry: Optional[IdentityRegistry] = None,
                 token_ledger: Optional[TokenLedger] = None,
                 sela_registry: Optional[SelaRegistry] = None,
                 activity_etzem: Optional[ActivityEtzemEngine] = None):
        """
        Initialize the EtzemEngine.
        
        Args:
            identity_registry: The identity registry
            token_ledger: The token ledger
            sela_registry: The Sela registry
            activity_etzem: The activity-based Etzem engine
        """
        self.identities = identity_registry or IdentityRegistry()
        self.ledger = token_ledger or TokenLedger()
        self.selas = sela_registry or SelaRegistry()
        self.activity_etzem = activity_etzem or ActivityEtzemEngine()
        
        # Define Etzem thresholds for badges
        self.etzem_thresholds = {
            "TRUSTED": 30,
            "REPUTABLE": 50,
            "ESTEEMED": 70,
            "VENERABLE": 90
        }
    
    def compute_etzem(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Compute the Etzem trust score for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The Etzem trust score or None if the identity does not exist
        """
        # Get the identity
        identity = self.identities.get_identity(identity_id)
        if not identity:
            return None
        
        # Get identity attributes
        badges = identity.get("badges", [])
        created_at = identity.get("created_at", int(time.time()))
        age_days = max(1, (int(time.time()) - created_at) // 86400)
        
        # Get transaction volume
        tx_volume = self.ledger.get_total_volume(identity_id)
        
        # Get reputation score
        reputation_score = identity.get("reputation", 0)
        
        # Get Sela participation
        founded_selas = identity.get("founded_selas", [])
        joined_selas = identity.get("joined_selas", [])
        sela_count = len(founded_selas) + len(joined_selas)
        
        # Get activity-based Etzem components
        activity_etzem = self.activity_etzem.calculate_etzem_score(identity_id)
        
        # Calculate Etzem components
        
        # 1. Consistency: Based on account age and activity consistency
        consistency_base = min(age_days / 30, 1.0) * 10  # Max 10 points from age
        consistency_activity = activity_etzem["consistency_score"] / 10  # Max 10 points from activity
        consistency = consistency_base + consistency_activity
        
        # 2. Transaction Score: Based on transaction volume
        tx_score = min(tx_volume / 10000, 1.0) * 20  # Max 20 points
        
        # 3. Trust Weight: Based on reputation received
        trust_weight = min(reputation_score / 100, 1.0) * 20  # Max 20 points
        
        # 4. Badge Bonus: Based on badges earned
        badge_bonus = min(len(badges) * 2, 20)  # Max 20 points
        
        # 5. Sela Participation: Based on Sela membership and founding
        sela_bonus = min(sela_count * 5, 10)  # Max 10 points
        founder_bonus = len(founded_selas) * 5  # 5 points per founded Sela
        sela_participation = min(sela_bonus + founder_bonus, 20)  # Max 20 points
        
        # 6. Token Impact: Based on token minting and usage
        token_impact = min(activity_etzem["token_impact"] / 5, 10)  # Max 10 points
        
        # Calculate total Etzem score
        total = (
            consistency +
            tx_score +
            trust_weight +
            badge_bonus +
            sela_participation +
            token_impact
        )
        
        # Return the Etzem score
        return {
            "identity_id": identity_id,
            "consistency": round(consistency, 2),
            "tx_score": round(tx_score, 2),
            "trust_weight": round(trust_weight, 2),
            "badge_bonus": round(badge_bonus, 2),
            "sela_participation": round(sela_participation, 2),
            "token_impact": round(token_impact, 2),
            "etzem": round(total, 2)
        }
    
    def update_etzem_badges(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Update the Etzem badges for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The updated identity or None if the identity does not exist
        """
        # Compute the Etzem score
        etzem_data = self.compute_etzem(identity_id)
        if not etzem_data:
            return None
        
        # Get the identity
        identity = self.identities.get_identity(identity_id)
        if not identity:
            return None
        
        # Get the Etzem score
        etzem_score = etzem_data["etzem"]
        
        # Update badges based on Etzem thresholds
        for badge, threshold in self.etzem_thresholds.items():
            badge_name = f"ETZEM_{badge}"
            
            # Add badge if Etzem score is above threshold
            if etzem_score >= threshold:
                if "badges" not in identity or badge_name not in identity["badges"]:
                    try:
                        self.identities.add_badge(identity_id, badge_name)
                    except Exception:
                        # Badge already exists
                        pass
            # Remove badge if Etzem score is below threshold
            else:
                if "badges" in identity and badge_name in identity["badges"]:
                    try:
                        self.identities.remove_badge(identity_id, badge_name)
                    except Exception:
                        # Badge doesn't exist
                        pass
        
        # Update the Etzem score in the identity
        self.identities.update_etzem_score(identity_id, etzem_score)
        
        # Return the updated identity
        return self.identities.get_identity(identity_id)
    
    def update_all_etzem_badges(self) -> Dict[str, Dict[str, Any]]:
        """
        Update the Etzem badges for all identities.
        
        Returns:
            A dictionary of updated identities
        """
        updated_identities = {}
        
        for identity_id in self.identities.get_all_identities():
            updated_identity = self.update_etzem_badges(identity_id)
            if updated_identity:
                updated_identities[identity_id] = updated_identity
        
        return updated_identities
    
    def get_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the Etzem leaderboard.
        
        Args:
            limit: The maximum number of identities to return
        
        Returns:
            A list of identities sorted by Etzem score
        """
        # Get all identities
        all_identities = self.identities.get_all_identities()
        
        # Compute Etzem scores for all identities
        etzem_scores = []
        for identity_id in all_identities:
            etzem_data = self.compute_etzem(identity_id)
            if etzem_data:
                etzem_scores.append(etzem_data)
        
        # Sort by Etzem score
        etzem_scores.sort(key=lambda x: x["etzem"], reverse=True)
        
        # Return the top identities
        return etzem_scores[:limit]
