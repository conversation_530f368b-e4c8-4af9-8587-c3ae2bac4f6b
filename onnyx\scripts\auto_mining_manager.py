#!/usr/bin/env python3
"""
ONNYX Auto-Mining Manager

Automated mining system for validators with configurable intervals, performance monitoring,
and automatic restart capabilities. Designed for business validators who want continuous
blockchain participation without manual intervention.
"""

import os
import sys
import json
import time
import signal
import logging
import threading
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import psutil

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from shared.db.db import db
except ImportError:
    # Fallback for testing
    class MockDB:
        def query(self, sql, params=None):
            return []
        def query_one(self, sql, params=None):
            return None
    db = MockDB()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("auto_mining.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("onnyx.auto_mining")

class AutoMiningManager:
    """Manages automated mining operations for ONNYX validators."""

    def __init__(self, config_file="auto_mining_config.json"):
        self.config_file = config_file
        self.mining_processes = {}
        self.monitoring_thread = None
        self.running = False
        self.performance_stats = {}

        # Default configuration
        self.default_config = {
            "enabled": True,
            "mining_interval": 10,  # seconds between blocks
            "restart_on_failure": True,
            "max_restart_attempts": 5,
            "restart_delay": 30,  # seconds
            "performance_monitoring": True,
            "earnings_tracking": True,
            "auto_start_on_boot": False,
            "mining_schedule": {
                "enabled": False,
                "start_time": "09:00",
                "end_time": "17:00",
                "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
            },
            "validators": {}
        }

        # Load configuration after default is set
        self.config = self.load_config()

        logger.info("Auto-Mining Manager initialized")

    def load_config(self):
        """Load auto-mining configuration."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_file}")
                return config
            else:
                logger.info("Using default configuration")
                return self.default_config.copy()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self.default_config.copy()

    def save_config(self):
        """Save current configuration."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            logger.error(f"Error saving config: {e}")

    def get_active_validators(self):
        """Get all active validators from the database."""
        try:
            validators = db.query("""
                SELECT sela_id, name, mining_tier, mining_power, status
                FROM selas
                WHERE status = 'active' AND mining_power > 0
                ORDER BY name ASC
            """)
            return validators
        except Exception as e:
            logger.error(f"Error getting validators: {e}")
            return []

    def configure_validator(self, sela_id, enabled=True, custom_interval=None,
                          schedule_enabled=False, start_time="09:00", end_time="17:00"):
        """Configure auto-mining for a specific validator."""
        try:
            # Get validator info
            validator = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
            if not validator:
                logger.error(f"❌ Validator {sela_id} not found")
                return False

            # Configure validator settings
            validator_config = {
                "enabled": enabled,
                "name": validator['name'],
                "mining_tier": validator.get('mining_tier', 'basic'),
                "mining_power": validator.get('mining_power', 1),
                "custom_interval": custom_interval or self.config.get('mining_interval', 10),
                "schedule": {
                    "enabled": schedule_enabled,
                    "start_time": start_time,
                    "end_time": end_time,
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
                },
                "restart_attempts": 0,
                "last_restart": None,
                "total_blocks_mined": 0,
                "total_earnings": 0.0,
                "uptime_start": None,
                "process_id": None
            }

            self.config["validators"][sela_id] = validator_config
            self.save_config()

            logger.info(f"✅ Configured auto-mining for {validator['name']}")
            return True

        except Exception as e:
            logger.error(f"❌ Error configuring validator {sela_id}: {e}")
            return False

    def is_mining_time(self, sela_id):
        """Check if it's currently mining time for a validator."""
        try:
            validator_config = self.config["validators"].get(sela_id, {})
            schedule = validator_config.get("schedule", {})

            if not schedule.get("enabled", False):
                return True  # Always mine if no schedule

            now = datetime.now()
            current_day = now.strftime("%A").lower()
            current_time = now.strftime("%H:%M")

            # Check if today is a mining day
            if current_day not in schedule.get("days", []):
                return False

            # Check if current time is within mining hours
            start_time = schedule.get("start_time", "00:00")
            end_time = schedule.get("end_time", "23:59")

            return start_time <= current_time <= end_time

        except Exception as e:
            logger.error(f"❌ Error checking mining time for {sela_id}: {e}")
            return True

    def start_validator_mining(self, sela_id):
        """Start mining for a specific validator."""
        try:
            validator_config = self.config["validators"].get(sela_id)
            if not validator_config or not validator_config.get("enabled", False):
                return False

            # Check if already mining
            if sela_id in self.mining_processes:
                process = self.mining_processes[sela_id]
                if process and process.poll() is None:
                    logger.info(f"⚡ {validator_config['name']} already mining")
                    return True

            # Check mining schedule
            if not self.is_mining_time(sela_id):
                logger.info(f"⏰ {validator_config['name']} outside mining hours")
                return False

            # Start mining process
            cmd = [
                sys.executable,
                "scripts/hybrid_production_miner.py",
                sela_id
            ]

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.mining_processes[sela_id] = process
            validator_config["process_id"] = process.pid
            validator_config["uptime_start"] = datetime.now().isoformat()
            validator_config["restart_attempts"] = 0

            logger.info(f"🚀 Started mining for {validator_config['name']} (PID: {process.pid})")
            return True

        except Exception as e:
            logger.error(f"❌ Error starting mining for {sela_id}: {e}")
            return False

    def stop_validator_mining(self, sela_id):
        """Stop mining for a specific validator."""
        try:
            validator_config = self.config["validators"].get(sela_id, {})

            if sela_id in self.mining_processes:
                process = self.mining_processes[sela_id]
                if process and process.poll() is None:
                    process.terminate()
                    time.sleep(2)
                    if process.poll() is None:
                        process.kill()

                    logger.info(f"⏹️  Stopped mining for {validator_config.get('name', sela_id)}")

                del self.mining_processes[sela_id]
                validator_config["process_id"] = None
                validator_config["uptime_start"] = None

            return True

        except Exception as e:
            logger.error(f"❌ Error stopping mining for {sela_id}: {e}")
            return False

    def restart_validator_mining(self, sela_id):
        """Restart mining for a validator."""
        try:
            validator_config = self.config["validators"].get(sela_id, {})

            # Check restart attempts
            max_attempts = self.config.get("max_restart_attempts", 5)
            if validator_config.get("restart_attempts", 0) >= max_attempts:
                logger.warning(f"⚠️  Max restart attempts reached for {validator_config.get('name', sela_id)}")
                return False

            # Stop current mining
            self.stop_validator_mining(sela_id)

            # Wait before restart
            restart_delay = self.config.get("restart_delay", 30)
            logger.info(f"⏳ Waiting {restart_delay}s before restart...")
            time.sleep(restart_delay)

            # Increment restart attempts
            validator_config["restart_attempts"] = validator_config.get("restart_attempts", 0) + 1
            validator_config["last_restart"] = datetime.now().isoformat()

            # Start mining again
            success = self.start_validator_mining(sela_id)
            if success:
                logger.info(f"🔄 Restarted mining for {validator_config.get('name', sela_id)}")

            return success

        except Exception as e:
            logger.error(f"❌ Error restarting mining for {sela_id}: {e}")
            return False

    def monitor_mining_processes(self):
        """Monitor mining processes and restart if needed."""
        while self.running:
            try:
                for sela_id in list(self.mining_processes.keys()):
                    validator_config = self.config["validators"].get(sela_id, {})
                    process = self.mining_processes.get(sela_id)

                    if not process:
                        continue

                    # Check if process is still running
                    if process.poll() is not None:
                        logger.warning(f"⚠️  Mining process stopped for {validator_config.get('name', sela_id)}")

                        # Remove dead process
                        del self.mining_processes[sela_id]

                        # Restart if enabled
                        if (self.config.get("restart_on_failure", True) and
                            validator_config.get("enabled", False)):
                            self.restart_validator_mining(sela_id)

                    # Check mining schedule
                    elif not self.is_mining_time(sela_id):
                        logger.info(f"⏰ Stopping {validator_config.get('name', sela_id)} - outside mining hours")
                        self.stop_validator_mining(sela_id)

                # Start mining for validators that should be mining
                for sela_id, validator_config in self.config["validators"].items():
                    if (validator_config.get("enabled", False) and
                        sela_id not in self.mining_processes and
                        self.is_mining_time(sela_id)):
                        self.start_validator_mining(sela_id)

                # Update performance stats
                self.update_performance_stats()

                # Sleep before next check
                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"❌ Error in monitoring loop: {e}")
                time.sleep(60)  # Wait longer on error

    def update_performance_stats(self):
        """Update performance statistics for all validators."""
        try:
            for sela_id, validator_config in self.config["validators"].items():
                # Get latest validator data from database
                validator = db.query_one("""
                    SELECT mining_rewards_earned, blocks_mined, last_mining_activity
                    FROM selas WHERE sela_id = ?
                """, (sela_id,))

                if validator:
                    validator_config["total_blocks_mined"] = validator.get("blocks_mined", 0)
                    validator_config["total_earnings"] = validator.get("mining_rewards_earned", 0.0)

                    # Calculate uptime
                    if validator_config.get("uptime_start"):
                        start_time = datetime.fromisoformat(validator_config["uptime_start"])
                        uptime = datetime.now() - start_time
                        validator_config["current_uptime"] = str(uptime).split('.')[0]  # Remove microseconds

            self.save_config()

        except Exception as e:
            logger.error(f"❌ Error updating performance stats: {e}")

    def start_auto_mining(self):
        """Start the auto-mining system."""
        if self.running:
            logger.warning("⚠️  Auto-mining already running")
            return

        logger.info("🚀 Starting ONNYX Auto-Mining System")
        self.running = True

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self.monitor_mining_processes)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()

        # Start mining for enabled validators
        for sela_id, validator_config in self.config["validators"].items():
            if validator_config.get("enabled", False):
                self.start_validator_mining(sela_id)

        logger.info("✅ Auto-mining system started")

    def stop_auto_mining(self):
        """Stop the auto-mining system."""
        logger.info("⏹️  Stopping ONNYX Auto-Mining System")
        self.running = False

        # Stop all mining processes
        for sela_id in list(self.mining_processes.keys()):
            self.stop_validator_mining(sela_id)

        # Wait for monitoring thread to finish
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=10)

        logger.info("✅ Auto-mining system stopped")

    def get_status(self):
        """Get current auto-mining status."""
        status = {
            "running": self.running,
            "total_validators": len(self.config["validators"]),
            "active_miners": len(self.mining_processes),
            "validators": {}
        }

        for sela_id, validator_config in self.config["validators"].items():
            is_mining = sela_id in self.mining_processes
            process = self.mining_processes.get(sela_id)

            status["validators"][sela_id] = {
                "name": validator_config.get("name", "Unknown"),
                "enabled": validator_config.get("enabled", False),
                "mining": is_mining,
                "process_id": process.pid if process else None,
                "mining_tier": validator_config.get("mining_tier", "basic"),
                "mining_power": validator_config.get("mining_power", 1),
                "total_blocks": validator_config.get("total_blocks_mined", 0),
                "total_earnings": validator_config.get("total_earnings", 0.0),
                "uptime": validator_config.get("current_uptime", "0:00:00"),
                "restart_attempts": validator_config.get("restart_attempts", 0),
                "last_restart": validator_config.get("last_restart"),
                "schedule_enabled": validator_config.get("schedule", {}).get("enabled", False),
                "in_mining_hours": self.is_mining_time(sela_id)
            }

        return status

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info("🛑 Received shutdown signal")
    if hasattr(signal_handler, 'manager'):
        signal_handler.manager.stop_auto_mining()
    sys.exit(0)

def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="ONNYX Auto-Mining Manager")
    parser.add_argument('action', choices=[
        'start', 'stop', 'status', 'configure', 'enable', 'disable'
    ], help='Action to perform')
    parser.add_argument('--sela-id', help='Validator Sela ID')
    parser.add_argument('--interval', type=int, help='Custom mining interval (seconds)')
    parser.add_argument('--schedule', action='store_true', help='Enable scheduled mining')
    parser.add_argument('--start-time', default='09:00', help='Mining start time (HH:MM)')
    parser.add_argument('--end-time', default='17:00', help='Mining end time (HH:MM)')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')

    args = parser.parse_args()

    manager = AutoMiningManager()
    signal_handler.manager = manager

    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        if args.action == 'start':
            manager.start_auto_mining()
            if args.daemon:
                # Keep running
                while manager.running:
                    time.sleep(60)
            else:
                print("Auto-mining started. Press Ctrl+C to stop.")
                while manager.running:
                    time.sleep(1)

        elif args.action == 'stop':
            manager.stop_auto_mining()

        elif args.action == 'status':
            status = manager.get_status()
            print(json.dumps(status, indent=2))

        elif args.action == 'configure':
            if not args.sela_id:
                print("❌ --sela-id required for configure action")
                return 1
            success = manager.configure_validator(
                args.sela_id,
                enabled=True,
                custom_interval=args.interval,
                schedule_enabled=args.schedule,
                start_time=args.start_time,
                end_time=args.end_time
            )
            if success:
                print("✅ Validator configured successfully")
            else:
                print("❌ Failed to configure validator")
                return 1

        elif args.action == 'enable':
            if not args.sela_id:
                print("❌ --sela-id required for enable action")
                return 1
            manager.config["validators"][args.sela_id]["enabled"] = True
            manager.save_config()
            print("✅ Validator enabled")

        elif args.action == 'disable':
            if not args.sela_id:
                print("❌ --sela-id required for disable action")
                return 1
            manager.stop_validator_mining(args.sela_id)
            manager.config["validators"][args.sela_id]["enabled"] = False
            manager.save_config()
            print("✅ Validator disabled")

        return 0

    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
        manager.stop_auto_mining()
        return 0
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
