"""
Onnyx Database Tests

This module provides tests for the Onnyx database.
"""

import os
import sys
import unittest
import sqlite3
import json

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data.db import Database

class TestDatabase(unittest.TestCase):
    """
    Test the Database class.
    """

    def setUp(self):
        """Set up the test environment."""
        # Create a test database
        self.db_path = os.path.join(os.path.dirname(__file__), "test_db.db")
        self.db = Database(self.db_path)

        # Create a test table
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
        CREATE TABLE IF NOT EXISTS test_table (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            data TEXT
        )
        """)
        conn.commit()
        conn.close()

    def tearDown(self):
        """Clean up the test environment."""
        # Close any open connections
        try:
            conn = sqlite3.connect(self.db_path)
            conn.close()
        except Exception as e:
            print(f"Warning: Failed to close database connection: {e}")

        # Remove the test database
        try:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
        except Exception as e:
            print(f"Warning: Failed to remove test database: {e}")

    def test_insert(self):
        """Test inserting data into a table."""
        # Insert data
        data = {
            "id": "test_id",
            "name": "Test Name",
            "data": json.dumps({"key": "value"})
        }

        row_id = self.db.insert("test_table", data)

        # Check that the data was inserted
        conn = sqlite3.connect(self.db_path)
        cursor = conn.execute("SELECT * FROM test_table WHERE id = ?", ("test_id",))
        row = cursor.fetchone()
        conn.close()

        self.assertIsNotNone(row)
        self.assertEqual(row[0], "test_id")
        self.assertEqual(row[1], "Test Name")
        self.assertEqual(json.loads(row[2]), {"key": "value"})

    def test_query_one(self):
        """Test querying a single row."""
        # Insert data
        data = {
            "id": "test_id",
            "name": "Test Name",
            "data": json.dumps({"key": "value"})
        }

        self.db.insert("test_table", data)

        # Query the data
        row = self.db.query_one("SELECT * FROM test_table WHERE id = ?", ("test_id",))

        # Check that the data was retrieved
        self.assertIsNotNone(row)
        self.assertEqual(row["id"], "test_id")
        self.assertEqual(row["name"], "Test Name")
        self.assertEqual(json.loads(row["data"]), {"key": "value"})

    def test_update(self):
        """Test updating data in a table."""
        # Insert data
        data = {
            "id": "test_id",
            "name": "Test Name",
            "data": json.dumps({"key": "value"})
        }

        self.db.insert("test_table", data)

        # Update the data
        update_data = {
            "name": "Updated Name",
            "data": json.dumps({"key": "updated_value"})
        }

        self.db.update("test_table", update_data, "id = ?", ("test_id",))

        # Query the updated data
        row = self.db.query_one("SELECT * FROM test_table WHERE id = ?", ("test_id",))

        # Check that the data was updated
        self.assertIsNotNone(row)
        self.assertEqual(row["id"], "test_id")
        self.assertEqual(row["name"], "Updated Name")
        self.assertEqual(json.loads(row["data"]), {"key": "updated_value"})

    def test_delete(self):
        """Test deleting data from a table."""
        # Insert data
        data = {
            "id": "test_id",
            "name": "Test Name",
            "data": json.dumps({"key": "value"})
        }

        self.db.insert("test_table", data)

        # Delete the data
        self.db.delete("test_table", "id = ?", ("test_id",))

        # Query the deleted data
        row = self.db.query_one("SELECT * FROM test_table WHERE id = ?", ("test_id",))

        # Check that the data was deleted
        self.assertIsNone(row)

if __name__ == "__main__":
    unittest.main()
