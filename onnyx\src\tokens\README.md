# Onnyx Token Module

This module enables identity-driven tokenization of any asset, role, or unit of value.

## Components

- `schema.py`: Defines the `OnnyxToken` data model and types (`FORKED`, `REPUTATION`, etc.)
- `registry.py`: Stores and manages token creation and retrieval
- `ledger.py`: Tracks balances for all identities and addresses

## Features

- Tokens must be tied to an identity (`creator_identity`)
- Mintable/transferable flags are respected in logic
- Ledger supports credit, debit, and balance check
- Optional metadata (membership tiers, access rules, etc.)

## Example Token

```json
{
  "token_id": "bobbux001",
  "name": "BOB_BUX",
  "symbol": "BOB",
  "creator_identity": "id123",
  "type": "FORKED",
  "supply": 100000,
  "mintable": true,
  "transferable": true,
  "decimals": 2,
  "metadata": {
    "description": "<PERSON>'s coffee rewards token",
    "tiered": true
  }
}
```
