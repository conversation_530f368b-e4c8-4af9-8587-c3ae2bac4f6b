#!/usr/bin/env python3
"""
Check the Onnyx database tables and add a new identity.
"""

import os
import sqlite3
import logging
import json
import time
import hashlib

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("onnyx.check_db")

def main():
    """Main function to check the database tables and add a new identity."""
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), "data", "onnyx.db")

    # Check if the database file exists
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return

    logger.info(f"Checking database at {db_path}")

    # Connect to the database
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()

    logger.info(f"Tables in the database: {[table[0] for table in tables]}")

    # Check if the identities table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='identities'")
    if cursor.fetchone() is None:
        logger.error("Identities table does not exist")
    else:
        logger.info("Identities table exists")

        # Get the schema of the identities table
        cursor.execute("PRAGMA table_info(identities)")
        schema = cursor.fetchall()
        logger.info(f"Identities table schema: {[dict(col) for col in schema]}")

        # Get the number of rows in the identities table
        cursor.execute("SELECT COUNT(*) FROM identities")
        count = cursor.fetchone()[0]
        logger.info(f"Number of rows in identities table: {count}")

        # Get all identities
        cursor.execute("SELECT * FROM identities")
        identities = cursor.fetchall()
        logger.info(f"Identities: {[dict(identity) for identity in identities]}")

        # Add the new identity from the web form
        try:
            # Check if the identity already exists
            cursor.execute("SELECT * FROM identities WHERE name = ?", ("Serenity Martin",))
            existing = cursor.fetchone()

            if existing:
                logger.info(f"Identity already exists: {dict(existing)}")
            else:
                # Create the identity
                public_key = "tMzZxzXPqwEGkgCS..."
                identity_id = hashlib.sha256(public_key.encode()).hexdigest()

                metadata = {
                    "nation": "Israel",
                    "tribe": "Benjamin",
                    "dob": "08/14/2021",
                    "region": "North America",
                    "purpose": "Teacher",
                    "soul_seal": "hi hi",
                    "description": "Just a 3 year old",
                    "created_at": int(time.time())
                }

                # Insert the identity
                cursor.execute(
                    "INSERT INTO identities (identity_id, name, public_key, nation, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                    (identity_id, "Serenity Martin", public_key, "Israel", json.dumps(metadata), int(time.time()))
                )
                conn.commit()

                logger.info(f"Added new identity: {identity_id}")

                # Verify the identity was added
                cursor.execute("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))
                new_identity = cursor.fetchone()
                logger.info(f"New identity: {dict(new_identity) if new_identity else None}")
        except Exception as e:
            logger.error(f"Error adding identity: {str(e)}")
            conn.rollback()

    # Close the connection
    conn.close()

if __name__ == "__main__":
    main()
