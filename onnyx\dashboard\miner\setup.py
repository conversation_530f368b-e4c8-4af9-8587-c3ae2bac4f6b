from setuptools import setup, find_packages

setup(
    name="onnyx-miner",
    version="0.1.0",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        "typer",
        "cryptography",
        "pydantic",
        "flask",
        "requests",
        "pyyaml"
    ],
    entry_points={
        "console_scripts": [
            "onnyx=onnyx_miner.cli:app"
        ]
    },
    package_data={
        "onnyx_miner.gui": ["templates/*", "static/*"],
        "": ["templates/*"]
    },
    description="Onnyx Miner Node CLI",
    author="Onnyx Labs",
    author_email="<EMAIL>",
    url="https://github.com/onnyx/onnyx-miner",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
)
