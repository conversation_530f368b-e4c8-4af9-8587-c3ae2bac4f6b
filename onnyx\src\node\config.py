# src/node/config.py

import os
import json
import uuid
from typing import List, Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.node.config")

# Default configuration
DEFAULT_CONFIG = {
    "node_id": f"onnyx_node_{uuid.uuid4().hex[:8]}",
    "node_port": 8080,
    "node_host": "localhost",
    "node_peers": [
        "ws://localhost:8081",
        "ws://localhost:8082"
    ],
    "node_public_key": "",
    "node_private_key": "",
    "data_dir": "data",
    "max_peers": 10,
    "sync_interval": 60,  # seconds
    "broadcast_interval": 30,  # seconds
    "mempool_sync_interval": 15,  # seconds
    "block_sync_interval": 60,  # seconds
    "peer_discovery_interval": 300,  # seconds
    "heartbeat_interval": 30,  # seconds
    "connection_timeout": 10,  # seconds
    "max_mempool_size": 1000,
    "max_block_size": 1000,
    "max_message_size": 1024 * 1024,  # 1MB
    "log_level": "INFO"
}

class NodeConfig:
    """
    Configuration for the Onnyx node.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the node configuration.
        
        Args:
            config_path: Path to the configuration file. If None, a default path will be used.
        """
        # Set up configuration path
        if config_path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.config_path = os.path.join(data_dir, "node_config.json")
        else:
            self.config_path = config_path
        
        # Initialize configuration with defaults
        self.config = DEFAULT_CONFIG.copy()
        
        # Load configuration from file if it exists
        self._load_config()
        
        # Set up logging level
        logging.getLogger("onnyx").setLevel(getattr(logging, self.config["log_level"]))
    
    def _load_config(self):
        """Load the configuration from the file."""
        try:
            if os.path.exists(self.config_path) and os.path.getsize(self.config_path) > 0:
                with open(self.config_path, "r") as f:
                    loaded_config = json.load(f)
                    # Update the default config with the loaded values
                    self.config.update(loaded_config)
                    logger.info(f"Loaded configuration from {self.config_path}")
            else:
                # Save the default configuration
                self._save_config()
                logger.info(f"Created default configuration at {self.config_path}")
        except Exception as e:
            logger.error(f"Error loading node configuration: {str(e)}")
    
    def _save_config(self):
        """Save the configuration to the file."""
        try:
            with open(self.config_path, "w") as f:
                json.dump(self.config, f, indent=2)
                logger.info(f"Saved configuration to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving node configuration: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.
        
        Args:
            key: The configuration key.
            default: The default value to return if the key is not found.
            
        Returns:
            The configuration value.
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """
        Set a configuration value.
        
        Args:
            key: The configuration key.
            value: The configuration value.
        """
        self.config[key] = value
        self._save_config()
    
    def add_peer(self, peer_url: str):
        """
        Add a peer to the list of peers.
        
        Args:
            peer_url: The URL of the peer to add.
        """
        if peer_url not in self.config["node_peers"]:
            self.config["node_peers"].append(peer_url)
            self._save_config()
            logger.info(f"Added peer: {peer_url}")
    
    def remove_peer(self, peer_url: str):
        """
        Remove a peer from the list of peers.
        
        Args:
            peer_url: The URL of the peer to remove.
        """
        if peer_url in self.config["node_peers"]:
            self.config["node_peers"].remove(peer_url)
            self._save_config()
            logger.info(f"Removed peer: {peer_url}")
    
    def get_peers(self) -> List[str]:
        """
        Get the list of peers.
        
        Returns:
            The list of peer URLs.
        """
        return self.config["node_peers"]
    
    def get_node_id(self) -> str:
        """
        Get the node ID.
        
        Returns:
            The node ID.
        """
        return self.config["node_id"]
    
    def get_node_url(self) -> str:
        """
        Get the node URL.
        
        Returns:
            The node URL.
        """
        return f"ws://{self.config['node_host']}:{self.config['node_port']}"
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get all configuration values.
        
        Returns:
            All configuration values.
        """
        return self.config.copy()


# Create a global instance of the configuration
node_config = NodeConfig()

# Export configuration values for easy access
NODE_ID = node_config.get("node_id")
NODE_PORT = node_config.get("node_port")
NODE_HOST = node_config.get("node_host")
NODE_PEERS = node_config.get("node_peers")
NODE_PUBLIC_KEY = node_config.get("node_public_key")
NODE_PRIVATE_KEY = node_config.get("node_private_key")
DATA_DIR = node_config.get("data_dir")
MAX_PEERS = node_config.get("max_peers")
SYNC_INTERVAL = node_config.get("sync_interval")
BROADCAST_INTERVAL = node_config.get("broadcast_interval")
MEMPOOL_SYNC_INTERVAL = node_config.get("mempool_sync_interval")
BLOCK_SYNC_INTERVAL = node_config.get("block_sync_interval")
PEER_DISCOVERY_INTERVAL = node_config.get("peer_discovery_interval")
HEARTBEAT_INTERVAL = node_config.get("heartbeat_interval")
CONNECTION_TIMEOUT = node_config.get("connection_timeout")
MAX_MEMPOOL_SIZE = node_config.get("max_mempool_size")
MAX_BLOCK_SIZE = node_config.get("max_block_size")
MAX_MESSAGE_SIZE = node_config.get("max_message_size")
LOG_LEVEL = node_config.get("log_level")
