/* Onnyx Nations Selection Styles */

/* Nations Grid */
.nations-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (min-width: 768px) {
    .nations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1200px) {
    .nations-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Nation Card */
.nation-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.nation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.nation-header {
    background-color: var(--dark-color);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nation-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.nation-symbol {
    font-size: 2rem;
}

.nation-content {
    padding: 1.5rem;
}

.nation-father {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.nation-description {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.nation-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 992px) {
    .nation-details {
        grid-template-columns: repeat(3, 1fr);
    }
}

.nation-covenant h4,
.nation-economy h4,
.nation-jurisdiction h4 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.nation-covenant p,
.nation-economy p,
.nation-jurisdiction p {
    font-size: 0.9rem;
    line-height: 1.5;
}

.nation-tribes h4 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.tribes-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

@media (min-width: 576px) {
    .tribes-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

.tribes-list li {
    font-size: 0.9rem;
    padding: 0.25rem 0;
}

.select-nation-btn {
    display: block;
    width: 100%;
    padding: 0.75rem;
    margin-top: 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.select-nation-btn:hover {
    background-color: var(--secondary-color);
}

/* Nation Selection Info */
.nation-selection-info {
    margin-bottom: 3rem;
}

.nation-selection-info h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.info-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .info-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

.info-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.info-card h3 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.info-card p {
    line-height: 1.6;
}

/* Navigation Buttons */
.navigation-buttons {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.nav-button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.nav-button.back {
    background-color: var(--light-color);
    color: var(--dark-color);
}

.nav-button.back:hover {
    background-color: #ddd;
}

.nav-button.next {
    background-color: var(--primary-color);
    color: white;
}

.nav-button.next:hover {
    background-color: var(--secondary-color);
}

/* Tribe Selection Modal */
.tribe-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.tribe-modal.active {
    display: flex;
}

.tribe-modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    padding: 2rem;
}

.tribe-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.tribe-modal-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.tribe-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--dark-color);
}

.tribe-list {
    margin-bottom: 1.5rem;
}

.tribe-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.tribe-option:hover {
    background-color: var(--light-color);
}

.tribe-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.tribe-option-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.tribe-option-info {
    flex: 1;
}

.tribe-option-info h4 {
    margin: 0 0 0.25rem 0;
    color: var(--dark-color);
}

.tribe-option-info p {
    margin: 0;
    font-size: 0.9rem;
}

.tribe-modal-actions {
    display: flex;
    justify-content: flex-end;
}

.tribe-modal-confirm {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.tribe-modal-confirm:hover {
    background-color: var(--secondary-color);
}
