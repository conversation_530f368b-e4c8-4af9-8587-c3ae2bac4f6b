<!DOCTYPE html>
<html>
<head>
  <title>Onnyx Miner Panel</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="{{ url_for('home') }}">Onnyx Miner</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('home') }}">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('wallet') }}">Wallet</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('governance') }}">Governance</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('logs') }}">Logs</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('sela') }}">Sela</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container mt-4">
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    {% block content %}{% endblock %}
  </div>

  <footer class="footer mt-5 py-3 bg-light">
    <div class="container text-center">
      <span class="text-muted">Onnyx Miner &copy; 2025</span>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  {% block scripts %}{% endblock %}
</body>
</html>
