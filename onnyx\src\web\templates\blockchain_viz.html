{% extends "base.html" %}

{% block title %}Onnyx Blockchain Visualization{% endblock %}

{% block head %}
<style>
    .viz-container {
        position: relative;
        width: 100%;
        height: 80vh;
        background-color: #000;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid var(--border-color);
    }

    /* Matrix background effect */
    .matrix-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        opacity: 0.15;
        pointer-events: none;
    }

    .matrix-column {
        position: absolute;
        top: 0;
        width: 20px;
        color: #0f0;
        font-size: 1.2rem;
        text-align: center;
        animation: matrix-rain linear infinite;
    }

    .viz-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: rgba(0, 0, 0, 0.7);
        border-bottom: 1px solid var(--border-color);
    }

    .viz-header-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .theme-selector {
        display: flex;
        gap: 0.5rem;
    }

    .theme-btn {
        padding: 0.3rem 0.8rem;
        border-radius: 4px;
        border: 1px solid var(--primary-color);
        background-color: transparent;
        color: white;
        cursor: pointer;
        transition: all 0.3s;
    }

    .theme-btn.active {
        background-color: var(--primary-color);
        color: white;
    }

    .viz-header-right {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: white;
    }

    .viz-stats {
        font-weight: bold;
    }

    .viz-stats .blocks {
        color: var(--primary-color);
    }

    .viz-stats .time {
        color: var(--secondary-color);
    }

    .viz-stats .propagation-status {
        margin-left: 15px;
        padding: 3px 8px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
        font-size: 0.9em;
        color: #4CAF50;
        border: 1px solid #333;
    }

    #blockchain-canvas {
        width: 100%;
        height: calc(100% - 60px);
        position: relative;
    }

    .viz-legend {
        display: flex;
        justify-content: center;
        gap: 2rem;
        padding: 1rem;
        background-color: rgba(0, 0, 0, 0.7);
        border-top: 1px solid var(--border-color);
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: white;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .legend-color.sela {
        background-color: #FFD700;
    }

    .legend-color.identity {
        background-color: #8A2BE2;
    }

    .legend-color.block {
        background-color: #4B0082;
    }

    .viz-description {
        text-align: center;
        color: #aaa;
        margin: 1.5rem 0;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .node {
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.3s, box-shadow 0.3s;
        animation: float 15s infinite ease-in-out;
    }

    .node:hover {
        transform: scale(1.5);
        z-index: 10;
    }

    .node.sela {
        background-color: #FFD700;
        box-shadow: 0 0 10px #FFD700;
        animation-delay: 0s;
    }

    .node.identity {
        background-color: #8A2BE2;
        box-shadow: 0 0 5px #8A2BE2;
        animation-delay: 2s;
    }

    .node.block {
        background-color: #4B0082;
        box-shadow: 0 0 5px #4B0082;
        animation-delay: 4s;
    }

    @keyframes float {
        0% {
            transform: translate(0, 0);
        }
        25% {
            transform: translate(10px, 10px);
        }
        50% {
            transform: translate(0, 20px);
        }
        75% {
            transform: translate(-10px, 10px);
        }
        100% {
            transform: translate(0, 0);
        }
    }

    .edge {
        position: absolute;
        height: 1px;
        background-color: rgba(138, 43, 226, 0.3);
        transform-origin: 0 0;
        pointer-events: none;
        animation: pulse-edge 4s infinite;
    }

    @keyframes pulse-edge {
        0% {
            opacity: 0.2;
            height: 1px;
        }
        50% {
            opacity: 0.8;
            height: 2px;
        }
        100% {
            opacity: 0.2;
            height: 1px;
        }
    }

    .tooltip {
        position: absolute;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        z-index: 100;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s;
        border: 1px solid var(--primary-color);
        max-width: 250px;
    }

    .pulse {
        animation: pulse-animation 4s infinite;
        z-index: 5;
    }

    @keyframes pulse-animation {
        0% {
            box-shadow: 0 0 0 0 rgba(138, 43, 226, 0.7);
            transform: scale(1);
        }
        20% {
            box-shadow: 0 0 0 10px rgba(138, 43, 226, 0.5);
            transform: scale(1.3);
        }
        40% {
            box-shadow: 0 0 0 20px rgba(138, 43, 226, 0.3);
            transform: scale(1.5);
        }
        60% {
            box-shadow: 0 0 0 30px rgba(138, 43, 226, 0.2);
            transform: scale(1.3);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(138, 43, 226, 0);
            transform: scale(1);
        }
    }

    /* Add data flow animation */
    .data-particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background-color: rgba(255, 215, 0, 0.8);
        border-radius: 50%;
        pointer-events: none;
        z-index: 4;
        animation: move-particle 3s linear;
    }

    @keyframes move-particle {
        0% {
            opacity: 0;
            transform: scale(0.5);
        }
        10% {
            opacity: 1;
            transform: scale(1);
        }
        90% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0.5);
        }
    }

    @keyframes matrix-rain {
        0% {
            transform: translateY(-100%);
        }
        100% {
            transform: translateY(100vh);
        }
    }

    .theme-controls {
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .theme-controls button {
        padding: 0.5rem 1rem;
        margin: 0 0.5rem;
        border-radius: 4px;
        background-color: var(--card-bg);
        border: 1px solid var(--border-color);
        color: var(--light-text);
        cursor: pointer;
        transition: all 0.3s;
    }

    .theme-controls button:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .theme-controls button.active {
        background-color: var(--primary-color);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<h1>Onnyx Blockchain Visualization</h1>

<div class="theme-controls">
    <button id="liveViewBtn" class="active">Live View</button>
    <button id="refreshBtn" class="refresh-btn">↻ Refresh Data</button>
    <button id="autoRefreshBtn">Auto Refresh: Off</button>
    <span id="refresh-notification" class="refresh-notification"></span>
</div>

<style>
    .refresh-btn {
        background-color: var(--primary-color) !important;
        color: white !important;
        font-weight: bold;
        position: relative;
        overflow: hidden;
    }

    .refresh-btn:hover {
        background-color: #7b27c7 !important;
    }

    .refresh-btn:active {
        transform: scale(0.95);
    }

    .refresh-notification {
        display: inline-block;
        margin-left: 10px;
        padding: 5px 10px;
        border-radius: 4px;
        background-color: rgba(0, 0, 0, 0.7);
        color: #4CAF50;
        font-size: 0.9em;
        opacity: 0;
        transition: opacity 0.3s;
    }
</style>

<div class="viz-container">
    <div class="matrix-bg" id="matrix-bg"></div>
    <div class="viz-header">
        <div class="viz-header-left">
            <span>Theme:</span>
            <div class="theme-selector">
                <button class="theme-btn active" data-theme="matrix">Matrix</button>
                <button class="theme-btn" data-theme="tribal">Tribal</button>
                <button class="theme-btn" data-theme="light">Light</button>
            </div>
        </div>
        <div class="viz-header-right">
            <div class="viz-stats">
                Blocks: <span class="blocks">{{ blocks_count }}</span>
                Last Block: <span class="time">{{ last_block_time }}</span>
                <span class="propagation-status" id="propagation-status">Propagation: 1-5s</span>
            </div>
        </div>
    </div>

    <div id="blockchain-canvas"></div>

    <div class="viz-legend">
        <div class="legend-item">
            <div class="legend-color sela"></div>
            <span>Sela Businesses</span>
        </div>
        <div class="legend-item">
            <div class="legend-color identity"></div>
            <span>Identities</span>
        </div>
        <div class="legend-item">
            <div class="legend-color block"></div>
            <span>Blocks</span>
        </div>
    </div>
</div>

<div class="viz-description">
    <p>This visualization represents the Onnyx blockchain network in real-time. Watch as new blocks are created by Sela validators and propagate through the network of identities.</p>
    <p>Hover over nodes to see their details. Each pulse represents a new block being added to the chain.</p>
    <p>The propagation time (1-5 seconds) shows how long it takes for data to travel between nodes in the network. Shorter distances propagate faster, while longer distances take more time, simulating real network latency.</p>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('blockchain-canvas');
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    document.body.appendChild(tooltip);

    // Data from server
    const identities = {{ identities|tojson }};
    const blocks = {{ blocks|tojson }};

    // Theme buttons
    const themeButtons = document.querySelectorAll('.theme-btn');
    let currentTheme = 'matrix';

    // Auto-refresh variables
    let autoRefreshInterval = null;
    let isAutoRefreshOn = false;

    // Create matrix background effect
    createMatrixBackground();

    themeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            themeButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentTheme = this.dataset.theme;

            // Update matrix color based on theme
            const matrixColumns = document.querySelectorAll('.matrix-column');
            if (currentTheme === 'matrix') {
                matrixColumns.forEach(col => col.style.color = '#0f0'); // Green
            } else if (currentTheme === 'tribal') {
                matrixColumns.forEach(col => col.style.color = '#ffd700'); // Gold
            } else if (currentTheme === 'light') {
                matrixColumns.forEach(col => col.style.color = '#8a2be2'); // Purple
            }

            updateVisualization();
        });
    });

    // Create nodes and edges
    function updateVisualization() {
        // Clear canvas
        canvas.innerHTML = '';

        // Create nodes for identities
        identities.forEach((identity, index) => {
            const node = document.createElement('div');
            node.className = 'node identity';
            node.style.left = `${20 + Math.random() * 60}%`;
            node.style.top = `${20 + Math.random() * 60}%`;

            node.addEventListener('mouseenter', function(e) {
                tooltip.innerHTML = `
                    <strong>${identity.name}</strong><br>
                    ID: ${identity.identity_id.substring(0, 10)}...<br>
                    Nation: ${identity.nation || 'Unknown'}<br>
                    Reputation: ${identity.reputation || 0}
                `;
                tooltip.style.left = `${e.pageX + 10}px`;
                tooltip.style.top = `${e.pageY + 10}px`;
                tooltip.style.opacity = 1;
            });

            node.addEventListener('mouseleave', function() {
                tooltip.style.opacity = 0;
            });

            canvas.appendChild(node);
        });

        // Create nodes for blocks (just a few for visualization)
        for (let i = 0; i < 5; i++) {
            const node = document.createElement('div');
            node.className = 'node block';
            node.style.left = `${10 + Math.random() * 80}%`;
            node.style.top = `${10 + Math.random() * 80}%`;

            node.addEventListener('mouseenter', function(e) {
                tooltip.innerHTML = `
                    <strong>Block #${i+1}</strong><br>
                    Hash: ${'0x' + Math.random().toString(16).substring(2, 10)}...<br>
                    Transactions: ${Math.floor(Math.random() * 10)}<br>
                    Time: ${new Date().toLocaleTimeString()}
                `;
                tooltip.style.left = `${e.pageX + 10}px`;
                tooltip.style.top = `${e.pageY + 10}px`;
                tooltip.style.opacity = 1;
            });

            node.addEventListener('mouseleave', function() {
                tooltip.style.opacity = 0;
            });

            canvas.appendChild(node);
        }

        // Create nodes for Selas (just a few for visualization)
        for (let i = 0; i < 3; i++) {
            const node = document.createElement('div');
            node.className = 'node sela';
            node.style.left = `${20 + Math.random() * 60}%`;
            node.style.top = `${20 + Math.random() * 60}%`;

            node.addEventListener('mouseenter', function(e) {
                tooltip.innerHTML = `
                    <strong>Sela Business #${i+1}</strong><br>
                    Owner: ${identities[i % identities.length].name}<br>
                    Status: Active<br>
                    Validator: Yes
                `;
                tooltip.style.left = `${e.pageX + 10}px`;
                tooltip.style.top = `${e.pageY + 10}px`;
                tooltip.style.opacity = 1;
            });

            node.addEventListener('mouseleave', function() {
                tooltip.style.opacity = 0;
            });

            canvas.appendChild(node);
        }

        // Create random edges between nodes
        const nodes = canvas.querySelectorAll('.node');
        for (let i = 0; i < nodes.length; i++) {
            for (let j = i + 1; j < nodes.length; j++) {
                if (Math.random() > 0.7) continue; // Only create some edges

                const nodeA = nodes[i];
                const nodeB = nodes[j];

                const rectA = nodeA.getBoundingClientRect();
                const rectB = nodeB.getBoundingClientRect();

                const canvasRect = canvas.getBoundingClientRect();

                const x1 = rectA.left + rectA.width/2 - canvasRect.left;
                const y1 = rectA.top + rectA.height/2 - canvasRect.top;
                const x2 = rectB.left + rectB.width/2 - canvasRect.left;
                const y2 = rectB.top + rectB.height/2 - canvasRect.top;

                const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                const edge = document.createElement('div');
                edge.className = 'edge';
                edge.style.width = `${length}px`;
                edge.style.left = `${x1}px`;
                edge.style.top = `${y1}px`;
                edge.style.transform = `rotate(${angle}deg)`;

                canvas.appendChild(edge);
            }
        }

        // Add pulse effect to a random block node
        const blockNodes = canvas.querySelectorAll('.node.block');
        if (blockNodes.length > 0) {
            const randomIndex = Math.floor(Math.random() * blockNodes.length);
            blockNodes[randomIndex].classList.add('pulse');
        }

        // Create data flow particles
        createDataFlowParticles();
    }

    // Function to create data flow particles
    function createDataFlowParticles() {
        const nodes = canvas.querySelectorAll('.node');
        if (nodes.length < 2) return;

        // Create 5 random data flows
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                // Select random source and target nodes
                const sourceIndex = Math.floor(Math.random() * nodes.length);
                let targetIndex;
                do {
                    targetIndex = Math.floor(Math.random() * nodes.length);
                } while (targetIndex === sourceIndex);

                const sourceNode = nodes[sourceIndex];
                const targetNode = nodes[targetIndex];

                // Get positions
                const sourceRect = sourceNode.getBoundingClientRect();
                const targetRect = targetNode.getBoundingClientRect();
                const canvasRect = canvas.getBoundingClientRect();

                const sourceX = sourceRect.left + sourceRect.width/2 - canvasRect.left;
                const sourceY = sourceRect.top + sourceRect.height/2 - canvasRect.top;
                const targetX = targetRect.left + targetRect.width/2 - canvasRect.left;
                const targetY = targetRect.top + targetRect.height/2 - canvasRect.top;

                // Create and animate the particle
                animateDataParticle(sourceX, sourceY, targetX, targetY);
            }, i * 1000); // Stagger the animations
        }
    }

    // Function to create the matrix background effect
    function createMatrixBackground() {
        const matrixBg = document.getElementById('matrix-bg');
        const characters = "01";
        const columnCount = 30;

        // Create matrix columns
        for (let i = 0; i < columnCount; i++) {
            const column = document.createElement('div');
            column.className = 'matrix-column';

            // Random position
            column.style.left = `${Math.floor(Math.random() * 100)}%`;

            // Random speed (3-8 seconds)
            const speed = 3 + Math.random() * 5;
            column.style.animationDuration = `${speed}s`;

            // Generate random characters
            const charCount = Math.floor(20 + Math.random() * 30);
            let columnContent = '';
            for (let j = 0; j < charCount; j++) {
                columnContent += characters.charAt(Math.floor(Math.random() * characters.length)) + '<br>';
            }
            column.innerHTML = columnContent;

            matrixBg.appendChild(column);
        }
    }

    // Function to update the propagation status display
    function updatePropagationStatus(duration) {
        const statusElement = document.getElementById('propagation-status');
        const seconds = (duration / 1000).toFixed(1);
        statusElement.textContent = `Propagation: ${seconds}s`;

        // Color code based on propagation time
        if (duration < 2000) {
            statusElement.style.color = '#4CAF50'; // Green for fast
        } else if (duration < 4000) {
            statusElement.style.color = '#FFC107'; // Yellow for medium
        } else {
            statusElement.style.color = '#F44336'; // Red for slow
        }

        // Flash effect
        statusElement.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        setTimeout(() => {
            statusElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        }, 300);
    }

    // Function to animate a data particle from source to target with realistic propagation
    function animateDataParticle(sourceX, sourceY, targetX, targetY, distance) {
        const particle = document.createElement('div');
        particle.className = 'data-particle';
        particle.style.left = `${sourceX}px`;
        particle.style.top = `${sourceY}px`;

        // Add a propagation indicator to the tooltip
        const propagationInfo = document.createElement('div');
        propagationInfo.className = 'propagation-info';
        propagationInfo.textContent = 'Propagating...';
        propagationInfo.style.position = 'absolute';
        propagationInfo.style.left = `${sourceX}px`;
        propagationInfo.style.top = `${sourceY - 20}px`;
        propagationInfo.style.backgroundColor = 'rgba(0,0,0,0.7)';
        propagationInfo.style.color = '#ffd700';
        propagationInfo.style.padding = '3px 8px';
        propagationInfo.style.borderRadius = '4px';
        propagationInfo.style.fontSize = '10px';
        propagationInfo.style.zIndex = '100';
        propagationInfo.style.opacity = '0';
        propagationInfo.style.transition = 'opacity 0.3s';

        canvas.appendChild(particle);
        canvas.appendChild(propagationInfo);

        // Show propagation info briefly
        setTimeout(() => {
            propagationInfo.style.opacity = '1';
            setTimeout(() => {
                propagationInfo.style.opacity = '0';
                setTimeout(() => {
                    canvas.removeChild(propagationInfo);
                }, 300);
            }, 1000);
        }, 100);

        // Calculate the distance and angle
        const dx = targetX - sourceX;
        const dy = targetY - sourceY;

        // Calculate realistic propagation time based on distance
        // In a real blockchain, propagation is affected by distance, network conditions, etc.
        // We'll use a base time plus a factor based on distance
        const basePropagationTime = 1000; // 1 second base time
        const distanceFactor = distance * 0.5; // 0.5ms per pixel
        const duration = Math.min(basePropagationTime + distanceFactor, 5000); // Cap at 5 seconds

        // Display the propagation time
        const propagationTime = (duration / 1000).toFixed(1);
        propagationInfo.textContent = `Propagating... ${propagationTime}s`;

        // Update the propagation status in the header
        updatePropagationStatus(duration);

        const startTime = performance.now();

        function moveParticle(timestamp) {
            const elapsed = timestamp - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentX = sourceX + dx * progress;
            const currentY = sourceY + dy * progress;

            particle.style.left = `${currentX}px`;
            particle.style.top = `${currentY}px`;

            if (progress < 1) {
                requestAnimationFrame(moveParticle);
            } else {
                // Remove the particle when animation is complete
                setTimeout(() => {
                    canvas.removeChild(particle);
                }, 500);
            }
        }

        requestAnimationFrame(moveParticle);
    }

    // Function to refresh data from server using the API endpoint
    function refreshData() {
        // Show loading indicator
        const statusElement = document.getElementById('propagation-status');
        statusElement.textContent = 'Refreshing data...';
        statusElement.style.color = '#FFC107';

        // Store current identity count
        const currentIdentityCount = identities.length;

        fetch('/api/blockchain-data')
            .then(response => response.json())
            .then(data => {
                // Check for new identities
                const newIdentityCount = data.identities.length - currentIdentityCount;

                // Update the data
                identities = data.identities;
                blocks = data.blocks;

                // Update the stats
                document.querySelector('.blocks').textContent = data.blocks_count;
                document.querySelector('.time').textContent = data.last_block_time;

                // Update the visualization
                updateVisualization();

                // Show success message
                statusElement.textContent = 'Data refreshed';
                statusElement.style.color = '#4CAF50';

                // Reset after 2 seconds
                setTimeout(() => {
                    statusElement.textContent = 'Propagation: 1-5s';
                    statusElement.style.color = '#4CAF50';
                }, 2000);

                // Show notification if new identities were found
                if (newIdentityCount > 0) {
                    const notificationElement = document.getElementById('refresh-notification');
                    notificationElement.textContent = `${newIdentityCount} new ${newIdentityCount === 1 ? 'identity' : 'identities'} found!`;
                    notificationElement.style.opacity = '1';
                    notificationElement.style.backgroundColor = 'rgba(76, 175, 80, 0.3)';
                    notificationElement.style.color = '#4CAF50';

                    // Highlight the new identities
                    setTimeout(() => {
                        const identityNodes = document.querySelectorAll('.node.identity');
                        // Highlight the last newIdentityCount nodes
                        for (let i = identityNodes.length - newIdentityCount; i < identityNodes.length; i++) {
                            if (identityNodes[i]) {
                                identityNodes[i].style.boxShadow = '0 0 20px #4CAF50';
                                identityNodes[i].style.transform = 'scale(1.5)';
                                identityNodes[i].style.zIndex = '10';

                                // Reset after 5 seconds
                                setTimeout(() => {
                                    identityNodes[i].style.boxShadow = '';
                                    identityNodes[i].style.transform = '';
                                    identityNodes[i].style.zIndex = '';
                                }, 5000);
                            }
                        }

                        // Hide notification after 5 seconds
                        setTimeout(() => {
                            notificationElement.style.opacity = '0';
                        }, 5000);
                    }, 500);
                }

                console.log('Data refreshed:', data);
            })
            .catch(error => {
                console.error('Error fetching data:', error);

                // Show error message
                statusElement.textContent = 'Refresh failed';
                statusElement.style.color = '#F44336';

                // Reset after 2 seconds
                setTimeout(() => {
                    statusElement.textContent = 'Propagation: 1-5s';
                    statusElement.style.color = '#4CAF50';
                }, 2000);
            });
    }

    // Initial visualization
    updateVisualization();

    // Refresh data after a short delay to ensure we have the latest data
    setTimeout(() => {
        refreshData();
    }, 1000);

    // Set up continuous data flow animation with propagation timing
    let dataFlowInterval = setInterval(() => {
        const nodes = canvas.querySelectorAll('.node');
        if (nodes.length >= 2) {
            // Select random source and target nodes
            const sourceIndex = Math.floor(Math.random() * nodes.length);

            // Create a propagation pattern - data flows from one node to multiple others
            // This better simulates how blockchain data propagates across the network
            const targetCount = 1 + Math.floor(Math.random() * 3); // 1-3 targets

            for (let i = 0; i < targetCount; i++) {
                // Add a small delay between each propagation to simulate network latency
                setTimeout(() => {
                    let targetIndex;
                    do {
                        targetIndex = Math.floor(Math.random() * nodes.length);
                    } while (targetIndex === sourceIndex);

                    const sourceNode = nodes[sourceIndex];
                    const targetNode = nodes[targetIndex];

                    // Get positions
                    const sourceRect = sourceNode.getBoundingClientRect();
                    const targetRect = targetNode.getBoundingClientRect();
                    const canvasRect = canvas.getBoundingClientRect();

                    const sourceX = sourceRect.left + sourceRect.width/2 - canvasRect.left;
                    const sourceY = sourceRect.top + sourceRect.height/2 - canvasRect.top;
                    const targetX = targetRect.left + targetRect.width/2 - canvasRect.left;
                    const targetY = targetRect.top + targetRect.height/2 - canvasRect.top;

                    // Calculate distance to determine propagation time
                    const dx = targetX - sourceX;
                    const dy = targetY - sourceY;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    // Create and animate the particle with distance-based duration
                    animateDataParticle(sourceX, sourceY, targetX, targetY, distance);
                }, i * 300); // Stagger the propagation
            }
        }
    }, 2000); // Create a new propagation event every 2 seconds

    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', function() {
        refreshData();
    });

    // Live view button
    document.getElementById('liveViewBtn').addEventListener('click', function() {
        this.classList.add('active');
        updateVisualization();
    });

    // Auto-refresh button
    document.getElementById('autoRefreshBtn').addEventListener('click', function() {
        if (isAutoRefreshOn) {
            // Turn off auto-refresh
            clearInterval(autoRefreshInterval);
            this.textContent = 'Auto Refresh: Off';
            this.style.backgroundColor = '';
            isAutoRefreshOn = false;
        } else {
            // Turn on auto-refresh
            refreshData(); // Refresh immediately
            autoRefreshInterval = setInterval(refreshData, 10000); // Refresh every 10 seconds
            this.textContent = 'Auto Refresh: On';
            this.style.backgroundColor = '#4CAF50';
            isAutoRefreshOn = true;

            // Show notification
            const notificationElement = document.getElementById('refresh-notification');
            notificationElement.textContent = 'Auto-refresh enabled (10s)';
            notificationElement.style.opacity = '1';
            notificationElement.style.backgroundColor = 'rgba(76, 175, 80, 0.3)';
            notificationElement.style.color = '#4CAF50';

            // Hide notification after 3 seconds
            setTimeout(() => {
                notificationElement.style.opacity = '0';
            }, 3000);
        }
    });
});
</script>
{% endblock %}
