"""
Onnyx Base Model

This module defines the base model for all Onnyx models.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union, ClassVar, Type, TypeVar

from shared.config.config import onnyx_config

# Set up logging
logger = logging.getLogger("onnyx.models.base_model")

T = TypeVar('T', bound='BaseModel')

class BaseModel:
    """
    Base model for all Onnyx models.
    
    This class provides common functionality for all models, such as
    database operations and serialization.
    """
    
    TABLE_NAME: ClassVar[str] = ""
    PRIMARY_KEY: ClassVar[str] = ""
    
    @classmethod
    def create_table(cls) -> None:
        """
        Create the table for this model if it doesn't exist.
        
        This method should be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement create_table")
    
    @classmethod
    def get_by_id(cls: Type[T], id_value: str) -> Optional[T]:
        """
        Get a model instance by ID.
        
        Args:
            id_value: The ID value
            
        Returns:
            The model instance, or None if not found
        """
        raise NotImplementedError("Subclasses must implement get_by_id")
    
    @classmethod
    def get_all(cls: Type[T]) -> List[T]:
        """
        Get all model instances.
        
        Returns:
            A list of all model instances
        """
        raise NotImplementedError("Subclasses must implement get_all")
    
    def save(self) -> None:
        """
        Save the model instance to the database.
        
        This method should be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement save")
    
    def delete(self) -> None:
        """
        Delete the model instance from the database.
        
        This is a default implementation that can be overridden by subclasses.
        """
        if not self.TABLE_NAME or not self.PRIMARY_KEY:
            raise ValueError("TABLE_NAME and PRIMARY_KEY must be defined")
        
        # Get the primary key value
        primary_key_value = getattr(self, self.PRIMARY_KEY)
        
        # Connect to the database
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        # Delete the record
        cursor.execute(f"""
        DELETE FROM {self.TABLE_NAME}
        WHERE {self.PRIMARY_KEY} = ?
        """, (primary_key_value,))
        
        # Commit the changes
        conn.commit()
        conn.close()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model instance to a dictionary.
        
        This method should be implemented by subclasses.
        
        Returns:
            The model instance as a dictionary
        """
        raise NotImplementedError("Subclasses must implement to_dict")
    
    def to_json(self) -> str:
        """
        Convert the model instance to a JSON string.
        
        Returns:
            The model instance as a JSON string
        """
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """
        Create a model instance from a dictionary.
        
        This method should be implemented by subclasses.
        
        Args:
            data: The dictionary
            
        Returns:
            The model instance
        """
        raise NotImplementedError("Subclasses must implement from_dict")
    
    @classmethod
    def from_json(cls: Type[T], json_str: str) -> T:
        """
        Create a model instance from a JSON string.
        
        Args:
            json_str: The JSON string
            
        Returns:
            The model instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
