FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    ONNYX_ENV=production \
    ONNYX_DATA_DIR=/data

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Create data directory
RUN mkdir -p /data/db /data/logs /data/config

# Expose port
EXPOSE 5000

# Set entrypoint
ENTRYPOINT ["python", "run_api_updated.py"]

# Set default command
CMD ["--host", "0.0.0.0", "--port", "5000", "--log-level", "info"]
