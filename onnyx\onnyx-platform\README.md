# ONNYX Platform - GitHub Pages + Render Deployment

## 🚀 Hybrid Deployment Architecture

This setup uses **GitHub Pages for the frontend** and **Render for the backend**, providing the best of both worlds:

- ✅ **GitHub Pages**: Free static hosting for the beautiful ONNYX interface
- ✅ **Render**: Powerful backend hosting for Flask app, database, and APIs

## 📁 Repository Structure

```
onnyx-frontend/
├── index.html          # Main ONNYX platform interface
├── styles.css          # Complete Onyx Stone theme
├── script.js           # Frontend logic + backend integration
└── README.md           # This deployment guide
```

## 🔧 Deployment Steps

### Step 1: Deploy Frontend to GitHub Pages

1. **Create GitHub Repository**
   ```bash
   # Create new repository: onnyx-platform
   # Make it public (required for free GitHub Pages)
   ```

2. **Upload Files**
   - Upload `index.html`, `styles.css`, `script.js`
   - Commit with message: "Deploy ONNYX Platform frontend"

3. **Enable GitHub Pages**
   - Go to repository Settings → Pages
   - Source: "Deploy from a branch"
   - Branch: `main` → `/ (root)`
   - Save settings

4. **Access Your Site**
   - URL: `https://yourusername.github.io/onnyx-platform`
   - Takes 5-10 minutes for initial deployment

### Step 2: Deploy Backend to Render

1. **Prepare Backend Repository**
   ```bash
   # Create separate repository: onnyx-backend
   # Upload your Flask app files:
   # - web/app.py
   # - requirements.txt
   # - All backend code
   ```

2. **Create Render Web Service**
   - Go to [Render.com](https://render.com)
   - Connect your GitHub account
   - Create "New Web Service"
   - Select `onnyx-backend` repository

3. **Configure Render Service**
   ```yaml
   Name: onnyx-backend
   Environment: Python 3
   Build Command: pip install -r requirements.txt
   Start Command: python web/app.py
   ```

4. **Get Render URL**
   - Your backend will be at: `https://onnyx-backend.onrender.com`
   - Copy this URL for frontend configuration

### Step 3: Connect Frontend to Backend

1. **Update Frontend Configuration**
   ```javascript
   // In script.js, update the API_BASE URL:
   const BACKEND_URL = 'https://your-onnyx-backend.onrender.com';
   ```

2. **Test Connection**
   - Open your GitHub Pages site
   - Check browser console for connection status
   - Verify data loading from Render backend

## 🌐 Live URLs

After deployment, you'll have:

- **Frontend**: `https://yourusername.github.io/onnyx-platform`
- **Backend**: `https://your-onnyx-backend.onrender.com`
- **Full Platform**: Frontend connects to backend automatically

## 🔧 Configuration

### Frontend Configuration (script.js)

```javascript
// Update these URLs after Render deployment
const BACKEND_URL = 'https://your-onnyx-backend.onrender.com';
const FALLBACK_URL = 'http://127.0.0.1:5000'; // Local development

// Auto-detection handles environment switching
const API_BASE = window.location.hostname === 'localhost' 
    ? FALLBACK_URL 
    : BACKEND_URL;
```

### Backend Configuration (Flask app)

```python
# Enable CORS for GitHub Pages domain
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=[
    'https://yourusername.github.io',
    'http://localhost:3000',
    'http://127.0.0.1:5000'
])
```

## 📊 Features Included

### ✅ GitHub Pages Frontend
- **Complete ONNYX Interface**: Home, validators, explorer, dashboard
- **Onyx Stone Theme**: Dark glassmorphism design with cyber accents
- **Responsive Design**: Mobile and desktop compatibility
- **Real-time Data**: Connects to Render backend for live data
- **Static Fallbacks**: Works even if backend is offline

### ✅ Render Backend Integration
- **Flask Application**: Full Python backend with all features
- **Database Operations**: SQLite/PostgreSQL for production data
- **API Endpoints**: RESTful APIs for frontend data
- **Authentication**: User login and registration
- **Mining System**: Blockchain operations and mining

## 🔄 Data Flow

```
GitHub Pages Frontend
        ↓ API Calls
Render Backend (Flask)
        ↓ Database Queries
Production Database
        ↓ Real-time Data
Frontend Display
```

## 🛠️ Development Workflow

### Local Development
```bash
# Frontend: Open index.html in browser
# Backend: Run Flask app locally
python web/app.py

# Frontend automatically detects local backend
```

### Production Deployment
```bash
# Frontend: Push to GitHub (auto-deploys to Pages)
git push origin main

# Backend: Push to GitHub (auto-deploys to Render)
git push origin main
```

## 🔐 Security Considerations

### CORS Configuration
```python
# Backend: Allow GitHub Pages domain
CORS(app, origins=['https://yourusername.github.io'])
```

### Environment Variables
```bash
# Render: Set environment variables
DATABASE_URL=your_database_url
SECRET_KEY=your_secret_key
FLASK_ENV=production
```

### API Security
```javascript
// Frontend: Secure API calls
const headers = {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
};
```

## 📱 Mobile Optimization

The frontend is fully responsive and optimized for:
- ✅ **Mobile Phones**: Touch-friendly interface
- ✅ **Tablets**: Optimized layouts
- ✅ **Desktop**: Full feature set
- ✅ **Progressive Web App**: Can be installed on devices

## 🚀 Performance Features

### Frontend Optimizations
- **Lazy Loading**: Images and content load on demand
- **Caching**: Static assets cached by GitHub Pages CDN
- **Compression**: Minified CSS and JavaScript
- **Responsive Images**: Optimized for different screen sizes

### Backend Optimizations
- **Database Indexing**: Fast query performance
- **API Caching**: Reduced database load
- **Connection Pooling**: Efficient database connections
- **Error Handling**: Graceful fallbacks

## 🔍 Monitoring & Analytics

### Frontend Monitoring
```javascript
// Built-in error tracking
window.addEventListener('error', function(e) {
    console.error('Frontend error:', e.error);
});
```

### Backend Monitoring
```python
# Flask logging
import logging
logging.basicConfig(level=logging.INFO)
```

## 🆘 Troubleshooting

### Common Issues

1. **CORS Errors**
   ```
   Solution: Update CORS configuration in Flask app
   ```

2. **API Connection Failed**
   ```
   Solution: Check Render backend URL in script.js
   ```

3. **GitHub Pages Not Updating**
   ```
   Solution: Check repository settings, wait 5-10 minutes
   ```

4. **Render App Sleeping**
   ```
   Solution: Render free tier sleeps after 15 minutes of inactivity
   ```

### Debug Mode
```javascript
// Enable debug logging in frontend
const DEBUG_MODE = true;
console.log('API Base:', API_BASE);
```

## 💰 Cost Breakdown

### Free Tier (Recommended for Testing)
- **GitHub Pages**: Free (public repositories)
- **Render**: Free tier (750 hours/month)
- **Total**: $0/month

### Production Tier
- **GitHub Pages**: Free (always)
- **Render Pro**: $7/month (always-on, faster)
- **Total**: $7/month

## 🎯 Next Steps

1. **Deploy Frontend**: Upload to GitHub Pages
2. **Deploy Backend**: Set up Render service
3. **Configure URLs**: Update API endpoints
4. **Test Integration**: Verify frontend-backend connection
5. **Custom Domain**: Optional custom domain setup
6. **SSL Certificate**: Automatic HTTPS on both platforms

## 📞 Support

- **GitHub Pages**: [GitHub Pages Documentation](https://pages.github.com/)
- **Render**: [Render Documentation](https://render.com/docs)
- **ONNYX Platform**: Check repository issues for support

---

**This hybrid deployment gives you a professional, scalable ONNYX platform with minimal cost and maximum performance!** 🚀✨
