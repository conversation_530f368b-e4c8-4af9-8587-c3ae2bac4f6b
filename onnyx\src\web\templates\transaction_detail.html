{% extends 'base.html' %}

{% block title %}Onnyx Explorer - Transaction Details{% endblock %}

{% block content %}
    <h1>Transaction Details</h1>

    <div class="transaction-detail">
        <div class="detail-section">
            <h2>Basic Information</h2>
            <table>
                <tr>
                    <th>Transaction ID</th>
                    <td>
                        {% if tx.tx_id is defined %}
                            {{ tx.tx_id }}
                        {% elif mempool %}
                            {{ tx.id }}
                        {% else %}
                            {{ tx.txid }}
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Type</th>
                    <td>
                        {% if tx.op is defined %}
                            {{ tx.op }}
                        {% else %}
                            {{ tx.type }}
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Timestamp</th>
                    <td>
                        {% if tx.timestamp is defined %}
                            {{ tx.timestamp | format_timestamp }}
                        {% else %}
                            {{ tx.created_at | format_timestamp }}
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Sender</th>
                    <td>
                        {% if tx.sender is defined %}
                            {{ tx.sender }}
                        {% else %}
                            Unknown
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td>
                        {% if mempool %}
                            <span class="status pending">Pending (Mempool)</span>
                        {% else %}
                            <span class="status confirmed">Confirmed</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>

        <div class="detail-section">
            <h2>Data</h2>
            <pre>
                {% if tx.data is defined %}
                    {% if tx.data is mapping %}
                        {{ tx.data | tojson(indent=2) }}
                    {% elif tx.data is string %}
                        {% if tx.data %}
                            {% set json_data = tx.data|tojson|fromjson %}
                            {% if json_data %}
                                {{ json_data | tojson(indent=2) }}
                            {% else %}
                                {{ tx.data }}
                            {% endif %}
                        {% else %}
                            {{ tx.data }}
                        {% endif %}
                    {% else %}
                        {{ tx.data | tojson(indent=2) }}
                    {% endif %}
                {% elif tx.payload is defined %}
                    {{ tx.payload | tojson(indent=2) }}
                {% else %}
                    No data available
                {% endif %}
            </pre>
        </div>

        {% if tx.type == 'forktoken' %}
            <div class="detail-section">
                <h2>Token Information</h2>
                <table>
                    <tr>
                        <th>Token ID</th>
                        <td>
                            <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                {{ tx.payload.token_id }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <td>{{ tx.payload.name }}</td>
                    </tr>
                    <tr>
                        <th>Symbol</th>
                        <td>{{ tx.payload.symbol }}</td>
                    </tr>
                    <tr>
                        <th>Creator</th>
                        <td>
                            <a href="{{ url_for('identity_detail', identity_id=tx.payload.creator_identity) }}">
                                {{ tx.payload.creator_identity }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Initial Supply</th>
                        <td>{{ tx.payload.supply }}</td>
                    </tr>
                </table>
            </div>
        {% elif tx.type == 'minttoken' or tx.type == 'sendtoken' or tx.type == 'burntoken' %}
            <div class="detail-section">
                <h2>Token Transfer</h2>
                <table>
                    <tr>
                        <th>Token ID</th>
                        <td>
                            <a href="{{ url_for('token_detail', token_id=tx.payload.token_id) }}">
                                {{ tx.payload.token_id }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Amount</th>
                        <td>{{ tx.payload.amount }}</td>
                    </tr>
                    {% if tx.type == 'minttoken' %}
                        <tr>
                            <th>Recipient</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">
                                    {{ tx.payload.to_address }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Minter</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.caller_identity) }}">
                                    {{ tx.payload.caller_identity }}
                                </a>
                            </td>
                        </tr>
                    {% elif tx.type == 'sendtoken' %}
                        <tr>
                            <th>Sender</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">
                                    {{ tx.payload.from_address }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Recipient</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_address) }}">
                                    {{ tx.payload.to_address }}
                                </a>
                            </td>
                        </tr>
                        {% if tx.payload.memo %}
                            <tr>
                                <th>Memo</th>
                                <td>{{ tx.payload.memo }}</td>
                            </tr>
                        {% endif %}
                    {% elif tx.type == 'burntoken' %}
                        <tr>
                            <th>Burned From</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.from_address) }}">
                                    {{ tx.payload.from_address }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Burner</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.burner_identity) }}">
                                    {{ tx.payload.burner_identity }}
                                </a>
                            </td>
                        </tr>
                    {% endif %}
                </table>
            </div>
        {% elif tx.type == 'createidentity' or tx.type == 'updateidentity' %}
            <div class="detail-section">
                <h2>Identity Information</h2>
                <table>
                    {% if tx.type == 'createidentity' %}
                        <tr>
                            <th>Identity ID</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.identity_id) }}">
                                    {{ tx.payload.identity_id }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Name</th>
                            <td>{{ tx.payload.name }}</td>
                        </tr>
                        <tr>
                            <th>Public Key</th>
                            <td>{{ tx.payload.public_key }}</td>
                        </tr>
                    {% elif tx.type == 'updateidentity' %}
                        <tr>
                            <th>Identity ID</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=tx.payload.identity_id) }}">
                                    {{ tx.payload.identity_id }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Field</th>
                            <td>{{ tx.payload.field }}</td>
                        </tr>
                        <tr>
                            <th>New Value</th>
                            <td>{{ tx.payload.value }}</td>
                        </tr>
                    {% endif %}
                </table>
            </div>
        {% elif tx.type == 'grantreputation' %}
            <div class="detail-section">
                <h2>Reputation Grant</h2>
                <table>
                    <tr>
                        <th>Recipient</th>
                        <td>
                            <a href="{{ url_for('identity_detail', identity_id=tx.payload.to_identity) }}">
                                {{ tx.payload.to_identity }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Reputation Type</th>
                        <td>{{ tx.payload.reputation_type }}</td>
                    </tr>
                    <tr>
                        <th>Value</th>
                        <td>{{ tx.payload.value }}</td>
                    </tr>
                    <tr>
                        <th>Issuer</th>
                        <td>
                            <a href="{{ url_for('identity_detail', identity_id=tx.payload.issuer_identity) }}">
                                {{ tx.payload.issuer_identity }}
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
        {% elif tx.op is defined and tx.op == 'CREATE_IDENTITY' %}
            <div class="detail-section">
                <h2>Identity Creation</h2>
                <table>
                    {% if tx.data is defined %}
                        {% if tx.data is mapping %}
                            {% set data = tx.data %}
                        {% elif tx.data is string %}
                            {% if tx.data %}
                                {% set data = tx.data|tojson|fromjson %}
                                {% if not data %}
                                    {% set data = {} %}
                                {% endif %}
                            {% else %}
                                {% set data = {} %}
                            {% endif %}
                        {% else %}
                            {% set data = {} %}
                        {% endif %}
                    {% else %}
                        {% set data = {} %}
                    {% endif %}

                    {% if data.identity_id is defined %}
                        <tr>
                            <th>Identity ID</th>
                            <td>
                                <a href="{{ url_for('identity_detail', identity_id=data.identity_id) }}">
                                    {{ data.identity_id }}
                                </a>
                            </td>
                        </tr>
                    {% endif %}

                    {% if data.name is defined %}
                        <tr>
                            <th>Name</th>
                            <td>{{ data.name }}</td>
                        </tr>
                    {% endif %}

                    {% if data.nation is defined %}
                        <tr>
                            <th>Nation</th>
                            <td>{{ data.nation }}</td>
                        </tr>
                    {% endif %}

                    {% if data.role is defined %}
                        <tr>
                            <th>Role</th>
                            <td>{{ data.role }}</td>
                        </tr>
                    {% endif %}
                </table>
            </div>
        {% endif %}
    </div>

    <div class="actions">
        <a href="{{ url_for('transactions' if not mempool else 'mempool_view') }}" class="button">Back to {{ 'Transactions' if not mempool else 'Mempool' }}</a>
    </div>
{% endblock %}

{% block head %}
<style>
    .transaction-detail {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 20px;
    }

    .detail-section {
        background-color: #222;
        padding: 20px;
        border-radius: 8px;
    }

    .detail-section h2 {
        margin-top: 0;
        border-bottom: 1px solid #444;
        padding-bottom: 10px;
    }

    .status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
    }

    .status.pending {
        background-color: #ff9900;
        color: #111;
    }

    .status.confirmed {
        background-color: #33cc33;
        color: #111;
    }

    .actions {
        margin-top: 20px;
        display: flex;
        gap: 10px;
    }

    .button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #0f0;
        color: #111;
        border-radius: 4px;
        font-weight: bold;
        text-decoration: none;
    }

    .button:hover {
        background-color: #00cc00;
        text-decoration: none;
    }

    @media (min-width: 768px) {
        .transaction-detail {
            grid-template-columns: 1fr 1fr;
        }

        .detail-section:first-child {
            grid-column: 1;
        }

        .detail-section:nth-child(2) {
            grid-column: 2;
        }

        .detail-section:nth-child(3) {
            grid-column: 1 / span 2;
        }
    }
</style>
{% endblock %}
