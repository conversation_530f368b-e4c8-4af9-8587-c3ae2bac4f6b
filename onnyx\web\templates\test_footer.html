{% extends "base.html" %}

{% block title %}Footer Test - ONNYX Platform{% endblock %}

{% block content %}
<div class="test-content hero-gradient cyber-grid relative py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Header Section -->
        <div class="text-center mb-16">
            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Footer Test Page</span>
            </h1>
            <p class="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                Testing footer positioning across different content heights
            </p>
        </div>

        <!-- Short Content Test -->
        <div class="glass-card p-8 mb-8 neuro-card">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">Short Content Test</h2>
            <p class="text-text-secondary">
                This is a short content section to test if the footer appears at the bottom of the viewport when there's minimal content.
            </p>
        </div>

        <!-- Medium Content Test -->
        <div class="glass-card p-8 mb-8 neuro-card">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-purple mb-4">Medium Content Test</h2>
            <div class="space-y-4 text-text-secondary">
                <p>This section contains medium-length content to test footer positioning.</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            </div>
        </div>

        <!-- Navigation Test -->
        <div class="text-center">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-8">Navigation Test</h2>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('index') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    🏠 Home
                </a>
                <a href="{{ url_for('explorer.index') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    🔍 Explorer
                </a>
                <a href="{{ url_for('sela.directory') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    🏢 Validators
                </a>
                <a href="{{ url_for('auth.login') }}"
                   class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                    🔐 Login
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test footer positioning
    function checkFooterPosition() {
        const footer = document.querySelector('footer');
        const body = document.body;
        const html = document.documentElement;
        
        const bodyHeight = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);
        const viewportHeight = window.innerHeight;
        
        console.log('Body height:', bodyHeight);
        console.log('Viewport height:', viewportHeight);
        console.log('Footer position:', footer ? footer.getBoundingClientRect() : 'Footer not found');
        
        if (footer) {
            const footerRect = footer.getBoundingClientRect();
            const isFooterVisible = footerRect.top < viewportHeight;
            console.log('Footer visible:', isFooterVisible);
            
            // Add visual indicator
            const indicator = document.createElement('div');
            indicator.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: ${isFooterVisible ? 'green' : 'red'};
                color: white;
                padding: 10px;
                border-radius: 5px;
                z-index: 9999;
                font-family: monospace;
                font-size: 12px;
            `;
            indicator.textContent = `Footer: ${isFooterVisible ? 'VISIBLE' : 'HIDDEN'}`;
            document.body.appendChild(indicator);
        }
    }
    
    // Check on load and resize
    checkFooterPosition();
    window.addEventListener('resize', checkFooterPosition);
});
</script>
{% endblock %}
