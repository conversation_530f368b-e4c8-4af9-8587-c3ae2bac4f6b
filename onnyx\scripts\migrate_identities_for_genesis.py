#!/usr/bin/env python3
"""
ONNYX Identities Table Migration for Genesis Identity

Adds role and metadata columns to the identities table to support Genesis Identity creation.
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def migrate_identities_table():
    """Migrate identities table to support Genesis Identity."""
    print("🔄 MIGRATING IDENTITIES TABLE FOR GENESIS IDENTITY")
    print("=" * 50)
    
    db_path = "shared/db/onnyx.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current identities table structure
        cursor.execute("PRAGMA table_info(identities)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 Current identities columns: {', '.join(columns)}")
        
        migrations_applied = []
        
        # Add role column if it doesn't exist
        if 'role' not in columns:
            print("   Adding role column...")
            cursor.execute("ALTER TABLE identities ADD COLUMN role TEXT")
            migrations_applied.append("role")
        
        # Add metadata column if it doesn't exist
        if 'metadata' not in columns:
            print("   Adding metadata column...")
            cursor.execute("ALTER TABLE identities ADD COLUMN metadata TEXT")
            migrations_applied.append("metadata")
        
        # Add public_key column if it doesn't exist
        if 'public_key' not in columns:
            print("   Adding public_key column...")
            cursor.execute("ALTER TABLE identities ADD COLUMN public_key TEXT")
            migrations_applied.append("public_key")
        
        # Update existing identities to have default role
        print("   Updating existing identities with default role...")
        cursor.execute("UPDATE identities SET role = 'User' WHERE role IS NULL")
        
        # Commit changes
        conn.commit()
        
        print(f"\n✅ Migration completed successfully!")
        print(f"   Applied migrations: {', '.join(migrations_applied) if migrations_applied else 'None needed'}")
        
        # Verify the migration
        cursor.execute("PRAGMA table_info(identities)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"   New identities columns: {', '.join(new_columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """Main entry point."""
    success = migrate_identities_table()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
