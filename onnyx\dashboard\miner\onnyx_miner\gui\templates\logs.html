{% extends "base.html" %}

{% block content %}
<div class="card">
  <div class="card-header">
    <h5 class="card-title">Logs</h5>
  </div>
  <div class="card-body">
    <div class="mb-3">
      <button class="btn btn-sm btn-primary" id="refresh-logs">Refresh Logs</button>
      <button class="btn btn-sm btn-secondary" id="clear-logs">Clear Display</button>
    </div>
    <div class="bg-dark text-light p-3 rounded" style="height: 500px; overflow-y: auto; font-family: monospace;">
      <pre id="log-content">{{ log_content }}</pre>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.getElementById('refresh-logs').addEventListener('click', function() {
    window.location.reload();
  });

  document.getElementById('clear-logs').addEventListener('click', function() {
    document.getElementById('log-content').textContent = '';
  });

  // Scroll to bottom of logs on load
  window.onload = function() {
    var logContent = document.getElementById('log-content');
    logContent.scrollTop = logContent.scrollHeight;
  };
</script>
{% endblock %}
