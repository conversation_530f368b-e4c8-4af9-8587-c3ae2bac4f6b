"""
Onnyx Etzem Engine Module

This module provides the EtzemEngine class for calculating Etzem scores.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional

class EtzemEngine:
    """
    EtzemEngine calculates Etzem scores for identities.
    
    The Etzem score is a measure of an identity's trustworthiness and contribution to the Onnyx ecosystem.
    """
    
    def __init__(self, activity_log_path: str = "data/activity_log.json"):
        """
        Initialize the EtzemEngine.
        
        Args:
            activity_log_path: Path to the activity log JSON file
        """
        self.activity_log_path = activity_log_path
        self.activity_log = self._load_activity_log()
        
        # Ensure the directory exists
        os.makedirs(os.path.dirname(self.activity_log_path), exist_ok=True)
    
    def _load_activity_log(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Load the activity log from the JSON file.
        
        Returns:
            The activity log as a dictionary
        """
        if os.path.exists(self.activity_log_path):
            try:
                with open(self.activity_log_path, "r") as f:
                    return json.load(f)
            except (json.J<PERSON>NDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def _save_activity_log(self) -> None:
        """Save the activity log to the JSON file."""
        with open(self.activity_log_path, "w") as f:
            json.dump(self.activity_log, f, indent=2)
    
    def log_activity(self, identity_id: str, activity_type: str, details: Dict[str, Any]) -> None:
        """
        Log an activity for an identity.
        
        Args:
            identity_id: The identity ID
            activity_type: The type of activity (e.g., "TOKEN_MINT", "REPUTATION_RECEIVED", "TASK_COMPLETED")
            details: Details about the activity
        """
        if identity_id not in self.activity_log:
            self.activity_log[identity_id] = []
        
        activity = {
            "timestamp": int(time.time()),
            "type": activity_type,
            "details": details
        }
        
        self.activity_log[identity_id].append(activity)
        self._save_activity_log()
    
    def get_activities(self, identity_id: str, activity_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get activities for an identity.
        
        Args:
            identity_id: The identity ID
            activity_type: The type of activity to filter by (optional)
        
        Returns:
            A list of activities for the identity
        """
        if identity_id not in self.activity_log:
            return []
        
        if activity_type:
            return [activity for activity in self.activity_log[identity_id] if activity["type"] == activity_type]
        
        return self.activity_log[identity_id]
    
    def calculate_consistency_score(self, identity_id: str) -> float:
        """
        Calculate the consistency score for an identity.
        
        The consistency score measures how consistently an identity participates in the Onnyx ecosystem.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The consistency score (0-100)
        """
        if identity_id not in self.activity_log:
            return 0
        
        activities = self.activity_log[identity_id]
        if not activities:
            return 0
        
        # Calculate the time span of activities
        timestamps = [activity["timestamp"] for activity in activities]
        time_span = max(timestamps) - min(timestamps)
        
        # If the time span is less than a day, return a low score
        if time_span < 86400:  # 24 hours in seconds
            return max(10, min(100, len(activities) * 5))
        
        # Calculate the average time between activities
        avg_time_between = time_span / (len(activities) - 1) if len(activities) > 1 else time_span
        
        # Calculate the consistency score based on the average time between activities
        # Lower average time between activities means higher consistency
        consistency = 100 - min(100, avg_time_between / 86400 * 10)  # 10 points per day of inactivity
        
        return max(0, min(100, consistency))
    
    def calculate_token_impact(self, identity_id: str) -> float:
        """
        Calculate the token impact score for an identity.
        
        The token impact score measures the impact of an identity's token-related activities.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The token impact score (0-100)
        """
        if identity_id not in self.activity_log:
            return 0
        
        # Count token mints and transfers
        token_mints = len([activity for activity in self.activity_log[identity_id] if activity["type"] == "TOKEN_MINT"])
        token_transfers = len([activity for activity in self.activity_log[identity_id] if activity["type"] == "TOKEN_TRANSFER"])
        
        # Calculate the token impact score
        # More mints and transfers means higher impact
        impact = min(100, (token_mints * 10 + token_transfers * 5))
        
        return impact
    
    def calculate_reputation(self, identity_id: str) -> float:
        """
        Calculate the reputation score for an identity.
        
        The reputation score measures the reputation an identity has received from others.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The reputation score (0-100)
        """
        if identity_id not in self.activity_log:
            return 0
        
        # Count reputation received
        reputation_activities = [activity for activity in self.activity_log[identity_id] if activity["type"] == "REPUTATION_RECEIVED"]
        
        if not reputation_activities:
            return 0
        
        # Calculate the total reputation received
        total_reputation = sum(activity["details"].get("amount", 0) for activity in reputation_activities)
        
        # Calculate the reputation score
        # More reputation means higher score
        reputation = min(100, total_reputation)
        
        return reputation
    
    def calculate_labor_contribution(self, identity_id: str) -> float:
        """
        Calculate the labor contribution score for an identity.
        
        The labor contribution score measures the amount of work an identity has contributed.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The labor contribution score (0-100)
        """
        if identity_id not in self.activity_log:
            return 0
        
        # Count tasks completed
        task_activities = [activity for activity in self.activity_log[identity_id] if activity["type"] == "TASK_COMPLETED"]
        
        if not task_activities:
            return 0
        
        # Calculate the total labor hours
        total_hours = sum(activity["details"].get("hours", 0) for activity in task_activities)
        
        # Calculate the labor contribution score
        # More hours means higher score
        labor = min(100, total_hours * 5)  # 5 points per hour
        
        return labor
    
    def calculate_etzem_score(self, identity_id: str) -> Dict[str, float]:
        """
        Calculate the Etzem score for an identity.
        
        The Etzem score is a composite score that measures an identity's trustworthiness and contribution.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            A dictionary containing the Etzem score and its components
        """
        # Calculate the component scores
        consistency = self.calculate_consistency_score(identity_id)
        token_impact = self.calculate_token_impact(identity_id)
        reputation = self.calculate_reputation(identity_id)
        labor = self.calculate_labor_contribution(identity_id)
        
        # Calculate the final Etzem score
        # The final score is a weighted average of the component scores
        final_etzem = (consistency * 0.25 + token_impact * 0.25 + reputation * 0.3 + labor * 0.2)
        
        return {
            "consistency_score": consistency,
            "token_impact": token_impact,
            "reputation": reputation,
            "labor_contribution": labor,
            "final_etzem": final_etzem
        }
