/**
 * Onnyx Network Visualization
 *
 * This script creates a dynamic visualization of the Onnyx blockchain network,
 * showing the relationships between blocks, identities, and Selas.
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeNetworkVisual();
});

function initializeNetworkVisual() {
  // Canvas setup
  const canvas = document.getElementById('networkCanvas');
  if (!canvas) return; // Exit if canvas not found

  const ctx = canvas.getContext('2d');

  let width, height;
  function resizeCanvas() {
    width = canvas.width = canvas.offsetWidth;
    height = canvas.height = canvas.offsetHeight;
  }
  window.addEventListener('resize', resizeCanvas);
  resizeCanvas();

  // Network elements
  const nodes = [];
  const links = [];
  const totalNodes = 50;
  const blockInterval = 8000; // New block every 8 seconds
  let lastBlockTime = Date.now();
  let hoverNode = null;
  let tooltipTimeout;
  let blockCount = 0;
  let currentTheme = 'matrix';

  // Theme configurations
  const themes = {
    matrix: {
      background: 'rgba(0, 0, 0, 0.3)',
      linkColor: 'rgba(166, 77, 255, ${opacity})', // Purple
      sela: '#FFD700', // Gold
      identity: '#a64dff', // Purple
      block: '#8a2be2', // BlueViolet
      tooltipBg: 'rgba(0, 0, 0, 0.7)',
      tooltipText: '#ffffff'
    },
    tribal: {
      background: 'rgba(30, 15, 5, 0.3)',
      linkColor: 'rgba(255, 180, 0, ${opacity})',
      sela: '#FF6B00',
      identity: '#FFC107',
      block: '#E65100',
      tooltipBg: 'rgba(50, 25, 0, 0.7)',
      tooltipText: '#FFC107'
    },
    light: {
      background: 'rgba(240, 240, 240, 0.3)',
      linkColor: 'rgba(100, 100, 100, ${opacity})',
      sela: '#FF9800',
      identity: '#009688',
      block: '#3F51B5',
      tooltipBg: 'rgba(255, 255, 255, 0.9)',
      tooltipText: '#333333'
    }
  };

  // UI elements
  const blockCountElement = document.getElementById('block-count');
  const lastBlockTimeElement = document.getElementById('last-block-time');
  const themeButtons = {
    matrix: document.getElementById('theme-matrix'),
    tribal: document.getElementById('theme-tribal'),
    light: document.getElementById('theme-light')
  };

  // Node types and their properties
  const nodeTypes = {
    sela: {
      radius: 6,
      label: 'Sela'
    },
    identity: {
      radius: 4,
      label: 'Identity'
    },
    block: {
      radius: 3,
      label: 'Block'
    }
  };

  // Apply theme to canvas background
  function applyTheme(themeName) {
    currentTheme = themeName;
    canvas.style.backgroundColor = themes[themeName].background;

    // Update node colors based on theme
    nodes.forEach(node => {
      node.color = themes[themeName][node.role];
      node.pulseColor = `rgba(${hexToRgb(node.color)}, 0.3)`;
    });

    // Update theme buttons
    Object.keys(themeButtons).forEach(theme => {
      if (themeButtons[theme]) {
        themeButtons[theme].classList.toggle('active', theme === themeName);
      }
    });
  }

  // Helper to convert hex to rgb for pulse effects
  function hexToRgb(hex) {
    // Remove # if present
    hex = hex.replace('#', '');

    // Convert 3-digit hex to 6-digits
    if (hex.length === 3) {
      hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }

    // Parse the hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
  }

  // Create the initial network
  function createNetwork() {
    // Create nodes
    for (let i = 0; i < totalNodes; i++) {
      const role = i % 10 === 0 ? 'sela' : (i % 7 === 0 ? 'identity' : 'block');
      nodes.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * 0.6,
        vy: (Math.random() - 0.5) * 0.6,
        role: role,
        radius: nodeTypes[role].radius,
        color: themes[currentTheme][role],
        pulseColor: `rgba(${hexToRgb(themes[currentTheme][role])}, 0.3)`,
        label: nodeTypes[role].label,
        pulse: 0,
        id: i
      });
    }

    // Create links between nodes
    nodes.forEach((n1, i) => {
      // Selas connect to more nodes
      const connectionLimit = n1.role === 'sela' ? 8 : (n1.role === 'identity' ? 5 : 3);
      let connections = 0;

      // Sort other nodes by distance
      const potentialConnections = nodes
        .map((n2, j) => {
          if (i === j) return null;
          const dist = Math.hypot(n1.x - n2.x, n1.y - n2.y);
          return { index: j, distance: dist };
        })
        .filter(c => c !== null)
        .sort((a, b) => a.distance - b.distance);

      // Connect to closest nodes up to the limit
      for (let c = 0; c < Math.min(connectionLimit, potentialConnections.length); c++) {
        const connection = potentialConnections[c];
        if (connection.distance < 200) {
          links.push({
            source: i,
            target: connection.index,
            opacity: 0.3 + Math.random() * 0.3,
            active: false
          });
          connections++;
        }
      }
    });

    // Apply initial theme
    applyTheme(currentTheme);
  }

  // Draw the network
  function drawNetwork() {
    ctx.clearRect(0, 0, width, height);

    // Draw links
    links.forEach(link => {
      const a = nodes[link.source];
      const b = nodes[link.target];

      // Calculate link opacity based on node activity
      const linkOpacity = link.active ? 0.8 : link.opacity;

      // Draw link with theme color
      ctx.beginPath();
      ctx.moveTo(a.x, a.y);
      ctx.lineTo(b.x, b.y);
      ctx.strokeStyle = themes[currentTheme].linkColor.replace('${opacity}', linkOpacity);
      ctx.lineWidth = link.active ? 1.5 : 0.8;
      ctx.stroke();
    });

    // Draw nodes
    nodes.forEach(node => {
      // Draw pulse effect if active
      if (node.pulse > 0) {
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius * (1 + node.pulse * 3), 0, Math.PI * 2);
        ctx.fillStyle = node.pulseColor.replace('0.3', 0.3 * (1 - node.pulse));
        ctx.fill();
        node.pulse -= 0.01;
      }

      // Draw node
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
      ctx.fillStyle = node.color;
      ctx.fill();

      // Draw hover effect and tooltip
      if (hoverNode === node.id) {
        // Highlight node
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius + 2, 0, Math.PI * 2);
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.stroke();

        // Draw tooltip
        const tooltipText = `${node.label} #${node.id}`;
        ctx.font = '12px Arial';
        const textWidth = ctx.measureText(tooltipText).width;

        ctx.fillStyle = themes[currentTheme].tooltipBg;
        ctx.fillRect(node.x - textWidth/2 - 5, node.y - 30, textWidth + 10, 20);

        ctx.fillStyle = themes[currentTheme].tooltipText;
        ctx.textAlign = 'center';
        ctx.fillText(tooltipText, node.x, node.y - 15);
      }
    });
  }

  // Animate the network
  function animate() {
    // Move nodes
    nodes.forEach(n => {
      n.x += n.vx;
      n.y += n.vy;

      // Bounce off edges
      if (n.x < n.radius || n.x > width - n.radius) n.vx *= -1;
      if (n.y < n.radius || n.y > height - n.radius) n.vy *= -1;

      // Keep nodes within bounds
      n.x = Math.max(n.radius, Math.min(width - n.radius, n.x));
      n.y = Math.max(n.radius, Math.min(height - n.radius, n.y));
    });

    // Check if it's time to create a new block
    const currentTime = Date.now();
    if (currentTime - lastBlockTime > blockInterval) {
      createNewBlock();
      lastBlockTime = currentTime;
    }

    drawNetwork();
    requestAnimationFrame(animate);
  }

  // Create a new block in the network
  function createNewBlock() {
    // Find a random Sela to create the block
    const selas = nodes.filter(n => n.role === 'sela');
    if (selas.length === 0) return;

    const creator = selas[Math.floor(Math.random() * selas.length)];
    creator.pulse = 1.0; // Start pulse animation

    // Increment block count and update UI
    blockCount++;
    if (blockCountElement) {
      blockCountElement.textContent = blockCount;
    }

    // Update last block time
    const now = new Date();
    const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    if (lastBlockTimeElement) {
      lastBlockTimeElement.textContent = timeString;
    }

    // Activate links from creator
    links.forEach(link => {
      if (link.source === creator.id || link.target === creator.id) {
        link.active = true;

        // Activate the connected node
        const connectedNodeId = link.source === creator.id ? link.target : link.source;
        const connectedNode = nodes[connectedNodeId];

        // Delay the pulse of connected nodes
        setTimeout(() => {
          connectedNode.pulse = 0.8;

          // Propagate to second-level connections with more delay
          setTimeout(() => {
            links.forEach(secondLink => {
              if ((secondLink.source === connectedNodeId || secondLink.target === connectedNodeId) &&
                  secondLink.source !== creator.id && secondLink.target !== creator.id) {
                secondLink.active = true;
                const secondNodeId = secondLink.source === connectedNodeId ? secondLink.target : secondLink.source;
                nodes[secondNodeId].pulse = 0.6;
              }
            });

            // Reset active links after animation
            setTimeout(() => {
              links.forEach(l => l.active = false);
            }, 1000);
          }, 300);
        }, 200);
      }
    });
  }

  // Handle mouse movement for tooltips
  canvas.addEventListener('mousemove', (e) => {
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // Clear any existing tooltip timeout
    if (tooltipTimeout) clearTimeout(tooltipTimeout);

    // Find node under cursor
    let found = false;
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      const distance = Math.hypot(mouseX - node.x, mouseY - node.y);

      if (distance <= node.radius + 2) {
        hoverNode = node.id;
        found = true;
        break;
      }
    }

    if (!found) {
      // Set a small delay before hiding tooltip to prevent flickering
      tooltipTimeout = setTimeout(() => {
        hoverNode = null;
      }, 100);
    }
  });

  // Set up theme switching
  Object.keys(themeButtons).forEach(theme => {
    const button = themeButtons[theme];
    if (button) {
      button.addEventListener('click', () => {
        applyTheme(theme);
      });
    }
  });

  // Initialize and start animation
  createNetwork();
  animate();

  // Create first block after a short delay
  setTimeout(createNewBlock, 2000);
}

// End of initializeNetworkVisual function
