# src/node/blockchain.py

import os
import time
import hashlib
import logging
from typing import List, Optional

from models.block import Block
from models.transaction import Transaction

# Configure logging
logger = logging.getLogger("onnyx.node.blockchain")

class LocalBlockchain:
    """
    Local blockchain storage and validation.
    """

    def __init__(self):
        """
        Initialize the local blockchain.
        """
        self.blocks_cache = []
        self._load()

    def _load(self):
        """
        Load the blockchain from the database.
        """
        try:
            # Get all blocks from the database
            blocks = Block.get_blocks(limit=1000)

            # Cache the blocks
            self.blocks_cache = blocks

            # If no blocks exist, create the genesis block
            if not self.blocks_cache:
                self._create_genesis_block()

            logger.info(f"Loaded blockchain with {len(self.blocks_cache)} blocks")
        except Exception as e:
            logger.error(f"Error loading blockchain: {str(e)}")
            self._create_genesis_block()

    def _create_genesis_block(self) -> Block:
        """
        Create the genesis block.

        Returns:
            The genesis block.
        """
        # Create the genesis block
        genesis_block = Block.create(
            block_height=0,
            previous_block_id="0",
            timestamp=1234567890,
            difficulty=1,
            nonce="0",
            miner_identity_id="GENESIS",
            transactions=[],
            metadata={"version": "1.0", "size": 0}
        )

        # Add to cache
        self.blocks_cache = [genesis_block]

        logger.info("Created genesis block")
        return genesis_block

    def _calculate_merkle_root(self, transactions: List[str]) -> str:
        """
        Calculate the merkle root of a list of transaction IDs.

        Args:
            transactions: The transaction IDs

        Returns:
            The merkle root
        """
        if not transactions:
            return "0"

        # If there's only one transaction, return its hash
        if len(transactions) == 1:
            return transactions[0]

        # Create a list of transaction hashes
        hashes = transactions.copy()

        # If the number of hashes is odd, duplicate the last hash
        if len(hashes) % 2 == 1:
            hashes.append(hashes[-1])

        # Calculate the merkle root
        while len(hashes) > 1:
            new_hashes = []
            for i in range(0, len(hashes), 2):
                combined = hashes[i] + hashes[i + 1]
                new_hash = hashlib.sha256(combined.encode()).hexdigest()
                new_hashes.append(new_hash)
            hashes = new_hashes

            # If the number of hashes is odd, duplicate the last hash
            if len(hashes) % 2 == 1 and len(hashes) > 1:
                hashes.append(hashes[-1])

        return hashes[0]

    def get_latest(self) -> Block:
        """
        Get the latest block in the chain.

        Returns:
            The latest block.
        """
        # Get the latest block from the database
        latest_block = Block.get_latest()

        # If no blocks exist, create the genesis block
        if not latest_block:
            latest_block = self._create_genesis_block()

        return latest_block

    def get_height(self) -> int:
        """
        Get the height of the chain.

        Returns:
            The height of the chain.
        """
        # Get the latest block
        latest_block = self.get_latest()

        # Return the block height
        return latest_block.block_height

    def get_block(self, height: int) -> Optional[Block]:
        """
        Get a block by height.

        Args:
            height: The height of the block to get.

        Returns:
            The block, or None if not found.
        """
        # Get the block from the database
        return Block.get_by_height(height)

    def validate_block(self, block: Block) -> bool:
        """
        Validate a block.

        Args:
            block: The block to validate.

        Returns:
            True if the block is valid, False otherwise.
        """
        # Get the latest block
        latest_block = self.get_latest()

        # Check if the block height is valid
        if block.block_height != latest_block.block_height + 1:
            logger.warning(f"Block height {block.block_height} does not match expected height {latest_block.block_height + 1}")
            return False

        # Check if the previous block ID matches the ID of the previous block
        if block.previous_block_id != latest_block.block_id:
            logger.warning(f"Block previous_block_id {block.previous_block_id} does not match previous block ID {latest_block.block_id}")
            return False

        # Check if the block timestamp is valid
        if block.timestamp <= latest_block.timestamp:
            logger.warning(f"Block timestamp {block.timestamp} is not greater than previous block timestamp {latest_block.timestamp}")
            return False

        # Check if the block timestamp is not in the future
        current_time = int(time.time())
        if block.timestamp > current_time + 60:  # Allow for some clock drift
            logger.warning(f"Block timestamp {block.timestamp} is in the future (current time: {current_time})")
            return False

        # Validate the merkle root if it exists in metadata
        if block.metadata and "merkle_root" in block.metadata:
            calculated_merkle_root = self._calculate_merkle_root(block.transactions)
            if block.metadata["merkle_root"] != calculated_merkle_root:
                logger.warning(f"Block merkle_root {block.metadata['merkle_root']} does not match calculated merkle_root {calculated_merkle_root}")
                return False

        # Validate transactions
        if not self.validate_transactions(block.transactions):
            logger.warning(f"Block transactions are invalid")
            return False

        return True

    def validate_transactions(self, transaction_ids: List[str]) -> bool:
        """
        Validate a list of transaction IDs.

        Args:
            transaction_ids: The transaction IDs to validate.

        Returns:
            True if the transactions are valid, False otherwise.
        """
        # If there are no transactions, return True
        if not transaction_ids:
            return True

        # Check if all transactions exist in the database
        for tx_id in transaction_ids:
            tx = Transaction.get_by_id(tx_id)
            if not tx:
                logger.warning(f"Transaction {tx_id} not found")
                return False

            # Check if the transaction is already in a block
            if tx.block_id:
                logger.warning(f"Transaction {tx_id} is already in block {tx.block_id}")
                return False

        return True

    def validate_chain(self, blocks: List[Block]) -> bool:
        """
        Validate a chain.

        Args:
            blocks: The chain to validate.

        Returns:
            True if the chain is valid, False otherwise.
        """
        # Check if the chain has at least one block
        if not blocks:
            logger.warning("Chain is empty")
            return False

        # Check if the first block is the genesis block
        if blocks[0].block_height != 0:
            logger.warning(f"First block height {blocks[0].block_height} is not 0")
            return False

        # Validate each block in the chain
        for i in range(1, len(blocks)):
            current_block = blocks[i]
            previous_block = blocks[i - 1]

            # Check if the block height is valid
            if current_block.block_height != i:
                logger.warning(f"Block height {current_block.block_height} does not match expected height {i}")
                return False

            # Check if the previous block ID matches the ID of the previous block
            if current_block.previous_block_id != previous_block.block_id:
                logger.warning(f"Block previous_block_id {current_block.previous_block_id} does not match previous block ID {previous_block.block_id}")
                return False

            # Check if the block timestamp is valid
            if current_block.timestamp <= previous_block.timestamp:
                logger.warning(f"Block timestamp {current_block.timestamp} is not greater than previous block timestamp {previous_block.timestamp}")
                return False

            # Validate the merkle root if it exists in metadata
            if current_block.metadata and "merkle_root" in current_block.metadata:
                calculated_merkle_root = self._calculate_merkle_root(current_block.transactions)
                if current_block.metadata["merkle_root"] != calculated_merkle_root:
                    logger.warning(f"Block merkle_root {current_block.metadata['merkle_root']} does not match calculated merkle_root {calculated_merkle_root}")
                    return False

        return True

    def add_block(self, block: Block) -> bool:
        """
        Add a block to the chain.

        Args:
            block: The block to add.

        Returns:
            True if the block was added, False otherwise.
        """
        # Validate the block
        if not self.validate_block(block):
            logger.warning(f"Block validation failed: {block.block_hash}")
            return False

        # Add the block to the database
        block.save()

        # Update the cache
        self.blocks_cache.append(block)

        logger.info(f"Added block {block.block_height} to chain")
        return True

    def get_chain(self, limit: int = 100, offset: int = 0) -> List[Block]:
        """
        Get the entire chain.

        Args:
            limit: The maximum number of blocks to return
            offset: The offset for pagination

        Returns:
            The chain.
        """
        return Block.get_blocks(limit=limit, offset=offset)

    def create_block(self, transaction_ids: List[str], miner: str) -> Block:
        """
        Create a new block.

        Args:
            transaction_ids: The transaction IDs to include in the block.
            miner: The miner identity ID.

        Returns:
            The new block.
        """
        # Get the latest block
        latest_block = self.get_latest()

        # Calculate the merkle root
        merkle_root = self._calculate_merkle_root(transaction_ids)

        # Create the new block
        block = Block.create(
            block_height=latest_block.block_height + 1,
            previous_block_id=latest_block.block_id,
            timestamp=int(time.time()),
            difficulty=1,  # TODO: Implement difficulty adjustment
            nonce="0",  # TODO: Implement proof of work
            miner_identity_id=miner,
            transactions=transaction_ids,
            metadata={"version": "1.0", "size": len(transaction_ids), "merkle_root": merkle_root}
        )

        # Add to cache
        self.blocks_cache.append(block)

        logger.info(f"Created block {block.block_height}")
        return block


# Create a global instance of the local blockchain
local_blockchain = LocalBlockchain()
