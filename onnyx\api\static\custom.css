/* Custom CSS for Onnyx API Documentation */

:root {
  --onnyx-primary: #3a506b;
  --onnyx-secondary: #5bc0be;
  --onnyx-accent: #ffa62b;
  --onnyx-dark: #1c2541;
  --onnyx-light: #f5f5f5;
  --onnyx-text: #333333;
  --onnyx-text-light: #f5f5f5;
  --onnyx-border: #e0e0e0;
}

/* Global styles */
body {
  font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--onnyx-text);
  background-color: var(--onnyx-light);
  margin: 0;
  padding: 0;
}

/* Header styles */
.onnyx-header {
  background-color: var(--onnyx-dark);
  color: var(--onnyx-text-light);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.onnyx-logo-container {
  display: flex;
  align-items: center;
}

.onnyx-logo {
  height: 60px;
  margin-right: 1rem;
}

.onnyx-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.onnyx-subtitle {
  font-size: 1rem;
  font-weight: 400;
  margin: 0.5rem 0 0 0;
  opacity: 0.8;
}

.onnyx-version {
  background-color: var(--onnyx-secondary);
  color: var(--onnyx-dark);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.8rem;
  margin-left: 1rem;
}

.onnyx-oas-version {
  background-color: var(--onnyx-accent);
  color: var(--onnyx-dark);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

/* Footer styles */
.onnyx-footer {
  background-color: var(--onnyx-dark);
  color: var(--onnyx-text-light);
  padding: 2rem;
  margin-top: 2rem;
  text-align: center;
}

.onnyx-footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.onnyx-footer-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.onnyx-footer-description {
  font-size: 1rem;
  line-height: 1.5;
  max-width: 800px;
  margin-bottom: 1.5rem;
}

.onnyx-footer-links {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.onnyx-footer-link {
  color: var(--onnyx-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.onnyx-footer-link:hover {
  color: var(--onnyx-accent);
}

.onnyx-footer-copyright {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Swagger UI customizations */
.swagger-ui .topbar {
  display: none;
}

.swagger-ui .info {
  margin: 30px 0;
}

.swagger-ui .info .title {
  color: var(--onnyx-primary);
}

.swagger-ui .opblock-tag {
  color: var(--onnyx-primary);
  font-weight: 600;
}

.swagger-ui .opblock .opblock-summary-method {
  background-color: var(--onnyx-secondary);
}

.swagger-ui .btn.execute {
  background-color: var(--onnyx-accent);
  color: var(--onnyx-dark);
  border-color: var(--onnyx-accent);
}

.swagger-ui .btn.execute:hover {
  background-color: var(--onnyx-dark);
  color: var(--onnyx-accent);
  border-color: var(--onnyx-accent);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .onnyx-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }
  
  .onnyx-logo {
    height: 40px;
  }
  
  .onnyx-title {
    font-size: 1.5rem;
  }
  
  .onnyx-footer-links {
    flex-direction: column;
    gap: 1rem;
  }
}
