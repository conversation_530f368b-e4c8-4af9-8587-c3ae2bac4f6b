# Onnyx Protocol Specification

## Overview

Onnyx is a blockchain protocol designed to tokenize identity, business operations, and community interactions. This document outlines the technical specifications of the Onnyx protocol.

## Blockchain Structure

### Block Format

Each block in the Onnyx blockchain contains:

- **Header**:
  - Version: Protocol version
  - Previous Block Hash: Hash of the previous block header
  - Merkle Root: Root of the Merkle tree of transactions
  - Timestamp: Block creation time
  - Bits: Compact representation of the target difficulty
  - Nonce: Value used for Proof-of-Work

- **Body**:
  - Transaction Count: Number of transactions in the block
  - Transactions: List of transactions

### Transaction Format

Transactions in Onnyx follow a UTXO model with extensions for identity and token operations:

- **Version**: Transaction version
- **Inputs**: List of transaction inputs (previous outputs being spent)
  - Previous Transaction Hash
  - Previous Output Index
  - Script Signature
  - Sequence Number
- **Outputs**: List of transaction outputs
  - Value: Amount in ONX
  - Script Public Key: Locking script
- **Token Operations**: Optional section for token-related operations
  - Token ID
  - Operation Type (mint, transfer, burn, etc.)
  - Operation Data
- **Identity Operations**: Optional section for identity-related operations
  - Identity ID
  - Operation Type (register, update, etc.)
  - Operation Data
- **Locktime**: Block height or timestamp until which the transaction is locked

## OnnyxScript

OnnyxScript is a stack-based scripting language used for transaction validation and token/identity operations.

### Opcodes

- **Standard Bitcoin-like Opcodes**:
  - OP_DUP, OP_HASH160, OP_EQUALVERIFY, OP_CHECKSIG, etc.

- **Token-specific Opcodes**:
  - OP_TOKENSPAWN: Create a new token
  - OP_TOKENMINT: Mint tokens
  - OP_TOKENBURN: Burn tokens
  - OP_TOKENTRANSFER: Transfer tokens

- **Identity-specific Opcodes**:
  - OP_IDREGISTER: Register a new identity
  - OP_IDUPDATE: Update identity information
  - OP_IDVERIFY: Verify identity ownership

## Identity System

### Identity Registration

To register an identity on Onnyx:

1. Create an identity registration transaction
2. Include required identity information (name, public key, metadata)
3. Pay the identity registration fee
4. Submit the transaction to the network

### Identity Structure

Each identity contains:

- **ID**: Unique identifier
- **Name**: Human-readable name
- **Public Key**: Associated public key
- **Reputation Score**: Calculated based on on-chain activity
- **Metadata**: Additional information (description, URL, type, etc.)
- **Tokens**: List of tokens owned by the identity

## Token System

### Token Creation

To create a token on Onnyx:

1. Must have a registered identity
2. Create a token creation transaction
3. Include token parameters (name, symbol, supply, etc.)
4. Pay the token creation fee
5. Submit the transaction to the network

### Token Structure

Each token contains:

- **ID**: Unique identifier
- **Name**: Human-readable name
- **Symbol**: Short ticker symbol
- **Decimals**: Number of decimal places
- **Supply**: Current circulating supply
- **Owner**: Identity that created the token
- **Metadata**: Additional information (description, URL, etc.)
- **Rules**: Optional rules for token behavior

## Consensus

Onnyx uses a Proof-of-Work consensus mechanism similar to Bitcoin, with plans to transition to a hybrid Proof-of-Identity system in the future.

### Mining

- Block time: 10 minutes
- Difficulty adjustment: Every 2016 blocks
- Block reward: 50 ONX, halving every 210,000 blocks

## Network

### P2P Protocol

Onnyx uses a P2P network for node communication:

- **Node Discovery**: DNS seeds and hardcoded seed nodes
- **Handshake**: Version exchange and verification
- **Message Types**:
  - version: Node version and capabilities
  - verack: Version acknowledgment
  - inv: Inventory announcement
  - getdata: Request for data
  - block: Block data
  - tx: Transaction data
  - getblocks: Request for block hashes
  - getheaders: Request for block headers
  - headers: Block headers
  - mempool: Request for mempool transactions

## Future Extensions

- **Reputation System**: Enhanced reputation scoring based on on-chain activity
- **Token Fork Engine**: Allow identities to spawn sub-tokens
- **Governance System**: On-chain governance for protocol upgrades
- **Privacy Features**: Optional privacy enhancements for transactions
