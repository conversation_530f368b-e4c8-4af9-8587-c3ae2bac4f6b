"""
Test the Onnyx Mikvah Token Minting

This script tests the Onnyx Mikvah Token Minting by minting tokens.
"""

import os
import sys
import json
import time

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from identity.registry import IdentityRegistry
from tokens.registry import TokenRegistry
from tokens.ledger import TokenLedger
from business.sela_registry import SelaRegistry
from trust.etzem_engine import EtzemEngine
from yovel.limits import calculate_yovel_cap

def setup_test_data():
    """Set up test data for the Mikvah Token Minting test."""
    # Create identity registry
    identity_registry = IdentityRegistry()
    
    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "Alice", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: Alice")
    
    try:
        bob = identity_registry.register_identity("bob", "<PERSON>", "0x987654321fedcba")
        print(f"Created identity: <PERSON>")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: Bob")
    
    try:
        charlie = identity_registry.register_identity("charlie", "Charlie", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")
    
    print("\nTest identities:")
    print(f"  Alice: {alice['identity_id']}")
    print(f"  Bob: {bob['identity_id']}")
    print(f"  Charlie: {charlie['identity_id']}")
    
    # Create token registry
    token_registry = TokenRegistry()
    
    # Create token ledger
    token_ledger = TokenLedger()
    
    # Create Sela registry
    sela_registry = SelaRegistry()
    
    # Create Etzem engine
    etzem_engine = EtzemEngine()
    
    return identity_registry, token_registry, token_ledger, sela_registry, etzem_engine

def test_mikvah_minting():
    """Test the Mikvah Token Minting functionality."""
    # Set up test data
    identity_registry, token_registry, token_ledger, sela_registry, etzem_engine = setup_test_data()
    
    # Set up a Sela for Bob
    try:
        sela = sela_registry.register_sela(
            "bobs_barbershop",
            "Bob's Barbershop",
            "bob",
            "BUSINESS",
            "BARBER_TOKEN"
        )
        identity_registry.link_sela("bob", "bobs_barbershop", "FOUNDER")
        print("\nRegistered Sela for Bob")
    except Exception as e:
        print(f"\nUsing existing Sela for Bob: {str(e)}")
    
    # Set up a Sela for Alice
    try:
        sela = sela_registry.register_sela(
            "alices_salon",
            "Alice's Salon",
            "alice",
            "BUSINESS",
            "SALON_TOKEN"
        )
        identity_registry.link_sela("alice", "alices_salon", "FOUNDER")
        print("Registered Sela for Alice")
    except Exception as e:
        print(f"Using existing Sela for Alice: {str(e)}")
    
    # Mock the Etzem engine
    def mock_compute_etzem(identity_id):
        if identity_id == "alice":
            return {
                "identity_id": "alice",
                "consistency": 9.33,
                "tx_score": 20.0,
                "trust_weight": 16.0,
                "badge_bonus": 12,
                "sela_participation": 15,
                "token_impact": 10,
                "etzem": 82.33
            }
        elif identity_id == "bob":
            return {
                "identity_id": "bob",
                "consistency": 6.33,
                "tx_score": 20.0,
                "trust_weight": 10.0,
                "badge_bonus": 4,
                "sela_participation": 10,
                "token_impact": 10,
                "etzem": 60.33
            }
        else:
            return {
                "identity_id": "charlie",
                "consistency": 3.33,
                "tx_score": 4.2,
                "trust_weight": 4.0,
                "badge_bonus": 0,
                "sela_participation": 0,
                "token_impact": 5.0,
                "etzem": 16.53
            }
    
    etzem_engine.compute_etzem = mock_compute_etzem
    
    # Test minting tokens
    print("\nTesting Mikvah Token Minting...")
    
    # Calculate Yovel mint caps
    alice_etzem = etzem_engine.compute_etzem("alice")["etzem"]
    bob_etzem = etzem_engine.compute_etzem("bob")["etzem"]
    charlie_etzem = etzem_engine.compute_etzem("charlie")["etzem"]
    
    alice_loyalty_cap = calculate_yovel_cap(alice_etzem, "loyalty")
    alice_equity_cap = calculate_yovel_cap(alice_etzem, "equity")
    alice_community_cap = calculate_yovel_cap(alice_etzem, "community")
    
    bob_loyalty_cap = calculate_yovel_cap(bob_etzem, "loyalty")
    bob_equity_cap = calculate_yovel_cap(bob_etzem, "equity")
    bob_community_cap = calculate_yovel_cap(bob_etzem, "community")
    
    charlie_loyalty_cap = calculate_yovel_cap(charlie_etzem, "loyalty")
    
    print("\nYovel mint caps:")
    print(f"  Alice (Etzem: {alice_etzem:.2f}):")
    print(f"    Loyalty: {alice_loyalty_cap}")
    print(f"    Equity: {alice_equity_cap}")
    print(f"    Community: {alice_community_cap}")
    
    print(f"  Bob (Etzem: {bob_etzem:.2f}):")
    print(f"    Loyalty: {bob_loyalty_cap}")
    print(f"    Equity: {bob_equity_cap}")
    print(f"    Community: {bob_community_cap}")
    
    print(f"  Charlie (Etzem: {charlie_etzem:.2f}):")
    print(f"    Loyalty: {charlie_loyalty_cap}")
    
    # Mint tokens for Alice
    try:
        alice_token = token_registry.register_token(
            "ALICE_LOYALTY",
            "Alice Loyalty Token",
            "ALT",
            "alice",
            alice_loyalty_cap,
            "loyalty",
            2,
            {"sela_id": "alices_salon"}
        )
        token_ledger.credit("alice", "ALICE_LOYALTY", alice_loyalty_cap)
        print(f"\nMinted token for Alice: {alice_token['name']} ({alice_token['symbol']})")
        print(f"  Supply: {alice_token['supply']}")
        print(f"  Category: {alice_token['category']}")
    except Exception as e:
        print(f"\nFailed to mint token for Alice: {str(e)}")
    
    # Mint tokens for Bob
    try:
        bob_token = token_registry.register_token(
            "BOB_LOYALTY",
            "Bob Loyalty Token",
            "BLT",
            "bob",
            bob_loyalty_cap,
            "loyalty",
            2,
            {"sela_id": "bobs_barbershop"}
        )
        token_ledger.credit("bob", "BOB_LOYALTY", bob_loyalty_cap)
        print(f"\nMinted token for Bob: {bob_token['name']} ({bob_token['symbol']})")
        print(f"  Supply: {bob_token['supply']}")
        print(f"  Category: {bob_token['category']}")
    except Exception as e:
        print(f"\nFailed to mint token for Bob: {str(e)}")
    
    # Try to mint tokens for Charlie (should fail because Charlie is not a Selaholder)
    try:
        charlie_token = token_registry.register_token(
            "CHARLIE_LOYALTY",
            "Charlie Loyalty Token",
            "CLT",
            "charlie",
            charlie_loyalty_cap,
            "loyalty",
            2,
            {}
        )
        token_ledger.credit("charlie", "CHARLIE_LOYALTY", charlie_loyalty_cap)
        print(f"\nMinted token for Charlie: {charlie_token['name']} ({charlie_token['symbol']})")
        print(f"  Supply: {charlie_token['supply']}")
        print(f"  Category: {charlie_token['category']}")
    except Exception as e:
        print(f"\nFailed to mint token for Charlie: {str(e)}")
    
    # Get tokens by Sela
    alice_sela_tokens = token_registry.get_tokens_by_creator("alice")
    bob_sela_tokens = token_registry.get_tokens_by_creator("bob")
    
    print("\nTokens by Sela:")
    print(f"  Alice's Salon: {len(alice_sela_tokens)} tokens")
    for token in alice_sela_tokens:
        print(f"    {token['name']} ({token['symbol']}): {token['supply']}")
    
    print(f"  Bob's Barbershop: {len(bob_sela_tokens)} tokens")
    for token in bob_sela_tokens:
        print(f"    {token['name']} ({token['symbol']}): {token['supply']}")
    
    # Get tokens by category
    loyalty_tokens = token_registry.get_tokens_by_category("loyalty")
    
    print("\nLoyalty tokens:")
    for token in loyalty_tokens:
        print(f"  {token['name']} ({token['symbol']}): {token['supply']}")
    
    print("\nMikvah Token Minting test completed successfully!")

if __name__ == "__main__":
    test_mikvah_minting()
