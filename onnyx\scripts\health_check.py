#!/usr/bin/env python3
"""
ONNYX Production Health Check

Comprehensive health check for all ONNYX services.
"""

import requests
import time
import sys

def check_service(name, url, expected_keys=None):
    """Check if a service is healthy."""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            if expected_keys:
                for key in expected_keys:
                    if key not in data:
                        print(f"❌ {name}: Missing key '{key}' in response")
                        return False
            
            print(f"✅ {name}: Healthy")
            return True
        else:
            print(f"❌ {name}: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {name}: Error - {e}")
        return False

def main():
    """Run comprehensive health checks."""
    print("🏥 ONNYX PRODUCTION HEALTH CHECK")
    print("=" * 50)
    
    services = [
        ("Blockchain Miner", "http://127.0.0.1:8000/health", ["status", "blocks"]),
        ("API Backend", "http://127.0.0.1:8000/", ["message", "version"]),
        ("Web Frontend", "http://127.0.0.1:5000/", None),  # HTML response
        ("API Blocks", "http://127.0.0.1:8000/api/blocks", ["blocks"]),
        ("API Latest Block", "http://127.0.0.1:8000/api/blocks/latest", ["block"])
    ]
    
    results = []
    
    for name, url, keys in services:
        if name == "Web Frontend":
            # Special check for HTML response
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200 and "ONNYX" in response.text:
                    print(f"✅ {name}: Healthy")
                    results.append(True)
                else:
                    print(f"❌ {name}: Invalid response")
                    results.append(False)
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
                results.append(False)
        else:
            results.append(check_service(name, url, keys))
        
        time.sleep(0.5)  # Be nice to the services
    
    print("\n📊 HEALTH CHECK SUMMARY")
    print("-" * 30)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Services Healthy: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL SERVICES HEALTHY - READY FOR PRODUCTION!")
        return 0
    else:
        print("⚠️  SOME SERVICES UNHEALTHY - CHECK LOGS")
        return 1

if __name__ == "__main__":
    sys.exit(main())
