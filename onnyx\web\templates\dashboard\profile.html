{% extends "base.html" %}

{% block title %}Profile Management - ONNYX Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
    <!-- Header Section -->
    <section class="pt-20 pb-8">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron">
                    Profile Management
                </h1>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Manage your identity and business information
                </p>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class="pb-20">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto space-y-8">
                
                <!-- Identity Profile Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Identity Information
                        </h2>
                        <p class="text-gray-300 mt-2">Update your basic identity information</p>
                    </div>
                    
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('dashboard.update_profile') }}" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Name Field -->
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-300 mb-2">
                                        Full Name *
                                    </label>
                                    <input type="text" 
                                           id="name" 
                                           name="name" 
                                           value="{{ identity.name }}"
                                           required
                                           maxlength="100"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                                </div>

                                <!-- Email Field -->
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                                        Email Address *
                                    </label>
                                    <input type="email" 
                                           id="email" 
                                           name="email" 
                                           value="{{ identity.email }}"
                                           required
                                           maxlength="255"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300">
                                </div>
                            </div>

                            <!-- Read-only Identity Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-700">
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">
                                        Identity ID
                                    </label>
                                    <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300 font-mono text-sm">
                                        {{ identity.identity_id[:16] }}...
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">
                                        Status
                                    </label>
                                    <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ identity.status.title() }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Role Information -->
                            {% if identity.metadata_parsed.get('role') %}
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">
                                    Role
                                </label>
                                <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300">
                                    {{ identity.metadata_parsed.role }}
                                    {% if identity.metadata_parsed.get('genesis_identity') %}
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            Genesis Identity
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Submit Button -->
                            <div class="flex justify-end pt-6">
                                <button type="submit" 
                                        class="px-8 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white font-semibold rounded-lg hover:from-cyan-600 hover:to-purple-700 focus:ring-4 focus:ring-cyan-400/20 transition-all duration-300 transform hover:scale-105">
                                    Update Identity
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Business Profile Card (if user has a Sela) -->
                {% if user_sela %}
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Business Validator Profile
                        </h2>
                        <p class="text-gray-300 mt-2">Manage your business information and services</p>
                    </div>
                    
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('dashboard.update_business_profile') }}" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Business Name -->
                                <div>
                                    <label for="business_name" class="block text-sm font-medium text-gray-300 mb-2">
                                        Business Name *
                                    </label>
                                    <input type="text" 
                                           id="business_name" 
                                           name="business_name" 
                                           value="{{ user_sela.name }}"
                                           required
                                           maxlength="100"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-300 mb-2">
                                        Business Category *
                                    </label>
                                    <select id="category" 
                                            name="category" 
                                            required
                                            class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                        <option value="">Select Category</option>
                                        <option value="services" {% if user_sela.category == 'services' %}selected{% endif %}>Services</option>
                                        <option value="healthcare" {% if user_sela.category == 'healthcare' %}selected{% endif %}>Healthcare</option>
                                        <option value="technology" {% if user_sela.category == 'technology' %}selected{% endif %}>Technology</option>
                                        <option value="retail" {% if user_sela.category == 'retail' %}selected{% endif %}>Retail</option>
                                        <option value="food" {% if user_sela.category == 'food' %}selected{% endif %}>Food & Beverage</option>
                                        <option value="education" {% if user_sela.category == 'education' %}selected{% endif %}>Education</option>
                                        <option value="finance" {% if user_sela.category == 'finance' %}selected{% endif %}>Finance</option>
                                        <option value="other" {% if user_sela.category == 'other' %}selected{% endif %}>Other</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                                    Business Description
                                </label>
                                <textarea id="description" 
                                          name="description" 
                                          rows="3"
                                          maxlength="500"
                                          placeholder="Describe your business and what you offer..."
                                          class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">{{ user_sela.description or '' }}</textarea>
                            </div>

                            <!-- Contact Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Phone -->
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">
                                        Phone Number
                                    </label>
                                    <input type="tel" 
                                           id="phone" 
                                           name="phone" 
                                           value="{{ user_sela.phone or '' }}"
                                           placeholder="(*************"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>

                                <!-- Website -->
                                <div>
                                    <label for="website" class="block text-sm font-medium text-gray-300 mb-2">
                                        Website
                                    </label>
                                    <input type="url" 
                                           id="website" 
                                           name="website" 
                                           value="{{ user_sela.website or '' }}"
                                           placeholder="https://yourbusiness.com"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>
                            </div>

                            <!-- Address -->
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-300 mb-2">
                                    Business Address
                                </label>
                                <input type="text" 
                                       id="address" 
                                       name="address" 
                                       value="{{ user_sela.address or '' }}"
                                       placeholder="123 Business St, City, State 12345"
                                       maxlength="255"
                                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                            </div>

                            <!-- Services -->
                            <div>
                                <label for="services" class="block text-sm font-medium text-gray-300 mb-2">
                                    Services Offered
                                </label>
                                <textarea id="services" 
                                          name="services" 
                                          rows="3"
                                          maxlength="500"
                                          placeholder="List the services you provide..."
                                          class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">{{ user_sela.services or '' }}</textarea>
                            </div>

                            <!-- Business Stats (Read-only) -->
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t border-gray-700">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyan-400">{{ user_sela.trust_score or 0 }}</div>
                                    <div class="text-sm text-gray-400">Trust Score</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-400">{{ user_sela.mining_tier or 'Basic' }}</div>
                                    <div class="text-sm text-gray-400">Mining Tier</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-400">{{ "%.2f"|format(user_sela.onx_balance or 0) }}</div>
                                    <div class="text-sm text-gray-400">ONX Balance</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-400">{{ user_sela.blocks_mined or 0 }}</div>
                                    <div class="text-sm text-gray-400">Blocks Mined</div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end pt-6">
                                <button type="submit" 
                                        class="px-8 py-3 bg-gradient-to-r from-purple-500 to-cyan-600 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-cyan-700 focus:ring-4 focus:ring-purple-400/20 transition-all duration-300 transform hover:scale-105">
                                    Update Business Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                {% endif %}

                <!-- Security Information Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Security Information
                        </h2>
                        <p class="text-gray-300 mt-2">Your cryptographic identity details</p>
                    </div>
                    
                    <div class="card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">
                                    Public Key
                                </label>
                                <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300 font-mono text-sm break-all">
                                    {{ identity.public_key[:64] }}...
                                </div>
                            </div>
                            
                            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <div>
                                        <h4 class="text-yellow-400 font-semibold mb-1">Security Notice</h4>
                                        <p class="text-yellow-200 text-sm">
                                            Your private key is stored securely and cannot be changed through this interface. 
                                            Keep your private key file safe as it controls your identity and assets.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
</div>

<!-- Profile Update Success Animation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to form elements
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('transform', 'scale-105');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('transform', 'scale-105');
        });
    });

    // Form validation feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Updating...';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>
{% endblock %}
