🟩 Phase A: Boot the Chain (1–2 Days)
Task	                    Purpose
init_node.py	            Create Genesis block and seed first validator
create_genesis_identity.py	Add root identity (admin/scribe)
start_miner.py	            Start local mining loop
submit_tx.py	            Add transactions to mempool
mine_block.py	            Write transactions → block → update state

Tools needed:               hashlib, sqlite3, ecdsa, json	