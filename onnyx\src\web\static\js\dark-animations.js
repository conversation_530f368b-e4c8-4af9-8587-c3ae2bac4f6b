/**
 * Onnyx Dark Theme Animations - Enhanced animations for the onboarding portal
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initAnimations();
    
    // Initialize scroll animations
    initScrollAnimations();
    
    // Initialize interactive elements
    initInteractiveElements();
    
    // Initialize mobile navigation
    initMobileNav();
    
    // Initialize particle background if it exists
    if (document.getElementById('particles-js')) {
        initParticles();
    }
});

/**
 * Initialize animations for elements that should animate on page load
 */
function initAnimations() {
    // Animate hero elements
    const heroElements = document.querySelectorAll('.hero-animate');
    heroElements.forEach((element, index) => {
        element.classList.add('slide-in-up');
        element.style.animationDelay = `${0.1 * (index + 1)}s`;
        element.style.opacity = '0';
    });
    
    // Animate cards with staggered delay
    const cards = document.querySelectorAll('.card-animate');
    cards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${0.2 + (0.1 * index)}s`;
        card.style.opacity = '0';
    });
    
    // Animate the logo with a pulse effect
    const logo = document.querySelector('.logo-pulse');
    if (logo) {
        logo.classList.add('pulse');
    }
    
    // Add a subtle hover effect to all cards
    const allCards = document.querySelectorAll('.card');
    allCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = 'var(--shadow-lg)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'var(--shadow-md)';
        });
    });
}

/**
 * Initialize scroll-triggered animations
 */
function initScrollAnimations() {
    // Get all elements that should animate on scroll
    const scrollAnimateElements = document.querySelectorAll('.scroll-animate');
    
    // Create an intersection observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // If the element is in view
            if (entry.isIntersecting) {
                // Add the animation class
                entry.target.classList.add('fade-in');
                // Stop observing the element
                observer.unobserve(entry.target);
            }
        });
    }, {
        root: null, // Use the viewport as the root
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: '0px 0px -50px 0px' // Trigger slightly before the element is in view
    });
    
    // Observe each element
    scrollAnimateElements.forEach(element => {
        element.style.opacity = '0';
        observer.observe(element);
    });
    
    // Add parallax effect to background elements
    window.addEventListener('scroll', function() {
        const parallaxElements = document.querySelectorAll('.parallax');
        const scrollPosition = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const speed = element.getAttribute('data-speed') || 0.5;
            element.style.transform = `translateY(${scrollPosition * speed}px)`;
        });
    });
}

/**
 * Initialize interactive elements
 */
function initInteractiveElements() {
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            
            button.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add hover effects to nation cards
    const nationCards = document.querySelectorAll('.nation-card');
    nationCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('nation-card-active');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('nation-card-active');
        });
    });
    
    // Add typing effect to taglines
    const typingElements = document.querySelectorAll('.typing-effect');
    typingElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        element.style.borderRight = '2px solid var(--accent-primary)';
        
        let i = 0;
        const typing = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(typing);
                element.style.borderRight = 'none';
            }
        }, 50);
    });
    
    // Create 3D tilt effect for special cards
    createTiltEffect('.tilt-card');
    
    // Create floating effect for decorative elements
    createFloatingEffect('.float-element');
    
    // Create glowing effect for important elements
    createGlowingEffect('.glow-element');
}

/**
 * Initialize mobile navigation
 */
function initMobileNav() {
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const navList = document.querySelector('.nav-list');
    
    if (mobileNavToggle && navList) {
        mobileNavToggle.addEventListener('click', function() {
            navList.classList.toggle('active');
            
            // Change the icon based on the state
            if (navList.classList.contains('active')) {
                mobileNavToggle.innerHTML = '&times;';
            } else {
                mobileNavToggle.innerHTML = '&#9776;';
            }
        });
        
        // Close the mobile nav when a link is clicked
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navList.classList.remove('active');
                mobileNavToggle.innerHTML = '&#9776;';
            });
        });
    }
}

/**
 * Create a 3D tilt effect for cards
 * @param {string} selector - CSS selector for the elements to apply the effect to
 */
function createTiltEffect(selector) {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(element => {
        element.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const xPercent = x / rect.width;
            const yPercent = y / rect.height;
            
            const rotateX = (0.5 - yPercent) * 10;
            const rotateY = (xPercent - 0.5) * 10;
            
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
        
        element.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
        });
    });
}

/**
 * Create a floating animation for elements
 * @param {string} selector - CSS selector for the elements to apply the effect to
 */
function createFloatingEffect(selector) {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach((element, index) => {
        // Create a random animation duration between 3-5 seconds
        const duration = 3 + Math.random() * 2;
        // Create a random delay
        const delay = Math.random() * 2;
        
        element.style.animation = `floating ${duration}s ease-in-out ${delay}s infinite`;
    });
}

/**
 * Create a glowing border effect for elements
 * @param {string} selector - CSS selector for the elements to apply the effect to
 */
function createGlowingEffect(selector) {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.classList.add('glowing');
        });
        
        element.addEventListener('mouseleave', function() {
            this.classList.remove('glowing');
        });
    });
}

/**
 * Initialize particle background
 * Requires particles.js library
 */
function initParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 80,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: '#3498db'
                },
                shape: {
                    type: 'circle',
                    stroke: {
                        width: 0,
                        color: '#000000'
                    }
                },
                opacity: {
                    value: 0.5,
                    random: false,
                    anim: {
                        enable: false,
                        speed: 1,
                        opacity_min: 0.1,
                        sync: false
                    }
                },
                size: {
                    value: 3,
                    random: true,
                    anim: {
                        enable: false,
                        speed: 40,
                        size_min: 0.1,
                        sync: false
                    }
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#3498db',
                    opacity: 0.4,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 2,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false,
                    attract: {
                        enable: false,
                        rotateX: 600,
                        rotateY: 1200
                    }
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'grab'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                },
                modes: {
                    grab: {
                        distance: 140,
                        line_linked: {
                            opacity: 1
                        }
                    },
                    push: {
                        particles_nb: 4
                    }
                }
            },
            retina_detect: true
        });
    } else {
        console.warn('particles.js not loaded');
    }
}
