# Onnyx CLI

A developer-facing command-line tool to interact with Onnyx during development and simulation.

## Commands

| Command     | Description                     |
|-------------|---------------------------------|
| `identity`  | Register a new identity         |
| `spawn`     | Spawn a new token under identity|
| `mint`      | Mint more supply                |
| `transfer`  | Send tokens to another address  |
| `balance`   | Check token balance             |
| `metadata`  | View token metadata             |

## Usage

```bash
python cli.py identity
python cli.py spawn
python cli.py mint
python cli.py transfer
python cli.py balance
python cli.py metadata
```
