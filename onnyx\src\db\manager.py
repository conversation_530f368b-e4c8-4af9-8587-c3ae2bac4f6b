# src/db/manager.py

import os
import sqlite3
import logging
import time
from typing import Optional

# Set up logging
logger = logging.getLogger("onnyx.db.manager")

class DatabaseManager:
    """
    Database manager for the Onnyx blockchain.
    """

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the DatabaseManager.

        Args:
            db_path: The path to the database file (optional)
        """
        # If no path is provided, use the default path in the data directory
        if db_path is None:
            # Get the project root directory
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))

            # Create the data directory if it doesn't exist
            data_dir = os.path.join(project_root, "data")
            os.makedirs(data_dir, exist_ok=True)

            # Set the database path
            db_path = os.path.join(data_dir, "onnyx.db")

        self.db_path = db_path
        self.connection = None

        logger.info(f"Database path: {self.db_path}")

    def get_connection(self):
        """
        Get a connection to the database.

        Returns:
            A connection to the database
        """
        if self.connection is None:
            # Create the connection
            self.connection = sqlite3.connect(self.db_path)

            # Enable foreign keys
            self.connection.execute("PRAGMA foreign_keys = ON")

            # Configure the connection
            self.connection.row_factory = sqlite3.Row

        return self.connection

    def close_connection(self):
        """
        Close the connection to the database.
        """
        if self.connection is not None:
            self.connection.close()
            self.connection = None

    def execute_query(self, query: str, params: tuple = ()):
        """
        Execute a query.

        Args:
            query: The query to execute
            params: The query parameters

        Returns:
            The query result
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        conn.commit()
        return cursor

    def execute_query_with_result(self, query: str, params: tuple = ()):
        """
        Execute a query and return the result.

        Args:
            query: The query to execute
            params: The query parameters

        Returns:
            The query result
        """
        cursor = self.execute_query(query, params)
        return cursor.fetchall()

    def execute_transaction(self, queries: list):
        """
        Execute a transaction.

        Args:
            queries: A list of (query, params) tuples

        Returns:
            True if the transaction was successful, False otherwise
        """
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Begin the transaction
            conn.execute("BEGIN TRANSACTION")

            # Execute the queries
            for query, params in queries:
                cursor.execute(query, params)

            # Commit the transaction
            conn.commit()

            return True
        except Exception as e:
            # Rollback the transaction
            conn.rollback()

            logger.error(f"Transaction failed: {str(e)}")

            return False

    def backup_database(self, backup_path: Optional[str] = None):
        """
        Backup the database.

        Args:
            backup_path: The path to the backup file (optional)

        Returns:
            The path to the backup file
        """
        # If no path is provided, use the default path
        if backup_path is None:
            # Get the project root directory
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))

            # Create the backup directory if it doesn't exist
            backup_dir = os.path.join(project_root, "data", "backup")
            os.makedirs(backup_dir, exist_ok=True)

            # Set the backup path
            backup_path = os.path.join(backup_dir, f"onnyx_{int(time.time())}.db")

        # Create a new connection to the backup file
        backup_conn = sqlite3.connect(backup_path)

        # Get the source connection
        source_conn = self.get_connection()

        # Backup the database
        source_conn.backup(backup_conn)

        # Close the backup connection
        backup_conn.close()

        logger.info(f"Database backed up to {backup_path}")

        return backup_path

# Create a singleton instance
db_manager = DatabaseManager()
