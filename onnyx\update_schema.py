#!/usr/bin/env python3

"""
Scrip<PERSON> to update the database schema.
"""

import os
import sys
import logging
import sqlite3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.update_schema")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def update_schema():
    """Update the database schema."""
    try:
        # Connect to the database
        db_path = os.path.join("data", "onnyx.db")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if the identities table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='identities'")
        if cursor.fetchone() is None:
            logger.warning("Identities table does not exist in the database")
            
            # Create the identities table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS identities (
                    identity_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    public_key TEXT NOT NULL,
                    nation TEXT,
                    metadata TEXT,
                    created_at INTEGER NOT NULL
                )
            """)
            
            logger.info("Created identities table")
        else:
            logger.info("Identities table already exists")
            
            # Check the columns in the identities table
            cursor.execute("PRAGMA table_info(identities)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            
            logger.info(f"Columns in identities table: {column_names}")
            
            # Check if the primary key is identity_id
            primary_key = None
            for column in columns:
                if column[5] == 1:  # Primary key
                    primary_key = column[1]
                    break
            
            logger.info(f"Primary key in identities table: {primary_key}")
            
            # If the primary key is not identity_id, update the schema
            if primary_key != "identity_id":
                logger.warning(f"Primary key is {primary_key}, not identity_id")
                
                # Create a new table with the correct schema
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS identities_new (
                        identity_id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        public_key TEXT NOT NULL,
                        nation TEXT,
                        metadata TEXT,
                        created_at INTEGER NOT NULL
                    )
                """)
                
                # Copy the data from the old table to the new table
                cursor.execute(f"""
                    INSERT INTO identities_new (identity_id, name, public_key, nation, metadata, created_at)
                    SELECT {primary_key}, name, public_key, nation, metadata, created_at
                    FROM identities
                """)
                
                # Drop the old table
                cursor.execute("DROP TABLE identities")
                
                # Rename the new table to the old table name
                cursor.execute("ALTER TABLE identities_new RENAME TO identities")
                
                logger.info("Updated identities table schema")
        
        # Commit the changes
        conn.commit()
        
        # Close the connection
        conn.close()
        
        logger.info("Schema update complete")
    except Exception as e:
        logger.error(f"Error updating schema: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def main():
    """Main entry point."""
    logger.info("Updating database schema...")
    
    update_schema()
    
    logger.info("Database schema update complete")

if __name__ == "__main__":
    main()
