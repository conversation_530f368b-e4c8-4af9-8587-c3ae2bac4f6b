# src/harashim/performance.py

from typing import Dict, Any, Optional, List
import time

from src.db.manager import db_manager
from src.etzem.engine import EtzemEngine
from src.identity.registry import IdentityRegistry
from src.harashim.contracts import ServiceContract

class HarashimPerformance:
    """
    Manages performance metrics and Etzem integration for Harashim.
    """

    def __init__(self,
                 etzem_engine: Optional[EtzemEngine] = None,
                 identity_registry: Optional[IdentityRegistry] = None,
                 service_contract: Optional[ServiceContract] = None):
        """
        Initialize the HarashimPerformance.

        Args:
            etzem_engine: The Etzem engine
            identity_registry: The identity registry
            service_contract: The service contract manager
        """
        self.etzem = etzem_engine or EtzemEngine()
        self.identities = identity_registry or IdentityRegistry()
        self.contracts = service_contract or ServiceContract()
        self.db = db_manager.get_connection()

    def apply_contract_result(self, contract_id: str) -> Dict[str, Any]:
        """
        Apply contract results to <PERSON>tz<PERSON> score and badges.

        Args:
            contract_id: The contract ID

        Returns:
            The performance update result

        Raises:
            Exception: If the contract does not exist or is not completed
        """
        # Get the contract
        contract = self.contracts.get_contract(contract_id)
        if not contract:
            raise Exception(f"Contract with ID '{contract_id}' not found")

        # Check if the contract is completed
        if contract["status"] != "COMPLETED":
            raise Exception(f"Contract with ID '{contract_id}' is not completed")

        harash_id = contract["harash_id"]
        rating = contract["rating"] or 3  # Default to neutral rating if none provided
        actual_hours = contract["actual_hours"]

        # Apply to Etzem score
        etzem_boost = self._calculate_etzem_boost(rating, actual_hours)
        self.etzem.apply_activity_boost(harash_id, "contract_completion", etzem_boost)

        # Check for badge eligibility
        self._check_badge_eligibility(harash_id)

        # Record the performance update
        performance_update = {
            "contract_id": contract_id,
            "harash_id": harash_id,
            "etzem_boost": etzem_boost,
            "applied_at": int(time.time())
        }

        cursor = self.db.cursor()
        cursor.execute(
            """
            INSERT INTO harashim_performance
            (contract_id, harash_id, etzem_boost, applied_at)
            VALUES (?, ?, ?, ?)
            """,
            (
                performance_update["contract_id"],
                performance_update["harash_id"],
                performance_update["etzem_boost"],
                performance_update["applied_at"]
            )
        )
        self.db.commit()

        return performance_update

    def _calculate_etzem_boost(self, rating: int, hours: int) -> float:
        """
        Calculate the Etzem boost based on rating and hours.

        Args:
            rating: The contract rating (1-5)
            hours: The actual hours spent

        Returns:
            The Etzem boost value
        """
        # Base boost from hours (0.1 points per hour)
        hours_boost = min(hours * 0.1, 10)  # Cap at 10 points

        # Rating multiplier (0.5x for 1-star, 1.5x for 5-star)
        rating_multiplier = 0.5 + (rating - 1) * 0.25

        # Calculate total boost
        total_boost = hours_boost * rating_multiplier

        return round(total_boost, 2)

    def _check_badge_eligibility(self, harash_id: str) -> None:
        """
        Check if the Harash is eligible for new badges.

        Args:
            harash_id: The Harash ID
        """
        # Get all completed contracts for the Harash
        completed_contracts = [
            contract for contract in self.contracts.get_contracts_by_harash(harash_id)
            if contract["status"] == "COMPLETED"
        ]

        # Count of completed contracts
        completed_count = len(completed_contracts)

        # Count of 5-star ratings
        five_star_count = len([
            contract for contract in completed_contracts
            if contract["rating"] == 5
        ])

        # Check for milestone badges
        badges_to_add = []

        if completed_count >= 10:
            badges_to_add.append("HARASH_10_CONTRACTS")

        if completed_count >= 25:
            badges_to_add.append("HARASH_25_CONTRACTS")

        if completed_count >= 50:
            badges_to_add.append("HARASH_50_CONTRACTS")

        if five_star_count >= 3:
            badges_to_add.append("HARASH_3_FIVE_STARS")

        if five_star_count >= 10:
            badges_to_add.append("HARASH_10_FIVE_STARS")

        # Add badges to the identity
        for badge in badges_to_add:
            try:
                self.identities.add_badge(harash_id, badge)
            except Exception:
                # Badge already exists
                pass

    def get_performance_metrics(self, harash_id: str) -> Dict[str, Any]:
        """
        Get performance metrics for a Harash.

        Args:
            harash_id: The Harash ID

        Returns:
            The performance metrics
        """
        # Get all contracts for the Harash
        all_contracts = self.contracts.get_contracts_by_harash(harash_id)

        # Completed contracts
        completed_contracts = [
            contract for contract in all_contracts
            if contract["status"] == "COMPLETED"
        ]

        # Active contracts
        active_contracts = [
            contract for contract in all_contracts
            if contract["status"] == "ACTIVE"
        ]

        # Calculate metrics
        total_contracts = len(all_contracts)
        completed_count = len(completed_contracts)
        active_count = len(active_contracts)
        completion_rate = round(completed_count / total_contracts * 100, 2) if total_contracts > 0 else 0

        # Calculate average rating
        ratings = [contract["rating"] for contract in completed_contracts if contract["rating"] is not None]
        avg_rating = round(sum(ratings) / len(ratings), 2) if ratings else 0

        # Calculate total hours
        total_hours = sum(contract["actual_hours"] for contract in completed_contracts)

        # Get badges
        identity = self.identities.get_identity(harash_id)
        badges = identity.get("badges", []) if identity else []
        harash_badges = [badge for badge in badges if badge.startswith("HARASH_")]

        return {
            "harash_id": harash_id,
            "total_contracts": total_contracts,
            "completed_contracts": completed_count,
            "active_contracts": active_count,
            "completion_rate": completion_rate,
            "average_rating": avg_rating,
            "total_hours": total_hours,
            "badges": harash_badges
        }
