# src/judah/staking.py

import json
import os
import time
import uuid
from typing import Dict, Any, List, Optional, Tuple
from src.tokens.ledger import TokenLedger
from src.chain.txlog import TxLogger

class ONXStaking:
    """
    ONX Staking - System for staking ONX tokens.

    The ONX Staking system allows identities to:
    - Stake ONX to gain roles like <PERSON><PERSON><PERSON><PERSON>OR, CREATOR, MENTOR
    - Lock ONX for configurable durations (e.g., 30, 90, 180 days)
    - Claim rewards after the lock period expires
    """

    # Define staking roles
    ROLES = ["VALIDATOR", "CREATOR", "MENTOR", "GOVERN<PERSON>", "AMBASSADOR"]

    # Define soulbound roles
    SOULBOUND_ROLES = ["G<PERSON><PERSON><PERSON><PERSON>", "COUNCIL_ELIGIBLE", "<PERSON><PERSON><PERSON>OR", "MENT<PERSON>", "CREATOR"]

    # Define staking tiers
    DEFAULT_TIERS = [
        {
            "role": "VALIDATOR",
            "min_amount": 10000,
            "benefits": ["REDUCED_FEES", "PRIORITY_PROCESSING", "GOVERNANCE_VOTE"],
            "reward_multiplier": 1.5
        },
        {
            "role": "CREATOR",
            "min_amount": 5000,
            "benefits": ["REDUCED_FEES", "PRIORITY_PROCESSING", "CONTENT_BOOST"],
            "reward_multiplier": 1.3
        },
        {
            "role": "MENTOR",
            "min_amount": 2500,
            "benefits": ["REDUCED_FEES", "EDUCATION_BOOST"],
            "reward_multiplier": 1.2
        },
        {
            "role": "GOVERNOR",
            "min_amount": 7500,
            "benefits": ["GOVERNANCE_VOTE", "PROPOSAL_RIGHTS"],
            "reward_multiplier": 1.4
        },
        {
            "role": "AMBASSADOR",
            "min_amount": 1000,
            "benefits": ["COMMUNITY_BOOST", "REFERRAL_BONUS"],
            "reward_multiplier": 1.1
        }
    ]

    # Define soulbound role tiers
    SOULBOUND_TIERS = [
        {
            "role": "GUARDIAN",
            "min_amount": 10000,
            "description": "Protector of the Onnyx ecosystem"
        },
        {
            "role": "COUNCIL_ELIGIBLE",
            "min_amount": 5000,
            "description": "Eligible for Council of Twelve Tribes"
        },
        {
            "role": "CURATOR",
            "min_amount": 1000,
            "description": "Curator of content and tokens"
        },
        {
            "role": "MENTOR",
            "min_amount": 500,
            "description": "Mentor to new users"
        },
        {
            "role": "CREATOR",
            "min_amount": 100,
            "description": "Creator of tokens and content"
        }
    ]

    # Define staking durations
    DEFAULT_DURATIONS = [
        {
            "days": 30,
            "reward_rate": 0.05,  # 5% annual rate
            "early_unlock_penalty": 0.5  # 50% penalty for early unlock
        },
        {
            "days": 90,
            "reward_rate": 0.075,  # 7.5% annual rate
            "early_unlock_penalty": 0.4  # 40% penalty for early unlock
        },
        {
            "days": 180,
            "reward_rate": 0.1,  # 10% annual rate
            "early_unlock_penalty": 0.3  # 30% penalty for early unlock
        },
        {
            "days": 365,
            "reward_rate": 0.15,  # 15% annual rate
            "early_unlock_penalty": 0.2  # 20% penalty for early unlock
        }
    ]

    def __init__(self, path=None, token_ledger: TokenLedger = None, txlog: TxLogger = None):
        """
        Initialize the ONX Staking system.

        Args:
            path: Path to the staking file. If None, a default path in the data directory will be used.
            token_ledger: The Token Ledger to use for token balance management.
            txlog: The Transaction Logger to use for transaction logging.
        """
        if path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.path = os.path.join(data_dir, "onx_stakes.json")
        else:
            self.path = path

        self.ledger = token_ledger or TokenLedger()
        self.txlog = txlog or TxLogger()

        # Initialize stakes
        self.stakes = {}

        # Initialize configuration
        self.config = {
            "tiers": self.DEFAULT_TIERS,
            "durations": self.DEFAULT_DURATIONS,
            "auto_compound": False,
            "min_stake_amount": 100,
            "early_unlock_enabled": True,
            "last_updated": int(time.time()),
            "last_updated_by": "system"
        }

        # Load stakes and configuration
        self._load()

    def _load(self):
        """Load the stakes from the file."""
        try:
            if os.path.exists(self.path) and os.path.getsize(self.path) > 0:
                with open(self.path, "r") as f:
                    data = json.load(f)
                    self.stakes = data.get("stakes", {})
                    self.config = data.get("config", self.config)
        except Exception as e:
            print(f"Error loading ONX stakes: {str(e)}")
            self.stakes = {}

    def _save(self):
        """Save the stakes to the file."""
        with open(self.path, "w") as f:
            data = {
                "stakes": self.stakes,
                "config": self.config
            }
            json.dump(data, f, indent=2)

    def stake(self, identity_id: str, amount: float, duration_days: int, role: str, identity_registry=None) -> Dict[str, Any]:
        """
        Stake ONX tokens for an identity.

        Args:
            identity_id: The identity ID to stake for.
            amount: The amount to stake.
            duration_days: The duration of the stake in days.
            role: The role to stake for.
            identity_registry: The Identity Registry to use for identity information.

        Returns:
            The stake record.

        Raises:
            ValueError: If the identity does not have enough ONX to stake, the amount is too low,
                       the duration is invalid, or the role is invalid.
        """
        # Validate amount
        if amount < self.config["min_stake_amount"]:
            raise ValueError(f"Stake amount must be at least {self.config['min_stake_amount']} ONX.")

        # Validate role
        if role not in self.ROLES:
            raise ValueError(f"Invalid role: {role}. Must be one of: {', '.join(self.ROLES)}")

        # Find the tier for the role
        tier = next((t for t in self.config["tiers"] if t["role"] == role), None)
        if not tier:
            raise ValueError(f"No tier found for role: {role}")

        # Check if the amount meets the minimum for the role
        if amount < tier["min_amount"]:
            raise ValueError(f"Stake amount must be at least {tier['min_amount']} ONX for role: {role}")

        # Validate duration
        duration_config = next((d for d in self.config["durations"] if d["days"] == duration_days), None)
        if not duration_config:
            valid_durations = [d["days"] for d in self.config["durations"]]
            raise ValueError(f"Invalid duration: {duration_days}. Must be one of: {', '.join(map(str, valid_durations))}")

        # Check if the identity has enough ONX
        onx_balance = self.ledger.get_balance(identity_id, "ONX")
        if onx_balance < amount:
            raise ValueError(f"Insufficient ONX to stake. Required: {amount}, Available: {onx_balance}")

        # Calculate unlock time
        unlock_time = int(time.time()) + (duration_days * 24 * 60 * 60)

        # Calculate reward rate
        reward_rate = duration_config["reward_rate"] * tier["reward_multiplier"]

        # Calculate estimated reward
        estimated_reward = amount * reward_rate * (duration_days / 365)

        # Create stake ID
        stake_id = f"stake_{identity_id}_{uuid.uuid4().hex[:8]}_{int(time.time())}"

        # Create stake record
        stake = {
            "id": stake_id,
            "identity_id": identity_id,
            "amount": amount,
            "role": role,
            "duration_days": duration_days,
            "locked_until": unlock_time,
            "staked_at": int(time.time()),
            "reward_rate": reward_rate,
            "estimated_reward": estimated_reward,
            "benefits": tier["benefits"],
            "status": "active"
        }

        # Debit the ONX from the identity
        self.ledger.debit(identity_id, "ONX", amount)

        # Add the stake to the stakes
        self.stakes.setdefault(identity_id, []).append(stake)

        # Save the stakes
        self._save()

        # Log the transaction
        self.txlog.record("stake_onx", {
            "identity_id": identity_id,
            "amount": amount,
            "role": role,
            "duration_days": duration_days,
            "stake_id": stake_id
        })

        # Check and assign soulbound roles if identity_registry is provided
        assigned_roles = []
        if identity_registry:
            assigned_roles = self.check_and_assign_roles(identity_id, identity_registry)
            stake["assigned_roles"] = assigned_roles

        return stake

    def get_stakes(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get all stakes for an identity.

        Args:
            identity_id: The identity ID to get stakes for.

        Returns:
            A list of stake records.
        """
        return self.stakes.get(identity_id, [])

    def get_stake(self, stake_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a stake by ID.

        Args:
            stake_id: The stake ID to get.

        Returns:
            The stake record, or None if not found.
        """
        for identity_id, stakes in self.stakes.items():
            for stake in stakes:
                if stake["id"] == stake_id:
                    return stake
        return None

    def get_unlockable_stakes(self, identity_id: str) -> List[Dict[str, Any]]:
        """
        Get all unlockable stakes for an identity.

        Args:
            identity_id: The identity ID to get unlockable stakes for.

        Returns:
            A list of unlockable stake records.
        """
        now = int(time.time())
        return [s for s in self.stakes.get(identity_id, []) if s["locked_until"] <= now and s["status"] == "active"]

    def unlock(self, identity_id: str, stake_id: Optional[str] = None, identity_registry=None) -> Dict[str, Any]:
        """
        Unlock stakes for an identity.

        Args:
            identity_id: The identity ID to unlock stakes for.
            stake_id: The stake ID to unlock. If None, all unlockable stakes will be unlocked.
            identity_registry: The Identity Registry to use for identity information.

        Returns:
            A dictionary with the unlock results.

        Raises:
            ValueError: If no unlockable stakes are found or the stake ID is not found.
        """
        # Get unlockable stakes
        if stake_id:
            # Get the specific stake
            stake = self.get_stake(stake_id)
            if not stake:
                raise ValueError(f"Stake not found: {stake_id}")
            if stake["identity_id"] != identity_id:
                raise ValueError(f"Stake {stake_id} does not belong to identity {identity_id}")

            # Check if the stake is unlockable
            now = int(time.time())
            if stake["locked_until"] > now:
                if not self.config["early_unlock_enabled"]:
                    raise ValueError(f"Stake {stake_id} is not yet unlockable and early unlock is disabled")

                # Apply early unlock penalty
                duration_config = next((d for d in self.config["durations"] if d["days"] == stake["duration_days"]), None)
                if not duration_config:
                    raise ValueError(f"Invalid duration: {stake['duration_days']}")

                penalty = duration_config["early_unlock_penalty"]
                penalty_amount = stake["amount"] * penalty
                unlocked_amount = stake["amount"] - penalty_amount

                # Update the stake status
                stake["status"] = "unlocked_early"
                stake["unlocked_at"] = now
                stake["penalty_amount"] = penalty_amount
                stake["unlocked_amount"] = unlocked_amount

                # Credit the unlocked amount to the identity
                self.ledger.credit(identity_id, "ONX", unlocked_amount)

                # Save the stakes
                self._save()

                # Log the transaction
                self.txlog.record("unlock_stake_early", {
                    "identity_id": identity_id,
                    "stake_id": stake_id,
                    "amount": stake["amount"],
                    "penalty_amount": penalty_amount,
                    "unlocked_amount": unlocked_amount
                })

                # Check and revoke soulbound roles if identity_registry is provided
                revoked_roles = []
                if identity_registry:
                    revoked_roles = self.check_and_revoke_roles(identity_id, identity_registry)

                return {
                    "unlocked": [stake],
                    "total_amount": unlocked_amount,
                    "penalty_amount": penalty_amount,
                    "early_unlock": True,
                    "revoked_roles": revoked_roles
                }
            else:
                # Calculate reward
                reward = stake["estimated_reward"]

                # Update the stake status
                stake["status"] = "unlocked"
                stake["unlocked_at"] = now
                stake["reward_amount"] = reward

                # Credit the stake amount and reward to the identity
                self.ledger.credit(identity_id, "ONX", stake["amount"] + reward)

                # Save the stakes
                self._save()

                # Log the transaction
                self.txlog.record("unlock_stake", {
                    "identity_id": identity_id,
                    "stake_id": stake_id,
                    "amount": stake["amount"],
                    "reward_amount": reward
                })

                # Check and revoke soulbound roles if identity_registry is provided
                revoked_roles = []
                if identity_registry:
                    revoked_roles = self.check_and_revoke_roles(identity_id, identity_registry)

                return {
                    "unlocked": [stake],
                    "total_amount": stake["amount"] + reward,
                    "reward_amount": reward,
                    "early_unlock": False,
                    "revoked_roles": revoked_roles
                }
        else:
            # Get all unlockable stakes
            unlockable_stakes = self.get_unlockable_stakes(identity_id)
            if not unlockable_stakes:
                raise ValueError(f"No unlockable stakes found for identity {identity_id}")

            # Calculate total amount and reward
            total_amount = sum(s["amount"] for s in unlockable_stakes)
            total_reward = sum(s["estimated_reward"] for s in unlockable_stakes)

            # Update the stake statuses
            now = int(time.time())
            for stake in unlockable_stakes:
                stake["status"] = "unlocked"
                stake["unlocked_at"] = now
                stake["reward_amount"] = stake["estimated_reward"]

            # Credit the total amount and reward to the identity
            self.ledger.credit(identity_id, "ONX", total_amount + total_reward)

            # Save the stakes
            self._save()

            # Log the transaction
            self.txlog.record("unlock_stakes", {
                "identity_id": identity_id,
                "stake_count": len(unlockable_stakes),
                "total_amount": total_amount,
                "total_reward": total_reward
            })

            # Check and revoke soulbound roles if identity_registry is provided
            revoked_roles = []
            if identity_registry:
                revoked_roles = self.check_and_revoke_roles(identity_id, identity_registry)

            return {
                "unlocked": unlockable_stakes,
                "total_amount": total_amount + total_reward,
                "reward_amount": total_reward,
                "early_unlock": False,
                "revoked_roles": revoked_roles
            }

    def get_active_roles(self, identity_id: str) -> List[str]:
        """
        Get all active roles for an identity.

        Args:
            identity_id: The identity ID to get active roles for.

        Returns:
            A list of active roles.
        """
        active_stakes = [s for s in self.stakes.get(identity_id, []) if s["status"] == "active"]
        return list(set(s["role"] for s in active_stakes))

    def get_active_benefits(self, identity_id: str) -> List[str]:
        """
        Get all active benefits for an identity.

        Args:
            identity_id: The identity ID to get active benefits for.

        Returns:
            A list of active benefits.
        """
        active_stakes = [s for s in self.stakes.get(identity_id, []) if s["status"] == "active"]
        benefits = []
        for stake in active_stakes:
            benefits.extend(stake["benefits"])
        return list(set(benefits))

    def get_total_staked(self, identity_id: str) -> float:
        """
        Get the total amount staked by an identity.

        Args:
            identity_id: The identity ID to get the total staked amount for.

        Returns:
            The total staked amount.
        """
        active_stakes = [s for s in self.stakes.get(identity_id, []) if s["status"] == "active"]
        return sum(s["amount"] for s in active_stakes)

    def check_and_assign_roles(self, identity_id: str, identity_registry) -> List[str]:
        """
        Check and assign soulbound roles to an identity based on their total staked amount.

        Args:
            identity_id: The identity ID to check and assign roles for.
            identity_registry: The Identity Registry to use for identity information.

        Returns:
            A list of assigned roles.
        """
        # Calculate total staked amount
        total_staked = self.get_total_staked(identity_id)

        # Determine roles based on total staked amount
        roles = []
        for tier in self.SOULBOUND_TIERS:
            if total_staked >= tier["min_amount"]:
                roles.append(tier["role"])

        # Get the identity
        identity = identity_registry.get_identity(identity_id)
        if not identity:
            return []

        # Update the identity's reputation with the roles
        for role in roles:
            identity["reputation"][f"badge:{role}"] = 1

        # Save the identity registry
        identity_registry._save()

        # Log the role assignment
        self.txlog.record("assign_roles", {
            "identity_id": identity_id,
            "roles": roles,
            "total_staked": total_staked
        })

        return roles

    def check_and_revoke_roles(self, identity_id: str, identity_registry) -> List[str]:
        """
        Check and revoke soulbound roles from an identity based on their total staked amount.

        Args:
            identity_id: The identity ID to check and revoke roles for.
            identity_registry: The Identity Registry to use for identity information.

        Returns:
            A list of revoked roles.
        """
        # Calculate total staked amount
        total_staked = self.get_total_staked(identity_id)

        # Get the identity
        identity = identity_registry.get_identity(identity_id)
        if not identity:
            return []

        # Determine roles to revoke
        revoked_roles = []
        for tier in self.SOULBOUND_TIERS:
            role = tier["role"]
            if total_staked < tier["min_amount"] and f"badge:{role}" in identity["reputation"]:
                identity["reputation"].pop(f"badge:{role}")
                revoked_roles.append(role)

        # Save the identity registry if roles were revoked
        if revoked_roles:
            identity_registry._save()

            # Log the role revocation
            self.txlog.record("revoke_roles", {
                "identity_id": identity_id,
                "roles": revoked_roles,
                "total_staked": total_staked
            })

        return revoked_roles

    def get_config(self) -> Dict[str, Any]:
        """
        Get the ONX Staking configuration.

        Returns:
            The configuration.
        """
        return self.config

    def update_config(self, updates: Dict[str, Any], updated_by: str) -> Dict[str, Any]:
        """
        Update the ONX Staking configuration.

        Args:
            updates: The updates to apply to the configuration.
            updated_by: The identity ID of the updater.

        Returns:
            The updated configuration.
        """
        # Apply updates
        for key, value in updates.items():
            if key in self.config:
                self.config[key] = value

        # Update metadata
        self.config["last_updated"] = int(time.time())
        self.config["last_updated_by"] = updated_by

        # Save configuration
        self._save()

        # Log the update
        self.txlog.record("update_staking_config", {
            "updates": updates,
            "updated_by": updated_by
        })

        return self.config
