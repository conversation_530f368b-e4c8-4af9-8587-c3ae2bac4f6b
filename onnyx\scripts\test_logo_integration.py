#!/usr/bin/env python3
"""
Test ONNYX Logo Integration

Verify that all logo integrations are working correctly.
"""

import os
import requests
import sys

def test_logo_files():
    """Test that all logo files exist."""
    print("🔍 Testing Logo Files...")
    
    files_to_check = [
        "onnyx_logo.png",
        "web/static/images/onnyx_logo.png",
        "web/static/images/favicon.ico",
        "web/static/images/favicon-16x16.png",
        "web/static/images/favicon-32x32.png",
        "web/static/images/favicon-48x48.png",
        "web/static/images/apple-touch-icon.png"
    ]
    
    results = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file_path} ({size} bytes)")
            results.append(True)
        else:
            print(f"  ❌ {file_path} (missing)")
            results.append(False)
    
    return all(results)

def test_web_pages():
    """Test that web pages load and contain logo references."""
    print("\n🌐 Testing Web Page Logo Integration...")
    
    pages_to_test = [
        ("Homepage", "http://127.0.0.1:5000/"),
        ("Access Portal", "http://127.0.0.1:5000/auth/login"),
        ("Registration", "http://127.0.0.1:5000/register"),
        ("Explorer", "http://127.0.0.1:5000/explorer"),
        ("Validators", "http://127.0.0.1:5000/sela")
    ]
    
    results = []
    for name, url in pages_to_test:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                content = response.text
                
                # Check for logo references
                logo_found = "onnyx_logo.png" in content
                favicon_found = "favicon" in content
                
                if logo_found and favicon_found:
                    print(f"  ✅ {name}: Logo and favicon references found")
                    results.append(True)
                elif logo_found:
                    print(f"  ⚠️  {name}: Logo found, favicon missing")
                    results.append(True)
                else:
                    print(f"  ❌ {name}: No logo references found")
                    results.append(False)
            else:
                print(f"  ❌ {name}: HTTP {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"  ❌ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def test_css_animations():
    """Test that CSS animations are defined."""
    print("\n🎨 Testing CSS Logo Animations...")
    
    css_file = "web/static/css/main.css"
    if not os.path.exists(css_file):
        print(f"  ❌ CSS file not found: {css_file}")
        return False
    
    with open(css_file, 'r') as f:
        css_content = f.read()
    
    animations_to_check = [
        "logoGlow",
        "logoSpin", 
        "logoPulse",
        ".logo-glow",
        ".logo-spin",
        ".logo-pulse",
        ".logo-loading"
    ]
    
    results = []
    for animation in animations_to_check:
        if animation in css_content:
            print(f"  ✅ {animation} animation found")
            results.append(True)
        else:
            print(f"  ❌ {animation} animation missing")
            results.append(False)
    
    return all(results)

def test_favicon_endpoints():
    """Test that favicon endpoints are accessible."""
    print("\n🔗 Testing Favicon Endpoints...")
    
    favicon_urls = [
        "http://127.0.0.1:5000/static/images/favicon.ico",
        "http://127.0.0.1:5000/static/images/favicon-32x32.png",
        "http://127.0.0.1:5000/static/images/apple-touch-icon.png"
    ]
    
    results = []
    for url in favicon_urls:
        try:
            response = requests.head(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {url.split('/')[-1]}: Accessible")
                results.append(True)
            else:
                print(f"  ❌ {url.split('/')[-1]}: HTTP {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"  ❌ {url.split('/')[-1]}: Error - {e}")
            results.append(False)
    
    return all(results)

def main():
    """Run all logo integration tests."""
    print("🎨 ONNYX LOGO INTEGRATION TEST")
    print("=" * 50)
    
    tests = [
        ("Logo Files", test_logo_files),
        ("Web Pages", test_web_pages),
        ("CSS Animations", test_css_animations),
        ("Favicon Endpoints", test_favicon_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"\n❌ {test_name} test failed: {e}")
            results.append(False)
    
    print("\n📊 TEST SUMMARY")
    print("-" * 30)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL LOGO INTEGRATION TESTS PASSED!")
        return 0
    else:
        print("⚠️  SOME LOGO INTEGRATION TESTS FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
