#!/usr/bin/env python3
"""
ONNYX Dashboard Update Verification
Verifies that the web dashboard displays accurate real-time data.
"""

import requests
import json
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def test_web_server_response():
    """Test that the web server is responding."""
    print("🌐 TESTING WEB SERVER RESPONSE")
    print("=" * 40)
    
    try:
        # Test main validator page
        response = requests.get("http://127.0.0.1:5000/sela", timeout=10)
        
        if response.status_code == 200:
            print("✅ Validator page loads successfully")
            print(f"   Status Code: {response.status_code}")
            print(f"   Content Length: {len(response.text)} characters")
            
            # Check for key content
            content = response.text.lower()
            
            # Check for validator names
            validators_found = []
            if "onnyx" in content:
                validators_found.append("ONNYX")
            if "sheryl williams" in content:
                validators_found.append("<PERSON><PERSON> Hair Replacement")
            if "gettwisted" in content or "twisted" in content:
                validators_found.append("GetTwisted Hair Studios")
            
            print(f"   Validators Found: {len(validators_found)}/3")
            for validator in validators_found:
                print(f"     ✅ {validator}")
            
            # Check for mining data
            mining_indicators = []
            if "mining power" in content or "mining tier" in content:
                mining_indicators.append("Mining Information")
            if "onx" in content and ("balance" in content or "earned" in content):
                mining_indicators.append("ONX Token Data")
            if "blocks mined" in content or "blocks validated" in content:
                mining_indicators.append("Block Statistics")
            
            print(f"   Mining Data Found: {len(mining_indicators)}/3")
            for indicator in mining_indicators:
                print(f"     ✅ {indicator}")
            
            return True
            
        else:
            print(f"❌ Validator page failed to load")
            print(f"   Status Code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Web server not responding: {e}")
        return False

def test_validators_redirect():
    """Test that /validators redirects to /sela."""
    print(f"\n🔀 TESTING VALIDATORS REDIRECT")
    print("=" * 35)
    
    try:
        response = requests.get("http://127.0.0.1:5000/validators", timeout=10, allow_redirects=False)
        
        if response.status_code in [301, 302]:
            print("✅ Validators redirect working")
            print(f"   Status Code: {response.status_code}")
            print(f"   Redirect Location: {response.headers.get('Location', 'N/A')}")
            return True
        else:
            print(f"❌ Validators redirect not working")
            print(f"   Status Code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Validators redirect test failed: {e}")
        return False

def verify_database_vs_web_consistency():
    """Verify that web data matches database data."""
    print(f"\n🔍 VERIFYING DATABASE VS WEB CONSISTENCY")
    print("=" * 45)
    
    try:
        # Get database statistics
        db_stats = {
            'active_validators': db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count'],
            'total_blocks': db.query_one("SELECT COUNT(*) as count FROM blocks")['count'],
            'total_mining_power': db.query_one("SELECT COALESCE(SUM(m.mining_power), 0) as total FROM selas s JOIN mining m ON s.sela_id = m.sela_id WHERE s.status = 'active'")['total'],
            'total_rewards': db.query_one("SELECT COALESCE(SUM(balance), 0) as total FROM tokens WHERE token_id = 'ONX'")['total']
        }
        
        print("📊 Database Statistics:")
        print(f"   Active Validators: {db_stats['active_validators']}")
        print(f"   Total Blocks: {db_stats['total_blocks']}")
        print(f"   Total Mining Power: {db_stats['total_mining_power']}x")
        print(f"   Total Rewards: {db_stats['total_rewards']} ONX")
        
        # Test web page content
        response = requests.get("http://127.0.0.1:5000/sela", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check if statistics appear in the web page
            consistency_checks = []
            
            if str(db_stats['active_validators']) in content:
                consistency_checks.append("Active Validators Count")
            
            if str(db_stats['total_blocks']) in content:
                consistency_checks.append("Total Blocks Count")
            
            if str(int(db_stats['total_mining_power'])) in content or f"{db_stats['total_mining_power']:.1f}" in content:
                consistency_checks.append("Total Mining Power")
            
            if str(int(db_stats['total_rewards'])) in content or f"{db_stats['total_rewards']:.1f}" in content:
                consistency_checks.append("Total Rewards")
            
            print(f"\n✅ Consistency Checks Passed: {len(consistency_checks)}/4")
            for check in consistency_checks:
                print(f"     ✅ {check}")
            
            return len(consistency_checks) >= 3  # At least 3 out of 4 should match
        else:
            print("❌ Could not fetch web page for consistency check")
            return False
            
    except Exception as e:
        print(f"❌ Consistency check failed: {e}")
        return False

def test_specific_validator_data():
    """Test that specific validator data is displayed correctly."""
    print(f"\n👥 TESTING SPECIFIC VALIDATOR DATA")
    print("=" * 40)
    
    try:
        # Get specific validator data from database
        validators = db.query("""
            SELECT s.name, i.name as owner_name, m.mining_tier, m.mining_power, 
                   COALESCE(t.balance, 0) as onx_balance
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            LEFT JOIN mining m ON s.sela_id = m.sela_id
            LEFT JOIN tokens t ON i.identity_id = t.identity_id AND t.token_id = 'ONX'
            WHERE s.status = 'active'
            ORDER BY m.mining_power DESC
        """)
        
        # Test web page content
        response = requests.get("http://127.0.0.1:5000/sela", timeout=10)
        
        if response.status_code == 200:
            content = response.text.lower()
            
            validator_checks = []
            
            for validator in validators:
                checks_for_validator = []
                
                # Check if validator name appears
                if validator['name'].lower() in content:
                    checks_for_validator.append("Name")
                
                # Check if owner name appears
                if validator['owner_name'].lower() in content:
                    checks_for_validator.append("Owner")
                
                # Check if mining tier appears
                if validator['mining_tier'] and validator['mining_tier'].lower() in content:
                    checks_for_validator.append("Mining Tier")
                
                # Check if mining power appears
                if validator['mining_power'] and str(validator['mining_power']) in content:
                    checks_for_validator.append("Mining Power")
                
                validator_checks.append({
                    'name': validator['name'],
                    'checks': checks_for_validator,
                    'total_checks': len(checks_for_validator)
                })
                
                print(f"✅ {validator['name']}: {len(checks_for_validator)}/4 data points found")
                for check in checks_for_validator:
                    print(f"     ✅ {check}")
            
            # Overall assessment
            total_checks = sum(v['total_checks'] for v in validator_checks)
            max_possible = len(validators) * 4
            
            print(f"\n📊 Overall Validator Data: {total_checks}/{max_possible} data points found")
            
            return total_checks >= (max_possible * 0.75)  # At least 75% of data should be present
        else:
            print("❌ Could not fetch web page for validator data check")
            return False
            
    except Exception as e:
        print(f"❌ Validator data check failed: {e}")
        return False

def main():
    """Run comprehensive dashboard update verification."""
    print("🚀 ONNYX DASHBOARD UPDATE VERIFICATION")
    print("=" * 60)
    print("Verifying that the web dashboard displays accurate real-time data")
    
    # Run all tests
    tests = [
        ("Web Server Response", test_web_server_response),
        ("Validators Redirect", test_validators_redirect),
        ("Database Consistency", verify_database_vs_web_consistency),
        ("Validator Data Display", test_specific_validator_data)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Final assessment
    print(f"\n🎯 DASHBOARD UPDATE VERIFICATION RESULTS")
    print("=" * 50)
    
    passed_tests = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / len(tests)) * 100
    
    print(f"\n📊 SUCCESS RATE: {passed_tests}/{len(tests)} tests passed ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        print(f"\n🎉 DASHBOARD UPDATE SUCCESSFUL!")
        print("✅ The validator network page displays accurate real-time data")
        print("✅ All 3 production validators are visible with mining information")
        print("✅ Network statistics show current blockchain state")
        print("✅ Database integration is working correctly")
        print(f"\n🌐 Access the updated dashboard at:")
        print("   • http://127.0.0.1:5000/validators")
        print("   • http://127.0.0.1:5000/sela")
    else:
        print(f"\n⚠️ DASHBOARD UPDATE NEEDS ATTENTION")
        print("Some tests failed - please review the issues above")
    
    return success_rate >= 75

if __name__ == "__main__":
    main()
