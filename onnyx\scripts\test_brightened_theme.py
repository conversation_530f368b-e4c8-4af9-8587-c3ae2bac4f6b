#!/usr/bin/env python3
"""
Test Brightened Onyx Stone Theme

Verify that the brightened theme enhancements are working correctly across all pages.
"""

import requests
import sys
import re

def test_brightened_theme():
    """Test the brightened Onyx Stone theme implementation."""
    print("🎨 TESTING BRIGHTENED ONYX STONE THEME")
    print("=" * 50)
    
    # Test pages with their expected brightened elements
    test_pages = [
        {
            "name": "Landing Page",
            "url": "http://127.0.0.1:5000/",
            "brightened_elements": [
                "text-text-secondary",
                "text-text-tertiary", 
                "text-cyber-cyan-glow",
                "text-cyber-purple-glow",
                "text-cyber-blue-glow",
                "glow-effect",
                "purple-glow-effect"
            ]
        },
        {
            "name": "Sela Directory",
            "url": "http://127.0.0.1:5000/sela/",
            "brightened_elements": [
                "text-text-primary",
                "text-text-secondary",
                "text-text-tertiary",
                "text-text-muted",
                "text-cyber-cyan-glow",
                "text-cyber-purple-glow",
                "text-cyber-blue-glow"
            ]
        },
        {
            "name": "Blockchain Explorer",
            "url": "http://127.0.0.1:5000/explorer/",
            "brightened_elements": [
                "onnyx_logo.png",
                "hologram-text",
                "glass-card",
                "neuro-card"
            ]
        },
        {
            "name": "Identity Registration",
            "url": "http://127.0.0.1:5000/auth/register/identity",
            "brightened_elements": [
                "onnyx_logo.png",
                "glass-card",
                "form-input"
            ]
        },
        {
            "name": "Login Portal",
            "url": "http://127.0.0.1:5000/auth/login",
            "brightened_elements": [
                "onnyx_logo.png",
                "glass-card",
                "glass-button-primary"
            ]
        }
    ]
    
    print("\n📋 Testing Brightened Theme Elements:")
    
    results = []
    for page in test_pages:
        try:
            response = requests.get(page["url"], timeout=10)
            if response.status_code == 200:
                content = response.text
                
                # Check for brightened elements
                found_elements = []
                missing_elements = []
                
                for element in page["brightened_elements"]:
                    if element in content:
                        found_elements.append(element)
                    else:
                        missing_elements.append(element)
                
                # Check for CSS variables usage
                has_css_variables = "--text-primary" in content or "text-text-primary" in content
                
                # Check for enhanced glow effects
                has_glow_effects = "glow-effect" in content or "purple-glow-effect" in content
                
                # Check for improved glass cards
                has_enhanced_glass = "glass-card" in content and "backdrop-blur" in content
                
                success_rate = len(found_elements) / len(page["brightened_elements"]) * 100
                
                if success_rate >= 80:
                    print(f"  ✅ {page['name']}: {success_rate:.0f}% brightened ({len(found_elements)}/{len(page['brightened_elements'])} elements)")
                    results.append(True)
                elif success_rate >= 50:
                    print(f"  ⚠️  {page['name']}: {success_rate:.0f}% brightened ({len(found_elements)}/{len(page['brightened_elements'])} elements)")
                    if missing_elements:
                        print(f"      Missing: {', '.join(missing_elements[:3])}{'...' if len(missing_elements) > 3 else ''}")
                    results.append(True)
                else:
                    print(f"  ❌ {page['name']}: {success_rate:.0f}% brightened ({len(found_elements)}/{len(page['brightened_elements'])} elements)")
                    if missing_elements:
                        print(f"      Missing: {', '.join(missing_elements)}")
                    results.append(False)
                    
            else:
                print(f"  ❌ {page['name']}: HTTP {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"  ❌ {page['name']}: Error - {e}")
            results.append(False)
    
    # Test CSS enhancements
    print("\n🎯 Testing CSS Enhancements:")
    css_tests = []
    
    try:
        css_response = requests.get("http://127.0.0.1:5000/static/css/main.css", timeout=5)
        if css_response.status_code == 200:
            css_content = css_response.text
            
            # Check for enhanced CSS variables
            has_text_variables = "--text-primary: #eeeeee" in css_content
            has_enhanced_glow = "@keyframes purpleGlow" in css_content
            has_improved_glass = "backdrop-blur: blur(12px)" in css_content
            has_utility_classes = ".text-cyber-cyan-glow" in css_content
            
            print(f"  ✅ Enhanced CSS Variables: {'Found' if has_text_variables else 'Missing'}")
            print(f"  ✅ Purple Glow Animation: {'Found' if has_enhanced_glow else 'Missing'}")
            print(f"  ✅ Improved Glass Effects: {'Found' if has_improved_glass else 'Missing'}")
            print(f"  ✅ Glow Utility Classes: {'Found' if has_utility_classes else 'Missing'}")
            
            css_tests = [has_text_variables, has_enhanced_glow, has_improved_glass, has_utility_classes]
        else:
            print(f"  ❌ CSS File: HTTP {css_response.status_code}")
            css_tests = [False]
            
    except Exception as e:
        print(f"  ❌ CSS File: Error - {e}")
        css_tests = [False]
    
    # Test Tailwind configuration
    print("\n⚙️  Testing Tailwind Configuration:")
    try:
        base_response = requests.get("http://127.0.0.1:5000/", timeout=5)
        if base_response.status_code == 200:
            base_content = base_response.text
            
            # Check for updated Tailwind colors
            has_updated_onyx = "'black': '#1a1a1a'" in base_content
            has_text_colors = "'primary': '#eeeeee'" in base_content
            has_enhanced_comments = "// Brightened for better accessibility" in base_content
            
            print(f"  ✅ Updated Onyx Colors: {'Found' if has_updated_onyx else 'Missing'}")
            print(f"  ✅ Text Color Variables: {'Found' if has_text_colors else 'Missing'}")
            print(f"  ✅ Enhancement Comments: {'Found' if has_enhanced_comments else 'Missing'}")
            
            tailwind_tests = [has_updated_onyx, has_text_colors]
        else:
            print(f"  ❌ Base Template: HTTP {base_response.status_code}")
            tailwind_tests = [False]
            
    except Exception as e:
        print(f"  ❌ Base Template: Error - {e}")
        tailwind_tests = [False]
    
    # Summary
    print("\n📊 BRIGHTENED THEME TEST SUMMARY")
    print("=" * 40)
    
    pages_passed = sum(results)
    css_passed = sum(css_tests)
    tailwind_passed = sum(tailwind_tests)
    
    total_passed = pages_passed + css_passed + tailwind_passed
    total_tests = len(results) + len(css_tests) + len(tailwind_tests)
    
    print(f"Page Enhancements: {pages_passed}/{len(results)} pages brightened")
    print(f"CSS Enhancements: {css_passed}/{len(css_tests)} features implemented")
    print(f"Tailwind Updates: {tailwind_passed}/{len(tailwind_tests)} configurations updated")
    print(f"Overall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("\n🎉 ALL BRIGHTENED THEME TESTS PASSED!")
        print("✅ Background colors optimized for accessibility")
        print("✅ Text colors enhanced for better readability")
        print("✅ Accent colors boosted with glow effects")
        print("✅ Glass cards improved with better contrast")
        print("✅ Neumorphic elements enhanced")
        print("✅ Utility classes added for consistent styling")
        print("✅ Sophisticated dark aesthetic maintained")
        return True
    else:
        print("\n⚠️  SOME BRIGHTENED THEME TESTS FAILED")
        print("The theme has been partially enhanced but may need additional adjustments.")
        return False

def main():
    """Main entry point."""
    success = test_brightened_theme()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
