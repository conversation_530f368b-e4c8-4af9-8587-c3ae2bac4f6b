# Onnyx Business Module

The Business module provides functionality for registering and managing business entities (Selas) on the Onnyx blockchain.

## Overview

A Sela is a business entity registered on the Onnyx blockchain. Registration requires staking ONX tokens, with the amount determined by the identity's reputation. Selas can have members, governance structures, and metadata.

## Components

### SelaRegistry

The `SelaRegistry` class manages the registration and updating of Selas. It provides methods for:

- Registering a new Sela
- Updating a Sela's information
- Adding members to a Sela
- Removing members from a Sela
- Retrieving Selas by ID, owner, or sector

### Stake Policy

The `stake_policy` module provides functions for calculating the stake required for Sela registration based on identity reputation and network maturity. It also defines stake tiers with different benefits.

## Stake Tiers

Selas are categorized into tiers based on the reputation of the owner:

- **Platinum Tier**: For identities with reputation score >= 100
  - 50% discount on stake amount
  - Priority transaction processing
  - Reduced fees
  - Governance voting rights
  - Access to exclusive network features

- **Gold Tier**: For identities with reputation score >= 50
  - Standard stake amount
  - Standard transaction processing
  - Standard fees
  - Governance voting rights

- **Silver Tier**: For identities with reputation score < 50
  - 2x penalty on stake amount
  - Standard transaction processing
  - Standard fees

## API Endpoints

### Register a Sela

```
POST /sela/register
```

Request body:
```json
{
  "identity_id": "string",
  "name": "string",
  "sector": "string",
  "metadata": {},
  "message": "string",
  "signature": "string"
}
```

### List Selas

```
GET /sela/list
```

Query parameters:
- `sector`: Filter by sector
- `owner`: Filter by owner
- `limit`: Maximum number of results to return
- `offset`: Number of results to skip

### Get a Sela

```
GET /sela/get/{sela_id}
```

### Update a Sela

```
POST /sela/update
```

Request body:
```json
{
  "identity_id": "string",
  "field": "string",
  "value": "any",
  "message": "string",
  "signature": "string"
}
```

### Add a Member

```
POST /sela/add_member
```

Request body:
```json
{
  "sela_id": "string",
  "identity_id": "string",
  "added_by": "string",
  "message": "string",
  "signature": "string"
}
```

### Remove a Member

```
POST /sela/remove_member
```

Request body:
```json
{
  "sela_id": "string",
  "identity_id": "string",
  "removed_by": "string",
  "message": "string",
  "signature": "string"
}
```

### Get Stake Tiers

```
GET /sela/tiers
```

## Example Usage

### Registering a Sela

```python
from src.business.sela import SelaRegistry
from src.business.stake_policy import calculate_sela_stake
from src.tokens.ledger import TokenLedger
from src.identity.registry import IdentityRegistry

# Initialize components
ledger = TokenLedger()
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()

# Calculate stake required
identity = identity_registry.get_identity("identity_id")
rep_score = sum(identity.reputation.values()) if identity.reputation else 0
stake_required = calculate_sela_stake(rep_score)

# Register a Sela
sela = sela_registry.register_sela(
    identity_id="identity_id",
    name="My Business",
    sector="Technology",
    stake_required=stake_required,
    ledger=ledger,
    identity_registry=identity_registry
)
```

### Adding a Member

```python
sela = sela_registry.add_member(
    sela_id="sela_id",
    identity_id="member_identity_id",
    identity_registry=identity_registry
)
```

### Updating a Sela

```python
sela = sela_registry.update_sela(
    identity_id="identity_id",
    field="name",
    value="New Business Name"
)
```

## Future Enhancements

- **Governance**: Implement proposal creation, voting, and execution
- **Reputation**: Integrate with the reputation system to provide benefits based on Sela reputation
- **Tokenization**: Allow Selas to issue their own tokens
- **Marketplace**: Create a marketplace for Selas to offer products and services
- **Verification**: Implement a verification process for Selas
