# Onnyx Transaction Viewer

This is a web-based transaction viewer for the Onnyx blockchain. It provides a clean, browser-based UI for viewing transactions, tokens, identities, and the mempool.

## Features

- **Transaction Viewing**: View all confirmed transactions with pagination
- **Mempool Monitoring**: View pending transactions in the mempool
- **Token Registry**: Browse all tokens on the blockchain
- **Identity Registry**: Browse all identities on the blockchain
- **Search**: Search for tokens, identities, or transactions
- **Transaction Details**: View detailed information about transactions
- **Token Details**: View detailed information about tokens, including transactions
- **Identity Details**: View detailed information about identities, including transactions and reputation

## Architecture

The Onnyx Transaction Viewer is built using Flask, a lightweight web framework for Python. It uses the following components:

- **Flask**: Web framework
- **Jinja2**: Templating engine
- **TxLogger**: Transaction logging
- **Mempool**: Pending transaction management
- **TokenRegistry**: Token management
- **IdentityRegistry**: Identity management

## Running the Viewer

To run the Onnyx Transaction Viewer, use the following command:

```bash
python onnyx-explorer.py
```

This will start the web server on port 8080. You can then access the viewer at http://localhost:8080.

## Directory Structure

- `app.py`: Main Flask application
- `templates/`: HTML templates
  - `base.html`: Base template with common elements
  - `index.html`: Home page
  - `transactions.html`: Transaction list
  - `transaction_detail.html`: Transaction details
  - `tokens.html`: Token list
  - `token_detail.html`: Token details
  - `identities.html`: Identity list
  - `identity_detail.html`: Identity details
  - `search.html`: Search results
- `static/`: Static files
  - `style.css`: CSS styles

## API Integration

The Onnyx Transaction Viewer integrates with the Onnyx API to provide real-time data. It uses the following components:

- **TxLogger**: For retrieving transaction history
- **Mempool**: For retrieving pending transactions
- **TokenRegistry**: For retrieving token information
- **IdentityRegistry**: For retrieving identity information

## Transaction Logging

The Onnyx Transaction Viewer uses the TxLogger component to log all transactions. Each transaction is logged with the following information:

- **Transaction ID**: A unique identifier for the transaction
- **Type**: The type of transaction (e.g., forktoken, minttoken, sendtoken, burntoken)
- **Timestamp**: The time the transaction was created
- **Payload**: The transaction data

## Mempool Management

The Onnyx Transaction Viewer uses the Mempool component to manage pending transactions. Each pending transaction is stored with the following information:

- **Transaction ID**: A unique identifier for the transaction
- **Type**: The type of transaction (e.g., forktoken, minttoken, sendtoken, burntoken)
- **Timestamp**: The time the transaction was created
- **Payload**: The transaction data

When a transaction is confirmed, it is removed from the mempool and added to the transaction log.

## Future Enhancements

- **Real-time Updates**: Use WebSockets to provide real-time updates of transactions and mempool
- **Advanced Search**: Implement advanced search functionality with filters
- **Transaction Graphs**: Visualize transaction relationships
- **Token Analytics**: Provide analytics for tokens, such as transaction volume and holder distribution
- **Identity Analytics**: Provide analytics for identities, such as reputation and token holdings
- **Mobile Optimization**: Optimize the UI for mobile devices
