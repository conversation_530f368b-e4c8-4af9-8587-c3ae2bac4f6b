# src/db/schema/sela.py

from src.db.manager import db_manager

def create_sela_tables():
    """
    Create the tables for the Sela system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()

    # Create selas table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS selas (
        sela_id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        founder_id TEXT NOT NULL,
        type TEXT NOT NULL,
        sector TEXT NOT NULL,
        description TEXT,
        metadata TEXT,
        created_at INTEGER NOT NULL,
        stake_amount INTEGER NOT NULL,
        status TEXT NOT NULL,
        FOREIGN KEY (founder_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create sela_members table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sela_members (
        sela_id TEXT NOT NULL,
        identity_id TEXT NOT NULL,
        role TEXT NOT NULL,
        joined_at INTEGER NOT NULL,
        PRIMARY KEY (sela_id, identity_id),
        FOREIGN KEY (sela_id) REFERENCES selas (sela_id) ON DELETE CASCADE,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (role) REFERENCES roles (role) ON DELETE RESTRICT
    )
    ''')

    # Create sela_services table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sela_services (
        service_id TEXT PRIMARY KEY,
        sela_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (sela_id) REFERENCES selas (sela_id) ON DELETE CASCADE
    )
    ''')

    # Create sela_stakes table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sela_stakes (
        sela_id TEXT NOT NULL,
        identity_id TEXT NOT NULL,
        amount INTEGER NOT NULL,
        staked_at INTEGER NOT NULL,
        PRIMARY KEY (sela_id, identity_id),
        FOREIGN KEY (sela_id) REFERENCES selas (sela_id) ON DELETE CASCADE,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_selas_founder_id ON selas (founder_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_selas_type ON selas (type)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_selas_sector ON selas (sector)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_sela_members_identity_id ON sela_members (identity_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_sela_members_role ON sela_members (role)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_sela_services_sela_id ON sela_services (sela_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_sela_stakes_identity_id ON sela_stakes (identity_id)')

    conn.commit()
