"""
Onnyx Identity Model

This module provides the Identity model for the Onnyx blockchain.
"""

import time
import logging
from typing import Dict, Any, List, Optional, ClassVar

from shared.models.base import BaseModel
from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.identity")

class Identity(BaseModel):
    """
    Identity model for the Onnyx blockchain.
    """

    # Table name
    table_name: ClassVar[str] = "identities"

    # Primary key column
    primary_key: ClassVar[str] = "identity_id"

    # JSON fields
    json_fields: ClassVar[List[str]] = ["metadata"]

    def __init__(
        self,
        identity_id: str,
        name: str,
        public_key: str,
        email: str = None,
        nation_id: str = None,
        metadata: Dict[str, Any] = None,
        status: str = "active",
        created_at: int = None,
        updated_at: int = None,
        **kwargs
    ):
        """
        Initialize the Identity model.

        Args:
            identity_id: The identity ID
            name: The identity name
            public_key: The identity public key
            email: The identity email
            nation_id: The nation ID this identity belongs to
            metadata: The identity metadata
            status: The identity status
            created_at: The creation timestamp
            updated_at: The update timestamp
            **kwargs: Additional attributes
        """
        self.identity_id = identity_id
        self.name = name
        self.public_key = public_key
        self.email = email
        self.nation_id = nation_id
        self.metadata = metadata or {}
        self.status = status
        self.created_at = created_at or int(time.time())
        self.updated_at = updated_at or int(time.time())

        # Backward compatibility
        self.nation = nation_id

        super().__init__(**kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the identity to a dictionary for database storage.

        Returns:
            A dictionary containing identity attributes
        """
        data = {
            'identity_id': self.identity_id,
            'name': self.name,
            'public_key': self.public_key,
            'email': self.email,
            'nation_id': self.nation_id,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

        # Handle JSON fields
        if hasattr(self, 'metadata') and self.metadata is not None:
            import json
            data['metadata'] = json.dumps(self.metadata)
        else:
            data['metadata'] = '{}'

        return data

    @classmethod
    def get_by_name(cls, name: str) -> Optional['Identity']:
        """
        Get an identity by name.

        Args:
            name: The identity name

        Returns:
            The identity or None if not found
        """
        query = f"SELECT * FROM {cls.table_name} WHERE name = ?"
        row = db.query_one(query, (name,))

        if row:
            return cls.from_dict(row)

        return None

    @classmethod
    def get_by_public_key(cls, public_key: str) -> Optional['Identity']:
        """
        Get an identity by public key.

        Args:
            public_key: The identity public key

        Returns:
            The identity or None if not found
        """
        query = f"SELECT * FROM {cls.table_name} WHERE public_key = ?"
        row = db.query_one(query, (public_key,))

        if row:
            return cls.from_dict(row)

        return None

    @classmethod
    def get_by_nation(cls, nation: str, limit: int = 100, offset: int = 0) -> List['Identity']:
        """
        Get identities by nation.

        Args:
            nation: The nation name
            limit: The maximum number of identities to return
            offset: The offset for pagination

        Returns:
            A list of identities
        """
        query = f"""
            SELECT *
            FROM {cls.table_name}
            WHERE nation = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """
        rows = db.query(query, (nation, limit, offset))

        return [cls.from_dict(row) for row in rows]

    def get_reputation(self) -> List[Dict[str, Any]]:
        """
        Get the identity's reputation.

        Returns:
            A list of reputation entries
        """
        query = "SELECT * FROM reputation WHERE identity_id = ? ORDER BY timestamp DESC"
        return db.query(query, (self.identity_id,))

    def get_badges(self) -> List[Dict[str, Any]]:
        """
        Get the identity's badges.

        Returns:
            A list of badge entries
        """
        query = "SELECT * FROM badges WHERE identity_id = ? ORDER BY timestamp DESC"
        return db.query(query, (self.identity_id,))

    def get_etzem_score(self) -> Optional[Dict[str, Any]]:
        """
        Get the identity's Etzem score.

        Returns:
            The Etzem score or None if not found
        """
        query = "SELECT * FROM etzem_scores WHERE identity_id = ?"
        return db.query_one(query, (self.identity_id,))

    def get_token_balances(self) -> List[Dict[str, Any]]:
        """
        Get the identity's token balances.

        Returns:
            A list of token balance entries
        """
        query = """
            SELECT tb.*, t.name, t.symbol, t.category
            FROM token_balances tb
            JOIN tokens t ON tb.token_id = t.token_id
            WHERE tb.identity_id = ?
            ORDER BY tb.balance DESC
        """
        return db.query(query, (self.identity_id,))

    def get_transactions(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get the identity's transactions.

        Args:
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of transaction entries
        """
        query = """
            SELECT *
            FROM transactions
            WHERE sender = ?
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        """
        return db.query(query, (self.identity_id, limit, offset))

    def get_token_transactions(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get the identity's token transactions.

        Args:
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of token transaction entries
        """
        query = """
            SELECT tt.*, t.name, t.symbol, t.category
            FROM token_transactions tt
            JOIN tokens t ON tt.token_id = t.token_id
            WHERE tt.from_id = ? OR tt.to_id = ?
            ORDER BY tt.timestamp DESC
            LIMIT ? OFFSET ?
        """
        return db.query(query, (self.identity_id, self.identity_id, limit, offset))

    def get_selas(self) -> List[Dict[str, Any]]:
        """
        Get the identity's Selas.

        Returns:
            A list of Sela entries
        """
        query = "SELECT * FROM selas WHERE identity_id = ?"
        return db.query(query, (self.identity_id,))

    def get_zeman_credits(self) -> List[Dict[str, Any]]:
        """
        Get the identity's Zeman credits.

        Returns:
            A list of Zeman credit entries
        """
        query = "SELECT * FROM zeman_credits WHERE identity_id = ? ORDER BY timestamp DESC"
        return db.query(query, (self.identity_id,))

    def get_voice_scrolls(self) -> List[Dict[str, Any]]:
        """
        Get the identity's Voice Scrolls.

        Returns:
            A list of Voice Scroll entries
        """
        query = "SELECT * FROM voice_scrolls WHERE proposer_id = ? ORDER BY created_at DESC"
        return db.query(query, (self.identity_id,))

    def get_scroll_votes(self) -> List[Dict[str, Any]]:
        """
        Get the identity's Voice Scroll votes.

        Returns:
            A list of Voice Scroll vote entries
        """
        query = "SELECT * FROM scroll_votes WHERE voter_id = ? ORDER BY timestamp DESC"
        return db.query(query, (self.identity_id,))

    def get_nation(self) -> Optional[str]:
        """
        Get the nation this identity belongs to.

        Returns:
            The nation name or None if not found
        """
        return self.nation

    def save(self) -> None:
        """
        Save the identity.
        """
        data = self.to_dict()

        # Add more detailed logging
        logger.info(f"Saving identity: {self.identity_id}, name: {self.name}")
        logger.info(f"Data to save: {data}")

        try:
            # Check if the identity already exists
            existing = db.query_one(f"SELECT identity_id FROM {self.table_name} WHERE identity_id = ?", (self.identity_id,))

            if existing:
                logger.info(f"Identity {self.identity_id} already exists, updating")
                # Update the identity
                db.update(
                    self.table_name,
                    data,
                    "identity_id = ?",
                    (self.identity_id,)
                )
            else:
                logger.info(f"Identity {self.identity_id} does not exist, inserting")
                # Insert a new identity
                db.insert(self.table_name, data)

            logger.info(f"Successfully saved identity {self.identity_id}")
        except Exception as e:
            logger.error(f"Error saving identity: {str(e)}")
            # Log the full exception traceback
            import traceback
            logger.error(traceback.format_exc())
            raise

    @classmethod
    def create(
        cls,
        name: str,
        public_key: str,
        email: str = None,
        nation_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> 'Identity':
        """
        Create a new identity.

        Args:
            name: The identity name
            public_key: The identity public key
            email: The identity email
            nation_id: The nation ID this identity belongs to
            metadata: The identity metadata

        Returns:
            The created identity
        """
        # Generate the identity ID from the public key
        import hashlib
        id_value = hashlib.sha256(public_key.encode()).hexdigest()

        # Create the identity
        identity = cls(
            identity_id=id_value,
            name=name,
            public_key=public_key,
            email=email,
            nation_id=nation_id,
            metadata=metadata or {}
        )

        # Save the identity
        identity.save()

        # Try to create a blockchain transaction for the identity creation
        try:
            # Import here to avoid circular imports
            from shared.models.transaction import Transaction

            # Prepare transaction data
            tx_data = {
                "name": name,
                "nation_id": nation_id,
                "role": metadata.get("purpose", "Citizen") if metadata else "Citizen",
                "identity_id": id_value
            }

            # Create a signature (in a real system, this would be signed with a private key)
            # For now, we'll use a placeholder signature
            signature = f"system_signature_{int(time.time())}"

            # Create the transaction
            Transaction.create(
                op="CREATE_IDENTITY",
                data=tx_data,
                sender="SYSTEM",  # System-generated transaction
                signature=signature
            )

            logger.info(f"Created blockchain transaction for identity creation: {id_value}")
        except Exception as e:
            logger.error(f"Error creating blockchain transaction for identity: {str(e)}")
            # We'll continue even if the transaction creation fails
            # The identity is still created in the database

        return identity
