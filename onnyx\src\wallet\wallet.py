# src/wallet/wallet.py

import os
import hashlib
import json
from ecdsa import Signing<PERSON>ey, Verifying<PERSON>ey, SECP256k1
import base64

class OnnyxWallet:
    def __init__(self, path=None):
        """
        Initialize a wallet with a key file path.
        If path is None, a default path in the data directory will be used.
        """
        if path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.path = os.path.join(data_dir, "wallet.json")
        else:
            self.path = path

        self.key = None
        self.public_key = None

        if os.path.exists(self.path):
            self.load()
        else:
            self.generate()

    def generate(self):
        """Generate a new private key."""
        self.key = SigningKey.generate(curve=SECP256k1)
        self.public_key = self.key.get_verifying_key()
        self.save()
        return self.get_address()

    def save(self):
        """Save the wallet to a file."""
        data = {
            "private_key": self.key.to_string().hex(),
            "public_key": self.public_key.to_string().hex(),
            "address": self.get_address()
        }
        with open(self.path, "w") as f:
            json.dump(data, f, indent=2)

    def load(self):
        """Load the wallet from a file."""
        try:
            # Check if the file is empty
            if os.path.getsize(self.path) == 0:
                self.generate()
                return

            with open(self.path, "r") as f:
                data = json.load(f)

            if "private_key" in data:
                # Load from hex string
                self.key = SigningKey.from_string(bytes.fromhex(data["private_key"]), curve=SECP256k1)
                self.public_key = self.key.get_verifying_key()
            else:
                # Backward compatibility with PEM format
                self.key = SigningKey.from_pem(data["key"])
                self.public_key = self.key.get_verifying_key()
        except json.JSONDecodeError:
            # File exists but is not valid JSON
            self.generate()
        except Exception as e:
            # Handle other errors
            print(f"Error loading wallet: {str(e)}")
            print("Generating a new wallet...")
            self.generate()

    def get_private_key(self):
        """Get the private key as a hex string."""
        return self.key.to_string().hex()

    def get_public_key(self):
        """Get the public key as a hex string."""
        return self.public_key.to_string().hex()

    def get_address(self):
        """
        Generate an Onnyx address from the public key.
        Similar to Bitcoin: RIPEMD160(SHA256(pubkey))
        """
        pubkey = self.public_key.to_string()
        sha256 = hashlib.sha256(pubkey).digest()
        ripemd160 = hashlib.new('ripemd160', sha256).digest()

        # Add a prefix for Onnyx addresses (similar to Bitcoin's 0x00)
        prefix = b'\x4f\x4e\x58'  # 'ONX' in ASCII
        prefixed = prefix + ripemd160

        # Add a checksum (first 4 bytes of double SHA256)
        checksum = hashlib.sha256(hashlib.sha256(prefixed).digest()).digest()[:4]

        # Combine everything and encode in base58
        address_bytes = prefixed + checksum
        address = base58_encode(address_bytes)

        return address

    def sign(self, message):
        """
        Sign a message with the private key.

        Args:
            message: The message to sign (string or bytes)

        Returns:
            The signature as a hex string
        """
        if isinstance(message, str):
            message = message.encode()

        signature = self.key.sign(message)
        return signature.hex()

    def verify(self, message, signature):
        """
        Verify a signature with the public key.

        Args:
            message: The message that was signed (string or bytes)
            signature: The signature to verify (hex string)

        Returns:
            True if the signature is valid, False otherwise
        """
        if isinstance(message, str):
            message = message.encode()

        try:
            return self.public_key.verify(bytes.fromhex(signature), message)
        except:
            return False

    @staticmethod
    def verify_external(message, signature, public_key_hex):
        """
        Verify a signature with an external public key.

        Args:
            message: The message that was signed (string or bytes)
            signature: The signature to verify (hex string)
            public_key_hex: The public key as a hex string

        Returns:
            True if the signature is valid, False otherwise
        """
        if isinstance(message, str):
            message = message.encode()

        try:
            public_key = VerifyingKey.from_string(bytes.fromhex(public_key_hex), curve=SECP256k1)
            return public_key.verify(bytes.fromhex(signature), message)
        except:
            return False

    @staticmethod
    def address_from_public_key(public_key_hex):
        """
        Generate an Onnyx address from a public key hex string.

        Args:
            public_key_hex: The public key as a hex string

        Returns:
            The Onnyx address
        """
        pubkey = bytes.fromhex(public_key_hex)
        sha256 = hashlib.sha256(pubkey).digest()
        ripemd160 = hashlib.new('ripemd160', sha256).digest()

        # Add a prefix for Onnyx addresses (similar to Bitcoin's 0x00)
        prefix = b'\x4f\x4e\x58'  # 'ONX' in ASCII
        prefixed = prefix + ripemd160

        # Add a checksum (first 4 bytes of double SHA256)
        checksum = hashlib.sha256(hashlib.sha256(prefixed).digest()).digest()[:4]

        # Combine everything and encode in base58
        address_bytes = prefixed + checksum
        address = base58_encode(address_bytes)

        return address

# Base58 encoding (similar to Bitcoin)
def base58_encode(data):
    """Encode bytes in base58 (similar to Bitcoin)."""
    # Base58 character set
    alphabet = '**********************************************************'

    # Convert bytes to integer
    n = int.from_bytes(data, byteorder='big')

    # Encode to base58
    result = ''
    while n > 0:
        n, remainder = divmod(n, 58)
        result = alphabet[remainder] + result

    # Add leading zeros
    for byte in data:
        if byte == 0:
            result = alphabet[0] + result
        else:
            break

    return result
