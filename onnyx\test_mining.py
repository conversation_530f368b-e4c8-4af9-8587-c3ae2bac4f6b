"""
Test the Onnyx Mining System

This script tests the Onnyx Mining System by creating a new block.
"""

import os
import json
import time
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from consensus.miner import BlockMiner
from node.blockchain import LocalBlockchain
from node.mempool import Mempool
from tokens.ledger import TokenLedger

# Mock identity registry
class MockIdentityRegistry:
    def __init__(self):
        self.identities = {}

    def get_identity(self, identity_id):
        return self.identities.get(identity_id)

# Create a mock identity registry
identity_registry = MockIdentityRegistry()

def setup_test_data():
    """Set up test data for the mining test."""
    # Create a test identity
    identity_id = "test_miner"
    identity_registry.identities[identity_id] = {
        "name": "Test Miner",
        "public_key": "0x123456789abcdef",
        "created_at": int(time.time())
    }

    # Create a recipient identity
    recipient_id = "recipient"
    identity_registry.identities[recipient_id] = {
        "name": "Test Recipient",
        "public_key": "0x987654321fedcba",
        "created_at": int(time.time())
    }

    # Create a test transaction
    tx = {
        "type": "send",
        "op": "OP_SEND",
        "txid": f"tx-{int(time.time())}",
        "from": identity_id,
        "data": {
            "token_id": "ONX",
            "to": recipient_id,
            "amount": 5
        },
        "timestamp": int(time.time())
    }

    # Add the transaction to the mempool
    mempool = Mempool()
    mempool.add_transaction(tx)

    # Give the sender some initial tokens
    ledger = TokenLedger()
    ledger.credit(identity_id, "ONX", 100)

    return identity_id

def test_mining():
    """Test the mining functionality."""
    # Set up test data
    identity_id = setup_test_data()

    # Create a miner
    miner = BlockMiner()

    # Get the initial chain length
    initial_length = miner.chain.get_chain_length()
    print(f"Initial chain length: {initial_length}")

    # Get the initial balance
    ledger = TokenLedger()
    initial_balance = ledger.get_balance(identity_id, "ONX")
    print(f"Initial balance of {identity_id}: {initial_balance} ONX")

    # Mine a block
    print(f"Mining a block with proposer {identity_id}...")
    block = miner.create_block(identity_id)

    # Print block information
    print(f"Block {block['index']} mined with hash {block['hash']}")
    print(f"Transactions: {len(block['transactions'])}")
    print(f"Timestamp: {block['timestamp']}")

    # Verify the chain length increased
    new_length = miner.chain.get_chain_length()
    print(f"New chain length: {new_length}")
    assert new_length == initial_length + 1, "Chain length did not increase"

    # Verify the miner was rewarded
    new_balance = ledger.get_balance(identity_id, "ONX")
    print(f"New balance of {identity_id}: {new_balance} ONX")
    assert new_balance == initial_balance + 10, "Miner was not rewarded correctly"  # BLOCK_REWARD is 10

    # Verify the mempool was cleared
    mempool = Mempool()
    assert mempool.get_count() == 0, "Mempool was not cleared"

    print("Mining test passed!")

if __name__ == "__main__":
    test_mining()
