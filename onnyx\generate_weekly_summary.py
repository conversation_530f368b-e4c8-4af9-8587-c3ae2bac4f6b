#!/usr/bin/env python3
"""
Generate Weekly Summary

This script generates a weekly summary of the Onnyx blockchain.
"""

import os
import sys
import time
import json
import argparse
from datetime import datetime

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analytics.event_logger import EventLogger

# Use the test event log
event_logger = EventLogger("data/test_event_log.json")

def generate_weekly_summary(output_file: str = None, days: int = 7):
    """
    Generate a weekly summary of the Onnyx blockchain.

    Args:
        output_file: Path to the output file (optional)
        days: Number of days to include in the summary
    """
    # Generate the summary
    summary = event_logger.generate_summary(days=days)

    # Format the summary
    formatted_summary = format_summary(summary, days)

    # Print the summary
    print(formatted_summary)

    # Save the summary to a file
    if output_file:
        with open(output_file, "w") as f:
            f.write(formatted_summary)
        print(f"\nSummary saved to {output_file}")

def format_summary(summary, days):
    """
    Format the summary as a string.

    Args:
        summary: The summary to format
        days: Number of days included in the summary

    Returns:
        The formatted summary
    """
    # Get the current date and time
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Format the summary
    formatted_summary = f"""
# Onnyx Blockchain {days}-Day Summary
Generated on: {now}

## Overview
- Period: {summary.get("start_timestamp_human", "N/A")} to {summary.get("end_timestamp_human", "N/A")}
- Blocks: {summary["blocks"]}
- Transactions: {summary["transactions"]}
- Unique Proposers: {summary["unique_proposer_count"]}

## Transaction Breakdown
- Token Mints: {summary["token_mints"]}
- Token Transfers: {summary["token_transfers"]}
- Token Burns: {summary["token_burns"]}
- Proposals: {summary["proposals"]}
- Votes: {summary["votes"]}
- Identities: {summary["identities"]}
- Reputation Grants: {summary["reputation_grants"]}
- Stakes: {summary["stakes"]}
- Rewards: {summary["rewards"]}

## Proposers
"""

    # Add proposers
    if summary["unique_proposers"]:
        for proposer in summary["unique_proposers"]:
            formatted_summary += f"- {proposer}\n"
    else:
        formatted_summary += "- No proposers in this period\n"

    # Calculate statistics
    if summary["blocks"] > 0:
        avg_tx_per_block = summary["transactions"] / summary["blocks"]
        formatted_summary += f"\n## Statistics\n- Average Transactions per Block: {avg_tx_per_block:.2f}\n"

        if summary["start_timestamp"] and summary["end_timestamp"]:
            days_elapsed = (summary["end_timestamp"] - summary["start_timestamp"]) / 86400
            if days_elapsed > 0:
                blocks_per_day = summary["blocks"] / days_elapsed
                formatted_summary += f"- Blocks per Day: {blocks_per_day:.2f}\n"

    return formatted_summary

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Generate a weekly summary of the Onnyx blockchain")
    parser.add_argument("--output", "-o", help="Path to the output file")
    parser.add_argument("--days", "-d", type=int, default=7, help="Number of days to include in the summary")

    args = parser.parse_args()

    generate_weekly_summary(output_file=args.output, days=args.days)

if __name__ == "__main__":
    main()
