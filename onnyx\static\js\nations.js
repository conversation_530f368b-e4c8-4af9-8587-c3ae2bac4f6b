/**
 * Onnyx Nations Selection JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the nations selection
    initNationsSelection();
});

/**
 * Initialize the nations selection
 */
function initNationsSelection() {
    // Add event listeners to nation selection buttons
    document.querySelectorAll('.select-nation-btn').forEach(button => {
        button.addEventListener('click', function() {
            const nationName = this.getAttribute('data-nation');
            selectNation(nationName);
        });
    });
    
    // Add event listeners to nation cards for expanding/collapsing
    document.querySelectorAll('.nation-card').forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if the select button was clicked
            if (e.target.classList.contains('select-nation-btn')) {
                return;
            }
            
            // Toggle the expanded class
            this.classList.toggle('expanded');
        });
    });
}

/**
 * Select a nation
 * @param {string} nationName - The name of the selected nation
 */
function selectNation(nationName) {
    // Get the nation data
    const nationData = getNationData(nationName);
    
    // If the nation has tribes/princes/dukes/clans, show the tribe selection modal
    if (hasTribeSelection(nationData)) {
        showTribeSelectionModal(nationName, nationData);
    } else {
        // Otherwise, just select the nation
        confirmNationSelection(nationName);
    }
}

/**
 * Check if a nation has tribes/princes/dukes/clans for selection
 * @param {Object} nationData - The nation data
 * @returns {boolean} Whether the nation has tribe selection
 */
function hasTribeSelection(nationData) {
    return nationData.tribes || nationData.princes || nationData.dukes || nationData.clans;
}

/**
 * Show the tribe selection modal
 * @param {string} nationName - The name of the selected nation
 * @param {Object} nationData - The nation data
 */
function showTribeSelectionModal(nationName, nationData) {
    // Create the modal
    const modal = document.createElement('div');
    modal.className = 'tribe-modal';
    
    // Create the modal content
    let modalContent = `
        <div class="tribe-modal-content">
            <div class="tribe-modal-header">
                <h3>Select Your ${getTribeType(nationData)} in ${nationName}</h3>
                <button class="tribe-modal-close">&times;</button>
            </div>
            <div class="tribe-list">
    `;
    
    // Add the tribe options
    const tribeOptions = getTribeOptions(nationData);
    tribeOptions.forEach(tribe => {
        modalContent += `
            <div class="tribe-option" data-tribe="${tribe}">
                <div class="tribe-option-icon">${getTribeIcon(tribe)}</div>
                <div class="tribe-option-info">
                    <h4>${tribe}</h4>
                    <p>${getTribeDescription(nationName, tribe)}</p>
                </div>
            </div>
        `;
    });
    
    // Add the modal actions
    modalContent += `
            </div>
            <div class="tribe-modal-actions">
                <button class="tribe-modal-confirm" disabled>Confirm Selection</button>
            </div>
        </div>
    `;
    
    // Set the modal content
    modal.innerHTML = modalContent;
    
    // Add the modal to the body
    document.body.appendChild(modal);
    
    // Show the modal
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
    
    // Add event listeners
    const closeButton = modal.querySelector('.tribe-modal-close');
    closeButton.addEventListener('click', () => {
        closeModal(modal);
    });
    
    const tribeOptionElements = modal.querySelectorAll('.tribe-option');
    tribeOptionElements.forEach(option => {
        option.addEventListener('click', () => {
            // Deselect all options
            tribeOptionElements.forEach(opt => opt.classList.remove('selected'));
            
            // Select this option
            option.classList.add('selected');
            
            // Enable the confirm button
            const confirmButton = modal.querySelector('.tribe-modal-confirm');
            confirmButton.removeAttribute('disabled');
        });
    });
    
    const confirmButton = modal.querySelector('.tribe-modal-confirm');
    confirmButton.addEventListener('click', () => {
        // Get the selected tribe
        const selectedTribe = modal.querySelector('.tribe-option.selected');
        if (selectedTribe) {
            const tribeName = selectedTribe.getAttribute('data-tribe');
            confirmNationAndTribeSelection(nationName, tribeName);
        }
        
        // Close the modal
        closeModal(modal);
    });
}

/**
 * Close a modal
 * @param {HTMLElement} modal - The modal element
 */
function closeModal(modal) {
    modal.classList.remove('active');
    setTimeout(() => {
        document.body.removeChild(modal);
    }, 300);
}

/**
 * Get the type of tribe for a nation (tribes, princes, dukes, clans)
 * @param {Object} nationData - The nation data
 * @returns {string} The tribe type
 */
function getTribeType(nationData) {
    if (nationData.tribes) return 'Tribe';
    if (nationData.princes) return 'Prince';
    if (nationData.dukes) return 'Duke';
    if (nationData.clans) return 'Clan';
    return 'Group';
}

/**
 * Get the tribe options for a nation
 * @param {Object} nationData - The nation data
 * @returns {Array} The tribe options
 */
function getTribeOptions(nationData) {
    if (nationData.tribes) {
        if (Array.isArray(nationData.tribes)) {
            return nationData.tribes;
        } else {
            return nationData.tribes.map(tribe => tribe.name);
        }
    }
    if (nationData.princes) return nationData.princes;
    if (nationData.dukes) return nationData.dukes;
    if (nationData.clans) return nationData.clans;
    return [];
}

/**
 * Get an icon for a tribe
 * @param {string} tribeName - The name of the tribe
 * @returns {string} The tribe icon
 */
function getTribeIcon(tribeName) {
    // Map of tribe names to icons
    const tribeIcons = {
        'Reuben': '💧',
        'Simeon': '🏙️',
        'Levi': '🛡️',
        'Judah': '🦁',
        'Dan': '⚖️',
        'Naphtali': '🦌',
        'Gad': '⛺',
        'Asher': '🫒',
        'Issachar': '☀️',
        'Zebulun': '🚢',
        'Joseph': '🌾',
        'Ephraim': '🐂',
        'Manasseh': '🌿',
        'Benjamin': '🐺'
    };
    
    return tribeIcons[tribeName] || '🔰';
}

/**
 * Get a description for a tribe
 * @param {string} nationName - The name of the nation
 * @param {string} tribeName - The name of the tribe
 * @returns {string} The tribe description
 */
function getTribeDescription(nationName, tribeName) {
    // Map of tribe names to descriptions
    const tribeDescriptions = {
        'Reuben': 'The firstborn of Jacob, known for strength but unstable character.',
        'Simeon': 'Known for zeal and passion, later absorbed into Judah.',
        'Levi': 'The priestly tribe, set apart for service in the Tabernacle and Temple.',
        'Judah': 'The royal tribe from which King David and ultimately the Messiah came.',
        'Dan': 'Known for judgment and justice.',
        'Naphtali': 'Known for eloquence and swift action.',
        'Gad': 'Known for military prowess.',
        'Asher': 'Known for prosperity and abundance.',
        'Issachar': 'Known for wisdom, understanding times and seasons.',
        'Zebulun': 'Known for commerce and seafaring.',
        'Joseph': 'Known for dreams, provision, and leadership in crisis.',
        'Ephraim': 'The more prominent of Joseph\'s sons, known for fruitfulness.',
        'Manasseh': 'Joseph\'s firstborn, known for strength and stability.',
        'Benjamin': 'The youngest tribe, known for fierceness and protection.'
    };
    
    return tribeDescriptions[tribeName] || `A ${getTribeType(getNationData(nationName)).toLowerCase()} of ${nationName}`;
}

/**
 * Get the data for a nation
 * @param {string} nationName - The name of the nation
 * @returns {Object} The nation data
 */
function getNationData(nationName) {
    // This would normally come from an API or database
    // For now, we'll just return a simple object
    const nationsData = {
        'Israel': {
            tribes: ['Reuben', 'Simeon', 'Levi', 'Judah', 'Dan', 'Naphtali', 'Gad', 'Asher', 'Issachar', 'Zebulun', 'Joseph', 'Benjamin', 'Ephraim', 'Manasseh']
        },
        'Ishmael': {
            princes: ['Nebajoth', 'Kedar', 'Adbeel', 'Mibsam', 'Mishma', 'Dumah', 'Massa', 'Hadar', 'Tema', 'Jetur', 'Naphish', 'Kedemah']
        },
        'Edom': {
            dukes: ['Teman', 'Omar', 'Zepho', 'Kenaz', 'Korah', 'Gatam', 'Amalek', 'Nahath', 'Zerah', 'Shammah', 'Mizzah', 'Magdiel', 'Iram']
        },
        'Midian': {
            clans: ['Ephah', 'Epher', 'Hanoch', 'Abida', 'Eldaah']
        }
    };
    
    return nationsData[nationName] || {};
}

/**
 * Confirm the selection of a nation
 * @param {string} nationName - The name of the selected nation
 */
function confirmNationSelection(nationName) {
    // Save the selection to local storage
    localStorage.setItem('selectedNation', nationName);
    
    // Show a confirmation message
    showNotification(`You have selected the nation of ${nationName}`, 'success');
    
    // Redirect to the next step
    setTimeout(() => {
        window.location.href = '/onboarding/sela/create.html';
    }, 2000);
}

/**
 * Confirm the selection of a nation and tribe
 * @param {string} nationName - The name of the selected nation
 * @param {string} tribeName - The name of the selected tribe
 */
function confirmNationAndTribeSelection(nationName, tribeName) {
    // Save the selection to local storage
    localStorage.setItem('selectedNation', nationName);
    localStorage.setItem('selectedTribe', tribeName);
    
    // Show a confirmation message
    showNotification(`You have selected the ${tribeName} tribe of ${nationName}`, 'success');
    
    // Redirect to the next step
    setTimeout(() => {
        window.location.href = '/onboarding/sela/create.html';
    }, 2000);
}

/**
 * Display a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, warning, error)
 */
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add close button
    const closeButton = document.createElement('button');
    closeButton.className = 'notification-close';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', () => {
        document.body.removeChild(notification);
    });
    
    notification.appendChild(closeButton);
    
    // Add to body
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 5000);
}
