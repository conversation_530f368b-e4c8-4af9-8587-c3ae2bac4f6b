"""
Add test transactions to the Onnyx mempool

This script adds test transactions to the Onnyx mempool for testing.
"""

import os
import json
import time
import sys
import argparse

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from node.mempool import Mempool
from tokens.ledger import TokenLedger

def create_test_transaction(tx_type, sender, recipient=None, token_id=None, amount=None):
    """
    Create a test transaction.
    
    Args:
        tx_type: The transaction type
        sender: The sender's identity ID
        recipient: The recipient's identity ID
        token_id: The token ID
        amount: The amount to transfer
    
    Returns:
        The test transaction
    """
    tx = {
        "type": tx_type,
        "op": f"OP_{tx_type.upper()}",
        "txid": f"{tx_type}-{int(time.time())}-{sender}",
        "from": sender,
        "timestamp": int(time.time())
    }
    
    if tx_type == "send":
        tx["data"] = {
            "token_id": token_id,
            "to": recipient,
            "amount": amount
        }
    elif tx_type == "mint":
        tx["data"] = {
            "symbol": token_id,
            "supply": amount
        }
    elif tx_type == "identity":
        tx["data"] = {
            "identity_id": recipient,
            "name": f"Test Identity {recipient}",
            "public_key": f"0x{recipient}123456789abcdef"
        }
    elif tx_type == "scroll":
        tx["data"] = {
            "title": f"Test Scroll {int(time.time())}",
            "description": "This is a test scroll",
            "category": "test"
        }
    
    return tx

def add_test_transactions(count, tx_type, sender, recipient=None, token_id=None, amount=None):
    """
    Add test transactions to the mempool.
    
    Args:
        count: The number of transactions to add
        tx_type: The transaction type
        sender: The sender's identity ID
        recipient: The recipient's identity ID
        token_id: The token ID
        amount: The amount to transfer
    
    Returns:
        The number of transactions added
    """
    mempool = Mempool()
    
    # Add transactions to the mempool
    for i in range(count):
        tx = create_test_transaction(tx_type, sender, recipient, token_id, amount)
        mempool.add_transaction(tx)
    
    return count

def main():
    """Add test transactions to the mempool."""
    parser = argparse.ArgumentParser(description="Add test transactions to the Onnyx mempool")
    parser.add_argument("--count", type=int, default=1, help="Number of transactions to add")
    parser.add_argument("--type", type=str, default="send", choices=["send", "mint", "identity", "scroll"], help="Transaction type")
    parser.add_argument("--sender", type=str, default="test_sender", help="Sender identity ID")
    parser.add_argument("--recipient", type=str, default="test_recipient", help="Recipient identity ID")
    parser.add_argument("--token", type=str, default="TEST", help="Token ID or symbol")
    parser.add_argument("--amount", type=int, default=10, help="Amount to transfer or mint")
    
    args = parser.parse_args()
    
    # Add test transactions
    count = add_test_transactions(
        args.count,
        args.type,
        args.sender,
        args.recipient,
        args.token,
        args.amount
    )
    
    print(f"Added {count} {args.type} transactions to the mempool")
    
    # Show mempool status
    mempool = Mempool()
    print(f"Mempool now contains {mempool.get_count()} transactions")

if __name__ == "__main__":
    main()
