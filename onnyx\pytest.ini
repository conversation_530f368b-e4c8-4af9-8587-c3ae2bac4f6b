[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
norecursedirs = .git .venv venv env node_modules onnyx-miner
addopts = -v
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    model: marks tests as model tests
    wallet: marks tests as wallet tests
    vm: marks tests as VM tests
    node: marks tests as node tests
    governance: marks tests as governance tests
    token: marks tests as token tests
    identity: marks tests as identity tests
    sela: marks tests as Sela tests
    etzem: marks tests as Etzem tests
    yovel: marks tests as Yovel tests
    zeman: marks tests as Zeman tests
    mikvah: marks tests as Mikvah tests
    judah: marks tests as Judah tests
    council: marks tests as Council tests
    rotation: marks tests as rotation tests
    mining: marks tests as mining tests
    explorer: marks tests as explorer tests
    cli: marks tests as CLI tests
    web: marks tests as web tests
    p2p: marks tests as P2P tests
    analytics: marks tests as analytics tests
    event_logging: marks tests as event logging tests
    chain_parameters: marks tests as chain parameters tests
    voice_scroll: marks tests as Voice Scroll tests
    transaction: marks tests as transaction tests
    block: marks tests as block tests
    mempool: marks tests as mempool tests
    consensus: marks tests as consensus tests
    network: marks tests as network tests
    config: marks tests as config tests
    db: marks tests as database tests
    utils: marks tests as utilities tests
    security: marks tests as security tests
    performance: marks tests as performance tests
    stress: marks tests as stress tests
    regression: marks tests as regression tests
    smoke: marks tests as smoke tests
    e2e: marks tests as end-to-end tests
    functional: marks tests as functional tests
    acceptance: marks tests as acceptance tests
    system: marks tests as system tests
    compatibility: marks tests as compatibility tests
    reliability: marks tests as reliability tests
    scalability: marks tests as scalability tests
    maintainability: marks tests as maintainability tests
    usability: marks tests as usability tests
    accessibility: marks tests as accessibility tests
    localization: marks tests as localization tests
    internationalization: marks tests as internationalization tests
    documentation: marks tests as documentation tests
    deployment: marks tests as deployment tests
    ci: marks tests as CI tests
    cd: marks tests as CD tests
    devops: marks tests as DevOps tests
    monitoring: marks tests as monitoring tests
    logging: marks tests as logging tests
    alerting: marks tests as alerting tests
    backup: marks tests as backup tests
    recovery: marks tests as recovery tests
    disaster_recovery: marks tests as disaster recovery tests
    business_continuity: marks tests as business continuity tests
    compliance: marks tests as compliance tests
    audit: marks tests as audit tests
    security_audit: marks tests as security audit tests
    penetration: marks tests as penetration tests
    vulnerability: marks tests as vulnerability tests
    threat_modeling: marks tests as threat modeling tests
    risk_assessment: marks tests as risk assessment tests
    code_quality: marks tests as code quality tests
    code_review: marks tests as code review tests
    static_analysis: marks tests as static analysis tests
    dynamic_analysis: marks tests as dynamic analysis tests
    fuzzing: marks tests as fuzzing tests
    mutation: marks tests as mutation tests
    property: marks tests as property tests
    contract: marks tests as contract tests
    snapshot: marks tests as snapshot tests
    visual_regression: marks tests as visual regression tests
    accessibility_audit: marks tests as accessibility audit tests
    performance_audit: marks tests as performance audit tests
    seo_audit: marks tests as SEO audit tests
    ux_audit: marks tests as UX audit tests
    ui_audit: marks tests as UI audit tests
    content_audit: marks tests as content audit tests
    analytics_audit: marks tests as analytics audit tests
    conversion_audit: marks tests as conversion audit tests
    usability_audit: marks tests as usability audit tests
    accessibility_audit: marks tests as accessibility audit tests
    performance_audit: marks tests as performance audit tests
    seo_audit: marks tests as SEO audit tests
    ux_audit: marks tests as UX audit tests
    ui_audit: marks tests as UI audit tests
    content_audit: marks tests as content audit tests
    analytics_audit: marks tests as analytics audit tests
    conversion_audit: marks tests as conversion audit tests
    usability_audit: marks tests as usability audit tests

# Exclude binary files and other non-test files
exclude_file_globs = *.jpg *.png *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot *.otf *.pdf *.zip *.tar.gz *.tar.bz2 *.tar.xz *.rar *.7z *.exe *.dll *.so *.dylib *.pyc *.pyo *.pyd *.db *.sqlite *.sqlite3 *.log *.csv *.tsv *.xls *.xlsx *.doc *.docx *.ppt *.pptx *.odt *.ods *.odp *.txt test2.txt test3.txt
