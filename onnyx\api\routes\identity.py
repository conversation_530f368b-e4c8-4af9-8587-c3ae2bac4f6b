"""
Onnyx Identity Routes

This module provides API routes for Identity operations.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional

from identity.registry import IdentityRegistry
from governance.etzem_engine import <PERSON>tzemEngine
from governance.role_progression import RoleProgression

# Create router
router = APIRouter()

# Create instances
identities = IdentityRegistry()
etzem_engine = EtzemEngine()
role_progression = RoleProgression(identities, etzem_engine)

@router.post("/identity/register")
def register_identity(identity_id: str, name: str, public_key: str) -> Dict[str, Any]:
    """
    Register a new identity.
    
    Args:
        identity_id: The identity ID
        name: The identity name
        public_key: The identity's public key
    
    Returns:
        Information about the registered identity
    """
    try:
        identity = identities.register_identity(identity_id, name, public_key)
        
        return {
            "status": "created",
            "identity": identity
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/identity/{identity_id}")
def get_identity(identity_id: str) -> Dict[str, Any]:
    """
    Get an identity by ID.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        The identity
    """
    identity = identities.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' not found")
    
    return identity

@router.get("/identities")
def list_identities() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all identities.
    
    Returns:
        All identities in the registry
    """
    return {
        "identities": list(identities.get_all_identities().values())
    }

@router.post("/identity/{identity_id}/badge")
def add_badge(identity_id: str, badge: str) -> Dict[str, Any]:
    """
    Add a badge to an identity.
    
    Args:
        identity_id: The identity ID
        badge: The badge to add
    
    Returns:
        Information about the updated identity
    """
    try:
        identities.add_badge(identity_id, badge)
        
        return {
            "status": "badge_added",
            "identity_id": identity_id,
            "badge": badge
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/identity/{identity_id}/badge")
def remove_badge(identity_id: str, badge: str) -> Dict[str, Any]:
    """
    Remove a badge from an identity.
    
    Args:
        identity_id: The identity ID
        badge: The badge to remove
    
    Returns:
        Information about the updated identity
    """
    try:
        identities.remove_badge(identity_id, badge)
        
        return {
            "status": "badge_removed",
            "identity_id": identity_id,
            "badge": badge
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/identity/{identity_id}/reputation")
def update_reputation(identity_id: str, reputation: int) -> Dict[str, Any]:
    """
    Update the reputation of an identity.
    
    Args:
        identity_id: The identity ID
        reputation: The new reputation
    
    Returns:
        Information about the updated identity
    """
    try:
        identities.update_reputation(identity_id, reputation)
        
        return {
            "status": "reputation_updated",
            "identity_id": identity_id,
            "reputation": reputation
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/identity/{identity_id}/etzem")
def get_etzem_score(identity_id: str) -> Dict[str, Any]:
    """
    Get the Etzem score of an identity.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        The Etzem score
    """
    try:
        identity = identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        etzem_score = etzem_engine.calculate_etzem_score(identity_id)
        
        return {
            "identity_id": identity_id,
            "etzem_score": etzem_score
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/identity/{identity_id}/roles")
def get_role_eligibility(identity_id: str) -> Dict[str, Any]:
    """
    Get the role eligibility of an identity.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        The role eligibility
    """
    try:
        identity = identities.get_identity(identity_id)
        if not identity:
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        eligibility = role_progression.check_role_eligibility(identity_id)
        
        return {
            "identity_id": identity_id,
            "eligibility": eligibility
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/identity/{identity_id}/roles")
def update_role_badges(identity_id: str) -> Dict[str, Any]:
    """
    Update the role badges of an identity.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        The updated identity
    """
    try:
        identity = role_progression.update_role_badges(identity_id)
        
        return {
            "status": "roles_updated",
            "identity": identity
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/roles/update-all")
def update_all_role_badges() -> Dict[str, Any]:
    """
    Update the role badges of all identities.
    
    Returns:
        The updated identities
    """
    try:
        updated_identities = role_progression.update_all_role_badges()
        
        return {
            "status": "all_roles_updated",
            "count": len(updated_identities)
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
