# Onnyx Testing System

The Onnyx testing system provides a framework for testing the Onnyx blockchain. It includes unit tests, integration tests, and fixtures for testing various components of the blockchain.

## Overview

The testing system is organized into the following components:

- **Unit Tests**: Tests for individual components, such as the API, blockchain, identity, tokens, and VM.
- **Integration Tests**: Tests for interactions between components, such as the API and blockchain.
- **Fixtures**: Sample data for testing, such as identities, tokens, blocks, and transactions.
- **Helpers**: Helper functions and classes for testing, such as the `OnnyxTestCase` class.

## Directory Structure

The testing system uses the following directory structure:

```
onnyx/
├── tests/              # Test files
│   ├── data/           # Test data
│   │   └── test_config.json
│   ├── fixtures/       # Test fixtures
│   │   ├── identities.json
│   │   ├── tokens.json
│   │   ├── blockchain.json
│   │   └── mempool.json
│   ├── test_api.py     # API tests
│   ├── test_chain.py   # Blockchain tests
│   ├── test_identity.py # Identity tests
│   ├── test_tokens.py  # Token tests
│   ├── test_vm.py      # VM tests
│   ├── test_helpers.py # Test helpers
│   └── __init__.py
```

## Usage

### Running Tests

To run all tests, use the following command:

```bash
python -m unittest discover tests
```

To run a specific test, use the following command:

```bash
python -m unittest tests.test_api
```

To run a specific test method, use the following command:

```bash
python -m unittest tests.test_api.TestAPI.test_root
```

### Writing Tests

To write a new test, create a new file in the `tests` directory with a name like `test_component.py`. The file should contain a test class that inherits from `unittest.TestCase` or `OnnyxTestCase`:

```python
import unittest
from tests.test_helpers import OnnyxTestCase

class TestComponent(OnnyxTestCase):
    def setUp(self):
        # Call the parent setUp method to set up the test environment
        super().setUp()
        
        # Set up component-specific test environment
        self.component = Component()
    
    def test_method(self):
        """Test a method of the component."""
        result = self.component.method()
        self.assertEqual(result, expected_result)
```

### Using Fixtures

The testing system includes fixtures for testing, such as identities, tokens, blocks, and transactions. These fixtures are located in the `tests/fixtures` directory and are automatically loaded by the `OnnyxTestCase` class.

To use a fixture in a test, access it through the `self.test_dir` property:

```python
def test_method(self):
    """Test a method that uses fixtures."""
    identities_path = os.path.join(self.test_dir, 'data', 'identities.json')
    with open(identities_path, 'r') as f:
        identities = json.load(f)
    
    result = self.component.method(identities)
    self.assertEqual(result, expected_result)
```

### Using the Test Database

The testing system includes a test database that is automatically initialized by the `OnnyxTestCase` class. The database is located at `self.db_path` and can be accessed using SQLite:

```python
def test_method(self):
    """Test a method that uses the database."""
    conn = sqlite3.connect(self.db_path)
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM identities")
    identities = cursor.fetchall()
    
    conn.close()
    
    result = self.component.method(identities)
    self.assertEqual(result, expected_result)
```

### Using the Test Configuration

The testing system includes a test configuration that is automatically initialized by the `OnnyxTestCase` class. The configuration is accessible through the `self.config` property:

```python
def test_method(self):
    """Test a method that uses the configuration."""
    block_reward = self.config.get_chain_param("block_reward")
    
    result = self.component.method(block_reward)
    self.assertEqual(result, expected_result)
```

## Test Helpers

The testing system includes helper functions and classes for testing, such as the `OnnyxTestCase` class. These helpers are located in the `tests/test_helpers.py` file.

### OnnyxTestCase

The `OnnyxTestCase` class is a base class for Onnyx tests. It provides common functionality for Onnyx tests, such as setting up a test environment with a temporary database and configuration.

```python
from tests.test_helpers import OnnyxTestCase

class TestComponent(OnnyxTestCase):
    def setUp(self):
        # Call the parent setUp method to set up the test environment
        super().setUp()
        
        # Set up component-specific test environment
        self.component = Component()
    
    def test_method(self):
        """Test a method of the component."""
        result = self.component.method()
        self.assertEqual(result, expected_result)
```

### get_test_config

The `get_test_config` function returns the test configuration from the `tests/data/test_config.json` file.

```python
from tests.test_helpers import get_test_config

def test_method():
    """Test a method that uses the test configuration."""
    config = get_test_config()
    block_reward = config["chain_params"]["block_reward"]
    
    result = component.method(block_reward)
    assert result == expected_result
```

## Test Fixtures

The testing system includes fixtures for testing, such as identities, tokens, blocks, and transactions. These fixtures are located in the `tests/fixtures` directory.

### identities.json

The `identities.json` file contains sample identities for testing.

```json
{
  "test_identity": {
    "identity_id": "test_identity",
    "name": "Test Identity",
    "public_key": "...",
    "metadata": {
      "type": "individual",
      "description": "Test identity for unit tests"
    },
    "created_at": 1620000000,
    "updated_at": 1620000000
  },
  "test_identity_2": {
    "identity_id": "test_identity_2",
    "name": "Test Identity 2",
    "public_key": "...",
    "metadata": {
      "type": "individual",
      "description": "Second test identity for unit tests"
    },
    "created_at": 1620000001,
    "updated_at": 1620000001
  },
  "test_sela": {
    "identity_id": "test_sela",
    "name": "Test Sela",
    "public_key": "...",
    "metadata": {
      "type": "business",
      "description": "Test Sela business for unit tests",
      "sela_id": "test_sela"
    },
    "created_at": 1620000002,
    "updated_at": 1620000002
  }
}
```

### tokens.json

The `tokens.json` file contains sample tokens for testing.

```json
{
  "TEST_ONX": {
    "token_id": "TEST_ONX",
    "name": "Test Onnyx Token",
    "symbol": "TEST_ONX",
    "creator_id": "test_identity",
    "supply": 1000000,
    "category": "native",
    "decimals": 8,
    "created_at": 1620000000,
    "metadata": {
      "description": "Test native token for Onnyx blockchain"
    }
  },
  "TEST_LOYALTY": {
    "token_id": "TEST_LOYALTY",
    "name": "Test Loyalty Token",
    "symbol": "TEST_LT",
    "creator_id": "test_sela",
    "supply": 10000,
    "category": "loyalty",
    "decimals": 2,
    "created_at": 1620000001,
    "metadata": {
      "description": "Test loyalty token for unit tests",
      "sela_id": "test_sela"
    }
  }
}
```

### blockchain.json

The `blockchain.json` file contains sample blockchain data for testing.

```json
{
  "blocks": [
    {
      "hash": "0000000000000000000000000000000000000000000000000000000000000000",
      "height": 0,
      "previous_hash": "0000000000000000000000000000000000000000000000000000000000000000",
      "timestamp": 1620000000,
      "difficulty": 1,
      "nonce": 0,
      "miner": "genesis",
      "transactions": [
        "genesis_tx"
      ],
      "merkle_root": "0000000000000000000000000000000000000000000000000000000000000000",
      "size": 0,
      "version": "1.0"
    }
  ],
  "transactions": {
    "genesis_tx": {
      "tx_id": "genesis_tx",
      "block_hash": "0000000000000000000000000000000000000000000000000000000000000000",
      "timestamp": 1620000000,
      "op": "OP_GENESIS",
      "data": {
        "tokens": [
          "TEST_ONX"
        ],
        "identities": [
          "test_identity",
          "test_identity_2",
          "test_sela"
        ]
      },
      "sender": "genesis",
      "signature": "",
      "status": "confirmed",
      "created_at": 1620000000
    }
  }
}
```

### mempool.json

The `mempool.json` file contains sample mempool data for testing.

```json
{
  "tx_3": {
    "tx_id": "tx_3",
    "timestamp": 1620000120,
    "op": "OP_MINT",
    "data": {
      "token_id": "TEST_LOYALTY",
      "amount": 500,
      "to_address": "test_identity"
    },
    "sender": "test_sela",
    "signature": "signature_3",
    "created_at": 1620000120
  }
}
```
