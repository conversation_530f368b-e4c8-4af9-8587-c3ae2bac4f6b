# Onnyx Identity Module

This module handles on-chain identities for individuals, businesses, or DAOs.

## Components

- `identity.py`: Defines the `Identity` class
- `registry.py`: Handles identity registration, lookup, and updates

## Features

- Identity ID = hash of name + pubkey + timestamp
- Stores metadata (bio, links, type, etc)
- Tracks reputation and token ownership
- Allows future enhancements like badges and delegated roles

## Example Identity

```json
{
  "identity_id": "1a2b...",
  "name": "bob_store",
  "public_key": "02d0de...",
  "metadata": {
    "type": "business",
    "url": "https://bobcoffee.com"
  },
  "reputation": {
    "score": 12,
    "transactions": 104,
    "minted_tokens": 1
  }
}
```
