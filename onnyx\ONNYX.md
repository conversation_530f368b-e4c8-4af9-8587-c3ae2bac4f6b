# Onnyx Blockchain

Onnyx is an identity-centric blockchain platform designed to support community-based economic systems with a focus on trust, reputation, and governance.

## Table of Contents

- [Overview](#overview)
- [Project Structure](#project-structure)
- [Core Components](#core-components)
- [Economic System](#economic-system)
- [Governance System](#governance-system)
- [API](#api)
- [Installation](#installation)
- [Running the Project](#running-the-project)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## Overview

Onnyx is a blockchain platform that prioritizes identity and community over pure token economics. It introduces several novel concepts:

- **Identity-Centric Design**: Every token and transaction is linked to a verified identity
- **Sela Business Registry**: A system for registering and managing businesses on the blockchain
- **Etzem Trust Scores**: A reputation system that combines on-chain activity, time contributions, and community standing
- **Voice Scrolls**: A governance system that allows community members to propose and vote on changes
- **Validator Rotation**: A fair system for selecting block validators based on reputation and contribution

## Project Structure

The Onnyx project is organized into the following directories:

```
onnyx/
├── api/                # API application
├── chain/              # Blockchain core functionality
├── cli/                # Command-line interface
├── config/             # Configuration files
├── consensus/          # Consensus mechanisms
├── docs/               # Documentation
├── governance/         # Governance system
├── identity/           # Identity management
├── models/             # Database models
├── node/               # Node functionality
├── tests/              # Test suite
├── tokens/             # Token management
├── vm/                 # Virtual machine for script execution
└── wallet/             # Wallet functionality
```

## Core Components

### Chain

The `chain` module implements the core blockchain functionality, including:

- Block structure and validation
- Chain management and consensus
- Transaction processing and validation

### Identity

The `identity` module manages identities on the Onnyx blockchain:

- Identity creation and verification
- Public key management
- Reputation tracking
- Metadata storage

### Tokens

The `tokens` module handles token operations:

- Token creation (forking)
- Minting and burning
- Transfers
- Token registry and ledger

### VM

The `vm` module implements the Onnyx Virtual Machine:

- Stack-based script interpreter
- Operation validation
- Transaction execution

### Node

The `node` module provides node functionality:

- P2P networking
- Block synchronization
- Mempool management
- Validator rotation

### Wallet

The `wallet` module implements wallet functionality:

- Key generation and management
- Transaction signing
- Balance tracking

## Economic System

Onnyx introduces several economic concepts:

### Zeman Time/Work Credits

Zeman tracks hours of service and converts them into:
- Minting power
- Fee discounts
- Reputation boosts
- Redeemable value

### Sela Business Registry

Sela is a business registry system that:
- Requires ONX token stakes
- Has different tiers based on identity reputation
- Enables businesses to mint tokens
- Provides services to the community

### Etzem Trust Scores

Etzem calculates on-chain integrity by combining:
- Zeman hours
- Reputation badges
- Sela registration
- Token activity
- Governance participation
- Account longevity

### Yovel Token Mint Limiter

Yovel caps how many tokens an identity can mint based on:
- Zeman (time/labor)
- Etzem (reputation score)
- Trust level (badges, history, stake)

### Mikvah Token Engine

Mikvah requires identities to:
- Have registered Selas (businesses)
- Meet Yovel minting limits
- Before they can mint new tokens

### Judah Engine

Judah manages economic logic including:
- Transaction fees with Zeman/Etzem discounts
- Creator rebates
- ONX token flow control
- Staking pool support
- Economic governance parameters

## Governance System

Onnyx implements a governance system with several components:

### Voice Scrolls

Voice Scrolls are governance proposals that:
- Can be created by any identity
- Allow community voting
- Have expiry windows
- Include automatic resolution
- Can modify chain parameters

### Council of Twelve Tribes

The Council assigns seats to specific tribes with:
- Councilors having soulbound roles
- Special voting powers in governance
- Oversight of economic parameters

### ONX Staking

ONX staking allows users to:
- Lock tokens for different durations
- Gain roles like VALIDATOR, CREATOR, and MENTOR
- Participate in governance
- Earn rewards

### Scroll-Controlled Chain Parameters

Scroll-Controlled Chain Parameters allow:
- Dynamic updates to core economic rules
- Community governance of parameters
- Transparent parameter changes

## API

The Onnyx API provides a RESTful interface for interacting with the blockchain:

### Endpoints

- `/identity`: Identity management
- `/token`: Token operations
- `/transaction`: Transaction queries
- `/sela`: Sela business operations
- `/etzem`: Reputation score management
- `/miner`: Mining operations
- `/sela-miner`: Sela miner operations
- `/rotation`: Validator rotation
- `/chain-parameters`: Chain parameter management
- `/governance`: Voice Scroll operations
- `/analytics`: Blockchain analytics

### Running the API

```bash
python run_api_updated.py --host 0.0.0.0 --port 5000
```

### API Documentation

See [API Documentation](docs/api_updated.md) for detailed information.

## Installation

### Prerequisites

- Python 3.8 or higher
- SQLite 3
- Git

### Setup

1. Clone the repository:

```bash
git clone https://github.com/yourusername/onnyx.git
cd onnyx
```

2. Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

4. Initialize the database:

```bash
python init_db.py
```

## Running the Project

### Running the Node

```bash
python run_node.py
```

### Running the API

```bash
python run_api_updated.py
```

### Running the CLI

```bash
python cli.py --help
```

## Testing

### Running Tests

```bash
python run_tests.py
```

### Running Specific Tests

```bash
python run_tests.py --test-file test_api_updated.py --test-class TestAPIUpdated --test-method test_root
```

## Deployment

### Deploying the API

```bash
python deploy_api.py --env production --use-systemd
```

### Deployment Options

- `--host`: Host to run the API on (default: 0.0.0.0)
- `--port`: Port to run the API on (default: 5000)
- `--workers`: Number of worker processes (default: 4)
- `--log-level`: Log level (default: info)
- `--env`: Deployment environment (default: production)
- `--data-dir`: Data directory (default: data)
- `--backup`: Backup data before deployment
- `--service-name`: Systemd service name (default: onnyx-api)
- `--use-systemd`: Use systemd to manage the service

## Contributing

We welcome contributions to the Onnyx project! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests for your changes
5. Run the tests
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## Onnyx Mining System

The Onnyx Mining System includes:
- Block Builder
- Coinbase TX for miner rewards
- Chain Advancer
- CLI/API Support
- VM Validator for block validation

## Sela-Owned Miner Nodes

Sela-Owned Miner Nodes feature:
- Identity-linked validator keys
- Node-specific configuration
- Validator rotation
- Local activity ledgers
- Specialized CLI tools

## Transaction Viewer

The Onnyx Transaction Viewer includes:
- Live data sync from the core node
- Detailed views for transactions/tokens/identities
- Pagination with filtering
- Chain statistics dashboard
- Node status with peer information
- Admin tools for blockchain interaction

## P2P Network

The Onnyx P2P network features:
- WebSockets and signed JSON messages
- Peer discovery
- Block sync
- Mempool gossip
- Signature validation
- Shared ledger functionality

## Smart Event Logging

Onnyx includes Smart Event Logging and Historical Analytics to track:
- Block activities
- Transactions
- Token mints
- Proposals
- Votes
- Weekly summaries for transparent reporting
