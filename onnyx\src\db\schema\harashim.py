# src/db/schema/harashim.py

from src.db.manager import db_manager

def create_harashim_tables():
    """
    Create the tables for the Harashim system.
    """
    conn = db_manager.get_connection()
    cursor = conn.cursor()

    # Create harashim_roles table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS harashim_roles (
        identity_id TEXT PRIMARY KEY,
        role TEXT NOT NULL,
        affiliated_sela TEXT,
        active_contracts INTEGER NOT NULL DEFAULT 0,
        completed_contracts INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (identity_id) REFERENCES identities (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (affiliated_sela) REFERENCES selas (sela_id) ON DELETE SET NULL,
        FOREIGN KEY (role) REFERENCES roles (role) ON DELETE RESTRICT
    )
    ''')

    # Create service_contracts table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS service_contracts (
        contract_id TEXT PRIMARY KEY,
        sela_id TEXT NOT NULL,
        harash_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        status TEXT NOT NULL,
        estimated_hours INTEGER NOT NULL,
        actual_hours INTEGER,
        reward_type TEXT NOT NULL,
        reward_amount REAL NOT NULL,
        deadline INTEGER,
        created_at INTEGER NOT NULL,
        completed_at INTEGER,
        feedback TEXT,
        rating INTEGER,
        FOREIGN KEY (sela_id) REFERENCES selas (sela_id) ON DELETE CASCADE,
        FOREIGN KEY (harash_id) REFERENCES harashim_roles (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create harashim_performance table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS harashim_performance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_id TEXT NOT NULL,
        harash_id TEXT NOT NULL,
        etzem_boost REAL NOT NULL,
        applied_at INTEGER NOT NULL,
        FOREIGN KEY (contract_id) REFERENCES service_contracts (contract_id) ON DELETE CASCADE,
        FOREIGN KEY (harash_id) REFERENCES harashim_roles (identity_id) ON DELETE CASCADE
    )
    ''')

    # Create contract_payouts table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS contract_payouts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_id TEXT NOT NULL UNIQUE,
        harash_id TEXT NOT NULL,
        sela_id TEXT NOT NULL,
        token_id TEXT NOT NULL,
        amount REAL NOT NULL,
        paid_at INTEGER NOT NULL,
        FOREIGN KEY (contract_id) REFERENCES service_contracts (contract_id) ON DELETE CASCADE,
        FOREIGN KEY (harash_id) REFERENCES harashim_roles (identity_id) ON DELETE CASCADE,
        FOREIGN KEY (sela_id) REFERENCES selas (sela_id) ON DELETE CASCADE,
        FOREIGN KEY (token_id) REFERENCES tokens (token_id) ON DELETE CASCADE
    )
    ''')

    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_harashim_roles_affiliated_sela ON harashim_roles (affiliated_sela)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_service_contracts_harash_id ON service_contracts (harash_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_service_contracts_sela_id ON service_contracts (sela_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_service_contracts_status ON service_contracts (status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_harashim_performance_harash_id ON harashim_performance (harash_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_contract_payouts_harash_id ON contract_payouts (harash_id)')

    conn.commit()
