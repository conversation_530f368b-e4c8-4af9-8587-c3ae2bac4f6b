# Judah Engine: Economic Logic Layer

The Judah Engine is the core economic logic layer of Onnyx.

> "The Judah Engine doesn't just track value. It enforces righteous economic flow — from fee cycles and staking to creator rebates and trust-weighted incentives."

## What Is the Judah Engine?

The Judah Engine governs:

| Component | Function |
|-----------|----------|
| 🔁 Transaction Fees | Dynamic fees with <PERSON>eman/<PERSON>tz<PERSON> discounts |
| 💰 Rebate Mechanics | Rebates for creators, builders, educators |
| 🧲 ONX Flow Control | Fee routing, burn %, reward pool tracking |
| 🧱 Staking Pool Support | Lock ONX for roles, rewards, or privileges |
| 🧠 Economic Governance | Council scrolls can adjust parameters (fee %, stake rules, thresholds) |

## ONX Staking System

The ONX Staking System allows identities to stake ONX tokens to gain roles, benefits, and rewards.

| Feature | Description |
|---------|-------------|
| 🪙 Stake to Earn Roles | Lock ONX to gain roles like VALIDATOR, CREATOR, MENTOR |
| 🧭 Staking Periods | Configurable lock durations (e.g. 30, 90, 180, 365 days) |
| 🔒 Locked Balance Ledger | Tracks who has what locked and until when |
| 🧾 Claim-on-Expire | Unlock manually after period or auto-redeem later |
| 📜 Scroll-adjustable | Staking thresholds set by Council proposals |

### Staking Roles

The ONX Staking System supports the following roles:

| Role | Description | Minimum Stake | Benefits |
|------|-------------|---------------|----------|
| VALIDATOR | Validate transactions and blocks | 10,000 ONX | REDUCED_FEES, PRIORITY_PROCESSING, GOVERNANCE_VOTE |
| CREATOR | Create and promote content | 5,000 ONX | REDUCED_FEES, PRIORITY_PROCESSING, CONTENT_BOOST |
| MENTOR | Educate and guide others | 2,500 ONX | REDUCED_FEES, EDUCATION_BOOST |
| GOVERNOR | Participate in governance | 7,500 ONX | GOVERNANCE_VOTE, PROPOSAL_RIGHTS |
| AMBASSADOR | Promote the Onnyx ecosystem | 1,000 ONX | COMMUNITY_BOOST, REFERRAL_BONUS |

### Soulbound Roles

The ONX Staking System also supports soulbound roles that are automatically assigned when ONX is staked and revoked when the stake is withdrawn:

| Role | Description | Minimum Stake |
|------|-------------|---------------|
| GUARDIAN | Protector of the Onnyx ecosystem | 10,000 ONX |
| COUNCIL_ELIGIBLE | Eligible for Council of Twelve Tribes | 5,000 ONX |
| CURATOR | Curator of content and tokens | 1,000 ONX |
| MENTOR | Mentor to new users | 500 ONX |
| CREATOR | Creator of tokens and content | 100 ONX |

These roles are:
- ✅ Soulbound to the identity
- 🧠 Auto-assigned when unlocked
- 🔄 Revoked if stake is withdrawn

### Staking Benefits

The ONX Staking System provides the following benefits:

| Benefit | Description |
|---------|-------------|
| REDUCED_FEES | Reduced transaction fees |
| PRIORITY_PROCESSING | Priority processing of transactions |
| GOVERNANCE_VOTE | Participate in governance decisions |
| PROPOSAL_RIGHTS | Create governance proposals |
| CONTENT_BOOST | Boost visibility of created content |
| EDUCATION_BOOST | Boost visibility of educational content |
| COMMUNITY_BOOST | Boost visibility of community contributions |
| REFERRAL_BONUS | Earn bonuses for referrals |

## Components

### JudahEngine

The `JudahEngine` class is the main logic for economic operations. It provides methods for:

- Calculating discounts based on Zeman hours and Etzem score
- Applying transaction fees
- Applying rebates
- Staking ONX tokens
- Managing economic parameters

## Transaction Fees

The Judah Engine calculates transaction fees based on the following factors:

- **Base Fee**: The base transaction fee (default: 5%)
- **Zeman Discount**: Discount based on Zeman hours
  - 100+ hours: 50% discount
  - 50+ hours: 25% discount
  - 0+ hours: 0% discount
- **Etzem Discount**: Discount based on Etzem score
  - 300+ score: 30% discount
  - 200+ score: 20% discount
  - 100+ score: 10% discount
  - 0+ score: 0% discount
- **Total Discount**: Sum of Zeman and Etzem discounts (capped at 70%)
- **Minimum Fee**: The minimum fee (default: 1%)

## Fee Distribution

When a transaction fee is applied, the fee is distributed as follows:

- **Burn**: 30% of the fee is burned (removed from circulation)
- **Reward Pool**: 70% of the fee is added to the reward pool

## Rebate Mechanics

The Judah Engine provides rebates for certain roles:

- **CREATOR**: Creators of content or tokens
- **EDUCATOR**: Educators who teach others about Onnyx
- **BUILDER**: Builders who contribute to the Onnyx ecosystem

Rebates are calculated as a percentage of the amount (default: 50%).

## Staking

The Judah Engine supports staking ONX tokens for various benefits:

- **GOVERNANCE**: Participate in governance decisions
- **REDUCED_FEES**: Reduced transaction fees
- **PRIORITY_PROCESSING**: Priority processing of transactions

Staking tiers:
- 10,000+ ONX: GOVERNANCE, REDUCED_FEES, PRIORITY_PROCESSING
- 5,000+ ONX: GOVERNANCE, REDUCED_FEES
- 1,000+ ONX: REDUCED_FEES

## Economic Governance

The Judah Engine's economic parameters can be adjusted by councilors through governance proposals. This allows the Onnyx economy to adapt to changing conditions.

## API Endpoints

### Transaction Fee Endpoints

- `POST /judah/fee` - Apply a transaction fee to an identity
- `GET /judah/discount/{identity_id}` - Get the discount for an identity

### Rebate Endpoints

- `POST /judah/rebate` - Apply a rebate to an identity based on their role

### Judah Staking Endpoints

- `POST /judah/stake` - Stake ONX tokens for an identity
- `GET /judah/staking/tiers` - Get the staking tiers

### Configuration Endpoints

- `GET /judah/config` - Get the Judah Engine configuration
- `POST /judah/config/update` - Update the Judah Engine configuration

### ONX Staking Endpoints

- `POST /staking/stake` - Stake ONX tokens for an identity
- `POST /staking/unlock` - Unlock stakes for an identity
- `GET /staking/stakes/{identity_id}` - Get all stakes for an identity
- `GET /staking/stake/{stake_id}` - Get a stake by ID
- `GET /staking/unlockable/{identity_id}` - Get all unlockable stakes for an identity
- `GET /staking/roles/{identity_id}` - Get all active roles for an identity
- `GET /staking/soulbound-roles/{identity_id}` - Get all soulbound roles for an identity
- `GET /staking/benefits/{identity_id}` - Get all active benefits for an identity
- `GET /staking/total/{identity_id}` - Get the total amount staked by an identity
- `GET /staking/config` - Get the ONX Staking configuration
- `POST /staking/config/update` - Update the ONX Staking configuration

## Example Output

### Transaction Fee

```json
{
  "status": "success",
  "fee_details": {
    "fee_amount": 0.025,
    "burn_amount": 0.0075,
    "reward_amount": 0.0175,
    "discount": 0.5,
    "discount_details": {
      "zeman_discount": 0.5,
      "etzem_discount": 0.0,
      "total_discount": 0.5
    }
  }
}
```

### Discount

```json
{
  "identity_id": "marcus_id",
  "discount_info": {
    "zeman_discount": 0.5,
    "etzem_discount": 0.3,
    "total_discount": 0.7
  }
}
```

### Rebate

```json
{
  "status": "success",
  "rebate_details": {
    "identity_id": "marcus_id",
    "role": "CREATOR",
    "amount": 10.0,
    "rebate_amount": 5.0,
    "rebate_percent": 0.5
  }
}
```

### Judah Staking

```json
{
  "status": "success",
  "stake_details": {
    "id": "stake_marcus_id_1712341234",
    "identity_id": "marcus_id",
    "amount": 10000.0,
    "lock_period": 2592000,
    "unlock_time": 1714933234,
    "benefits": [
      "GOVERNANCE",
      "REDUCED_FEES",
      "PRIORITY_PROCESSING"
    ],
    "created_at": 1712341234,
    "status": "active"
  }
}
```

### ONX Staking

```json
{
  "status": "success",
  "stake": {
    "id": "stake_marcus_id_abc12345_1712341234",
    "identity_id": "marcus_id",
    "amount": 10000.0,
    "role": "VALIDATOR",
    "duration_days": 30,
    "locked_until": 1714933234,
    "staked_at": 1712341234,
    "reward_rate": 0.075,
    "estimated_reward": 61.64,
    "benefits": [
      "REDUCED_FEES",
      "PRIORITY_PROCESSING",
      "GOVERNANCE_VOTE"
    ],
    "status": "active"
  }
}
```

### Unlock Stake

```json
{
  "status": "success",
  "result": {
    "unlocked": [
      {
        "id": "stake_marcus_id_abc12345_1712341234",
        "identity_id": "marcus_id",
        "amount": 10000.0,
        "role": "VALIDATOR",
        "duration_days": 30,
        "locked_until": 1714933234,
        "staked_at": 1712341234,
        "reward_rate": 0.075,
        "estimated_reward": 61.64,
        "benefits": [
          "REDUCED_FEES",
          "PRIORITY_PROCESSING",
          "GOVERNANCE_VOTE"
        ],
        "status": "unlocked",
        "unlocked_at": 1714933300,
        "reward_amount": 61.64
      }
    ],
    "total_amount": 10061.64,
    "reward_amount": 61.64,
    "early_unlock": false,
    "revoked_roles": ["GUARDIAN"]
  }
}
```

### Soulbound Roles

```json
{
  "identity_id": "marcus_id",
  "soulbound_roles": [
    "COUNCIL_ELIGIBLE",
    "CURATOR",
    "MENTOR",
    "CREATOR"
  ]
}
```

### Configuration

```json
{
  "config": {
    "base_fee": 0.05,
    "burn_percent": 0.3,
    "reward_percent": 0.7,
    "rebate_roles": [
      "CREATOR",
      "EDUCATOR",
      "BUILDER"
    ],
    "rebate_percent": 0.5,
    "zeman_discount_tiers": [
      {
        "hours": 100,
        "discount": 0.5
      },
      {
        "hours": 50,
        "discount": 0.25
      },
      {
        "hours": 0,
        "discount": 0.0
      }
    ],
    "etzem_discount_tiers": [
      {
        "score": 300,
        "discount": 0.3
      },
      {
        "score": 200,
        "discount": 0.2
      },
      {
        "score": 100,
        "discount": 0.1
      },
      {
        "score": 0,
        "discount": 0.0
      }
    ],
    "staking_tiers": [
      {
        "amount": 10000,
        "benefits": [
          "GOVERNANCE",
          "REDUCED_FEES",
          "PRIORITY_PROCESSING"
        ]
      },
      {
        "amount": 5000,
        "benefits": [
          "GOVERNANCE",
          "REDUCED_FEES"
        ]
      },
      {
        "amount": 1000,
        "benefits": [
          "REDUCED_FEES"
        ]
      }
    ],
    "staking_lock_period": 2592000,
    "staking_reward_rate": 0.05,
    "min_fee": 0.01,
    "reward_pool_address": "reward_pool",
    "burn_address": "burn_address",
    "last_updated": 1712341234,
    "last_updated_by": "system"
  }
}
```

## Integration with Other Systems

The Judah Engine integrates with the following systems:

- **Token Ledger**: Manages token balances
- **Zeman Ledger**: Tracks time credits
- **Etzem Scorer**: Calculates trust scores
- **Identity Registry**: Manages identities
- **Sela Registry**: Manages businesses
- **Transaction Logger**: Logs transactions
- **Council Registry**: Verifies councilors for configuration updates
