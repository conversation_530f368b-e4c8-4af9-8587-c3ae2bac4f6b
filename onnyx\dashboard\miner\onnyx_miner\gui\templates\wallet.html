{% extends "base.html" %}

{% block content %}
<div class="row">
  <div class="col-md-6">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Wallet Information</h5>
      </div>
      <div class="card-body">
        <h6>Identity</h6>
        <p>
          <strong>ID:</strong> {{ wallet.identity_id }}<br>
          <strong>Name:</strong> {{ wallet.name }}<br>
          <strong>Etzem Score:</strong> {{ wallet.etzem_score }}
        </p>

        <h6>Balances</h6>
        {% if wallet.balances %}
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Token</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {% for token_id, amount in wallet.balances.items() %}
                <tr>
                  <td>{{ token_id }}</td>
                  <td>{{ amount }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <p>No balances found.</p>
        {% endif %}

        <h6>Badges</h6>
        {% if wallet.badges %}
          <div class="mb-3">
            {% for badge in wallet.badges %}
              <span class="badge bg-info me-1">{{ badge }}</span>
            {% endfor %}
          </div>
        {% else %}
          <p>No badges found.</p>
        {% endif %}

        <h6>Stakes</h6>
        {% if wallet.stakes %}
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Token</th>
                <th>Amount</th>
                <th>Locked Until</th>
              </tr>
            </thead>
            <tbody>
              {% for stake in wallet.stakes %}
                <tr>
                  <td>{{ stake.token_id }}</td>
                  <td>{{ stake.amount }}</td>
                  <td>{{ stake.locked_until }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <p>No stakes found.</p>
        {% endif %}
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Transfer Tokens</h5>
      </div>
      <div class="card-body">
        <form action="{{ url_for('transfer') }}" method="post">
          <div class="mb-3">
            <label for="to_id" class="form-label">Recipient ID</label>
            <input type="text" class="form-control" id="to_id" name="to_id" required>
          </div>
          <div class="mb-3">
            <label for="token_id" class="form-label">Token ID</label>
            <select class="form-select" id="token_id" name="token_id" required>
              {% for token_id, amount in wallet.balances.items() %}
                <option value="{{ token_id }}">{{ token_id }} ({{ amount }})</option>
              {% endfor %}
            </select>
          </div>
          <div class="mb-3">
            <label for="amount" class="form-label">Amount</label>
            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required>
          </div>
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">Transfer</button>
          </div>
        </form>
      </div>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title">Stake Tokens</h5>
      </div>
      <div class="card-body">
        <form action="{{ url_for('stake') }}" method="post">
          <div class="mb-3">
            <label for="stake_token_id" class="form-label">Token ID</label>
            <select class="form-select" id="stake_token_id" name="token_id" required>
              {% for token_id, amount in wallet.balances.items() %}
                <option value="{{ token_id }}">{{ token_id }} ({{ amount }})</option>
              {% endfor %}
            </select>
          </div>
          <div class="mb-3">
            <label for="stake_amount" class="form-label">Amount</label>
            <input type="number" class="form-control" id="stake_amount" name="amount" step="0.01" min="0.01" required>
          </div>
          <div class="mb-3">
            <label for="duration" class="form-label">Duration (seconds)</label>
            <select class="form-select" id="duration" name="duration" required>
              <option value="2592000">30 days</option>
              <option value="7776000">90 days</option>
              <option value="15552000">180 days</option>
              <option value="31536000">1 year</option>
            </select>
          </div>
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">Stake</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div class="card">
  <div class="card-header">
    <h5 class="card-title">Transaction History</h5>
  </div>
  <div class="card-body">
    {% if transactions %}
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>ID</th>
              <th>Type</th>
              <th>From</th>
              <th>To</th>
              <th>Token</th>
              <th>Amount</th>
              <th>Timestamp</th>
            </tr>
          </thead>
          <tbody>
            {% for tx in transactions %}
              <tr>
                <td>{{ tx.txid }}</td>
                <td>{{ tx.op }}</td>
                <td>{{ tx.from }}</td>
                <td>{{ tx.to }}</td>
                <td>{{ tx.token_id }}</td>
                <td>{{ tx.amount }}</td>
                <td>{{ tx.timestamp }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <p>No transactions found.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
