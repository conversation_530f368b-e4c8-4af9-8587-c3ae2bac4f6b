"""
Test the Onnyx Etzem Trust Growth Engine

This script tests the Onnyx Etzem Trust Growth Engine by calculating Etzem trust scores.
"""

import os
import sys
import json
import time

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from identity.registry import IdentityRegistry
from tokens.ledger import TokenLedger
from business.sela_registry import SelaRegistry
from governance.etzem_engine import EtzemEngine as ActivityEtzemEngine
from trust.etzem_engine import EtzemEngine

def setup_test_data():
    """Set up test data for the Etzem Trust Growth Engine test."""
    # Create identity registry
    identity_registry = IdentityRegistry()

    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "<PERSON>", "0x123456789abcdef")
        print(f"Created identity: Alice")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: Alice")

    try:
        bob = identity_registry.register_identity("bob", "<PERSON>", "0x987654321fedcba")
        print(f"Created identity: <PERSON>")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: Bob")

    try:
        charlie = identity_registry.register_identity("charlie", "Charlie", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")

    print("\nTest identities:")
    print(f"  Alice: {alice['identity_id']}")
    print(f"  Bob: {bob['identity_id']}")
    print(f"  Charlie: {charlie['identity_id']}")

    # Create token ledger
    token_ledger = TokenLedger()

    # Create Sela registry
    sela_registry = SelaRegistry()

    # Create activity-based Etzem engine
    activity_etzem = ActivityEtzemEngine()

    # Create Etzem Trust Growth Engine
    etzem = EtzemEngine(identity_registry, token_ledger, sela_registry, activity_etzem)

    return identity_registry, token_ledger, sela_registry, activity_etzem, etzem

def test_etzem_trust_growth_engine():
    """Test the Etzem Trust Growth Engine functionality."""
    # Set up test data
    identity_registry, token_ledger, sela_registry, activity_etzem, etzem = setup_test_data()

    # Set up a Sela for Bob
    try:
        sela = sela_registry.register_sela(
            "bobs_barbershop",
            "Bob's Barbershop",
            "bob",
            "BUSINESS",
            "BARBER_TOKEN"
        )
        identity_registry.link_sela("bob", "bobs_barbershop", "FOUNDER")
        print("\nRegistered Sela for Bob")
    except Exception as e:
        print(f"\nUsing existing Sela for Bob: {str(e)}")

    # Set up a Sela for Alice
    try:
        sela = sela_registry.register_sela(
            "alices_salon",
            "Alice's Salon",
            "alice",
            "BUSINESS",
            "SALON_TOKEN"
        )
        identity_registry.link_sela("alice", "alices_salon", "FOUNDER")
        print("Registered Sela for Alice")
    except Exception as e:
        print(f"Using existing Sela for Alice: {str(e)}")

    # Add some tokens to the ledger
    token_ledger.credit("alice", "ONX", 1000)
    token_ledger.credit("bob", "ONX", 500)
    token_ledger.credit("charlie", "ONX", 100)

    token_ledger.credit("alice", "SALON_TOKEN", 5000)
    token_ledger.credit("bob", "BARBER_TOKEN", 2000)

    # Add some badges
    try:
        identity_registry.add_badge("alice", "STAKER")
    except Exception:
        print("Alice already has STAKER badge")

    try:
        identity_registry.add_badge("alice", "VALIDATOR_ELIGIBLE_BADGE")
    except Exception:
        print("Alice already has VALIDATOR_ELIGIBLE_BADGE badge")

    try:
        identity_registry.add_badge("bob", "PROPOSAL_ELIGIBLE_BADGE")
    except Exception:
        print("Bob already has PROPOSAL_ELIGIBLE_BADGE badge")

    # Update reputation
    try:
        identity_registry.update_reputation("alice", 80)
        identity_registry.update_reputation("bob", 50)
        identity_registry.update_reputation("charlie", 20)
        print("Updated reputation for all identities")
    except Exception as e:
        print(f"Error updating reputation: {str(e)}")

    # Mock the activity-based Etzem engine
    def mock_calculate_etzem_score(identity_id):
        if identity_id == "alice":
            return {
                "consistency_score": 90,
                "token_impact": 85,
                "reputation": 95,
                "labor_contribution": 90,
                "final_etzem": 90
            }
        elif identity_id == "bob":
            return {
                "consistency_score": 60,
                "token_impact": 55,
                "reputation": 65,
                "labor_contribution": 60,
                "final_etzem": 60
            }
        else:
            return {
                "consistency_score": 30,
                "token_impact": 25,
                "reputation": 35,
                "labor_contribution": 30,
                "final_etzem": 30
            }

    activity_etzem.calculate_etzem_score = mock_calculate_etzem_score

    # Calculate Etzem trust scores
    print("\nCalculating Etzem trust scores...")

    alice_etzem = etzem.compute_etzem("alice")
    bob_etzem = etzem.compute_etzem("bob")
    charlie_etzem = etzem.compute_etzem("charlie")

    print("\nAlice's Etzem trust score:")
    for key, value in alice_etzem.items():
        print(f"  {key}: {value}")

    print("\nBob's Etzem trust score:")
    for key, value in bob_etzem.items():
        print(f"  {key}: {value}")

    print("\nCharlie's Etzem trust score:")
    for key, value in charlie_etzem.items():
        print(f"  {key}: {value}")

    # Update Etzem badges
    print("\nUpdating Etzem badges...")

    alice = etzem.update_etzem_badges("alice")
    bob = etzem.update_etzem_badges("bob")
    charlie = etzem.update_etzem_badges("charlie")

    print("\nAlice's badges:")
    print(f"  {alice.get('badges', [])}")

    print("\nBob's badges:")
    print(f"  {bob.get('badges', [])}")

    print("\nCharlie's badges:")
    print(f"  {charlie.get('badges', [])}")

    # Get the leaderboard
    print("\nEtzem leaderboard:")
    leaderboard = etzem.get_leaderboard()

    for i, entry in enumerate(leaderboard, 1):
        print(f"  {i}. {entry['identity_id']}: {entry['etzem']}")

    print("\nEtzem Trust Growth Engine test completed successfully!")

if __name__ == "__main__":
    test_etzem_trust_growth_engine()
