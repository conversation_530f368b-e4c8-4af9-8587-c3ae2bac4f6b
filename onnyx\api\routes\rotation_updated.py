"""
Onnyx Validator Rotation Routes

This module provides API routes for validator rotation.
"""

import logging
import time
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from blockchain.consensus.rotation_engine import rotation_engine
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry

from shared.models.rotation import Rotation
from shared.models.sela import Sela
from shared.models.identity import Identity
from shared.models.block import Block

# Set up logging
logger = logging.getLogger("onnyx.routes.rotation")

# Create router
router = APIRouter()

# Create instances
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()

@router.get("/rotation/status")
def get_rotation_status() -> Dict[str, Any]:
    """
    Get the current rotation status.

    Returns:
        The rotation status
    """
    logger.info("Getting rotation status")

    # Get the rotation from the database
    rotation = Rotation.get_main()

    # If the rotation doesn't exist, create it
    if not rotation:
        logger.info("Creating main rotation")

        # Get the rotation status from the engine
        status = rotation_engine.get_rotation_status()

        # Create the rotation in the database
        rotation = Rotation.create(
            id="main",
            current_validator=status.get("current_validator"),
            queue=status.get("queue", []),
            update_interval=status.get("update_interval", 3600),
            min_etzem_score=status.get("min_etzem_score", 100),
            required_badges=status.get("required_badges", ["VALIDATOR"])
        )

    # Get the rotation status from the engine
    engine_status = rotation_engine.get_rotation_status()

    # Update the rotation if needed
    if engine_status.get("queue") != rotation.queue:
        logger.info("Updating rotation queue")
        rotation.update_queue(engine_status.get("queue", []))

    if engine_status.get("current_validator") != rotation.current_validator:
        logger.info(f"Updating current validator to {engine_status.get('current_validator')}")
        rotation.set_current_validator(engine_status.get("current_validator"))

    # Get the latest block
    latest_block = Block.get_latest()

    # Get the next validator
    next_validator = None
    if latest_block:
        next_validator = rotation.get_next_validator(latest_block.index + 1)

    logger.info(f"Retrieved rotation status with {len(rotation.queue)} validators in queue")
    return {
        "current_validator": rotation.current_validator,
        "queue": rotation.queue,
        "queue_length": len(rotation.queue),
        "last_updated": rotation.last_updated,
        "update_interval": rotation.update_interval,
        "min_etzem_score": rotation.min_etzem_score,
        "required_badges": rotation.required_badges,
        "next_validator": next_validator,
        "next_height": latest_block.index + 1 if latest_block else 0
    }

@router.get("/rotation/next-validator/{height}")
def get_next_validator(height: int) -> Dict[str, Any]:
    """
    Get the next validator for a given block height.

    Args:
        height: The block height

    Returns:
        The next validator
    """
    try:
        logger.info(f"Getting next validator for height {height}")

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=status.get("queue", []),
                update_interval=status.get("update_interval", 3600),
                min_etzem_score=status.get("min_etzem_score", 100),
                required_badges=status.get("required_badges", ["VALIDATOR"])
            )

        # Get the next validator from the engine
        engine_next_validator = rotation_engine.get_next_validator(height)

        # Get the next validator from the database
        next_validator = rotation.get_next_validator(height)

        # Update the queue if needed
        if engine_next_validator != next_validator:
            logger.info("Updating rotation queue")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Update the rotation queue
            rotation.update_queue(status.get("queue", []))

            # Get the next validator again
            next_validator = rotation.get_next_validator(height)

        # Get the Sela details
        sela = Sela.get_by_id(next_validator)

        # Get the founder's identity
        founder = None
        if sela:
            founder = Identity.get_by_id(sela.founder_id)

        logger.info(f"Next validator for height {height} is {next_validator}")
        return {
            "height": height,
            "next_validator": next_validator,
            "sela": sela.to_dict() if sela else None,
            "founder": founder.to_dict() if founder else None
        }
    except Exception as e:
        logger.error(f"Error getting next validator: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/update-queue")
def update_queue() -> Dict[str, Any]:
    """
    Update the queue of eligible validators.

    Returns:
        The updated queue
    """
    try:
        logger.info("Updating validator queue")

        # Update the queue in the engine
        queue = rotation_engine.update_queue()

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=queue,
                update_interval=status.get("update_interval", 3600),
                min_etzem_score=status.get("min_etzem_score", 100),
                required_badges=status.get("required_badges", ["VALIDATOR"])
            )
        else:
            # Update the rotation queue
            rotation.update_queue(queue)

        # Get details for each Sela in the queue
        queue_details = []
        for sela_id in queue:
            sela = Sela.get_by_id(sela_id)
            founder = None
            if sela:
                founder = Identity.get_by_id(sela.founder_id)

            queue_details.append({
                "sela_id": sela_id,
                "sela": sela.to_dict() if sela else None,
                "founder": founder.to_dict() if founder else None
            })

        logger.info(f"Updated validator queue with {len(queue)} validators")
        return {
            "queue": queue,
            "queue_details": queue_details
        }
    except Exception as e:
        logger.error(f"Error updating queue: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/set-update-interval")
def set_update_interval(interval: int = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set the queue update interval.

    Args:
        interval: The update interval in seconds

    Returns:
        The updated rotation status
    """
    try:
        logger.info(f"Setting update interval to {interval} seconds")

        # Set the update interval in the engine
        rotation_engine.set_update_interval(interval)

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=status.get("queue", []),
                update_interval=interval,
                min_etzem_score=status.get("min_etzem_score", 100),
                required_badges=status.get("required_badges", ["VALIDATOR"])
            )
        else:
            # Update the rotation update interval
            rotation.set_update_interval(interval)

        logger.info(f"Update interval set to {interval} seconds")
        return get_rotation_status()
    except Exception as e:
        logger.error(f"Error setting update interval: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/set-min-etzem-score")
def set_min_etzem_score(score: int = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set the minimum Etzem score required for eligibility.

    Args:
        score: The minimum Etzem score

    Returns:
        The updated rotation status
    """
    try:
        logger.info(f"Setting minimum Etzem score to {score}")

        # Set the minimum Etzem score in the engine
        rotation_engine.set_min_etzem_score(score)

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=status.get("queue", []),
                update_interval=status.get("update_interval", 3600),
                min_etzem_score=score,
                required_badges=status.get("required_badges", ["VALIDATOR"])
            )
        else:
            # Update the rotation minimum Etzem score
            rotation.set_min_etzem_score(score)

        logger.info(f"Minimum Etzem score set to {score}")
        return get_rotation_status()
    except Exception as e:
        logger.error(f"Error setting minimum Etzem score: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/set-required-badges")
def set_required_badges(badges: List[str] = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set the required badges for eligibility.

    Args:
        badges: The required badges

    Returns:
        The updated rotation status
    """
    try:
        logger.info(f"Setting required badges to {badges}")

        # Set the required badges in the engine
        rotation_engine.set_required_badges(badges)

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=status.get("queue", []),
                update_interval=status.get("update_interval", 3600),
                min_etzem_score=status.get("min_etzem_score", 100),
                required_badges=badges
            )
        else:
            # Update the rotation required badges
            rotation.set_required_badges(badges)

        logger.info(f"Required badges set to {badges}")
        return get_rotation_status()
    except Exception as e:
        logger.error(f"Error setting required badges: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/rotation/is-valid-proposer/{sela_id}/{height}")
def is_valid_proposer(sela_id: str, height: int) -> Dict[str, Any]:
    """
    Check if a Sela is the valid proposer for a given block height.

    Args:
        sela_id: The Sela ID to check
        height: The block height

    Returns:
        Whether the Sela is the valid proposer
    """
    try:
        logger.info(f"Checking if Sela {sela_id} is valid proposer for height {height}")

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=status.get("queue", []),
                update_interval=status.get("update_interval", 3600),
                min_etzem_score=status.get("min_etzem_score", 100),
                required_badges=status.get("required_badges", ["VALIDATOR"])
            )

        # Check if the Sela is the valid proposer
        is_valid = rotation.is_valid_proposer(sela_id, height)

        # Get the next validator
        next_validator = rotation.get_next_validator(height)

        # Check if the engine agrees
        engine_next_validator = rotation_engine.get_next_validator(height)

        # Update the queue if needed
        if engine_next_validator != next_validator:
            logger.info("Updating rotation queue")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Update the rotation queue
            rotation.update_queue(status.get("queue", []))

            # Check again
            is_valid = rotation.is_valid_proposer(sela_id, height)
            next_validator = rotation.get_next_validator(height)

        logger.info(f"Sela {sela_id} is {'valid' if is_valid else 'not valid'} proposer for height {height}")
        return {
            "sela_id": sela_id,
            "height": height,
            "is_valid_proposer": is_valid,
            "next_validator": next_validator
        }
    except Exception as e:
        logger.error(f"Error checking if Sela is valid proposer: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/rotation/eligible-validators")
def get_eligible_validators() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all eligible validators.

    Returns:
        A list of eligible validators
    """
    try:
        logger.info("Getting eligible validators")

        # Get the rotation from the database
        rotation = Rotation.get_main()

        # If the rotation doesn't exist, create it
        if not rotation:
            logger.info("Creating main rotation")

            # Get the rotation status from the engine
            status = rotation_engine.get_rotation_status()

            # Create the rotation in the database
            rotation = Rotation.create(
                id="main",
                current_validator=status.get("current_validator"),
                queue=status.get("queue", []),
                update_interval=status.get("update_interval", 3600),
                min_etzem_score=status.get("min_etzem_score", 100),
                required_badges=status.get("required_badges", ["VALIDATOR"])
            )

        # Get all Selas
        selas_list = Sela.get_all()

        # Filter eligible validators
        eligible_validators = []
        for sela in selas_list:
            # Get the founder
            founder = Identity.get_by_id(sela.founder_id)

            # Check if the founder has the required badges
            if founder and founder.metadata.get("badges"):
                has_required_badges = all(badge in founder.metadata["badges"] for badge in rotation.required_badges)

                # Check if the founder has the minimum Etzem score
                etzem_score = 0
                if "etzem_score" in founder.metadata:
                    etzem_score = founder.metadata["etzem_score"]

                if has_required_badges and etzem_score >= rotation.min_etzem_score:
                    eligible_validators.append({
                        "sela_id": sela.sela_id,
                        "sela": sela.to_dict(),
                        "founder": founder.to_dict(),
                        "etzem_score": etzem_score,
                        "badges": founder.metadata.get("badges", []),
                        "in_queue": sela.sela_id in rotation.queue
                    })

        logger.info(f"Found {len(eligible_validators)} eligible validators")
        return {
            "eligible_validators": eligible_validators
        }
    except Exception as e:
        logger.error(f"Error getting eligible validators: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
