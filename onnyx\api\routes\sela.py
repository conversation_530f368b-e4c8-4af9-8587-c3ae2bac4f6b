"""
Onnyx Sela Routes

This module provides API routes for Sela operations.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional

from sela.registry.sela_registry import SelaRegistry
from identity.registry import IdentityRegistry

# Create router
router = APIRouter()

# Create instances
selas = SelaRegistry()
identities = IdentityRegistry()

@router.post("/sela/register")
def register_sela(sela_id: str, name: str, founder_id: str, sela_type: str, token_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Register a new Sela.
    
    Args:
        sela_id: The Sela ID
        name: The Sela name
        founder_id: The founder's identity ID
        sela_type: The Sela type (e.g., "BUSINESS", "NONPROFIT", "COMMUNITY")
        token_id: The token ID associated with the Sela (optional)
    
    Returns:
        Information about the registered Sela
    """
    try:
        # Check if the founder identity exists
        if not identities.get_identity(founder_id):
            raise Exception(f"Identity with ID '{founder_id}' not found")
        
        # Register the Sela
        sela = selas.register_sela(sela_id, name, founder_id, sela_type, token_id)
        
        # Link the Sela to the founder's identity
        identities.link_sela(founder_id, sela_id, "FOUNDER")
        
        return {
            "status": "created",
            "sela": sela
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/join")
def join_sela(sela_id: str, identity_id: str, role: str = "MEMBER") -> Dict[str, Any]:
    """
    Add an identity to a Sela.
    
    Args:
        sela_id: The Sela ID
        identity_id: The identity ID to add
        role: The role of the identity in the Sela (default: "MEMBER")
    
    Returns:
        Information about the joined Sela
    """
    try:
        # Check if the identity exists
        if not identities.get_identity(identity_id):
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the Sela exists
        if not selas.get_sela(sela_id):
            raise Exception(f"Sela with ID '{sela_id}' not found")
        
        # Join the Sela
        selas.join_sela(sela_id, identity_id, role)
        
        # Link the Sela to the identity
        identities.link_sela(identity_id, sela_id, role)
        
        return {
            "status": "joined",
            "sela_id": sela_id,
            "identity_id": identity_id,
            "role": role
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/leave")
def leave_sela(sela_id: str, identity_id: str) -> Dict[str, Any]:
    """
    Remove an identity from a Sela.
    
    Args:
        sela_id: The Sela ID
        identity_id: The identity ID to remove
    
    Returns:
        Information about the left Sela
    """
    try:
        # Check if the identity exists
        if not identities.get_identity(identity_id):
            raise Exception(f"Identity with ID '{identity_id}' not found")
        
        # Check if the Sela exists
        sela = selas.get_sela(sela_id)
        if not sela:
            raise Exception(f"Sela with ID '{sela_id}' not found")
        
        # Get the role before leaving
        role = sela["roles"].get(identity_id, "MEMBER")
        
        # Leave the Sela
        selas.leave_sela(sela_id, identity_id)
        
        # Unlink the Sela from the identity
        identities.unlink_sela(identity_id, sela_id, role)
        
        return {
            "status": "left",
            "sela_id": sela_id,
            "identity_id": identity_id
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sela/{sela_id}/service")
def add_service(sela_id: str, service: str) -> Dict[str, Any]:
    """
    Add a service to a Sela.
    
    Args:
        sela_id: The Sela ID
        service: The service to add
    
    Returns:
        Information about the updated Sela
    """
    try:
        # Check if the Sela exists
        if not selas.get_sela(sela_id):
            raise Exception(f"Sela with ID '{sela_id}' not found")
        
        # Add the service
        selas.add_service(sela_id, service)
        
        return {
            "status": "service_added",
            "sela_id": sela_id,
            "service": service
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/sela/{sela_id}/service")
def remove_service(sela_id: str, service: str) -> Dict[str, Any]:
    """
    Remove a service from a Sela.
    
    Args:
        sela_id: The Sela ID
        service: The service to remove
    
    Returns:
        Information about the updated Sela
    """
    try:
        # Check if the Sela exists
        if not selas.get_sela(sela_id):
            raise Exception(f"Sela with ID '{sela_id}' not found")
        
        # Remove the service
        selas.remove_service(sela_id, service)
        
        return {
            "status": "service_removed",
            "sela_id": sela_id,
            "service": service
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/sela/{sela_id}")
def get_sela(sela_id: str) -> Dict[str, Any]:
    """
    Get a Sela by ID.
    
    Args:
        sela_id: The Sela ID
    
    Returns:
        The Sela
    """
    sela = selas.get_sela(sela_id)
    if not sela:
        raise HTTPException(status_code=404, detail=f"Sela with ID '{sela_id}' not found")
    
    return sela

@router.get("/selas")
def list_selas() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all Selas.
    
    Returns:
        All Selas in the registry
    """
    return {
        "selas": list(selas.get_all_selas().values())
    }

@router.get("/selas/founder/{founder_id}")
def get_selas_by_founder(founder_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all Selas founded by an identity.
    
    Args:
        founder_id: The founder's identity ID
    
    Returns:
        A list of Selas founded by the identity
    """
    return {
        "selas": selas.get_selas_by_founder(founder_id)
    }

@router.get("/selas/member/{identity_id}")
def get_selas_by_member(identity_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all Selas that an identity is a member of.
    
    Args:
        identity_id: The identity ID
    
    Returns:
        A list of Selas that the identity is a member of
    """
    return {
        "selas": selas.get_selas_by_member(identity_id)
    }
