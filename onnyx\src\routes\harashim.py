# src/routes/harashim.py

from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import time

from src.harashim.registry import HarashimRegistry
from src.harashim.contracts import ServiceContract
from src.harashim.performance import HarashimPerformance
from src.harashim.payout import ContractPayout

# Create router
router = APIRouter(
    prefix="/harashim",
    tags=["harashim"],
    responses={404: {"description": "Not found"}}
)

# Create instances
harashim_registry = HarashimRegistry()
service_contract = ServiceContract()
harashim_performance = HarashimPerformance()
contract_payout = ContractPayout()

# Pydantic models for request validation
class HarashRegistration(BaseModel):
    identity_id: str
    affiliated_sela: Optional[str] = None

class ContractCreation(BaseModel):
    sela_id: str
    harash_id: str
    title: str
    description: str
    estimated_hours: int = Field(..., gt=0)
    reward_type: str = Field(..., regex="^(ONX|MIKVAH|ZEMAN|ALL)$")
    reward_amount: float = Field(..., gt=0)
    deadline: Optional[int] = None

class ContractCompletion(BaseModel):
    actual_hours: int = Field(..., gt=0)
    feedback: Optional[str] = None
    rating: Optional[int] = Field(None, ge=1, le=5)

# Routes
@router.post("/register", response_model=Dict[str, Any])
async def register_harash(registration: HarashRegistration):
    """
    Register an identity as a Harash.
    """
    try:
        harash = harashim_registry.register_harash(
            registration.identity_id,
            registration.affiliated_sela
        )
        return {
            "status": "success",
            "message": "Harash registered successfully",
            "data": harash
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/contract", response_model=Dict[str, Any])
async def create_contract(contract: ContractCreation):
    """
    Create a new service contract.
    """
    try:
        new_contract = service_contract.create_contract(
            contract.sela_id,
            contract.harash_id,
            contract.title,
            contract.description,
            contract.estimated_hours,
            contract.reward_type,
            contract.reward_amount,
            contract.deadline
        )
        return {
            "status": "success",
            "message": "Contract created successfully",
            "data": new_contract
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/contracts/{identity_id}", response_model=Dict[str, Any])
async def get_contracts(identity_id: str, role: str = Query("harash", regex="^(harash|sela)$")):
    """
    Get contracts for an identity.
    """
    try:
        if role == "harash":
            contracts = service_contract.get_contracts_by_harash(identity_id)
        else:
            contracts = service_contract.get_contracts_by_sela(identity_id)
        
        return {
            "status": "success",
            "count": len(contracts),
            "data": contracts
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.patch("/contract/{contract_id}/complete", response_model=Dict[str, Any])
async def complete_contract(contract_id: str, completion: ContractCompletion):
    """
    Complete a service contract.
    """
    try:
        # Complete the contract
        updated_contract = service_contract.complete_contract(
            contract_id,
            completion.actual_hours,
            completion.feedback,
            completion.rating
        )
        
        if not updated_contract:
            raise HTTPException(status_code=404, detail=f"Contract with ID '{contract_id}' not found")
        
        # Apply contract result to Etzem score
        performance_update = harashim_performance.apply_contract_result(contract_id)
        
        # Process token payout if applicable
        payout_result = None
        if updated_contract["reward_type"] in ["ONX", "MIKVAH", "ALL"]:
            try:
                payout_result = contract_payout.process_payout(contract_id)
            except Exception as e:
                # Log the error but don't fail the request
                print(f"Error processing payout: {str(e)}")
        
        return {
            "status": "success",
            "message": "Contract completed successfully",
            "data": {
                "contract": updated_contract,
                "performance": performance_update,
                "payout": payout_result
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/performance/{identity_id}", response_model=Dict[str, Any])
async def get_performance(identity_id: str):
    """
    Get performance metrics for a Harash.
    """
    try:
        # Check if the identity is a Harash
        harash = harashim_registry.get_harash_role(identity_id)
        if not harash:
            raise HTTPException(status_code=404, detail=f"Identity with ID '{identity_id}' is not a Harash")
        
        # Get performance metrics
        metrics = harashim_performance.get_performance_metrics(identity_id)
        
        # Get payouts
        payouts = contract_payout.get_payouts_by_harash(identity_id)
        
        return {
            "status": "success",
            "data": {
                "harash": harash,
                "metrics": metrics,
                "payouts": payouts
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/contract/{contract_id}", response_model=Dict[str, Any])
async def get_contract(contract_id: str):
    """
    Get a contract by ID.
    """
    try:
        contract = service_contract.get_contract(contract_id)
        if not contract:
            raise HTTPException(status_code=404, detail=f"Contract with ID '{contract_id}' not found")
        
        # Get payout information if available
        payout = contract_payout.get_payout(contract_id)
        
        return {
            "status": "success",
            "data": {
                "contract": contract,
                "payout": payout
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/sela/{sela_id}/harashim", response_model=Dict[str, Any])
async def get_sela_harashim(sela_id: str):
    """
    Get all Harashim affiliated with a Sela.
    """
    try:
        harashim = harashim_registry.get_harashim_by_sela(sela_id)
        
        return {
            "status": "success",
            "count": len(harashim),
            "data": harashim
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
