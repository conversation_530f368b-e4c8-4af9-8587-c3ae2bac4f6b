#!/usr/bin/env python3
"""
ONNYX Hybrid Mining System Test Suite

Comprehensive testing for the Proof-of-Trust + Performance Boost Hybrid Mining system.
"""

import os
import sys
import json
import time
import subprocess
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

class HybridMiningTester:
    """Test suite for the hybrid mining system."""

    def __init__(self):
        self.test_results = []

    def log_test(self, test_name, passed, details=""):
        """Log a test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")

        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })

    def test_database_schema(self):
        """Test that the database schema has been properly migrated."""
        print("\n🔍 TESTING DATABASE SCHEMA")
        print("-" * 40)

        try:
            # Check if selas table has new columns
            import sqlite3
            conn = sqlite3.connect("shared/db/onnyx.db")
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(selas)")
            columns = [column[1] for column in cursor.fetchall()]

            required_columns = ['mining_power', 'mining_tier', 'last_mining_activity',
                              'mining_rewards_earned', 'blocks_mined']

            for col in required_columns:
                if col in columns:
                    self.log_test(f"Column '{col}' exists", True)
                else:
                    self.log_test(f"Column '{col}' exists", False, f"Missing column: {col}")

            # Check if new tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]

            required_tables = ['mining_tier_history', 'mining_rewards']
            for table in required_tables:
                if table in tables:
                    self.log_test(f"Table '{table}' exists", True)
                else:
                    self.log_test(f"Table '{table}' exists", False, f"Missing table: {table}")

        except Exception as e:
            self.log_test("Database schema check", False, f"Error: {e}")

    def test_miner_configuration(self):
        """Test the ONNYX miner configuration system."""
        print("\n⚙️  TESTING MINER CONFIGURATION")
        print("-" * 40)

        # Check if config file exists
        config_path = "onnyx_miner_config.toml"
        if os.path.exists(config_path):
            self.log_test("ONNYX miner config file exists", True)

            try:
                import toml
                with open(config_path, 'r') as f:
                    config = toml.load(f)

                # Check required configuration sections
                required_sections = ['miner', 'security', 'network', 'rewards']
                for section in required_sections:
                    if section in config:
                        self.log_test(f"Config section '{section}' exists", True)
                    else:
                        self.log_test(f"Config section '{section}' exists", False)

                # Check miner identification
                is_official = config.get('miner', {}).get('is_official_miner', False)
                self.log_test("Official miner flag configured", True, f"is_official_miner: {is_official}")

            except Exception as e:
                self.log_test("Config file parsing", False, f"Error: {e}")
        else:
            self.log_test("ONNYX miner config file exists", False, "Config file not found")

    def test_validator_management(self):
        """Test validator approval and tier management."""
        print("\n🏢 TESTING VALIDATOR MANAGEMENT")
        print("-" * 40)

        try:
            # Check if we have any validators
            validators = db.query("SELECT * FROM selas LIMIT 5")
            self.log_test("Validators exist in database", len(validators) > 0, f"Found {len(validators)} validators")

            if validators:
                # Check mining tier data
                for validator in validators[:3]:
                    tier = validator.get('mining_tier', 'basic')
                    power = validator.get('mining_power', 1)
                    self.log_test(f"Validator {validator['name'][:20]} has mining data",
                                True, f"Tier: {tier}, Power: {power}x")

            # Test validator management script exists
            script_path = "scripts/approve_validator.py"
            if os.path.exists(script_path):
                self.log_test("Validator management script exists", True)
            else:
                self.log_test("Validator management script exists", False)

        except Exception as e:
            self.log_test("Validator management test", False, f"Error: {e}")

    def test_hybrid_miner_script(self):
        """Test the hybrid mining script."""
        print("\n⛏️  TESTING HYBRID MINER")
        print("-" * 40)

        # Check if hybrid miner script exists
        script_path = "scripts/hybrid_production_miner.py"
        if os.path.exists(script_path):
            self.log_test("Hybrid miner script exists", True)

            # Test script syntax
            try:
                result = subprocess.run([sys.executable, '-m', 'py_compile', script_path],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.log_test("Hybrid miner script syntax", True)
                else:
                    self.log_test("Hybrid miner script syntax", False, result.stderr)
            except Exception as e:
                self.log_test("Hybrid miner script syntax", False, f"Error: {e}")
        else:
            self.log_test("Hybrid miner script exists", False)

    def test_mining_rewards_calculation(self):
        """Test mining rewards calculation logic."""
        print("\n💰 TESTING MINING REWARDS")
        print("-" * 40)

        try:
            # Test reward calculation for different tiers
            test_cases = [
                {'tier': 'basic', 'power': 1, 'base_reward': 1.0, 'expected': 1.0},
                {'tier': 'optimized', 'power': 2, 'base_reward': 1.0, 'expected': 2.2},  # 2x + 10% bonus
                {'tier': 'optimized', 'power': 5, 'base_reward': 1.0, 'expected': 5.5},  # 5x + 10% bonus
                {'tier': 'pro', 'power': 5, 'base_reward': 1.0, 'expected': 5.5},       # 5x + 10% bonus
            ]

            for case in test_cases:
                # Simulate reward calculation
                base_reward = case['base_reward']
                mining_power = case['power']
                is_official = case['tier'] in ['optimized', 'pro']

                final_reward = base_reward * mining_power
                if is_official:
                    final_reward *= 1.1  # 10% performance bonus

                expected = case['expected']
                passed = abs(final_reward - expected) < 0.01

                self.log_test(f"Reward calculation for {case['tier']} tier",
                            passed, f"Expected: {expected}, Got: {final_reward}")

        except Exception as e:
            self.log_test("Mining rewards calculation", False, f"Error: {e}")

    def test_mining_tier_history(self):
        """Test mining tier history tracking."""
        print("\n📋 TESTING TIER HISTORY")
        print("-" * 40)

        try:
            # Check if mining_tier_history table has data
            history_count = db.query_one("SELECT COUNT(*) as count FROM mining_tier_history")['count']
            self.log_test("Mining tier history table accessible", True, f"Found {history_count} history records")

            # Check if mining_rewards table has data
            rewards_count = db.query_one("SELECT COUNT(*) as count FROM mining_rewards")['count']
            self.log_test("Mining rewards table accessible", True, f"Found {rewards_count} reward records")

        except Exception as e:
            self.log_test("Mining tier history test", False, f"Error: {e}")

    def test_dashboard_integration(self):
        """Test dashboard integration with mining data."""
        print("\n🎛️  TESTING DASHBOARD INTEGRATION")
        print("-" * 40)

        try:
            # Check if dashboard route file exists and has mining updates
            dashboard_path = "web/routes/dashboard.py"
            if os.path.exists(dashboard_path):
                with open(dashboard_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Check for mining-related code
                mining_keywords = ['mining_tier', 'mining_power', 'mining_rewards_earned']
                for keyword in mining_keywords:
                    if keyword in content:
                        self.log_test(f"Dashboard includes '{keyword}'", True)
                    else:
                        self.log_test(f"Dashboard includes '{keyword}'", False)
            else:
                self.log_test("Dashboard route file exists", False)

            # Check dashboard template
            template_path = "web/templates/dashboard/overview.html"
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Check for mining tier display
                if 'mining_tier' in content or 'Mining Tier' in content:
                    self.log_test("Dashboard template shows mining tier", True)
                else:
                    self.log_test("Dashboard template shows mining tier", False)
            else:
                self.log_test("Dashboard template exists", False)

        except Exception as e:
            self.log_test("Dashboard integration test", False, f"Error: {e}")

    def test_security_features(self):
        """Test security and anti-spoofing features."""
        print("\n🔒 TESTING SECURITY FEATURES")
        print("-" * 40)

        try:
            # Check for signature verification in hybrid miner
            miner_path = "scripts/hybrid_production_miner.py"
            if os.path.exists(miner_path):
                with open(miner_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                security_features = [
                    'verify_miner_signature',
                    'is_official_miner',
                    'mining_tier',
                    'mining_power'
                ]

                for feature in security_features:
                    if feature in content:
                        self.log_test(f"Security feature '{feature}' implemented", True)
                    else:
                        self.log_test(f"Security feature '{feature}' implemented", False)

        except Exception as e:
            self.log_test("Security features test", False, f"Error: {e}")

    def test_backward_compatibility(self):
        """Test backward compatibility with existing blockchain."""
        print("\n🔄 TESTING BACKWARD COMPATIBILITY")
        print("-" * 40)

        try:
            # Check if existing blocks are preserved
            block_count = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']
            self.log_test("Existing blocks preserved", block_count > 0, f"Found {block_count} blocks")

            # Check if existing transactions are preserved
            tx_count = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
            self.log_test("Existing transactions preserved", tx_count >= 0, f"Found {tx_count} transactions")

            # Check if existing identities are preserved
            identity_count = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
            self.log_test("Existing identities preserved", identity_count >= 0, f"Found {identity_count} identities")

        except Exception as e:
            self.log_test("Backward compatibility test", False, f"Error: {e}")

    def run_all_tests(self):
        """Run all tests and generate a report."""
        print("🚀 ONNYX HYBRID MINING SYSTEM TEST SUITE")
        print("=" * 60)

        # Run all test categories
        self.test_database_schema()
        self.test_miner_configuration()
        self.test_validator_management()
        self.test_hybrid_miner_script()
        self.test_mining_rewards_calculation()
        self.test_mining_tier_history()
        self.test_dashboard_integration()
        self.test_security_features()
        self.test_backward_compatibility()

        # Generate summary
        print("\n📊 TEST SUMMARY")
        print("=" * 30)

        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t['passed']])
        failed_tests = total_tests - passed_tests

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test in self.test_results:
                if not test['passed']:
                    print(f"  - {test['test']}: {test['details']}")

        # Overall result
        if passed_tests >= total_tests * 0.8:  # 80% pass rate
            print("\n🎉 HYBRID MINING SYSTEM TESTS PASSED!")
            print("✅ System is ready for production use")
            return True
        else:
            print("\n⚠️  HYBRID MINING SYSTEM NEEDS ATTENTION")
            print("Some critical tests failed. Please review and fix issues.")
            return False

def main():
    """Main entry point."""
    tester = HybridMiningTester()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
