<div class="explorer-tabs">
    <div class="explorer-tabs-inner">
        <a href="{{ url_for('explorer') }}" {% if active_tab == 'dashboard' %}class="active"{% endif %}>Dashboard</a>
        <a href="{{ url_for('transactions') }}" {% if active_tab == 'transactions' %}class="active"{% endif %}>Transactions</a>
        <a href="{{ url_for('mempool_view') }}" {% if active_tab == 'mempool' %}class="active"{% endif %}>Mempool</a>
        <a href="{{ url_for('tokens') }}" {% if active_tab == 'tokens' %}class="active"{% endif %}>Tokens</a>
        <a href="{{ url_for('identities') }}" {% if active_tab == 'identities' %}class="active"{% endif %}>Identities</a>
        <a href="{{ url_for('search') }}" {% if active_tab == 'search' %}class="active"{% endif %}>Search</a>
    </div>
</div>

<style>
    .explorer-tabs {
        background-color: var(--darker-bg);
        border-radius: 8px;
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .explorer-tabs-inner {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        justify-content: center;
    }

    .explorer-tabs-inner::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }

    .explorer-tabs a {
        padding: 1rem 1.5rem;
        color: var(--light-text);
        text-decoration: none;
        transition: all 0.3s ease;
        white-space: nowrap;
        border-bottom: 3px solid transparent;
    }

    .explorer-tabs a:hover {
        background-color: rgba(138, 43, 226, 0.2);
        border-bottom-color: rgba(138, 43, 226, 0.5);
    }

    .explorer-tabs a.active {
        background-color: rgba(138, 43, 226, 0.3);
        border-bottom-color: var(--primary-color);
        font-weight: bold;
    }

    @media (max-width: 768px) {
        .explorer-tabs a {
            padding: 0.8rem 1rem;
            font-size: 0.9rem;
        }
    }
</style>
