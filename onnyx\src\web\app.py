# src/web/app.py

import os
import sys
import datetime
import uuid
import json
import time
import logging
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session

# Set up logging
logger = logging.getLogger("onnyx.web.app")

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.chain.txlog import TxLogger
from src.chain.mempool import Mempool
from src.tokens.registry import TokenRegistry
from src.identity.registry import IdentityRegistry
from src.node.crypto import generate_keys, public_key_to_base64
from src.chain.chain import Blockchain

# Initialize components
app = Flask(__name__)
app.secret_key = 'onnyx_secret_key'  # For flash messages

txlog = TxLogger()
mempool = Mempool()
token_registry = TokenRegistry()
identity_registry = IdentityRegistry()
blockchain = Blockchain()

# Create data directories if they don't exist
os.makedirs(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "keys"), exist_ok=True)

# Add current year to all templates
@app.context_processor
def inject_now():
    return {'now': datetime.datetime.now(), 'min': min}

# Helper function to format timestamps
def format_timestamp(timestamp):
    return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

# Register the filter with Jinja2
app.jinja_env.filters['format_timestamp'] = format_timestamp

@app.route('/')
def home():
    """Home page with links to different sections."""
    return render_template('index.html')

@app.route('/transactions')
def transactions():
    """View confirmed transactions with pagination."""
    # Get pagination parameters
    limit = int(request.args.get('limit', 25))
    offset = int(request.args.get('offset', 0))

    try:
        # Get transactions directly from the database
        from data.db import db
        all_tx = db.query("SELECT * FROM transactions ORDER BY timestamp DESC LIMIT ? OFFSET ?", (limit, offset))
        total_count = db.query_one("SELECT COUNT(*) as count FROM transactions")
        total = total_count['count'] if total_count else 0

        # Parse JSON data in transactions
        for tx in all_tx:
            if tx['data'] and isinstance(tx['data'], str):
                try:
                    tx['data'] = json.loads(tx['data'])
                except json.JSONDecodeError:
                    pass
    except Exception as e:
        logger.error(f"Error getting transactions from database: {str(e)}")
        # Fall back to txlog if database query fails
        all_tx = txlog.get_all(limit=limit, offset=offset)
        total = len(txlog.get_all())

    # Render the template
    return render_template(
        'transactions.html',
        transactions=all_tx,
        limit=limit,
        offset=offset,
        total=total,
        mempool=False,
        title='Confirmed Transactions',
        active_tab='transactions'
    )

@app.route('/mempool')
def mempool_view():
    """View pending transactions in the mempool."""
    # Get pagination parameters
    limit = int(request.args.get('limit', 25))
    offset = int(request.args.get('offset', 0))

    # Get pending transactions
    all_pending = mempool.get_all(limit=limit, offset=offset)
    total = len(all_pending)

    # Render the template
    return render_template(
        'transactions.html',
        transactions=all_pending,
        limit=limit,
        offset=offset,
        total=total,
        mempool=True,
        title='Mempool Transactions',
        active_tab='mempool'
    )

@app.route('/transaction/<txid>')
def transaction_detail(txid):
    """View details of a specific transaction."""
    # Check if it's a mempool transaction
    if txid.startswith('mem_'):
        tx = mempool.get_by_id(txid)
        if tx:
            return render_template('transaction_detail.html', tx=tx, mempool=True)
    else:
        try:
            # Try to get the transaction from the database first
            from data.db import db
            tx = db.query_one("SELECT * FROM transactions WHERE tx_id = ?", (txid,))

            if tx:
                # Parse JSON data if it's a string
                if tx['data'] and isinstance(tx['data'], str):
                    try:
                        tx['data'] = json.loads(tx['data'])
                    except json.JSONDecodeError:
                        pass

                return render_template('transaction_detail.html', tx=tx, mempool=False)
        except Exception as e:
            logger.error(f"Error getting transaction from database: {str(e)}")
            # Fall back to txlog if database query fails
            pass

        # Check confirmed transactions in txlog
        tx = txlog.get_by_txid(txid)
        if tx:
            return render_template('transaction_detail.html', tx=tx, mempool=False)

    # Transaction not found
    flash('Transaction not found', 'error')
    return redirect(url_for('transactions'))

@app.route('/tokens')
def tokens():
    """View all tokens."""
    # Get pagination parameters
    limit = int(request.args.get('limit', 25))
    offset = int(request.args.get('offset', 0))

    # Get tokens
    all_tokens = token_registry.list_tokens()
    total = len(all_tokens)
    paginated_tokens = all_tokens[offset:offset+limit]

    # Render the template
    return render_template(
        'tokens.html',
        tokens=paginated_tokens,
        limit=limit,
        offset=offset,
        total=total
    )

@app.route('/token/<token_id>')
def token_detail(token_id):
    """View details of a specific token."""
    # Get the token
    token = token_registry.get_token(token_id)
    if not token:
        flash('Token not found', 'error')
        return redirect(url_for('tokens'))

    # Get token transactions
    token_txs = txlog.get_by_token(token_id)

    # Render the template
    return render_template(
        'token_detail.html',
        token=token,
        transactions=token_txs
    )

@app.route('/identities')
def identities():
    """View all identities."""
    # Get pagination parameters
    limit = int(request.args.get('limit', 25))
    offset = int(request.args.get('offset', 0))

    # Get identities
    all_identities = identity_registry.get_all_identities()
    total = len(all_identities)
    paginated_identities = all_identities[offset:offset+limit]

    # Render the template
    return render_template(
        'identities.html',
        identities=paginated_identities,
        limit=limit,
        offset=offset,
        total=total
    )

@app.route('/identity/<identity_id>')
def identity_detail(identity_id):
    """View details of a specific identity."""
    # Get the identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        flash('Identity not found', 'error')
        return redirect(url_for('identities'))

    # Get identity transactions
    identity_txs = txlog.get_by_identity(identity_id)

    # Render the template
    return render_template(
        'identity_detail.html',
        identity=identity,
        transactions=identity_txs
    )

@app.route('/create-identity', methods=['GET', 'POST'])
def create_identity():
    """Create a new soulbound identity."""
    if request.method == 'POST':
        try:
            # Check if the request is JSON or form data
            if request.is_json:
                data = request.get_json()
                name = data.get('name')
                nation = data.get('nation')
                tribe = data.get('tribe')
                dob = data.get('dob')
                region = data.get('region')
                purpose = data.get('purpose')
                soul_seal = data.get('soul_seal')
                description = data.get('description')
            else:
                # Get form data
                name = request.form.get('name')
                nation = request.form.get('nation')
                tribe = request.form.get('tribe')
                dob = request.form.get('dob')
                region = request.form.get('region')
                purpose = request.form.get('purpose')
                soul_seal = request.form.get('soul_seal')
                description = request.form.get('description')

            # Validate required fields
            if not name or not nation:
                error_msg = 'Name and ancestral nation are required'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('create_identity.html', error=error_msg)

            # Create metadata
            metadata = {
                'nation': nation,
                'tribe': tribe,
                'dob': dob,
                'region': region,
                'purpose': purpose,
                'soul_seal': soul_seal,
                'description': description,
                'created_at': int(datetime.datetime.now().timestamp())
            }

            # Generate a unique ID for the keys directory
            identity_uuid = str(uuid.uuid4())
            keys_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "keys", identity_uuid)
            os.makedirs(keys_dir, exist_ok=True)

            # Generate keys
            _, public_key = generate_keys(keys_dir)  # We don't need to use the private key here

            # Convert public key to base64 for storage
            public_key_base64 = public_key_to_base64(public_key)

            # Register the identity
            identity = identity_registry.register_identity(name, public_key_base64, nation=nation, metadata=metadata)

            # Store the identity ID in the session
            session['identity_id'] = identity.identity_id

            # Create a blockchain transaction for the identity creation
            try:
                # Prepare transaction data
                tx_data = {
                    "name": name,
                    "nation": nation,
                    "role": metadata.get("purpose", "Citizen"),
                    "identity_id": identity.identity_id
                }

                # Create a signature (in a real system, this would be signed with a private key)
                # For now, we'll use a placeholder signature
                signature = f"system_signature_{int(time.time())}"

                # Record the transaction in the transaction log
                tx_id = txlog.record(
                    op="CREATE_IDENTITY",
                    data=tx_data,
                    sender="SYSTEM",  # System-generated transaction
                    signature=signature
                )

                # Add the transaction to the mempool for mining
                mempool.add(
                    op="CREATE_IDENTITY",
                    data=tx_data,
                    sender="SYSTEM",
                    signature=signature
                )

                logger.info(f"Created blockchain transaction for identity creation: {tx_id}")
            except Exception as e:
                logger.error(f"Error creating blockchain transaction for identity: {str(e)}")
                # We'll continue even if the transaction creation fails
                # The identity is still created in the database

            # Return JSON response if the request is JSON
            if request.is_json:
                return jsonify({
                    'success': True,
                    'identity_id': identity.identity_id,
                    'public_key': public_key_base64,
                    'created_at': format_timestamp(identity.created_at)
                })

            # Return HTML response if the request is form data
            return render_template(
                'create_identity.html',
                success=True,
                identity_id=identity.identity_id,
                public_key=public_key_base64
            )
        except Exception as e:
            error_msg = f'Error creating identity: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')
            return render_template('create_identity.html', error=error_msg)

    return render_template('create_identity.html')

@app.route('/create-token', methods=['GET', 'POST'])
def create_token():
    """Create a new token."""
    if request.method == 'POST':
        try:
            # Check if the request is JSON or form data
            if request.is_json:
                data = request.get_json()
                name = data.get('name')
                symbol = data.get('symbol')
                creator_id = data.get('creator_id')
                token_type = data.get('token_type')
                total_supply = float(data.get('supply', 0))
                decimals = int(data.get('decimals', 2))
                mintable = data.get('mintable', False)
                transferable = data.get('transferable', True)
                description = data.get('description')
            else:
                # Get form data
                name = request.form.get('name')
                symbol = request.form.get('symbol')
                creator_id = request.form.get('creator_id')
                token_type = request.form.get('token_type')
                total_supply = float(request.form.get('supply', 0))
                decimals = int(request.form.get('decimals', 2))
                mintable = request.form.get('mintable') == 'on'
                transferable = request.form.get('transferable') == 'on'
                description = request.form.get('description')

            # Validate required fields
            if not name or not symbol or not creator_id or not token_type:
                error_msg = 'Name, symbol, creator ID, and token type are required'
                if request.is_json:
                    return jsonify({'error': error_msg}), 400
                flash(error_msg, 'error')
                return render_template('create_token.html', error=error_msg)

            # Create metadata
            metadata = {
                'description': description,
                'mintable': mintable,
                'transferable': transferable,
                'created_at': int(datetime.datetime.now().timestamp())
            }

            # Create the token
            token = token_registry.fork_token(
                name=name,
                symbol=symbol,
                creator_id=creator_id,
                token_type=token_type,
                supply=total_supply,
                decimals=decimals,
                mintable=mintable,
                transferable=transferable,
                metadata=metadata
            )

            # Return JSON response if the request is JSON
            if request.is_json:
                return jsonify({
                    'success': True,
                    'token_id': token.token_id,
                    'name': token.name,
                    'symbol': token.symbol,
                    'creator_id': token.creator_id,
                    'supply': token.supply,
                    'created_at': format_timestamp(token.created_at)
                })

            # Return HTML response if the request is form data
            return render_template(
                'create_token.html',
                success=True,
                token_id=token.token_id,
                name=token.name,
                symbol=token.symbol
            )
        except Exception as e:
            error_msg = f'Error creating token: {str(e)}'
            if request.is_json:
                return jsonify({'error': error_msg}), 500
            flash(error_msg, 'error')
            return render_template('create_token.html', error=error_msg)

    return render_template('create_token.html')

@app.route('/explorer')
def explorer():
    """Blockchain explorer home page."""
    try:
        # Get statistics directly from the database
        from data.db import db
        tx_count = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
        mempool_count = db.query_one("SELECT COUNT(*) as count FROM mempool")['count']
        token_count = db.query_one("SELECT COUNT(*) as count FROM tokens")['count']
        identity_count = db.query_one("SELECT COUNT(*) as count FROM identities")['count']

        # Get recent transactions from the database
        recent_transactions = db.query("SELECT * FROM transactions ORDER BY timestamp DESC LIMIT 5")

        # Parse JSON data in transactions
        for tx in recent_transactions:
            if tx['data'] and isinstance(tx['data'], str):
                try:
                    tx['data'] = json.loads(tx['data'])
                except json.JSONDecodeError:
                    pass
    except Exception as e:
        logger.error(f"Error getting data from database: {str(e)}")
        # Fall back to the original method if database query fails
        tx_count = len(txlog.get_all())
        mempool_count = len(mempool.get_all())
        token_count = len(token_registry.list_tokens())
        identity_count = len(identity_registry.get_all_identities())
        recent_transactions = txlog.get_all(limit=5)

    # Get recent tokens
    recent_tokens = token_registry.list_tokens()[:5]

    # Render the template
    return render_template(
        'explorer.html',
        tx_count=tx_count,
        mempool_count=mempool_count,
        token_count=token_count,
        identity_count=identity_count,
        recent_transactions=recent_transactions,
        recent_tokens=recent_tokens
    )

@app.route('/blockchain-viz')
def blockchain_viz():
    """Blockchain visualization page."""
    # Get blockchain data
    blocks_count = len(blockchain.chain)
    last_block_time = datetime.datetime.now().strftime('%I:%M:%S %p')

    # Get identities and convert to dictionaries for JSON serialization
    identity_objects = identity_registry.get_all_identities()
    identities = []
    for identity in identity_objects:
        # Convert Identity objects to dictionaries
        identity_dict = {
            'identity_id': identity.identity_id,
            'name': identity.name,
            'public_key': identity.public_key,
            'nation': identity.nation if hasattr(identity, 'nation') else 'Unknown',
            'reputation': identity.reputation if hasattr(identity, 'reputation') else 0,
            'created_at': identity.created_at if hasattr(identity, 'created_at') else int(datetime.datetime.now().timestamp())
        }
        identities.append(identity_dict)

    # Get blocks (simplified for visualization)
    blocks = []
    if blockchain.chain:
        blocks = [
            {
                'index': block.index,
                'hash': block.hash[:10] if hasattr(block, 'hash') else '',
                'timestamp': block.timestamp if hasattr(block, 'timestamp') else int(datetime.datetime.now().timestamp()),
                'tx_count': len(block.transactions) if hasattr(block, 'transactions') else 0
            }
            for block in blockchain.chain
        ]

    # Render the template
    return render_template(
        'blockchain_viz.html',
        blocks_count=blocks_count,
        last_block_time=last_block_time,
        identities=identities,
        blocks=blocks
    )

@app.route('/search')
def search():
    """Search for tokens, identities, or transactions."""
    query = request.args.get('q', '')
    if not query:
        return render_template('search.html', results=None)

    results = {
        'tokens': [],
        'identities': [],
        'transactions': []
    }

    # Search for tokens
    tokens = token_registry.list_tokens()
    for token in tokens:
        if (
            query.lower() in token.token_id.lower() or
            query.lower() in token.name.lower() or
            query.lower() in token.symbol.lower()
        ):
            results['tokens'].append(token)

    # Search for identities
    identities = identity_registry.get_all_identities()
    for identity in identities:
        if (
            query.lower() in identity.identity_id.lower() or
            query.lower() in identity.name.lower()
        ):
            results['identities'].append(identity)

    # Search for transactions
    transactions = txlog.get_all()
    for tx in transactions:
        if query.lower() in tx['txid'].lower():
            results['transactions'].append(tx)

    # Render the template
    return render_template('search.html', results=results, query=query)

@app.route('/api/blockchain-data')
def api_blockchain_data():
    """API endpoint to get the latest blockchain data for visualization."""
    # Get blockchain data
    blocks_count = len(blockchain.chain)
    last_block_time = datetime.datetime.now().strftime('%I:%M:%S %p')

    # Get identities and convert to dictionaries for JSON serialization
    identity_objects = identity_registry.get_all_identities()
    identities = []
    for identity in identity_objects:
        # Convert Identity objects to dictionaries
        identity_dict = {
            'identity_id': identity.identity_id,
            'name': identity.name,
            'public_key': identity.public_key,
            'nation': identity.nation if hasattr(identity, 'nation') else 'Unknown',
            'reputation': identity.reputation if hasattr(identity, 'reputation') else 0,
            'created_at': identity.created_at if hasattr(identity, 'created_at') else int(datetime.datetime.now().timestamp())
        }
        identities.append(identity_dict)

    # Get blocks (simplified for visualization)
    blocks = []
    if blockchain.chain:
        blocks = [
            {
                'index': block.index,
                'hash': block.hash[:10] if hasattr(block, 'hash') else '',
                'timestamp': block.timestamp if hasattr(block, 'timestamp') else int(datetime.datetime.now().timestamp()),
                'tx_count': len(block.transactions) if hasattr(block, 'transactions') else 0
            }
            for block in blockchain.chain
        ]

    # Return JSON response
    return jsonify({
        'blocks_count': blocks_count,
        'last_block_time': last_block_time,
        'identities': identities,
        'blocks': blocks
    })

@app.route('/api/statistics')
def api_statistics():
    """API endpoint to get the latest statistics for the explorer."""
    # Get statistics directly from database (production data)
    try:
        from data.db import db

        # Get all counts from database
        identity_count = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
        tx_count = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
        mempool_count = db.query_one("SELECT COUNT(*) as count FROM mempool")['count'] if db.table_exists('mempool') else 0
        token_count = db.query_one("SELECT COUNT(*) as count FROM tokens")['count'] if db.table_exists('tokens') else 0

    except Exception as e:
        logger.error(f"Error getting statistics from database: {str(e)}")
        # Fallback to old method only if database fails
        tx_count = len(txlog.get_all())
        mempool_count = len(mempool.get_all())
        token_count = len(token_registry.list_tokens())
        identity_count = len(identity_registry.get_all_identities())

    # Return JSON response
    return jsonify({
        'tx_count': tx_count,
        'mempool_count': mempool_count,
        'token_count': token_count,
        'identity_count': identity_count,
        'timestamp': datetime.datetime.now().strftime('%I:%M:%S %p')
    })

@app.route('/api/transactions')
def api_transactions():
    """Return a list of transactions."""
    try:
        # Get the limit parameter
        limit = request.args.get('limit', 10, type=int)

        # Get the transactions from the database
        from data.db import db
        transactions = db.query("SELECT * FROM transactions ORDER BY timestamp DESC LIMIT ?", (limit,))

        # Convert the transactions to a list of dictionaries
        result = []
        for tx in transactions:
            # Parse the data field if it's a JSON string
            data = tx['data']
            if data and isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError:
                    pass

            # Create a dictionary for the transaction
            tx_dict = {
                'tx_id': tx['tx_id'],
                'timestamp': tx['timestamp'],
                'op': tx['op'],
                'data': data,
                'sender': tx['sender'],
                'status': tx['status'],
                'block_hash': tx['block_hash']
            }

            result.append(tx_dict)

        # Return the transactions as JSON
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error getting transactions: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    import argparse

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Run the Onnyx Web Application")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to bind to")

    args = parser.parse_args()

    print(f"Starting Onnyx Web Application on {args.host}:{args.port}")
    app.run(debug=True, host=args.host, port=args.port)
