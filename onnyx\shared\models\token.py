"""
Onnyx Token Model

This module provides the Token model for the Onnyx blockchain.
"""

import time
import logging
from typing import Dict, Any, List, Optional, ClassVar

from shared.models.base import BaseModel
from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.token")

class Token(BaseModel):
    """
    Token model for the Onnyx blockchain.
    """

    # Table name
    table_name: ClassVar[str] = "tokens"

    # Primary key column
    primary_key: ClassVar[str] = "token_id"

    # JSON fields
    json_fields: ClassVar[List[str]] = ["metadata"]

    def __init__(
        self,
        token_id: str,
        name: str,
        symbol: str,
        creator_id: str,
        total_supply: float,
        created_at: int = None,
        metadata: Dict[str, Any] = None,
        **kwargs
    ):
        """
        Initialize the Token model.

        Args:
            token_id: The token ID
            name: The token name
            symbol: The token symbol
            creator_id: The creator identity ID
            total_supply: The token total supply
            created_at: The creation timestamp
            metadata: The token metadata
            **kwargs: Additional attributes
        """
        self.token_id = token_id
        self.name = name
        self.symbol = symbol
        self.creator_id = creator_id
        self.total_supply = total_supply
        self.created_at = created_at or int(time.time())
        self.metadata = metadata or {}

        super().__init__(**kwargs)

    def __getattr__(self, name):
        """
        Handle attribute access for backward compatibility.

        Args:
            name: The attribute name

        Returns:
            The attribute value

        Raises:
            AttributeError: If the attribute doesn't exist
        """
        if name == 'supply':
            return self.total_supply
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the token to a dictionary.

        Returns:
            A dictionary containing token attributes
        """
        data = super().to_dict()

        # Add supply attribute for backward compatibility
        data['supply'] = self.total_supply

        return data

    def save(self) -> None:
        """
        Save the token.

        Ensures both supply and total_supply are saved.
        """
        # Make sure both supply and total_supply are set
        data = self.to_dict()
        if 'total_supply' in data and 'supply' not in data:
            data['supply'] = data['total_supply']
        elif 'supply' in data and 'total_supply' not in data:
            data['total_supply'] = data['supply']

        # Check if the primary key is set
        if hasattr(self, self.primary_key) and getattr(self, self.primary_key):
            # Update the instance
            primary_key_value = getattr(self, self.primary_key)

            # Remove the primary key from the data
            if self.primary_key in data:
                del data[self.primary_key]

            try:
                # Update the instance
                db.update(
                    self.table_name,
                    data,
                    f"{self.primary_key} = ?",
                    (primary_key_value,)
                )
            except Exception as e:
                logger.error(f"Error updating {self.__class__.__name__}: {str(e)}")
                raise
        else:
            try:
                # Insert a new instance
                row_id = db.insert(self.table_name, data)

                # Set the primary key if it's an auto-increment field
                if self.primary_key == "id":
                    setattr(self, self.primary_key, row_id)
            except Exception as e:
                logger.error(f"Error inserting {self.__class__.__name__}: {str(e)}")
                raise

    @classmethod
    def get_by_symbol(cls, symbol: str) -> Optional['Token']:
        """
        Get a token by symbol.

        Args:
            symbol: The token symbol

        Returns:
            The token or None if not found
        """
        query = f"SELECT * FROM {cls.table_name} WHERE symbol = ?"
        row = db.query_one(query, (symbol,))

        if row:
            return cls.from_dict(row)

        return None

    @classmethod
    def get_by_creator(cls, creator_id: str) -> List['Token']:
        """
        Get tokens by creator.

        Args:
            creator_id: The creator identity ID

        Returns:
            A list of tokens
        """
        return cls.filter("creator_id = ?", (creator_id,))

    @classmethod
    def get_by_category(cls, category: str) -> List['Token']:
        """
        Get tokens by category.

        Args:
            category: The token category

        Returns:
            A list of tokens
        """
        return cls.filter("category = ?", (category,))

    def get_balances(self) -> List[Dict[str, Any]]:
        """
        Get the token balances.

        Returns:
            A list of token balance entries
        """
        query = """
            SELECT tb.*, i.name as identity_name
            FROM token_balances tb
            JOIN identities i ON tb.identity_id = i.identity_id
            WHERE tb.token_id = ?
            ORDER BY tb.balance DESC
        """
        return db.query(query, (self.token_id,))

    def get_transactions(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get the token transactions.

        Args:
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of token transaction entries
        """
        query = """
            SELECT tt.*, i_from.name as from_name, i_to.name as to_name
            FROM token_transactions tt
            LEFT JOIN identities i_from ON tt.from_id = i_from.identity_id
            LEFT JOIN identities i_to ON tt.to_id = i_to.identity_id
            WHERE tt.token_id = ?
            ORDER BY tt.timestamp DESC
            LIMIT ? OFFSET ?
        """
        return db.query(query, (self.token_id, limit, offset))

    def get_balance(self, identity_id: str) -> int:
        """
        Get the token balance for an identity.

        Args:
            identity_id: The identity ID

        Returns:
            The token balance
        """
        query = "SELECT balance FROM token_balances WHERE token_id = ? AND identity_id = ?"
        row = db.query_one(query, (self.token_id, identity_id))

        return row["balance"] if row else 0

    def mint(self, to_identity_id: str, amount: float) -> None:
        """
        Mint tokens to an identity.

        Args:
            to_identity_id: The recipient identity ID
            amount: The amount to mint
        """
        # Update the token supply
        self.total_supply += amount
        self.save()

        # Update the token balance
        self._update_balance(to_identity_id, amount)

        # Record the token transaction
        self._record_transaction(None, to_identity_id, amount, "MINT")

    def transfer(self, from_identity_id: str, to_identity_id: str, amount: float) -> None:
        """
        Transfer tokens from one identity to another.

        Args:
            from_identity_id: The sender identity ID
            to_identity_id: The recipient identity ID
            amount: The amount to transfer

        Raises:
            ValueError: If the sender doesn't have enough tokens
        """
        # Check if the sender has enough tokens
        sender_balance = self.get_balance(from_identity_id)

        if sender_balance < amount:
            raise ValueError(f"Insufficient balance: {sender_balance} < {amount}")

        # Update the token balances
        self._update_balance(from_identity_id, -amount)
        self._update_balance(to_identity_id, amount)

        # Record the token transaction
        self._record_transaction(from_identity_id, to_identity_id, amount, "SEND")

    def burn(self, from_identity_id: str, amount: float) -> None:
        """
        Burn tokens from an identity.

        Args:
            from_identity_id: The sender identity ID
            amount: The amount to burn

        Raises:
            ValueError: If the sender doesn't have enough tokens
        """
        # Check if the sender has enough tokens
        sender_balance = self.get_balance(from_identity_id)

        if sender_balance < amount:
            raise ValueError(f"Insufficient balance: {sender_balance} < {amount}")

        # Update the token supply
        self.total_supply -= amount
        self.save()

        # Update the token balance
        self._update_balance(from_identity_id, -amount)

        # Record the token transaction
        self._record_transaction(from_identity_id, None, amount, "BURN")

    def _update_balance(self, identity_id: str, amount: float) -> None:
        """
        Update a token balance.

        Args:
            identity_id: The identity ID
            amount: The amount to add (can be negative)
        """
        # Get the current balance
        current_balance = self.get_balance(identity_id)

        # Calculate the new balance
        new_balance = current_balance + amount

        # Update the balance
        try:
            if current_balance == 0:
                # Insert a new balance
                db.insert(
                    "token_balances",
                    {
                        "token_id": self.token_id,
                        "identity_id": identity_id,
                        "balance": new_balance,
                        "updated_at": int(time.time())
                    }
                )
            else:
                # Update the existing balance
                db.update(
                    "token_balances",
                    {
                        "balance": new_balance,
                        "updated_at": int(time.time())
                    },
                    "token_id = ? AND identity_id = ?",
                    (self.token_id, identity_id)
                )
        except Exception as e:
            logger.error(f"Error updating token balance: {str(e)}")
            # If the error is a foreign key constraint failure, it means the identity doesn't exist
            # Let's check if the identity exists
            from shared.models.identity import Identity
            identity = Identity.get_by_id(identity_id)
            if not identity:
                logger.error(f"Identity {identity_id} does not exist")
                raise ValueError(f"Identity {identity_id} does not exist")
            # If the identity exists, it means the token doesn't exist
            token = Token.get_by_id(self.token_id)
            if not token:
                logger.error(f"Token {self.token_id} does not exist")
                raise ValueError(f"Token {self.token_id} does not exist")
            # If both exist, re-raise the original error
            raise

    def _record_transaction(
        self,
        from_identity_id: Optional[str],
        to_identity_id: Optional[str],
        amount: float,
        operation: str
    ) -> None:
        """
        Record a token transaction.

        Args:
            from_identity_id: The sender identity ID
            to_identity_id: The recipient identity ID
            amount: The transaction amount
            operation: The transaction operation
        """
        db.insert(
            "token_transactions",
            {
                "tx_id": f"tx_{int(time.time())}_{self.token_id}_{operation}",
                "token_id": self.token_id,
                "from_id": from_identity_id,
                "to_id": to_identity_id,
                "amount": amount,
                "operation": operation,
                "timestamp": int(time.time())
            }
        )

    @classmethod
    def create(
        cls,
        token_id: str,
        name: str,
        symbol: str,
        creator_id: str,
        supply: float = 0,
        metadata: Dict[str, Any] = None
    ) -> 'Token':
        """
        Create a new token.

        Args:
            token_id: The token ID
            name: The token name
            symbol: The token symbol
            creator_id: The creator identity ID
            supply: The token supply
            metadata: The token metadata

        Returns:
            The created token
        """
        # Check if the creator identity exists
        from shared.models.identity import Identity
        creator = Identity.get_by_id(creator_id)
        if not creator:
            logger.error(f"Creator identity {creator_id} does not exist")
            raise ValueError(f"Creator identity {creator_id} does not exist")

        # Create the token
        token = cls(
            token_id=token_id,
            name=name,
            symbol=symbol,
            creator_id=creator_id,
            total_supply=supply,
            metadata=metadata or {}
        )

        try:
            # Save the token
            token.save()
        except Exception as e:
            logger.error(f"Error creating token: {str(e)}")
            raise

        return token
