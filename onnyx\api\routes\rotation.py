"""
Onnyx Validator Rotation Routes

This module provides API routes for validator rotation.
"""

from fastapi import APIRouter, Query, HTTPException, Body
from typing import Dict, Any, List, Optional

from blockchain.consensus.rotation_engine import rotation_engine
from identity.registry import IdentityRegistry
from sela.registry.sela_registry import SelaRegistry

# Create router
router = APIRouter()

# Create instances
identity_registry = IdentityRegistry()
sela_registry = SelaRegistry()

@router.get("/rotation/status")
def get_rotation_status() -> Dict[str, Any]:
    """
    Get the current rotation status.
    
    Returns:
        The rotation status
    """
    return rotation_engine.get_rotation_status()

@router.get("/rotation/next-validator/{height}")
def get_next_validator(height: int) -> Dict[str, Any]:
    """
    Get the next validator for a given block height.
    
    Args:
        height: The block height
    
    Returns:
        The next validator
    """
    try:
        next_validator = rotation_engine.get_next_validator(height)
        
        # Get the Sela details
        sela = sela_registry.get_sela(next_validator)
        
        # Get the founder's identity
        founder_id = sela.get("founder")
        founder = identity_registry.get_identity(founder_id) if founder_id else None
        
        return {
            "height": height,
            "next_validator": next_validator,
            "sela": sela,
            "founder": founder
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/update-queue")
def update_queue() -> Dict[str, Any]:
    """
    Update the queue of eligible validators.
    
    Returns:
        The updated queue
    """
    try:
        queue = rotation_engine.update_queue()
        
        # Get details for each Sela in the queue
        queue_details = []
        for sela_id in queue:
            sela = sela_registry.get_sela(sela_id)
            founder_id = sela.get("founder")
            founder = identity_registry.get_identity(founder_id) if founder_id else None
            
            queue_details.append({
                "sela_id": sela_id,
                "sela": sela,
                "founder": founder
            })
        
        return {
            "queue": queue,
            "queue_details": queue_details
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/set-update-interval")
def set_update_interval(interval: int = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set the queue update interval.
    
    Args:
        interval: The update interval in seconds
    
    Returns:
        The updated rotation status
    """
    try:
        rotation_engine.set_update_interval(interval)
        return rotation_engine.get_rotation_status()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/set-min-etzem-score")
def set_min_etzem_score(score: int = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set the minimum Etzem score required for eligibility.
    
    Args:
        score: The minimum Etzem score
    
    Returns:
        The updated rotation status
    """
    try:
        rotation_engine.set_min_etzem_score(score)
        return rotation_engine.get_rotation_status()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/rotation/set-required-badges")
def set_required_badges(badges: List[str] = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Set the required badges for eligibility.
    
    Args:
        badges: The required badges
    
    Returns:
        The updated rotation status
    """
    try:
        rotation_engine.set_required_badges(badges)
        return rotation_engine.get_rotation_status()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/rotation/is-valid-proposer/{sela_id}/{height}")
def is_valid_proposer(sela_id: str, height: int) -> Dict[str, Any]:
    """
    Check if a Sela is the valid proposer for a given block height.
    
    Args:
        sela_id: The Sela ID to check
        height: The block height
    
    Returns:
        Whether the Sela is the valid proposer
    """
    try:
        is_valid = rotation_engine.is_valid_proposer(sela_id, height)
        next_validator = rotation_engine.get_next_validator(height)
        
        return {
            "sela_id": sela_id,
            "height": height,
            "is_valid_proposer": is_valid,
            "next_validator": next_validator
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
