#!/usr/bin/env python3
"""
ONNYX Platform UX/UI Enhancement Test Suite
Tests the comprehensive design system improvements across all pages.
"""

import requests
import time
from bs4 import BeautifulSoup

def test_enhanced_design_system():
    """Test the enhanced CSS design system components."""
    print("🎨 Testing Enhanced Design System")
    print("=" * 50)

    base_url = "http://127.0.0.1:5000"

    try:
        # Test CSS file with enhanced components
        css_response = requests.get(f"{base_url}/static/css/main.css?v=modern-nav-2024", timeout=10)
        if css_response.status_code == 200:
            css_content = css_response.text

            # Test enhanced design system variables
            design_system_tests = [
                ("Enhanced Color Variables", "--cyber-green:" in css_content),
                ("Typography Scale", "--font-size-xs:" in css_content),
                ("Spacing Scale", "--space-xs:" in css_content),
                ("Border Radius Scale", "--radius-sm:" in css_content),
                ("Shadow System", "--shadow-cyber:" in css_content),
                ("Animation Timing", "--transition-fast:" in css_content),
                ("Modern Button System", ".btn {" in css_content),
                ("Enhanced Card System", ".card {" in css_content),
                ("Form System", ".form-control {" in css_content),
                ("Layout Utilities", ".container {" in css_content),
            ]

            for test_name, test_result in design_system_tests:
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")

            return all(result for _, result in design_system_tests)
        else:
            print(f"  ❌ CSS file: HTTP {css_response.status_code}")
            return False

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_home_page_enhancements():
    """Test home page UX/UI improvements."""
    print("\n🏠 Testing Home Page Enhancements")
    print("=" * 50)

    base_url = "http://127.0.0.1:5000"

    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Test enhanced components
            home_tests = [
                ("Enhanced CTA Buttons", soup.find('a', class_='btn btn-primary btn-lg') is not None),
                ("Modern Statistics Cards", soup.find('div', class_='card text-center') is not None),
                ("Technology Cards with Headers", soup.find('div', class_='card-header') is not None),
                ("Card Titles", soup.find('h3', class_='card-title') is not None),
                ("Card Bodies", soup.find('div', class_='card-body') is not None),
                ("Card Footers", soup.find('div', class_='card-footer') is not None),
                ("Hologram Text Effects", soup.find('span', class_='hologram-text') is not None),
                ("Cyber Glow Effects", soup.find(class_='text-cyber-cyan-glow') is not None),
            ]

            for test_name, test_result in home_tests:
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")

            return all(result for _, result in home_tests)
        else:
            print(f"  ❌ Home page: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_auth_page_enhancements():
    """Test authentication page improvements."""
    print("\n🔐 Testing Authentication Page Enhancements")
    print("=" * 50)

    base_url = "http://127.0.0.1:5000"

    try:
        response = requests.get(f"{base_url}/auth/login", timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Test enhanced auth components
            auth_tests = [
                ("Enhanced Card Structure", soup.find('div', class_='card') is not None),
                ("Card Header", soup.find('div', class_='card-header') is not None),
                ("Card Body", soup.find('div', class_='card-body') is not None),
                ("Card Footer", soup.find('div', class_='card-footer') is not None),
                ("Form Groups", soup.find('div', class_='form-group') is not None),
                ("Form Labels", soup.find('label', class_='form-label') is not None),
                ("Form Controls", soup.find('input', class_='form-control') is not None),
                ("Enhanced Buttons", soup.find('button', class_='btn') is not None),
                ("Container Layout", soup.find('div', class_='container-sm') is not None),
            ]

            for test_name, test_result in auth_tests:
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")

            return all(result for _, result in auth_tests)
        else:
            print(f"  ❌ Login page: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_sela_directory_enhancements():
    """Test Sela validator directory improvements."""
    print("\n🏢 Testing Sela Directory Enhancements")
    print("=" * 50)

    base_url = "http://127.0.0.1:5000"

    try:
        response = requests.get(f"{base_url}/sela/", timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Test enhanced directory components (including empty state)
            sela_tests = [
                ("Container Layout", soup.find('div', class_='container') is not None),
                ("Enhanced Search Card", soup.find('div', class_='card mb-12') is not None),
                ("Form Groups in Search", soup.find('div', class_='form-group') is not None),
                ("Form Controls", soup.find('input', class_='form-control') is not None),
                ("Select Controls", soup.find('select', class_='form-control form-select') is not None),
                ("Card Headers", soup.find('div', class_='card-header') is not None),
                ("Card Bodies", soup.find('div', class_='card-body') is not None),
                ("Card Footers", soup.find('div', class_='card-footer') is not None),
                ("Enhanced Buttons", soup.find('a', class_='btn btn-primary') is not None),
                ("Empty State Message", soup.find('h3', class_='card-title') is not None),
            ]

            for test_name, test_result in sela_tests:
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")

            return all(result for _, result in sela_tests)
        else:
            print(f"  ❌ Sela directory: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_responsive_design():
    """Test responsive design improvements."""
    print("\n📱 Testing Responsive Design")
    print("=" * 50)

    # Test responsive utility classes in CSS
    base_url = "http://127.0.0.1:5000"

    try:
        css_response = requests.get(f"{base_url}/static/css/main.css?v=modern-nav-2024", timeout=10)
        if css_response.status_code == 200:
            css_content = css_response.text

            responsive_tests = [
                ("Grid System", ".grid-cols-1" in css_content and ".grid-cols-2" in css_content),
                ("Container Sizes", ".container-sm" in css_content and ".container-md" in css_content),
                ("Flex Utilities", ".flex" in css_content and ".flex-col" in css_content),
                ("Gap Utilities", ".gap-sm" in css_content and ".gap-md" in css_content),
                ("Margin Utilities", ".mb-sm" in css_content and ".mt-lg" in css_content),
                ("Text Alignment", ".text-center" in css_content and ".text-left" in css_content),
                ("Mobile Navigation", ".mobile-menu-overlay" in css_content),
                ("Button Sizes", ".btn-sm" in css_content and ".btn-lg" in css_content),
            ]

            for test_name, test_result in responsive_tests:
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")

            return all(result for _, result in responsive_tests)
        else:
            print(f"  ❌ CSS file: HTTP {css_response.status_code}")
            return False

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_accessibility_features():
    """Test accessibility improvements."""
    print("\n♿ Testing Accessibility Features")
    print("=" * 50)

    base_url = "http://127.0.0.1:5000"

    try:
        css_response = requests.get(f"{base_url}/static/css/main.css?v=modern-nav-2024", timeout=10)
        if css_response.status_code == 200:
            css_content = css_response.text

            accessibility_tests = [
                ("Minimum Touch Targets", "min-height: 44px" in css_content),
                ("Focus States", ":focus" in css_content),
                ("Required Field Indicators", ".required::after" in css_content),
                ("Form Validation States", ".form-control.error" in css_content),
                ("Transition Timing", "300ms" in css_content),
                ("Color Contrast", "--text-primary: #eeeeee" in css_content),
                ("Disabled States", ":disabled" in css_content),
            ]

            for test_name, test_result in accessibility_tests:
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")

            return all(result for _, result in accessibility_tests)
        else:
            print(f"  ❌ CSS file: HTTP {css_response.status_code}")
            return False

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Run all UX/UI enhancement tests."""
    print("🚀 ONNYX Platform UX/UI Enhancement Test Suite")
    print("=" * 60)

    tests = [
        test_enhanced_design_system,
        test_home_page_enhancements,
        test_auth_page_enhancements,
        test_sela_directory_enhancements,
        test_responsive_design,
        test_accessibility_features,
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append(False)

    print("\n📊 Test Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("\n🎉 All UX/UI enhancements are working correctly!")
        print("✨ The ONNYX platform now features:")
        print("   • Enhanced design system with consistent variables")
        print("   • Modern button and card components")
        print("   • Improved form controls and validation")
        print("   • Responsive layout utilities")
        print("   • Accessibility improvements")
        print("   • Professional enterprise-grade appearance")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")

    return passed == total

if __name__ == "__main__":
    main()
