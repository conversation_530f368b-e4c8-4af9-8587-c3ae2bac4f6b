#!/usr/bin/env python
# run_node.py

import asyncio
import argparse
import logging
import sys
from src.node.config import node_config
from src.node.server import node_server

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("onnyx.run_node")

async def main():
    """
    Main entry point for the node server.
    """
    parser = argparse.ArgumentParser(description="Run an Onnyx node")
    parser.add_argument("--host", type=str, help="Host to bind to")
    parser.add_argument("--port", type=int, help="Port to bind to")
    parser.add_argument("--id", type=str, help="Node ID")
    parser.add_argument("--peer", type=str, action="append", help="Peer URL to connect to")
    parser.add_argument("--config", type=str, help="Path to config file")
    parser.add_argument("--log-level", type=str, choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="Log level")
    
    args = parser.parse_args()
    
    # Load config from file if specified
    if args.config:
        node_config._config_path = args.config
        node_config._load_config()
    
    # Override config with command line arguments
    if args.host:
        node_config.set("node_host", args.host)
    
    if args.port:
        node_config.set("node_port", args.port)
    
    if args.id:
        node_config.set("node_id", args.id)
    
    if args.peer:
        for peer in args.peer:
            node_config.add_peer(peer)
    
    if args.log_level:
        node_config.set("log_level", args.log_level)
        logging.getLogger("onnyx").setLevel(getattr(logging, args.log_level))
    
    logger.info(f"Starting Onnyx node {node_config.get('node_id')} on {node_config.get('node_host')}:{node_config.get('node_port')}")
    logger.info(f"Peers: {node_config.get_peers()}")
    
    try:
        await node_server.start()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, stopping server")
    except Exception as e:
        logger.error(f"Error starting server: {str(e)}")
    finally:
        await node_server.stop()

if __name__ == "__main__":
    asyncio.run(main())
