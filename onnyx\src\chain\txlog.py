# src/chain/txlog.py

import time
import uuid
import logging
from typing import List, Optional

from models.transaction import Transaction

# Set up logging
logger = logging.getLogger("onnyx.chain.txlog")

class TxLogger:
    def __init__(self):
        """
        Initialize a transaction logger.
        """
        self.transactions_cache = []
        self._load()

    def _load(self):
        """
        Load transactions from the database.
        """
        try:
            # Get all transactions from the database
            transactions = Transaction.get_all()

            # Cache the transactions
            self.transactions_cache = transactions

            logger.info(f"Loaded {len(self.transactions_cache)} transactions from the database")
        except Exception as e:
            logger.error(f"Error loading transaction log: {str(e)}")
            self.transactions_cache = []

    def record(self, op: str, data: dict, sender: str, signature: str) -> str:
        """
        Record a transaction in the log.

        Args:
            op: The operation type (e.g., "OP_MINT", "OP_SEND", "OP_BURN")
            data: The transaction data
            sender: The sender identity ID
            signature: The transaction signature

        Returns:
            The transaction ID
        """
        # Generate a unique transaction ID
        tx_id = f"tx_{uuid.uuid4().hex[:16]}_{int(time.time())}"

        # Create the transaction in the database
        tx = Transaction.create(
            tx_id=tx_id,
            timestamp=int(time.time()),
            op=op,
            data=data,
            sender=sender,
            signature=signature
        )

        # Add the transaction to the cache
        self.transactions_cache.append(tx)

        logger.info(f"Recorded transaction {tx_id}")
        return tx_id

    def get_all(self, limit: int = 100, offset: int = 0) -> List[Transaction]:
        """
        Get all transactions.

        Args:
            limit: Maximum number of transactions to return
            offset: Number of transactions to skip

        Returns:
            A list of transactions
        """
        # Refresh the cache
        self._load()

        # Sort transactions by timestamp (newest first)
        sorted_txs = sorted(self.transactions_cache, key=lambda x: x.timestamp, reverse=True)

        # Apply pagination
        return sorted_txs[offset:offset+limit]

    def get_by_identity(self, identity_id: str, limit: int = 100, offset: int = 0) -> List[Transaction]:
        """
        Get transactions for a specific identity.

        Args:
            identity_id: The identity ID
            limit: Maximum number of transactions to return
            offset: Number of transactions to skip

        Returns:
            A list of transactions
        """
        # Get the identity's transactions from the database
        query = "SELECT * FROM transactions WHERE sender = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        rows = Transaction.query(query, (identity_id, limit, offset))

        # Convert to Transaction objects
        return [Transaction.from_dict(row) for row in rows]

    def get_by_token(self, token_id: str, limit: int = 100, offset: int = 0) -> List[Transaction]:
        """
        Get transactions for a specific token.

        Args:
            token_id: The token ID
            limit: Maximum number of transactions to return
            offset: Number of transactions to skip

        Returns:
            A list of transactions
        """
        # Get the token's transactions from the database
        query = "SELECT * FROM transactions WHERE json_extract(data, '$.token_id') = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        rows = Transaction.query(query, (token_id, limit, offset))

        # Convert to Transaction objects
        return [Transaction.from_dict(row) for row in rows]

    def get_by_txid(self, tx_id: str) -> Optional[Transaction]:
        """
        Get a transaction by its ID.

        Args:
            tx_id: The transaction ID

        Returns:
            The transaction, or None if not found
        """
        # Try to get from the cache first
        for tx in self.transactions_cache:
            if tx.tx_id == tx_id:
                return tx

        # If not in cache, try to get from the database
        return Transaction.get_by_id(tx_id)
