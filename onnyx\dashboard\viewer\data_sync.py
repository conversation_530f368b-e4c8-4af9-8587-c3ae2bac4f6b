# onnyx_viewer/data_sync.py

import os
import json
import time
import logging
import threading
import requests
from typing import Dict, Any, List, Optional

# Configure logging
logger = logging.getLogger("onnyx.viewer.data_sync")

class DataSync:
    """
    Handles synchronization of blockchain data from the Onnyx node.
    """
    
    def __init__(self, data_dir: str = "data", api_url: str = "http://localhost:8000", sync_interval: int = 30):
        """
        Initialize the data sync module.
        
        Args:
            data_dir: Directory to store the data files.
            api_url: URL of the Onnyx API.
            sync_interval: Interval in seconds between sync operations.
        """
        self.data_dir = data_dir
        self.api_url = api_url
        self.sync_interval = sync_interval
        self.last_sync = 0
        self.sync_thread = None
        self.running = False
        
        # Create the data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Initialize empty data files if they don't exist
        self._initialize_data_files()
    
    def _initialize_data_files(self):
        """Initialize empty data files if they don't exist."""
        for filename in ["blockchain.json", "mempool.json", "identities.json", "tokens.json"]:
            filepath = os.path.join(self.data_dir, filename)
            if not os.path.exists(filepath):
                with open(filepath, "w") as f:
                    if filename == "blockchain.json":
                        json.dump([], f)
                    else:
                        json.dump([], f)
                logger.info(f"Created empty {filename}")
    
    def start(self):
        """Start the data sync thread."""
        if self.running:
            logger.warning("Data sync already running")
            return
        
        self.running = True
        self.sync_thread = threading.Thread(target=self._sync_loop)
        self.sync_thread.daemon = True
        self.sync_thread.start()
        logger.info("Data sync started")
    
    def stop(self):
        """Stop the data sync thread."""
        if not self.running:
            logger.warning("Data sync not running")
            return
        
        self.running = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        logger.info("Data sync stopped")
    
    def _sync_loop(self):
        """Main sync loop that runs in a separate thread."""
        while self.running:
            try:
                self.sync()
            except Exception as e:
                logger.error(f"Error in sync loop: {str(e)}")
            
            # Sleep until the next sync interval
            time.sleep(self.sync_interval)
    
    def sync(self):
        """Synchronize data from the Onnyx node."""
        logger.info("Syncing data from Onnyx node")
        
        # Sync blockchain
        self._sync_blockchain()
        
        # Sync mempool
        self._sync_mempool()
        
        # Sync identities
        self._sync_identities()
        
        # Sync tokens
        self._sync_tokens()
        
        # Update last sync time
        self.last_sync = time.time()
        logger.info("Data sync completed")
    
    def _sync_blockchain(self):
        """Sync blockchain data from the Onnyx node."""
        try:
            # Try to get blockchain data from the API
            response = requests.get(f"{self.api_url}/chain")
            if response.status_code == 200:
                blockchain = response.json()
                
                # Save the blockchain data
                with open(os.path.join(self.data_dir, "blockchain.json"), "w") as f:
                    json.dump(blockchain, f, indent=2)
                
                logger.info(f"Synced blockchain with {len(blockchain)} blocks")
            else:
                logger.warning(f"Failed to sync blockchain: {response.status_code}")
        except requests.RequestException as e:
            logger.error(f"Error syncing blockchain: {str(e)}")
            
            # Fallback to direct file access if API is not available
            self._sync_file("blockchain.json")
    
    def _sync_mempool(self):
        """Sync mempool data from the Onnyx node."""
        try:
            # Try to get mempool data from the API
            response = requests.get(f"{self.api_url}/mempool")
            if response.status_code == 200:
                mempool = response.json()
                
                # Save the mempool data
                with open(os.path.join(self.data_dir, "mempool.json"), "w") as f:
                    json.dump(mempool, f, indent=2)
                
                logger.info(f"Synced mempool with {len(mempool)} transactions")
            else:
                logger.warning(f"Failed to sync mempool: {response.status_code}")
        except requests.RequestException as e:
            logger.error(f"Error syncing mempool: {str(e)}")
            
            # Fallback to direct file access if API is not available
            self._sync_file("mempool.json")
    
    def _sync_identities(self):
        """Sync identity data from the Onnyx node."""
        try:
            # Try to get identity data from the API
            response = requests.get(f"{self.api_url}/identity/list")
            if response.status_code == 200:
                identities = response.json()
                
                # Save the identity data
                with open(os.path.join(self.data_dir, "identities.json"), "w") as f:
                    json.dump(identities, f, indent=2)
                
                logger.info(f"Synced identities with {len(identities)} identities")
            else:
                logger.warning(f"Failed to sync identities: {response.status_code}")
        except requests.RequestException as e:
            logger.error(f"Error syncing identities: {str(e)}")
            
            # Fallback to direct file access if API is not available
            self._sync_file("identities.json")
    
    def _sync_tokens(self):
        """Sync token data from the Onnyx node."""
        try:
            # Try to get token data from the API
            response = requests.get(f"{self.api_url}/token/list")
            if response.status_code == 200:
                tokens = response.json()
                
                # Save the token data
                with open(os.path.join(self.data_dir, "tokens.json"), "w") as f:
                    json.dump(tokens, f, indent=2)
                
                logger.info(f"Synced tokens with {len(tokens)} tokens")
            else:
                logger.warning(f"Failed to sync tokens: {response.status_code}")
        except requests.RequestException as e:
            logger.error(f"Error syncing tokens: {str(e)}")
            
            # Fallback to direct file access if API is not available
            self._sync_file("tokens.json")
    
    def _sync_file(self, filename: str):
        """
        Sync a file directly from the Onnyx data directory.
        
        Args:
            filename: Name of the file to sync.
        """
        try:
            # Try to copy the file from the Onnyx data directory
            source_path = os.path.join("..", "data", filename)
            target_path = os.path.join(self.data_dir, filename)
            
            if os.path.exists(source_path):
                # Read the source file
                with open(source_path, "r") as f:
                    data = json.load(f)
                
                # Write to the target file
                with open(target_path, "w") as f:
                    json.dump(data, f, indent=2)
                
                logger.info(f"Synced {filename} from file")
            else:
                logger.warning(f"Source file {source_path} does not exist")
        except Exception as e:
            logger.error(f"Error syncing file {filename}: {str(e)}")
    
    def get_last_sync_time(self) -> float:
        """
        Get the timestamp of the last successful sync.
        
        Returns:
            Timestamp of the last successful sync.
        """
        return self.last_sync
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        Get the status of the data sync.
        
        Returns:
            Dictionary with sync status information.
        """
        return {
            "running": self.running,
            "last_sync": self.last_sync,
            "sync_interval": self.sync_interval,
            "api_url": self.api_url
        }


# Create a global instance of the data sync module
data_sync = DataSync()
