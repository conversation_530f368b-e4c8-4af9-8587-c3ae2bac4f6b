"""
Explorer Routes

Blockchain explorer and governance interface.
"""

import os
import sys
import json
import logging
from flask import Blueprint, render_template, request, jsonify

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.db.db import db

logger = logging.getLogger("onnyx.web.explorer")

explorer_bp = Blueprint('explorer', __name__)

@explorer_bp.route('/')
def index():
    """Explorer home page."""
    try:
        # Get recent blocks
        recent_blocks = []
        if db.table_exists('blocks'):
            recent_blocks = db.query("""
                SELECT * FROM blocks
                ORDER BY block_height DESC
                LIMIT 10
            """)

        # Get recent transactions
        recent_transactions = db.query("""
            SELECT * FROM transactions
            ORDER BY created_at DESC
            LIMIT 10
        """)

        # Parse transaction data
        for tx in recent_transactions:
            try:
                tx['data_parsed'] = json.loads(tx['data'])
            except:
                tx['data_parsed'] = {}

        # Get network statistics
        stats = {
            'total_identities': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
            'total_selas': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
            'total_transactions': db.query_one("SELECT COUNT(*) as count FROM transactions")['count'],
            'total_blocks': db.query_one("SELECT COUNT(*) as count FROM blocks")['count'] if db.table_exists('blocks') else 0,
            'active_selas': db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count']
        }

        # Get latest block height for display
        latest_block = stats['total_blocks']

        # Calculate average block time if we have blocks
        avg_block_time = "~10s"
        if len(recent_blocks) >= 2:
            time_diff = recent_blocks[0]['timestamp'] - recent_blocks[1]['timestamp']
            avg_block_time = f"~{int(time_diff)}s"

        return render_template('explorer/index.html',
                             recent_blocks=recent_blocks,
                             recent_transactions=recent_transactions,
                             stats=stats,
                             latest_block=latest_block,
                             total_transactions=stats['total_transactions'],
                             network_hashrate="Auto",
                             avg_block_time=avg_block_time,
                             network_difficulty="Auto",
                             active_validators=stats['active_selas'],
                             total_supply="N/A")

    except Exception as e:
        logger.error(f"Error loading explorer: {e}")
        return render_template('explorer/index.html',
                             recent_blocks=[],
                             recent_transactions=[],
                             stats={})

@explorer_bp.route('/blocks')
def blocks():
    """Blocks explorer page."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        offset = (page - 1) * per_page

        # Get blocks with pagination
        blocks_list = []
        total_count = 0

        if db.table_exists('blocks'):
            blocks_list = db.query("""
                SELECT * FROM blocks
                ORDER BY block_height DESC
                LIMIT ? OFFSET ?
            """, (per_page, offset))

            total_count = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']

        # Parse transaction data for each block
        for block in blocks_list:
            try:
                block['transactions_parsed'] = json.loads(block['transactions'])
            except:
                block['transactions_parsed'] = []

        # Calculate pagination
        total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1

        return render_template('explorer/blocks.html',
                             blocks=blocks_list,
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count)

    except Exception as e:
        logger.error(f"Error loading blocks: {e}")
        return render_template('explorer/blocks.html', blocks=[], page=1, total_pages=1, total_count=0)

@explorer_bp.route('/block/<block_hash>')
def block_detail(block_hash):
    """Block detail page."""
    try:
        # Get block details
        block = None
        if db.table_exists('blocks'):
            block = db.query_one("SELECT * FROM blocks WHERE block_hash = ?", (block_hash,))

        if not block:
            return render_template('errors/404.html'), 404

        # Parse transactions
        try:
            transaction_ids = json.loads(block['transactions'])
        except:
            transaction_ids = []

        # Get transaction details
        block_transactions = []
        if transaction_ids:
            placeholders = ','.join(['?' for _ in transaction_ids])
            block_transactions = db.query(f"""
                SELECT * FROM transactions
                WHERE tx_id IN ({placeholders})
                ORDER BY timestamp ASC
            """, transaction_ids)

            # Parse transaction data
            for tx in block_transactions:
                try:
                    tx['data_parsed'] = json.loads(tx['data'])
                except:
                    tx['data_parsed'] = {}

        return render_template('explorer/block_detail.html',
                             block=block,
                             transactions=block_transactions)

    except Exception as e:
        logger.error(f"Error loading block {block_hash}: {e}")
        return render_template('errors/500.html'), 500

@explorer_bp.route('/transactions')
def transactions():
    """Transactions explorer page."""
    try:
        page = request.args.get('page', 1, type=int)
        op_filter = request.args.get('op', '')
        per_page = 20
        offset = (page - 1) * per_page

        # Build query
        where_conditions = []
        params = []

        if op_filter:
            where_conditions.append("op = ?")
            params.append(op_filter)

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # Get transactions with pagination
        transactions_list = db.query(f"""
            SELECT * FROM transactions
            {where_clause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        # Parse transaction data
        for tx in transactions_list:
            try:
                tx['data_parsed'] = json.loads(tx['data'])
            except:
                tx['data_parsed'] = {}

        # Get total count
        total_count = db.query_one(f"""
            SELECT COUNT(*) as count FROM transactions
            {where_clause}
        """, params)['count']

        # Get available operation types
        op_types = db.query("SELECT DISTINCT op FROM transactions ORDER BY op")

        # Calculate pagination
        total_pages = (total_count + per_page - 1) // per_page

        return render_template('explorer/transactions.html',
                             transactions=transactions_list,
                             op_types=[op['op'] for op in op_types],
                             current_op=op_filter,
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count)

    except Exception as e:
        logger.error(f"Error loading transactions: {e}")
        return render_template('explorer/transactions.html',
                             transactions=[],
                             op_types=[],
                             page=1,
                             total_pages=1,
                             total_count=0)

@explorer_bp.route('/transaction/<tx_id>')
def transaction_detail(tx_id):
    """Transaction detail page."""
    try:
        # Get transaction details
        transaction = db.query_one("SELECT * FROM transactions WHERE tx_id = ?", (tx_id,))

        if not transaction:
            return render_template('errors/404.html'), 404

        # Parse transaction data
        try:
            transaction['data_parsed'] = json.loads(transaction['data'])
        except:
            transaction['data_parsed'] = {}

        # Get sender identity if available
        sender_identity = None
        if transaction['sender'] != 'SYSTEM':
            sender_identity = db.query_one("""
                SELECT identity_id, name, email FROM identities
                WHERE identity_id = ?
            """, (transaction['sender'],))

        # Get block information if transaction is confirmed
        block_info = None
        if transaction['block_hash'] and db.table_exists('blocks'):
            block_info = db.query_one("""
                SELECT block_hash, block_height, timestamp, miner
                FROM blocks
                WHERE block_hash = ?
            """, (transaction['block_hash'],))

        return render_template('explorer/transaction_detail.html',
                             transaction=transaction,
                             sender_identity=sender_identity,
                             block_info=block_info)

    except Exception as e:
        logger.error(f"Error loading transaction {tx_id}: {e}")
        return render_template('errors/500.html'), 500

@explorer_bp.route('/search')
def search():
    """Search functionality."""
    try:
        query = request.args.get('q', '').strip()

        if not query:
            return render_template('explorer/search.html', results=None, query='')

        results = {
            'blocks': [],
            'transactions': [],
            'identities': [],
            'selas': []
        }

        # Search blocks by hash or height
        if db.table_exists('blocks'):
            if query.isdigit():
                # Search by block height
                block = db.query_one("SELECT * FROM blocks WHERE block_height = ?", (int(query),))
                if block:
                    results['blocks'].append(block)
            else:
                # Search by block hash
                block = db.query_one("SELECT * FROM blocks WHERE block_hash LIKE ?", (f"%{query}%",))
                if block:
                    results['blocks'].append(block)

        # Search transactions by ID
        transaction = db.query_one("SELECT * FROM transactions WHERE tx_id LIKE ?", (f"%{query}%",))
        if transaction:
            results['transactions'].append(transaction)

        # Search identities by name or ID
        identities = db.query("""
            SELECT identity_id, name, email FROM identities
            WHERE name LIKE ? OR identity_id LIKE ? OR email LIKE ?
            LIMIT 10
        """, (f"%{query}%", f"%{query}%", f"%{query}%"))
        results['identities'] = identities

        # Search Selas by name or ID
        selas = db.query("""
            SELECT s.*, i.name as owner_name FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.name LIKE ? OR s.sela_id LIKE ? OR s.category LIKE ?
            LIMIT 10
        """, (f"%{query}%", f"%{query}%", f"%{query}%"))
        results['selas'] = selas

        return render_template('explorer/search.html', results=results, query=query)

    except Exception as e:
        logger.error(f"Error in search: {e}")
        return render_template('explorer/search.html', results=None, query=query, error="Search failed")
