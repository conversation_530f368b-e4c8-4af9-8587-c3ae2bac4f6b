# ONNYX: Identity-Centric Blockchain

ONNYX is a new blockchain protocol that puts identity at the center of the token ecosystem where identity, ownership, and contribution are encoded on-chain and protected by mathematics, not governments.

## 🔑 Key Features

- **Identity-Centric**: Every token is linked to an identity
- **Reputation System**: Build reputation through on-chain activity
- **Token Forking**: Create tokens based on existing ones
- **Soulbound Badges**: Non-transferable tokens that represent achievements
- **OnnyxScript**: Stack-based scripting language for token operations
- **Sela Business Registry**: Register and manage business entities on the blockchain
- **Zeman Time/Work Credits**: Convert labor, service, and creativity into on-chain value
- **Yovel Token Mint Limiter**: Cap token minting based on trust, reputation, and Zeman
- **Etzem Trust Score System**: Composite trust score for on-chain integrity
- **Council of Twelve Tribes**: Governance system powered by identity, trust, and reputation
- **Mikvah Token Engine**: Contribution-gated system for launching new tokens
- **Judah Engine**: Economic logic layer for fees, rebates, and staking

## 🏗️ Project Structure

```
onnyx/
├── src/
│   ├── chain/        # Blockchain core (blocks, chain, txlog, mempool)
│   ├── identity/     # Identity management
│   ├── tokens/       # Token management
│   ├── vm/           # Virtual machine for OnnyxScript
│   ├── node/         # Node implementation and P2P networking
│   ├── wallet/       # Wallet implementation
│   ├── business/     # Business registry (Sela)
│   ├── zeman/        # Time/Work credit system
│   ├── yovel/        # Token mint limiter
│   ├── etzem/        # Trust score system
│   ├── governance/   # Voice Scrolls governance proposals
│   ├── council/      # Council of Twelve Tribes authority
│   ├── mikvah/       # Mikvah Token Engine
│   ├── judah/        # Economic logic layer
│   ├── routes/       # API routes
│   └── web/          # Web interface
├── tests/            # Unit tests
├── data/             # Data storage
├── onnyx-cli.py      # Command-line interface
├── onnyx-api.py      # API server
├── onnyx-explorer.py # Web interface
├── run_node.py       # Run a node with P2P networking
├── mine_block.py     # Mine a new block
└── generate_keys.py  # Generate ECDSA keys for a node
```

## 🚀 Getting Started

### Running the API Server

```bash
# Install dependencies
pip install fastapi uvicorn pydantic ecdsa flask

# Run the API server
python onnyx-api.py
```

The API server will be available at http://127.0.0.1:8000. You can access the API documentation at http://127.0.0.1:8000/docs.

### Running the Web Interface

```bash
# Run the web interface
python onnyx-explorer.py
```

The web interface will be available at http://127.0.0.1:8080. You can use it to browse transactions, tokens, identities, and the mempool.

### Running the CLI

```bash
# Run the CLI in interactive mode
python onnyx-cli.py

# Run a specific command
python onnyx-cli.py identity  # Create a new identity
python onnyx-cli.py spawn     # Spawn a new token
python onnyx-cli.py mine      # Mine a new block
```

### Running the Node Server

```bash
# Run a node with default settings
python run_node.py

# Run a node with custom settings
python run_node.py --host localhost --port 8081 --id onnyx_node_1 --peer ws://localhost:8082 --peer ws://localhost:8083

# Run a node with a custom config file
python run_node.py --config data/node_config.json

# Run a node with debug logging
python run_node.py --log-level DEBUG
```

The node server will start a WebSocket server that listens for connections from other nodes. It will also connect to any peers specified in the configuration or command line arguments. The node server will synchronize the blockchain and mempool with its peers, ensuring that all nodes have the same state.

### Mining Blocks

```bash
# Mine a block with default settings
python mine_block.py

# Mine a block with a specific number of dummy transactions
python mine_block.py --transactions 10
```

The mining script will create a new block with transactions from the mempool, perform a simple proof of work, and broadcast the block to all connected peers. The peers will validate the block and add it to their blockchain if it's valid.

### Generating Keys

```bash
# Generate keys with default settings
python generate_keys.py

# Generate keys in a specific directory
python generate_keys.py --path data/node1/keys

# Overwrite existing keys
python generate_keys.py --force
```

The key generation script will create a new ECDSA key pair for the node. The keys are used to sign outgoing messages and verify incoming messages. The public key is shared with peers during the initial hello message.

## 📡 API Endpoints

### Token Endpoints

- `POST /token/forktoken` - Create a new token linked to an identity
- `POST /token/minttoken` - Mint additional tokens
- `POST /token/sendtoken` - Transfer tokens between addresses
- `POST /token/burntoken` - Burn tokens
- `GET /token/gettokenbalance` - Get the balance of a token for an address
- `GET /token/tokenregistry` - Get a list of tokens, optionally filtered by creator or type
- `GET /token/transactions` - Get a list of transactions, optionally filtered by token ID, identity ID, or transaction ID

### Identity Endpoints

- `POST /identity/createidentity` - Create a new identity
- `GET /identity/getidentity` - Get information about an identity
- `POST /identity/updateidentity` - Update an identity's metadata
- `POST /identity/grantreputation` - Grant reputation to an identity
- `GET /identity/identityscore` - Get the composite score and detailed reputation metrics for an identity
- `GET /identity/transactions` - Get transactions for a specific identity

### Nation Endpoints

- `POST /nation/create` - Create a new nation
- `GET /nation/get/<nation_id>` - Get information about a nation
- `GET /nation/list` - Get a list of nations
- `PUT /nation/update/<nation_id>` - Update a nation's information
- `GET /nation/members/<nation_id>` - Get the members of a nation
- `POST /nation/join/<nation_id>` - Join a nation
- `POST /nation/leave` - Leave a nation

## 📊 Economic Components

### Zeman Time/Work Credits

Zeman is a system for tracking hours of service and converting them into minting power, fee discounts, reputation boosts, and redeemable value. It allows contributors to earn credits for their time and work, which can be used to mint tokens, reduce transaction fees, and increase their reputation.

### Sela Business Registry

Sela is a business registry system that requires ONX token stakes with different tiers based on identity reputation scores. It allows businesses to register on the blockchain and gain access to special features, such as minting tokens and participating in governance.

### Etzem Trust Score System

Etzem is a trust score system that calculates on-chain integrity by combining Zeman hours, reputation badges, Sela registration, token activity, governance participation, and account longevity. It provides a comprehensive measure of an identity's trustworthiness and reputation.

### Yovel Token Mint Limiter

Yovel is a token mint limiter system that caps how many tokens an identity can mint based on Zeman (time/labor), Etzem (reputation score), and trust level (badges, history, stake). It ensures that only trusted identities can mint tokens and prevents abuse of the token minting system.

### Mikvah Token Engine

Mikvah is a token engine that requires identities to have registered Selas (businesses) and meet Yovel minting limits before they can mint new tokens. It allows Selas to mint various token types, such as loyalty tokens, equity tokens, and community tokens.

### Judah Engine

Judah is an economic logic layer that manages transaction fees with Zeman/Etzem discounts, creator rebates, ONX token flow control, staking pool support, and economic governance parameters. It ensures that the economic incentives of the blockchain are aligned with the goals of the community.

## 🏛️ Governance

### Voice Scrolls

Voice Scrolls are governance proposals that allow the community to vote on changes to the blockchain. They include automatic resolution with tallying logic, expiry windows, outcome tagging, and quorum requirements. Voice Scrolls can be used to change economic parameters, add new features, and make other governance decisions.

### Council of Twelve Tribes

The Council of Twelve Tribes is a governance body that assigns seats to specific tribes with councilors having soulbound roles and special voting powers in governance. It provides a balanced representation of the community and ensures that governance decisions are made with the input of all stakeholders.

### Staking

Onnyx includes ONX staking functionality that allows users to lock tokens for different durations to gain roles like VALIDATOR, CREATOR, and MENTOR, with configurable lock periods and Council-adjustable staking thresholds. Staking roles are soulbound to identities, automatically assigned when unlocked, and revoked if stake is withdrawn.

## 🔄 P2P Network

### Validator Rotation Engine

The Validator Rotation Engine fairly selects which Sela miner can propose the next block, using round-robin scheduling among eligible Selas with sufficient Etzem score or VALIDATOR badge. It ensures that block production is distributed fairly among validators and prevents centralization of mining power.

### Block Sync and Validation

The P2P network includes Block Sync and Validation with features like Block Broadcast, Sync-on-Connect, Validation Logic, and Local Blockchain Store for consensus and trust. It ensures that all nodes have the same view of the blockchain and can validate new blocks.

### Digital Signature Validation

The P2P network requires digital signature validation where each node has its own public/private key pair (ECDSA), all messages are signed and verified, and nodes share their public keys during hello or registration messages. It ensures that all messages are authentic and prevents tampering.

## 📱 Transaction Viewer

The Onnyx Transaction Viewer is a web application that provides a user-friendly interface for exploring the blockchain. It includes:

- Live data sync from the core node
- Detailed views for transactions, tokens, and identities
- Pagination with filtering
- Chain statistics dashboard
- Node status with peer information
- Admin tools for blockchain interaction

## 📚 Documentation

For more detailed documentation on specific components, please refer to the `docs/` directory. Each component has its own documentation file with detailed information on its design, implementation, and usage.

## 🧪 Testing

The `tests/` directory contains unit tests for all components of the Onnyx blockchain. To run the tests, use the following command:

```bash
# Run all tests
python -m unittest discover tests

# Run a specific test
python -m unittest tests.test_api
```

## 🤝 Contributing

Contributions to Onnyx are welcome! Please feel free to submit a pull request or open an issue if you have any questions or suggestions.

## 📄 License

Onnyx is licensed under the MIT License. See the LICENSE file for more information.
