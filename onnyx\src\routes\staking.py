# src/routes/staking.py

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List
from src.judah.staking import ONXStaking
from src.tokens.ledger import TokenLedger
from src.identity.registry import IdentityRegistry
from src.chain.txlog import TxLogger
from src.chain.mempool import Mempool
from src.council.registry import CouncilRegistry

# Initialize components
staking_router = APIRouter(prefix="/staking", tags=["staking"])
token_ledger = TokenLedger()
identity_registry = IdentityRegistry()
txlog = TxLogger()
mempool = Mempool()
council_registry = CouncilRegistry()

# Initialize the ONX Staking system
staking_engine = ONXStaking(
    token_ledger=token_ledger,
    txlog=txlog
)

# Request models
class StakeRequest(BaseModel):
    identity_id: str
    amount: float
    duration_days: int
    role: str
    message: str
    signature: str

class UnlockRequest(BaseModel):
    identity_id: str
    stake_id: Optional[str] = None
    message: str
    signature: str

class ConfigUpdateRequest(BaseModel):
    updates: Dict[str, Any]
    identity_id: str
    message: str
    signature: str

# Routes
@staking_router.post("/stake")
def stake_onx(req: StakeRequest):
    """
    Stake ONX tokens for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "amount": req.amount,
        "duration_days": req.duration_days,
        "role": req.role
    }
    mempool_tx = mempool.add("stake_onx", payload)

    try:
        # Stake ONX
        stake = staking_engine.stake(
            identity_id=req.identity_id,
            amount=req.amount,
            duration_days=req.duration_days,
            role=req.role,
            identity_registry=identity_registry
        )

        # Remove from mempool
        mempool.remove(mempool_tx["id"])

        return {
            "status": "success",
            "stake": stake
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@staking_router.post("/unlock")
def unlock_stake(req: UnlockRequest):
    """
    Unlock stakes for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "stake_id": req.stake_id
    }
    mempool_tx = mempool.add("unlock_stake", payload)

    try:
        # Unlock stake
        result = staking_engine.unlock(
            identity_id=req.identity_id,
            stake_id=req.stake_id,
            identity_registry=identity_registry
        )

        # Remove from mempool
        mempool.remove(mempool_tx["id"])

        return {
            "status": "success",
            "result": result
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@staking_router.get("/stakes/{identity_id}")
def get_stakes(identity_id: str):
    """
    Get all stakes for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get stakes
    stakes = staking_engine.get_stakes(identity_id)

    return {
        "identity_id": identity_id,
        "stakes": stakes
    }

@staking_router.get("/stake/{stake_id}")
def get_stake(stake_id: str):
    """
    Get a stake by ID.
    """
    # Get stake
    stake = staking_engine.get_stake(stake_id)
    if not stake:
        raise HTTPException(status_code=404, detail="Stake not found")

    return stake

@staking_router.get("/unlockable/{identity_id}")
def get_unlockable_stakes(identity_id: str):
    """
    Get all unlockable stakes for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get unlockable stakes
    unlockable_stakes = staking_engine.get_unlockable_stakes(identity_id)

    return {
        "identity_id": identity_id,
        "unlockable_stakes": unlockable_stakes
    }

@staking_router.get("/roles/{identity_id}")
def get_active_roles(identity_id: str):
    """
    Get all active roles for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get active roles
    roles = staking_engine.get_active_roles(identity_id)

    return {
        "identity_id": identity_id,
        "roles": roles
    }

@staking_router.get("/soulbound-roles/{identity_id}")
def get_soulbound_roles(identity_id: str):
    """
    Get all soulbound roles for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get soulbound roles from identity reputation
    soulbound_roles = []
    for key, value in identity.get("reputation", {}).items():
        if key.startswith("badge:") and value == 1:
            soulbound_roles.append(key.replace("badge:", ""))

    return {
        "identity_id": identity_id,
        "soulbound_roles": soulbound_roles
    }

@staking_router.get("/benefits/{identity_id}")
def get_active_benefits(identity_id: str):
    """
    Get all active benefits for an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get active benefits
    benefits = staking_engine.get_active_benefits(identity_id)

    return {
        "identity_id": identity_id,
        "benefits": benefits
    }

@staking_router.get("/total/{identity_id}")
def get_total_staked(identity_id: str):
    """
    Get the total amount staked by an identity.
    """
    # Verify identity
    identity = identity_registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get total staked
    total_staked = staking_engine.get_total_staked(identity_id)

    return {
        "identity_id": identity_id,
        "total_staked": total_staked
    }

@staking_router.get("/config")
def get_config():
    """
    Get the ONX Staking configuration.
    """
    return {
        "config": staking_engine.get_config()
    }

@staking_router.post("/config/update")
def update_config(req: ConfigUpdateRequest):
    """
    Update the ONX Staking configuration.
    """
    # Verify identity
    identity = identity_registry.get_identity(req.identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Check if the identity is a councilor
    is_councilor = council_registry.is_councilor(req.identity_id)
    if not is_councilor:
        raise HTTPException(status_code=403, detail="Only councilors can update the ONX Staking configuration")

    # Add to mempool
    payload = {
        "identity_id": req.identity_id,
        "updates": req.updates
    }
    mempool_tx = mempool.add("update_staking_config", payload)

    try:
        # Update the configuration
        updated_config = staking_engine.update_config(
            updates=req.updates,
            updated_by=req.identity_id
        )

        # Remove from mempool
        mempool.remove(mempool_tx["id"])

        return {
            "status": "success",
            "config": updated_config
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
