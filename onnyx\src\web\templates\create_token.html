{% extends 'base.html' %}

{% block title %}Create Token - Onnyx Blockchain{% endblock %}

{% block head %}
<style>
    .token-form {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: var(--card-bg);
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        background-color: var(--darker-bg);
        border: 1px solid var(--border-color);
        color: var(--light-text);
        padding: 10px;
        border-radius: 4px;
        width: 100%;
    }

    .form-check-input {
        margin-right: 10px;
    }

    .token-success {
        background-color: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .token-error {
        background-color: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .token-id {
        font-family: monospace;
        word-break: break-all;
    }

    .token-types {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }

    .token-type-option {
        flex: 1;
        min-width: 150px;
        padding: 15px;
        background-color: var(--darker-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        text-align: center;
    }

    .token-type-option:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
    }

    .token-type-option.selected {
        border-color: var(--primary-color);
        background-color: rgba(138, 43, 226, 0.2);
        box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
    }

    .token-type-option h4 {
        margin-top: 0;
        color: var(--secondary-color);
    }

    .token-type-option p {
        font-size: 0.9rem;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="text-center mb-4">Create New Token</h1>
    
    {% if success %}
    <div class="token-success">
        <h3>Token Created Successfully!</h3>
        <p>Your token has been created with the following details:</p>
        <ul>
            <li><strong>Token ID:</strong> <span class="token-id">{{ token_id }}</span></li>
            <li><strong>Name:</strong> {{ name }}</li>
            <li><strong>Symbol:</strong> {{ symbol }}</li>
        </ul>
        <p>You can now <a href="{{ url_for('token_detail', token_id=token_id) }}">view your token</a> or <a href="{{ url_for('tokens') }}">see all tokens</a>.</p>
    </div>
    {% endif %}
    
    {% if error %}
    <div class="token-error">
        <h3>Error Creating Token</h3>
        <p>{{ error }}</p>
    </div>
    {% endif %}
    
    <div class="token-form">
        <form method="post" action="{{ url_for('create_token') }}">
            <div class="form-group">
                <label for="name">Token Name:</label>
                <input type="text" class="form-control" id="name" name="name" placeholder="Enter token name" required>
            </div>
            
            <div class="form-group">
                <label for="symbol">Token Symbol:</label>
                <input type="text" class="form-control" id="symbol" name="symbol" placeholder="Enter token symbol (e.g., BTC)" required>
                <small class="text-muted">Usually 3-4 characters, all caps.</small>
            </div>
            
            <div class="form-group">
                <label for="creator_id">Creator Identity ID:</label>
                <input type="text" class="form-control" id="creator_id" name="creator_id" placeholder="Enter your identity ID" required>
                <small class="text-muted">The identity that will be registered as the token creator.</small>
            </div>
            
            <div class="form-group">
                <label>Token Type:</label>
                <div class="token-types">
                    <div class="token-type-option" data-type="utility">
                        <h4>Utility</h4>
                        <p>For services, access rights, or platform usage.</p>
                    </div>
                    <div class="token-type-option" data-type="governance">
                        <h4>Governance</h4>
                        <p>For voting and decision-making rights.</p>
                    </div>
                    <div class="token-type-option" data-type="asset">
                        <h4>Asset</h4>
                        <p>Represents ownership of a physical or digital asset.</p>
                    </div>
                </div>
                <input type="hidden" id="token_type" name="token_type" required>
            </div>
            
            <div class="form-group">
                <label for="supply">Initial Supply:</label>
                <input type="number" class="form-control" id="supply" name="supply" min="0" value="1000" required>
            </div>
            
            <div class="form-group">
                <label for="decimals">Decimals:</label>
                <input type="number" class="form-control" id="decimals" name="decimals" min="0" max="18" value="2" required>
                <small class="text-muted">Number of decimal places (0-18).</small>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="mintable" name="mintable">
                    <label class="form-check-label" for="mintable">Mintable (can create more tokens later)</label>
                </div>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="transferable" name="transferable" checked>
                    <label class="form-check-label" for="transferable">Transferable (can be sent between identities)</label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter token description"></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary btn-block">Create Token</button>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Token type selection
        const tokenTypeOptions = document.querySelectorAll('.token-type-option');
        const tokenTypeInput = document.getElementById('token_type');
        
        tokenTypeOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                tokenTypeOptions.forEach(opt => opt.classList.remove('selected'));
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Set the hidden input value
                tokenTypeInput.value = this.dataset.type;
            });
        });
        
        // Set default token type
        tokenTypeOptions[0].click();
    });
</script>
{% endblock %}
