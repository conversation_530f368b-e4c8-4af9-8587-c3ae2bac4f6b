"""
Run the Onnyx API

This script runs the Onnyx API using uvicorn.
"""

import uvicorn
import argparse

def main():
    """Run the Onnyx API."""
    parser = argparse.ArgumentParser(description="Run the Onnyx API")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    print(f"Starting Onnyx API on {args.host}:{args.port}")
    
    uvicorn.run(
        "api.api:app",
        host=args.host,
        port=args.port,
        reload=args.reload
    )

if __name__ == "__main__":
    main()
