#!/usr/bin/env python3
"""
Initialize Onnyx Production Database

This script creates a fresh production database with the correct schema.
"""

import os
import sys
import sqlite3
import logging
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("onnyx.init_production_db")

def init_production_db(db_path: str, schema_path: str) -> None:
    """
    Initialize the production database with the schema.
    
    Args:
        db_path: Path to the database file
        schema_path: Path to the schema SQL file
    """
    logger.info(f"Initializing production database at: {db_path}")
    logger.info(f"Using schema from: {schema_path}")
    
    # Create database directory if it doesn't exist
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Read the schema
    with open(schema_path, 'r') as f:
        schema = f.read()
    
    # Create the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Execute the schema
        cursor.executescript(schema)
        conn.commit()
        
        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        logger.info(f"Successfully created {len(tables)} tables:")
        for table in tables:
            if table[0] != 'sqlite_sequence':  # Skip internal SQLite table
                logger.info(f"  - {table[0]}")
        
        # Insert default chain parameters
        insert_default_chain_parameters(cursor)
        conn.commit()
        
        logger.info("Production database initialized successfully!")
        
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        conn.rollback()
        raise
    finally:
        conn.close()

def insert_default_chain_parameters(cursor) -> None:
    """Insert default chain parameters."""
    logger.info("Inserting default chain parameters...")
    
    now = int(time.time())
    
    default_params = [
        ('block_reward', '10', '10', 'Block mining reward in ONX', 'mining'),
        ('reward_token', 'ONX', 'ONX', 'Token used for block rewards', 'mining'),
        ('block_time', '60', '60', 'Target block time in seconds', 'mining'),
        ('difficulty', '1', '1', 'Mining difficulty', 'mining'),
        ('max_block_size', '1048576', '1048576', 'Maximum block size in bytes', 'mining'),
        ('min_etzem_score', '100', '100', 'Minimum Etzem score for validation', 'governance'),
        ('voting_period', '604800', '604800', 'Voting period in seconds (1 week)', 'governance'),
        ('quorum_threshold', '51', '51', 'Quorum threshold percentage', 'governance'),
        ('platform_version', '1.0.0', '1.0.0', 'Platform version', 'system'),
        ('genesis_timestamp', str(now), str(now), 'Genesis block timestamp', 'system')
    ]
    
    for key, value, default_value, description, category in default_params:
        cursor.execute('''
            INSERT OR REPLACE INTO chain_parameters 
            (key, value, default_value, description, category, last_updated)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (key, value, default_value, description, category, now))
    
    logger.info(f"Inserted {len(default_params)} default chain parameters")

def create_genesis_identity(cursor) -> None:
    """Create the genesis identity for system operations."""
    logger.info("Creating genesis identity...")
    
    now = int(time.time())
    
    # Create genesis identity
    cursor.execute('''
        INSERT OR REPLACE INTO identities 
        (identity_id, name, public_key, status, created_at, updated_at, metadata)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        'genesis',
        'Genesis Identity',
        'genesis_public_key',
        'system',
        now,
        now,
        '{"type": "system", "description": "Genesis identity for system operations"}'
    ))
    
    logger.info("Genesis identity created")

def main():
    """Main function."""
    logger.info("Starting Onnyx Production Database Initialization")
    
    # Define paths
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    schema_path = os.path.join(root_dir, 'shared', 'schemas', 'production_schema.sql')
    db_path = os.path.join(root_dir, 'shared', 'db', 'onnyx.db')
    
    # Initialize the database
    init_production_db(db_path, schema_path)
    
    # Create genesis identity
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    create_genesis_identity(cursor)
    conn.commit()
    conn.close()
    
    logger.info("Onnyx production database initialization complete!")
    logger.info(f"Database location: {db_path}")

if __name__ == "__main__":
    main()
