{% extends "base.html" %}

{% block title %}Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Blockchain Overview</h2>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-cube"></i>
            </div>
            <div class="stat-value">{{ total_blocks }}</div>
            <div class="stat-label">Total Blocks</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stat-value">{{ total_transactions }}</div>
            <div class="stat-label">Total Transactions</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <div class="stat-value">{{ pending_transactions }}</div>
            <div class="stat-label">Pending Transactions</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-coins"></i>
            </div>
            <div class="stat-value">{{ total_tokens }}</div>
            <div class="stat-label">Total Tokens</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="stat-value">{{ total_identities }}</div>
            <div class="stat-label">Total Identities</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-value">{{ total_selas }}</div>
            <div class="stat-label">Active Selas</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-database"></i>
            </div>
            <div class="stat-value">{{ blockchain_size|round(1) }} KB</div>
            <div class="stat-label">Blockchain Size</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value">{{ avg_txs_per_block|round(1) }}</div>
            <div class="stat-label">Avg Txs Per Block</div>
        </div>
    </div>

    <div class="section">
        <div class="section-header">
            <h2 class="section-title">Token Distribution</h2>
        </div>

        <div class="stats-grid">
            {% for type, count in token_types.items() %}
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <div class="stat-value">{{ count }}</div>
                <div class="stat-label">{{ type|capitalize }} Tokens</div>
            </div>
            {% endfor %}

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-value">{{ total_token_supply|round(1) }}</div>
                <div class="stat-label">Total Token Supply</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-header">
            <h2 class="section-title">Transaction Types</h2>
        </div>

        <div class="stats-grid">
            {% for type, count in tx_types.items() %}
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="stat-value">{{ count }}</div>
                <div class="stat-label">{{ type|capitalize }} Transactions</div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<div class="section">
    <div class="section-header">
        <h2 class="section-title">Latest Block</h2>
        <a href="{{ url_for('transactions') }}" class="view-all">View All Blocks</a>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="card-title">Block #{{ latest_block.index }}</div>
            <div class="card-subtitle">{{ format_timestamp(latest_block.timestamp) }}</div>
        </div>

        <div class="property-list">
            <div class="property-item">
                <div class="property-label">Hash</div>
                <div class="property-value">{{ latest_block.hash }}</div>
            </div>

            <div class="property-item">
                <div class="property-label">Previous Hash</div>
                <div class="property-value">{{ latest_block.previous_hash }}</div>
            </div>

            <div class="property-item">
                <div class="property-label">Transactions</div>
                <div class="property-value">{{ latest_block.transactions|length }}</div>
            </div>

            <div class="property-item">
                <div class="property-label">Nonce</div>
                <div class="property-value">{{ latest_block.nonce }}</div>
            </div>
        </div>
    </div>
</div>

<div class="section">
    <div class="section-header">
        <h2 class="section-title">Recent Transactions</h2>
        <a href="{{ url_for('transactions') }}" class="view-all">View All Transactions</a>
    </div>

    <table>
        <thead>
            <tr>
                <th>Transaction ID</th>
                <th>Block</th>
                <th>Type</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            {% for tx in recent_txs %}
            <tr>
                <td><a href="{{ url_for('tx_detail', txid=tx.txid) }}">{{ tx.txid[:10] }}...</a></td>
                <td>{{ tx.block }}</td>
                <td>
                    <span class="tx-type tx-{{ tx.type }}">{{ tx.type }}</span>
                </td>
                <td>{{ format_timestamp(tx.timestamp) }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="4" style="text-align: center;">No transactions found</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<div class="section">
    <div class="section-header">
        <h2 class="section-title">About Onnyx</h2>
    </div>

    <div class="card">
        <p>Onnyx is a new blockchain protocol that puts identity at the center of the token ecosystem where identity, ownership, and contribution are encoded on-chain and protected by mathematics, not governments.</p>

        <h3 style="margin-top: 1rem;">Key Features</h3>
        <ul style="margin-left: 2rem; margin-top: 0.5rem;">
            <li><strong>Identity-Centric:</strong> Every token is linked to an identity</li>
            <li><strong>Reputation System:</strong> Build reputation through on-chain activity</li>
            <li><strong>Token Forking:</strong> Create tokens based on existing ones</li>
            <li><strong>Soulbound Badges:</strong> Non-transferable tokens that represent achievements</li>
            <li><strong>OnnyxScript:</strong> Stack-based scripting language for token operations</li>
        </ul>
    </div>
</div>
{% endblock %}
