#!/usr/bin/env python3

"""
Script to test creating an identity.
"""

import os
import sys
import logging
import time
import requests
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.test_create_identity")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def create_identity():
    """Create a new identity."""
    try:
        # Create the identity
        data = {
            "name": f"Test Identity {int(time.time())}",
            "nation": "Israel",
            "tribe": "Judah",
            "dob": "01/01/2000",
            "region": "North America",
            "purpose": "Testing",
            "soul_seal": "test_seal"
        }
        
        logger.info(f"Creating identity with data: {data}")
        
        response = requests.post("http://localhost:8082/create-identity", data=data)
        
        # Check the response
        if response.status_code == 200:
            logger.info("Identity created successfully")
            
            # Check the database directly
            from data.db import db
            identities = db.query("SELECT identity_id, name FROM identities")
            
            logger.info(f"Identities in database: {len(identities)}")
            for identity in identities:
                logger.info(f"  {identity['identity_id']}: {identity['name']}")
            
            return True
        else:
            logger.error(f"Error creating identity: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error creating identity: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main entry point."""
    logger.info("Testing identity creation...")
    
    success = create_identity()
    
    if success:
        logger.info("Identity creation test passed")
    else:
        logger.error("Identity creation test failed")

if __name__ == "__main__":
    main()
