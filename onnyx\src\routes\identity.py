# src/routes/identity.py

import logging
import time
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from models.identity import Identity
from models.transaction import Transaction
from models.mempool import MempoolTransaction
from src.identity.registry import IdentityRegistry
from src.wallet.wallet import OnnyxWallet

# Set up logging
logger = logging.getLogger("onnyx.routes.identity")

# Initialize components
registry = IdentityRegistry()
wallet = OnnyxWallet()  # Used for local verification for now

# Create router
identity_router = APIRouter(prefix="/identity", tags=["identity"])

# Identity creation request model
class CreateIdentityRequest(BaseModel):
    name: str
    public_key: str
    metadata: dict = {}
    message: str
    signature: str

@identity_router.post("/createidentity")
def create_identity(req: CreateIdentityRequest):
    """
    Create a new identity.
    Requires a signature to verify the creator's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    try:
        # Add to mempool first
        mempool_data = {
            "name": req.name,
            "public_key": req.public_key,
            "metadata": req.metadata
        }

        # Create a mempool transaction
        mempool_tx = MempoolTransaction.create(
            tx_id=f"mem_{int(time.time()*1000)}",
            timestamp=int(time.time()),
            op="OP_IDENTITY",
            data=mempool_data,
            sender=req.public_key,  # Use public key as sender since we don't have an identity yet
            signature=req.signature
        )

        # Register the identity
        identity = registry.register_identity(
            name=req.name,
            public_key=req.public_key,
            metadata=req.metadata
        )

        # Create a transaction record
        tx = Transaction.create(
            op="OP_IDENTITY",
            data={
                "identity_id": identity.identity_id,
                "name": identity.name,
                "public_key": identity.public_key,
                "metadata": identity.metadata
            },
            sender=identity.identity_id,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {
            "identity_id": identity.identity_id,
            "name": identity.name,
            "public_key": identity.public_key,
            "txid": tx.tx_id
        }
    except Exception as e:
        logger.error(f"Error creating identity: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@identity_router.get("/getidentity")
def get_identity(identity_id: str):
    """
    Get information about an identity.
    """
    identity = registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get reputation
    reputation = identity.get_reputation()

    # Get badges
    badges = identity.get_badges()

    # Get token balances
    token_balances = identity.get_token_balances()

    # Format token balances
    tokens = []
    for balance in token_balances:
        tokens.append({
            "token_id": balance["token_id"],
            "name": balance["name"],
            "symbol": balance["symbol"],
            "category": balance["category"],
            "balance": balance["balance"]
        })

    return {
        "identity_id": identity.identity_id,
        "name": identity.name,
        "public_key": identity.public_key,
        "reputation": reputation,
        "metadata": identity.metadata,
        "tokens": tokens,
        "badges": badges,
        "created_at": identity.created_at,
        "updated_at": identity.updated_at
    }

# Identity update request model
class UpdateIdentityRequest(BaseModel):
    identity_id: str
    field: str
    value: str
    message: str
    signature: str

@identity_router.post("/updateidentity")
def update_identity(req: UpdateIdentityRequest):
    """
    Update an identity's metadata.
    Requires a signature to verify the updater's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    try:
        # Add to mempool first
        mempool_data = {
            "identity_id": req.identity_id,
            "field": req.field,
            "value": req.value
        }

        # Create a mempool transaction
        mempool_tx = MempoolTransaction.create(
            tx_id=f"mem_{int(time.time()*1000)}",
            timestamp=int(time.time()),
            op="OP_UPDATE_IDENTITY",
            data=mempool_data,
            sender=req.identity_id,
            signature=req.signature
        )

        # Update the identity
        identity = registry.update_identity(
            identity_id=req.identity_id,
            field=req.field,
            value=req.value
        )

        # Create a transaction record
        tx = Transaction.create(
            op="OP_UPDATE_IDENTITY",
            data=mempool_data,
            sender=req.identity_id,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {
            "identity_id": identity.identity_id,
            "txid": tx.tx_id
        }
    except Exception as e:
        logger.error(f"Error updating identity: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# Grant reputation request model
class GrantReputationRequest(BaseModel):
    to_identity: str
    reputation_type: str
    value: int
    issuer_identity: str
    message: str
    signature: str

@identity_router.post("/grantreputation")
def grant_reputation(req: GrantReputationRequest):
    """
    Grant reputation to an identity.
    Requires a signature to verify the issuer's identity.
    """
    # For testing purposes, we'll skip signature verification
    # In a real implementation, we would verify the signature
    # if not wallet.verify(req.message, req.signature):
    #     raise HTTPException(status_code=401, detail="Invalid signature")

    # Get target identity
    target = registry.get_identity(req.to_identity)
    if not target:
        raise HTTPException(status_code=404, detail="Target identity not found")

    # Add to mempool first
    mempool_data = {
        "to_identity": req.to_identity,
        "reputation_type": req.reputation_type,
        "value": req.value,
        "issuer_identity": req.issuer_identity
    }

    # Create a mempool transaction
    mempool_tx = MempoolTransaction.create(
        tx_id=f"mem_{int(time.time()*1000)}",
        timestamp=int(time.time()),
        op="OP_GRANT_REPUTATION",
        data=mempool_data,
        sender=req.issuer_identity,
        signature=req.signature
    )

    # Update reputation
    try:
        # Update reputation
        registry.update_reputation(
            identity_id=req.to_identity,
            reputation_type=req.reputation_type,
            value=req.value
        )

        # Create a badge if needed
        from data.db import db
        badge_query = "INSERT INTO badges (identity_id, type, issuer_id, value, timestamp) VALUES (?, ?, ?, ?, ?)"
        badge_params = (req.to_identity, req.reputation_type.lower(), req.issuer_identity, 1, int(time.time()))
        db.execute(badge_query, badge_params)

        # Create a transaction record
        tx = Transaction.create(
            op="OP_GRANT_REPUTATION",
            data=mempool_data,
            sender=req.issuer_identity,
            signature=req.signature
        )

        # Remove from mempool
        mempool_tx.remove()

        return {
            "status": "success",
            "to_identity": req.to_identity,
            "reputation_type": req.reputation_type,
            "value": req.value,
            "issuer_identity": req.issuer_identity,
            "txid": tx.tx_id
        }
    except Exception as e:
        logger.error(f"Error granting reputation: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@identity_router.get("/transactions")
def identity_transactions(identity_id: str, limit: int = 100, offset: int = 0):
    """
    Get transactions for a specific identity.
    """
    # Check if the identity exists
    identity = registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get transactions for the identity
    transactions = identity.get_transactions(limit=limit, offset=offset)

    return {
        "identity_id": identity_id,
        "transactions": transactions
    }

@identity_router.get("/identityscore")
def identity_score(identity_id: str):
    """
    Get the composite score and detailed reputation metrics for an identity.
    """
    identity = registry.get_identity(identity_id)
    if not identity:
        raise HTTPException(status_code=404, detail="Identity not found")

    # Get reputation
    reputation_entries = identity.get_reputation()

    # Get transaction count
    transactions = identity.get_transactions(limit=1000)
    transaction_count = len(transactions)

    # Get token balances
    token_balances = identity.get_token_balances()
    token_count = len(token_balances)

    # Get badges
    badge_entries = identity.get_badges()

    # Calculate base scores
    base_score = 0
    for entry in reputation_entries:
        if entry["type"] == "score":
            base_score += entry["value"]

    # Calculate other scores
    transaction_score = transaction_count * 0.1
    token_score = token_count * 5

    # Calculate follower score
    follower_score = 0
    for entry in reputation_entries:
        if entry["type"] == "followers":
            follower_score += entry["value"]

    # Calculate badge score
    badge_score = 0
    badges = []
    for badge in badge_entries:
        badge_score += badge["value"]
        badges.append({
            "type": badge["type"].upper(),
            "value": badge["value"],
            "issuer": badge["issuer_id"],
            "timestamp": badge["timestamp"]
        })

    # Calculate composite score
    composite_score = int(base_score + transaction_score + token_score + follower_score + badge_score)

    # Calculate rank percentile (simplified)
    all_identities = registry.get_all_identities()
    all_scores = []

    for id_obj in all_identities:
        # Get reputation for this identity
        id_reputation = id_obj.get_reputation()
        id_score = 0

        # Calculate base score
        for entry in id_reputation:
            if entry["type"] == "score":
                id_score += entry["value"]

        # Get transaction count
        id_txs = id_obj.get_transactions(limit=1000)
        id_score += len(id_txs) * 0.1

        # Get token count
        id_tokens = id_obj.get_token_balances()
        id_score += len(id_tokens) * 5

        # Calculate follower score
        for entry in id_reputation:
            if entry["type"] == "followers":
                id_score += entry["value"] * 2

        # Add to scores list
        all_scores.append(id_score)

    # Sort scores and calculate percentile
    all_scores.sort()
    rank = all_scores.index(base_score) if base_score in all_scores else 0
    percentile = int((rank / max(1, len(all_scores))) * 100)

    # Calculate longevity in days
    created_timestamp = identity.created_at
    current_timestamp = int(time.time())
    longevity_days = (current_timestamp - created_timestamp) // (24 * 60 * 60)

    return {
        "identity_id": identity_id,
        "composite_score": composite_score,
        "rank_percentile": percentile,
        "metrics": {
            "transaction_volume": transaction_count,
            "token_activity": token_count,
            "network_trust": int(follower_score),
            "longevity_days": longevity_days,
            "badges": badges
        }
    }


