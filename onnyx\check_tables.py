#!/usr/bin/env python3

"""
Script to check the tables in the database.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnyx.check_tables")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from data.db import db

def check_tables():
    """Check the tables in the database."""
    try:
        # Get the tables
        tables = db.get_tables()
        
        logger.info(f"Found {len(tables)} tables in the database:")
        for table in tables:
            logger.info(f"  {table}")
        
        # Check if the transactions table exists
        if "transactions" in tables:
            logger.info("Transactions table exists")
            
            # Check the schema of the transactions table
            conn = db.get_connection()
            cursor = conn.execute("PRAGMA table_info(transactions)")
            columns = cursor.fetchall()
            conn.close()
            
            logger.info("Transactions table schema:")
            for column in columns:
                logger.info(f"  {column['name']} ({column['type']})")
        else:
            logger.error("Transactions table does not exist")
        
        return True
    except Exception as e:
        logger.error(f"Error checking tables: {str(e)}")
        return False

def main():
    """Main entry point."""
    logger.info("Checking tables...")
    
    success = check_tables()
    
    if success:
        logger.info("Table check completed successfully")
    else:
        logger.error("Table check failed")

if __name__ == "__main__":
    main()
