"""
Onnyx Block Miner Module

This module provides the BlockMiner class for creating new blocks.
"""

import time
import hashlib
import json
import sys
import os
import logging
from typing import Dict, List, Any

# Set up logging
logger = logging.getLogger("onnyx.consensus.miner")

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from blockchain.node.blockchain import LocalBlockchain
from blockchain.node.mempool import Mempool
from tokens.ledger.ledger import TokenLedger

# Import VM block validator if available
try:
    from blockchain.vm.block_validator import validate_block
except ImportError:
    # Mock validate_block function if not available
    def validate_block(block, previous_block):
        return True

# Flag to bypass validation (for testing)
BYPASS_VALIDATION = False

# Import chain parameters
from shared.config.chain_parameters import chain_parameters
from shared.utils.event_logger import event_logger

# Get chain parameters
BLOCK_REWARD = chain_parameters.get("block_reward")  # ONX
TOKEN_ID = chain_parameters.get("reward_token")

class BlockMiner:
    """
    BlockMiner creates new blocks and adds them to the blockchain.
    """

    def __init__(self, blockchain_file: str = "blockchain.json",
                 mempool_file: str = "mempool.json",
                 ledger_file: str = "token_ledger.json"):
        """
        Initialize the BlockMiner.

        Args:
            blockchain_file: Path to the blockchain JSON file
            mempool_file: Path to the mempool JSON file
            ledger_file: Path to the ledger JSON file
        """
        self.chain = LocalBlockchain(blockchain_file)
        self.mempool = Mempool(mempool_file)
        self.ledger = TokenLedger(ledger_file)

    def hash_block(self, block: Dict[str, Any]) -> str:
        """
        Calculate the hash of a block.

        Args:
            block: The block to hash

        Returns:
            The hash of the block
        """
        # Create a copy of the block without the hash field
        block_copy = {
            "index": block["index"],
            "timestamp": block["timestamp"],
            "transactions": block["transactions"],
            "previous_hash": block["previous_hash"],
            "nonce": block["nonce"]
        }

        # Convert to string and hash
        block_string = json.dumps(block_copy, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()

    def create_coinbase_tx(self, proposer_id: str) -> Dict[str, Any]:
        """
        Create a coinbase transaction.

        Args:
            proposer_id: The identity ID of the block proposer

        Returns:
            The coinbase transaction
        """
        # Get the current block reward from chain parameters
        block_reward = chain_parameters.get("block_reward")
        reward_token = chain_parameters.get("reward_token")

        return {
            "type": "reward",
            "op": "OP_REWARD",
            "txid": f"coinbase-{int(time.time())}-{proposer_id}",
            "to": proposer_id,
            "amount": block_reward,
            "token_id": reward_token,
            "timestamp": int(time.time())
        }

    def create_block(self, proposer_id: str, sela_id: str = None, private_key_pem: str = None) -> Dict[str, Any]:
        """
        Create a new block.

        Args:
            proposer_id: The identity ID of the block proposer
            sela_id: The Sela ID (optional)
            private_key_pem: The private key in PEM format (optional)

        Returns:
            The new block
        """
        # Get the latest block
        latest = self.chain.get_latest()

        # Get transactions from the mempool
        mempool_txs = self.mempool.get_all()

        # Create the coinbase transaction
        coinbase = self.create_coinbase_tx(proposer_id)

        # Create the block
        block = {
            "index": latest["index"] + 1,
            "timestamp": int(time.time()),
            "transactions": [coinbase] + mempool_txs,
            "previous_hash": latest["hash"],
            "nonce": 0,  # For future PoW
            "signed_by": sela_id or proposer_id,
            "signature": ""  # Will be filled in later
        }

        # Calculate the block hash
        block["hash"] = self.hash_block(block)

        # Sign the block if a private key is provided
        if private_key_pem and sela_id:
            try:
                # Import here to avoid circular imports
                from blockchain.node.block_signer import block_signer
                block = block_signer.sign_block(block, sela_id, private_key_pem)
            except ImportError:
                logger.warning("Block signer not available, skipping block signature")
            except Exception as e:
                logger.error(f"Error signing block: {str(e)}")

        # Validate the block (unless bypassed)
        if not BYPASS_VALIDATION:
            validate_block(block, latest)

        # Add the block to the chain
        self.chain.add_block(block)

        # Clear the mempool
        self.mempool.clear()

        # Credit the block reward to the proposer
        self.ledger.credit(proposer_id, TOKEN_ID, BLOCK_REWARD)

        # Record the mining activity in the activity ledger
        try:
            from blockchain.node.activity_ledger import activity_ledger
            activity_ledger.record_activity("mine_block", {
                "block_index": block["index"],
                "block_hash": block["hash"],
                "proposer_id": proposer_id,
                "sela_id": sela_id or proposer_id,
                "transaction_count": len(block["transactions"])
            })
        except ImportError:
            logger.warning("Activity ledger not available, skipping activity recording")
        except Exception as e:
            logger.error(f"Error recording mining activity: {str(e)}")

        return block

    def mine_block(self, identity_id: str, sela_id: str = None, private_key_pem: str = None, enforce_rotation: bool = True) -> Dict[str, Any]:
        """
        Mine a new block.

        Args:
            identity_id: The identity ID of the miner
            sela_id: The Sela ID (optional)
            private_key_pem: The private key in PEM format (optional)
            enforce_rotation: Whether to enforce validator rotation (default: True)

        Returns:
            The mined block

        Raises:
            Exception: If it's not the miner's turn to propose a block
        """
        # Get the current chain height
        current_height = self.chain.get_latest()["index"] + 1

        # Check if it's this Sela's turn to propose a block
        if enforce_rotation and sela_id:
            try:
                # Import here to avoid circular imports
                from blockchain.consensus.rotation_engine import rotation_engine

                if not rotation_engine.is_valid_proposer(sela_id, current_height):
                    next_validator = rotation_engine.get_next_validator(current_height)
                    raise Exception(f"Not your turn — current proposer should be: {next_validator}")
            except ImportError:
                logger.warning("Rotation engine not available, skipping rotation check")
            except Exception as e:
                logger.error(f"Error checking validator rotation: {str(e)}")
                raise

        # Create a new block
        block = self.create_block(identity_id, sela_id, private_key_pem)

        # Mark the block as proposed
        if sela_id:
            try:
                # Import here to avoid circular imports
                from blockchain.consensus.rotation_engine import rotation_engine

                rotation_engine.mark_proposed(sela_id, block["index"])
            except ImportError:
                logger.warning("Rotation engine not available, skipping mark_proposed")
            except Exception as e:
                logger.error(f"Error marking block as proposed: {str(e)}")

        logger.info(f"Mined block {block['index']} with hash {block['hash']}")

        # Log the block event
        try:
            event_logger.log_block(block, block["transactions"], identity_id)
            logger.info(f"Logged block {block['index']} events")
        except Exception as e:
            logger.error(f"Error logging block events: {str(e)}")

        return block

    def get_mining_stats(self) -> Dict[str, Any]:
        """
        Get mining statistics.

        Returns:
            Mining statistics
        """
        return {
            "chain_height": self.chain.get_chain_length(),
            "latest_block_hash": self.chain.get_latest()["hash"],
            "mempool_size": self.mempool.get_count(),
            "block_reward": BLOCK_REWARD,
            "reward_token": TOKEN_ID
        }

    def get_latest_blocks(self, count: int) -> List[Dict[str, Any]]:
        """
        Get the latest blocks.

        Args:
            count: The number of blocks to get

        Returns:
            The latest blocks
        """
        chain = self.chain.get_all_blocks()
        return chain[-count:] if len(chain) >= count else chain
