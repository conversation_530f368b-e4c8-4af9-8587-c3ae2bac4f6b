"""
Onnyx Block Miner Module

This module provides the BlockMiner class for creating new blocks.
"""

import time
import hashlib
import json
import sys
import os
import logging
from typing import Dict, List, Any

# Set up logging
logger = logging.getLogger("onnyx.consensus.miner")

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import production database
try:
    from shared.db.db import db
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    logger.warning("Production database not available, falling back to JSON files")

# Import legacy blockchain for fallback
try:
    from blockchain.node.blockchain import LocalBlockchain
    from blockchain.node.mempool import Mempool
    from tokens.ledger.ledger import TokenLedger
    LEGACY_AVAILABLE = True
except ImportError:
    LEGACY_AVAILABLE = False

# Import VM block validator if available
try:
    from blockchain.vm.block_validator import validate_block
except ImportError:
    # Mock validate_block function if not available
    def validate_block(block, previous_block):
        return True

# Flag to bypass validation (for testing)
BYPASS_VALIDATION = False

# Import chain parameters
from shared.config.chain_parameters import chain_parameters
from shared.utils.event_logger import event_logger

# Get chain parameters
BLOCK_REWARD = chain_parameters.get("block_reward")  # ONX
TOKEN_ID = chain_parameters.get("reward_token")

class BlockMiner:
    """
    BlockMiner creates new blocks and adds them to the blockchain.
    Uses production database when available, falls back to JSON files.
    """

    def __init__(self, blockchain_file: str = "blockchain.json",
                 mempool_file: str = "mempool.json",
                 ledger_file: str = "token_ledger.json"):
        """
        Initialize the BlockMiner.

        Args:
            blockchain_file: Path to the blockchain JSON file (fallback)
            mempool_file: Path to the mempool JSON file (fallback)
            ledger_file: Path to the ledger JSON file (fallback)
        """
        self.use_database = DATABASE_AVAILABLE

        if self.use_database:
            logger.info("Using production database for mining operations")
        else:
            logger.info("Using JSON files for mining operations (fallback)")
            if LEGACY_AVAILABLE:
                self.chain = LocalBlockchain(blockchain_file)
                self.mempool = Mempool(mempool_file)
                self.ledger = TokenLedger(ledger_file)
            else:
                raise ImportError("Neither database nor legacy blockchain available")

    def hash_block(self, block: Dict[str, Any]) -> str:
        """
        Calculate the hash of a block.

        Args:
            block: The block to hash

        Returns:
            The hash of the block
        """
        # Create a copy of the block without the hash field
        block_copy = {
            "index": block["index"],
            "timestamp": block["timestamp"],
            "transactions": block["transactions"],
            "previous_hash": block["previous_hash"],
            "nonce": block["nonce"]
        }

        # Convert to string and hash
        block_string = json.dumps(block_copy, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()

    def create_coinbase_tx(self, proposer_id: str) -> Dict[str, Any]:
        """
        Create a coinbase transaction.

        Args:
            proposer_id: The identity ID of the block proposer

        Returns:
            The coinbase transaction
        """
        # Get the current block reward from chain parameters
        block_reward = chain_parameters.get("block_reward")
        reward_token = chain_parameters.get("reward_token")

        return {
            "type": "reward",
            "op": "OP_REWARD",
            "txid": f"coinbase-{int(time.time())}-{proposer_id}",
            "to": proposer_id,
            "amount": block_reward,
            "token_id": reward_token,
            "timestamp": int(time.time())
        }

    def create_block(self, proposer_id: str, sela_id: str = None, private_key_pem: str = None) -> Dict[str, Any]:
        """
        Create a new block.

        Args:
            proposer_id: The identity ID of the block proposer
            sela_id: The Sela ID (optional)
            private_key_pem: The private key in PEM format (optional)

        Returns:
            The new block
        """
        # Get the latest block using the new method
        latest = self.get_latest_block()

        # Get transactions from the mempool
        if self.use_database:
            try:
                # Get pending transactions from database
                mempool_txs = db.query("SELECT * FROM mempool WHERE status = 'pending' ORDER BY created_at ASC LIMIT 10") or []
            except:
                mempool_txs = []  # No mempool table or no transactions
        else:
            mempool_txs = self.mempool.get_all()

        # Create the coinbase transaction
        coinbase = self.create_coinbase_tx(proposer_id)

        # Create the block
        block = {
            "index": latest["index"] + 1,
            "timestamp": int(time.time()),
            "transactions": [coinbase] + mempool_txs,
            "previous_hash": latest["hash"],
            "nonce": 0,  # For future PoW
            "signed_by": sela_id or proposer_id,
            "signature": ""  # Will be filled in later
        }

        # Calculate the block hash
        block["hash"] = self.hash_block(block)

        # Sign the block if a private key is provided
        if private_key_pem and sela_id:
            try:
                # Import here to avoid circular imports
                from blockchain.node.block_signer import block_signer
                block = block_signer.sign_block(block, sela_id, private_key_pem)
            except ImportError:
                logger.warning("Block signer not available, skipping block signature")
            except Exception as e:
                logger.error(f"Error signing block: {str(e)}")

        # Validate the block (unless bypassed)
        if not BYPASS_VALIDATION:
            validate_block(block, latest)

        # Add the block to the chain
        if self.use_database:
            try:
                # Insert block into database
                db.execute("""
                    INSERT INTO blocks (block_hash, block_number, previous_hash, data, timestamp, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    block["hash"],
                    block["index"],
                    block["previous_hash"],
                    json.dumps({
                        "transactions": block["transactions"],
                        "signed_by": block["signed_by"],
                        "signature": block["signature"],
                        "nonce": block["nonce"]
                    }),
                    block["timestamp"],
                    int(time.time())
                ))

                # Clear processed transactions from mempool
                if mempool_txs:
                    # Mark transactions as processed (if they have IDs)
                    for tx in mempool_txs:
                        if isinstance(tx, dict) and "id" in tx:
                            try:
                                db.execute("UPDATE mempool SET status = 'processed' WHERE id = ?", (tx["id"],))
                            except:
                                pass  # Ignore if mempool table doesn't exist

                logger.info(f"Block {block['index']} added to database")

            except Exception as e:
                logger.error(f"Error adding block to database: {e}")
                raise
        else:
            # Use legacy method
            self.chain.add_block(block)
            self.mempool.clear()

        # Credit the block reward to the proposer
        if self.use_database:
            try:
                # Update token balance in database (if tokens table exists)
                db.execute("""
                    INSERT OR REPLACE INTO tokens (identity_id, token_id, balance, updated_at)
                    VALUES (?, ?, COALESCE((SELECT balance FROM tokens WHERE identity_id = ? AND token_id = ?), 0) + ?, ?)
                """, (proposer_id, TOKEN_ID, proposer_id, TOKEN_ID, BLOCK_REWARD, int(time.time())))

                logger.info(f"Credited {BLOCK_REWARD} {TOKEN_ID} to {proposer_id}")

            except Exception as e:
                logger.warning(f"Could not credit block reward to database: {e}")
                # Continue without failing - reward tracking is not critical for mining
        else:
            # Use legacy method
            if hasattr(self, 'ledger'):
                self.ledger.credit(proposer_id, TOKEN_ID, BLOCK_REWARD)

        # Record the mining activity in the activity ledger
        try:
            from blockchain.node.activity_ledger import activity_ledger
            activity_ledger.record_activity("mine_block", {
                "block_index": block["index"],
                "block_hash": block["hash"],
                "proposer_id": proposer_id,
                "sela_id": sela_id or proposer_id,
                "transaction_count": len(block["transactions"])
            })
        except ImportError:
            logger.warning("Activity ledger not available, skipping activity recording")
        except Exception as e:
            logger.error(f"Error recording mining activity: {str(e)}")

        return block

    def mine_block(self, identity_id: str, sela_id: str = None, private_key_pem: str = None, enforce_rotation: bool = True) -> Dict[str, Any]:
        """
        Mine a new block.

        Args:
            identity_id: The identity ID of the miner
            sela_id: The Sela ID (optional)
            private_key_pem: The private key in PEM format (optional)
            enforce_rotation: Whether to enforce validator rotation (default: True)

        Returns:
            The mined block

        Raises:
            Exception: If it's not the miner's turn to propose a block
        """
        # Get the current chain height
        current_height = self.get_latest_block()["index"] + 1

        # Check if it's this Sela's turn to propose a block
        if enforce_rotation and sela_id:
            try:
                # Import here to avoid circular imports
                from blockchain.consensus.rotation_engine import rotation_engine

                if not rotation_engine.is_valid_proposer(sela_id, current_height):
                    next_validator = rotation_engine.get_next_validator(current_height)
                    raise Exception(f"Not your turn — current proposer should be: {next_validator}")
            except ImportError:
                logger.warning("Rotation engine not available, skipping rotation check")
            except Exception as e:
                logger.error(f"Error checking validator rotation: {str(e)}")
                raise

        # Create a new block
        block = self.create_block(identity_id, sela_id, private_key_pem)

        # Mark the block as proposed
        if sela_id:
            try:
                # Import here to avoid circular imports
                from blockchain.consensus.rotation_engine import rotation_engine

                rotation_engine.mark_proposed(sela_id, block["index"])
            except ImportError:
                logger.warning("Rotation engine not available, skipping mark_proposed")
            except Exception as e:
                logger.error(f"Error marking block as proposed: {str(e)}")

        logger.info(f"Mined block {block['index']} with hash {block['hash']}")

        # Log the block event
        try:
            event_logger.log_block(block, block["transactions"], identity_id)
            logger.info(f"Logged block {block['index']} events")
        except Exception as e:
            logger.error(f"Error logging block events: {str(e)}")

        return block

    def get_latest_block(self) -> Dict[str, Any]:
        """
        Get the latest block from database or JSON file.

        Returns:
            The latest block in standardized format
        """
        if self.use_database:
            try:
                # Get latest block from database
                latest = db.query_one("SELECT * FROM blocks ORDER BY block_number DESC LIMIT 1")

                if not latest:
                    # No blocks found, return genesis block info
                    return {
                        "index": -1,
                        "hash": "0000000000000000000000000000000000000000000000000000000000000000",
                        "previous_hash": "0000000000000000000000000000000000000000000000000000000000000000",
                        "timestamp": int(time.time()),
                        "transactions": []
                    }

                # Convert database block to standard format
                return {
                    "index": latest["block_number"],
                    "hash": latest["block_hash"],
                    "previous_hash": latest["previous_hash"],
                    "timestamp": latest["timestamp"],
                    "transactions": json.loads(latest.get("data", "{}")).get("transactions", [])
                }

            except Exception as e:
                logger.error(f"Error getting latest block from database: {e}")
                raise
        else:
            # Use legacy JSON file method
            latest = self.chain.get_latest()
            # Ensure it has the 'hash' field (not 'block_hash')
            if "block_hash" in latest and "hash" not in latest:
                latest["hash"] = latest["block_hash"]
            return latest

    def get_mining_stats(self) -> Dict[str, Any]:
        """
        Get mining statistics.

        Returns:
            Mining statistics
        """
        if self.use_database:
            try:
                # Get chain height from database
                chain_height_result = db.query_one("SELECT COUNT(*) as count FROM blocks")
                chain_height = chain_height_result["count"] if chain_height_result else 0

                # Get latest block hash
                latest_block = self.get_latest_block()
                latest_hash = latest_block["hash"]

                # Get mempool size (if mempool table exists)
                try:
                    mempool_result = db.query_one("SELECT COUNT(*) as count FROM mempool")
                    mempool_size = mempool_result["count"] if mempool_result else 0
                except:
                    mempool_size = 0

                return {
                    "chain_height": chain_height,
                    "latest_block_hash": latest_hash,
                    "mempool_size": mempool_size,
                    "block_reward": BLOCK_REWARD,
                    "reward_token": TOKEN_ID
                }

            except Exception as e:
                logger.error(f"Error getting mining stats from database: {e}")
                raise
        else:
            # Use legacy method
            return {
                "chain_height": self.chain.get_chain_length(),
                "latest_block_hash": self.chain.get_latest()["hash"],
                "mempool_size": self.mempool.get_count(),
                "block_reward": BLOCK_REWARD,
                "reward_token": TOKEN_ID
            }

    def get_latest_blocks(self, count: int) -> List[Dict[str, Any]]:
        """
        Get the latest blocks.

        Args:
            count: The number of blocks to get

        Returns:
            The latest blocks
        """
        chain = self.chain.get_all_blocks()
        return chain[-count:] if len(chain) >= count else chain
