# Onnyx Node Module

This module defines the Onnyx node runtime. It acts as the local daemon that controls the chain lifecycle, mining, and syncing.

## Features

- In-memory or JSON-based persistent chain
- Mempool for pending txs
- Mining simulation (no PoW yet)
- Chain status, block retrieval
- P2P networking for node communication
- Block and mempool synchronization
- Peer discovery and management
- Message signing and verification

## Commands

```bash
python cli.py mine     # Mine a block
python cli.py status   # Get node status
python cli.py chain    # Dump full blockchain
python cli.py peers    # List connected peers
python cli.py connect  # Connect to a peer
```

## P2P Networking Layer

The Onnyx Node P2P Networking Layer enables real blockchain functionality where nodes can communicate, synchronize data, and cooperate in a trustless environment.

> "If each node is a tribe, then Onnyx must unify the tribes under the same truth."

### Core Features

| Feature | Description |
|---------|-------------|
| 🔗 Peer Discovery | Add/connect to known node URLs |
| 🔄 Block Sync | Sync latest block from peers |
| 📨 Mempool Gossip | Share unconfirmed txs across nodes |
| 🔏 Signature Validation | All messages signed with node private key and verified with public key |
| 🧾 Shared Ledger | Tokens, identities, and scrolls synced |

### Components

#### NodeConfig

The `NodeConfig` class manages the configuration for the Onnyx node. It provides methods for:

- Loading and saving configuration from/to a file
- Getting and setting configuration values
- Managing the list of peers

#### Peer and PeerManager

The `Peer` class represents a peer node in the Onnyx network. It stores information about the peer, such as its URL, node ID, and connection status.

The `PeerManager` class manages peer connections in the Onnyx network. It provides methods for:

- Adding and removing peers
- Connecting to and disconnecting from peers
- Broadcasting messages to all connected peers
- Sending messages to specific peers
- Registering message handlers

#### NodeServer

The `NodeServer` class is a WebSocket server for the Onnyx node. It handles incoming connections and messages from peers. It provides methods for:

- Starting and stopping the server
- Handling WebSocket connections
- Processing messages from peers
- Synchronizing the mempool and chain with peers

#### Message

The `Message` class represents a message in the Onnyx network. It provides methods for:

- Creating and parsing messages
- Signing messages with the node's private key
- Verifying message signatures

#### KeyManager

The `KeyManager` class manages cryptographic keys for the node. It provides methods for:

- Generating and loading ECDSA key pairs
- Signing messages with the node's private key
- Verifying message signatures with peers' public keys
- Managing peer public keys

### Message Protocol

All messages in the Onnyx network are JSON-based and have the following structure:

```json
{
  "type": "message_type",
  "from": "node_id",
  "timestamp": 1234567890,
  "payload_field1": "value1",
  "payload_field2": "value2",
  "signature": "base64_encoded_signature"
}
```

All messages are signed with the sender's private key, and the signature is verified by the receiver using the sender's public key. The public key is exchanged during the initial hello message.

### Message Types

#### Peer Discovery

- `hello`: Sent when a node connects to a peer, contains the node's public key
- `get_peers`: Request a list of peers from a node
- `peers`: Response to a `get_peers` message, contains a list of peer URLs

#### Mempool Synchronization

- `get_mempool`: Request the mempool from a node
- `mempool`: Response to a `get_mempool` message, contains a list of transactions
- `mempool_tx`: Broadcast a new transaction to all peers

#### Chain Synchronization

- `get_chain_height`: Request the chain height from a node
- `chain_height`: Response to a `get_chain_height` message, contains the chain height
- `get_block`: Request a specific block from a node
- `block`: Response to a `get_block` message, contains a block
- `new_block`: Broadcast a new block to all peers
- `request_chain`: Request the entire chain from a node
- `chain_data`: Response to a `request_chain` message, contains the entire chain

#### Heartbeat

- `heartbeat`: Sent periodically to keep connections alive
