{% extends "base.html" %}

{% block title %}Node Status - Onnyx Blockchain Explorer{% endblock %}

{% block content %}
<div class="section">
    <div class="section-header">
        <h2 class="section-title">Node Status</h2>
    </div>
    
    <div class="card" style="margin-bottom: 2rem;">
        <p>This page shows the status of the local node and its connected peers in the Onnyx P2P network.</p>
    </div>
    
    <div class="detail-section">
        <h3 class="detail-section-title">Local Node</h3>
        
        <div class="property-list">
            <div class="property-item">
                <div class="property-label">Node ID</div>
                <div class="property-value">{{ local_node.id }}</div>
            </div>
            
            <div class="property-item">
                <div class="property-label">Block Height</div>
                <div class="property-value">{{ local_node.block_height }}</div>
            </div>
            
            <div class="property-item">
                <div class="property-label">Latest Block Hash</div>
                <div class="property-value">{{ local_node.latest_block_hash }}</div>
            </div>
            
            <div class="property-item">
                <div class="property-label">Latest Block Time</div>
                <div class="property-value">{{ format_timestamp(local_node.latest_block_time) }}</div>
            </div>
            
            <div class="property-item">
                <div class="property-label">Status</div>
                <div class="property-value">
                    <span class="status-tag status-confirmed">{{ local_node.status }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="detail-section">
        <h3 class="detail-section-title">Connected Peers</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Peer ID</th>
                    <th>URL</th>
                    <th>Block Height</th>
                    <th>Status</th>
                    <th>Last Seen</th>
                    <th>Sync Status</th>
                </tr>
            </thead>
            <tbody>
                {% for peer in peers %}
                <tr>
                    <td>{{ peer.id }}</td>
                    <td>{{ peer.url }}</td>
                    <td>{{ peer.block_height }}</td>
                    <td>
                        <span class="status-tag status-{% if peer.status == 'active' %}confirmed{% elif peer.status == 'syncing' %}pending{% else %}failed{% endif %}">
                            {{ peer.status }}
                        </span>
                    </td>
                    <td>{{ format_timestamp(peer.last_seen) }}</td>
                    <td>
                        {% if peer.block_height > local_node.block_height %}
                        <span class="status-tag status-pending">Behind by {{ peer.block_height - local_node.block_height }} blocks</span>
                        {% elif peer.block_height < local_node.block_height %}
                        <span class="status-tag status-error">Ahead by {{ local_node.block_height - peer.block_height }} blocks</span>
                        {% else %}
                        <span class="status-tag status-confirmed">In sync</span>
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="6" style="text-align: center;">No connected peers</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="detail-section">
        <h3 class="detail-section-title">Data Sync Status</h3>
        
        <div class="property-list">
            <div class="property-item">
                <div class="property-label">Running</div>
                <div class="property-value">
                    <span class="status-tag status-{% if sync_status.running %}confirmed{% else %}failed{% endif %}">
                        {{ "Yes" if sync_status.running else "No" }}
                    </span>
                </div>
            </div>
            
            <div class="property-item">
                <div class="property-label">Last Sync</div>
                <div class="property-value">
                    {% if sync_status.last_sync %}
                    {{ format_timestamp(sync_status.last_sync) }}
                    {% else %}
                    Never
                    {% endif %}
                </div>
            </div>
            
            <div class="property-item">
                <div class="property-label">Sync Interval</div>
                <div class="property-value">{{ sync_status.sync_interval }} seconds</div>
            </div>
            
            <div class="property-item">
                <div class="property-label">API URL</div>
                <div class="property-value">{{ sync_status.api_url }}</div>
            </div>
        </div>
        
        <div style="margin-top: 1rem; text-align: center;">
            <a href="{{ url_for('refresh') }}" class="btn-primary">Refresh Data Now</a>
        </div>
    </div>
</div>
{% endblock %}
