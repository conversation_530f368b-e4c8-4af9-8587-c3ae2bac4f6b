#!/usr/bin/env python3
"""
ONNYX Phase 1 Production Launch - Production Network Verification
Final verification that the ONNYX trusted business network is production ready.
"""

import sqlite3
import json
import time
from datetime import datetime

def verify_genesis_identity():
    """Verify Genesis Identity is properly created."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM identities WHERE metadata LIKE '%Platform Founder%'")
        genesis = cursor.fetchone()
        
        if genesis:
            metadata = json.loads(genesis[4])
            conn.close()
            return {
                'exists': True,
                'identity_id': genesis[0],
                'name': genesis[1],
                'email': genesis[2],
                'role': metadata.get('role'),
                'organization': metadata.get('organization'),
                'genesis_identity': metadata.get('genesis_identity', False),
                'platform_founder': metadata.get('platform_founder', False)
            }
        
        conn.close()
        return {'exists': False}
        
    except Exception as e:
        print(f"Error verifying Genesis Identity: {e}")
        return {'exists': False}

def verify_genesis_block():
    """Verify Genesis Block #0 exists."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM blocks WHERE block_number = 0")
        genesis_block = cursor.fetchone()
        
        if genesis_block:
            block_data = json.loads(genesis_block[3])
            conn.close()
            return {
                'exists': True,
                'block_hash': genesis_block[0],
                'block_number': genesis_block[1],
                'data': block_data,
                'timestamp': genesis_block[4]
            }
        
        conn.close()
        return {'exists': False}
        
    except Exception as e:
        print(f"Error verifying Genesis Block: {e}")
        return {'exists': False}

def verify_business_validators():
    """Verify all business validators are registered."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT s.sela_id, s.name, s.category, s.status, m.mining_tier, m.mining_power
            FROM selas s
            LEFT JOIN mining m ON s.sela_id = m.sela_id
            ORDER BY s.name
        """)
        
        validators = cursor.fetchall()
        conn.close()
        
        return {
            'count': len(validators),
            'validators': validators,
            'active_count': len([v for v in validators if v[3] == 'active'])
        }
        
    except Exception as e:
        print(f"Error verifying business validators: {e}")
        return {'count': 0, 'validators': [], 'active_count': 0}

def verify_mining_system():
    """Verify mining system is configured."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Get mining statistics
        cursor.execute("SELECT COUNT(*) FROM mining")
        mining_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(mining_power) FROM mining")
        total_power = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT COUNT(*) FROM mining WHERE sela_id IS NOT NULL")
        validator_miners = cursor.fetchone()[0]
        
        # Get mining tiers
        cursor.execute("SELECT DISTINCT mining_tier FROM mining")
        tiers = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        return {
            'mining_nodes': mining_count,
            'total_power': total_power,
            'validator_miners': validator_miners,
            'tiers': tiers
        }
        
    except Exception as e:
        print(f"Error verifying mining system: {e}")
        return {'mining_nodes': 0, 'total_power': 0, 'validator_miners': 0, 'tiers': []}

def verify_database_integrity():
    """Verify database integrity and relationships."""
    try:
        conn = sqlite3.connect("data/onnyx.db")
        cursor = conn.cursor()
        
        # Check table existence
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['identities', 'selas', 'blocks', 'mining', 'transactions']
        missing_tables = [table for table in required_tables if table not in tables]
        
        # Check foreign key relationships
        cursor.execute("""
            SELECT COUNT(*) FROM selas s
            LEFT JOIN identities i ON s.identity_id = i.identity_id
            WHERE i.identity_id IS NULL
        """)
        orphaned_selas = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM mining m
            LEFT JOIN identities i ON m.identity_id = i.identity_id
            WHERE i.identity_id IS NULL
        """)
        orphaned_mining = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'tables_exist': len(missing_tables) == 0,
            'missing_tables': missing_tables,
            'orphaned_selas': orphaned_selas,
            'orphaned_mining': orphaned_mining,
            'integrity_ok': len(missing_tables) == 0 and orphaned_selas == 0 and orphaned_mining == 0
        }
        
    except Exception as e:
        print(f"Error verifying database integrity: {e}")
        return {'tables_exist': False, 'integrity_ok': False}

def production_network_verification():
    """Perform comprehensive production network verification."""
    print("🔍 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("=" * 70)
    print("PRODUCTION NETWORK VERIFICATION")
    print("Final verification of the ONNYX trusted business network")
    print("Date:", datetime.now().strftime("%B %d, %Y at %I:%M %p"))
    print("=" * 70)
    
    verification_results = {}
    
    # 1. Verify Genesis Identity
    print("1. Verifying Genesis Identity...")
    genesis_info = verify_genesis_identity()
    verification_results['genesis_identity'] = genesis_info
    
    if genesis_info['exists']:
        print(f"   ✅ Genesis Identity found")
        print(f"      Name: {genesis_info['name']}")
        print(f"      Email: {genesis_info['email']}")
        print(f"      Role: {genesis_info['role']}")
        print(f"      Organization: {genesis_info['organization']}")
        print(f"      Platform Founder: {genesis_info['platform_founder']}")
    else:
        print("   ❌ Genesis Identity not found")
    
    print()
    
    # 2. Verify Genesis Block
    print("2. Verifying Genesis Block...")
    genesis_block_info = verify_genesis_block()
    verification_results['genesis_block'] = genesis_block_info
    
    if genesis_block_info['exists']:
        print(f"   ✅ Genesis Block #0 found")
        print(f"      Block Hash: {genesis_block_info['block_hash']}")
        print(f"      Timestamp: {datetime.fromtimestamp(genesis_block_info['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("   ❌ Genesis Block not found")
    
    print()
    
    # 3. Verify Business Validators
    print("3. Verifying Business Validators...")
    validators_info = verify_business_validators()
    verification_results['validators'] = validators_info
    
    print(f"   ✅ Total Validators: {validators_info['count']}")
    print(f"   ✅ Active Validators: {validators_info['active_count']}")
    
    if validators_info['validators']:
        print("   📋 Registered Validators:")
        for sela_id, name, category, status, mining_tier, mining_power in validators_info['validators']:
            tier_info = f" | {mining_tier} ({mining_power}x)" if mining_tier else ""
            print(f"      • {name} ({category}) - {status}{tier_info}")
    
    print()
    
    # 4. Verify Mining System
    print("4. Verifying Mining System...")
    mining_info = verify_mining_system()
    verification_results['mining'] = mining_info
    
    print(f"   ✅ Mining Nodes: {mining_info['mining_nodes']}")
    print(f"   ✅ Validator Miners: {mining_info['validator_miners']}")
    print(f"   ✅ Total Mining Power: {mining_info['total_power']}x")
    print(f"   ✅ Mining Tiers: {', '.join(mining_info['tiers'])}")
    
    print()
    
    # 5. Verify Database Integrity
    print("5. Verifying Database Integrity...")
    db_info = verify_database_integrity()
    verification_results['database'] = db_info
    
    if db_info['integrity_ok']:
        print("   ✅ Database integrity verified")
        print("   ✅ All required tables exist")
        print("   ✅ Foreign key relationships intact")
    else:
        print("   ⚠️ Database integrity issues detected")
        if db_info['missing_tables']:
            print(f"      Missing tables: {', '.join(db_info['missing_tables'])}")
        if db_info['orphaned_selas'] > 0:
            print(f"      Orphaned Selas: {db_info['orphaned_selas']}")
        if db_info['orphaned_mining'] > 0:
            print(f"      Orphaned Mining records: {db_info['orphaned_mining']}")
    
    return verification_results

def display_final_status(verification_results):
    """Display final production launch status."""
    print("\n🎯 PRODUCTION NETWORK STATUS SUMMARY")
    print("=" * 60)
    
    # Calculate overall status
    checks = [
        ("Genesis Identity", verification_results['genesis_identity']['exists']),
        ("Genesis Block #0", verification_results['genesis_block']['exists']),
        ("Business Validators (≥5)", verification_results['validators']['active_count'] >= 5),
        ("Mining System", verification_results['mining']['mining_nodes'] >= 5),
        ("Database Integrity", verification_results['database']['integrity_ok'])
    ]
    
    passed_checks = sum(1 for _, passed in checks)
    total_checks = len(checks)
    
    print("📊 Verification Results:")
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"   {status} {check_name}")
    
    print()
    print(f"📈 Overall Status: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks == total_checks:
        print("\n🎉 ONNYX PHASE 1 PRODUCTION LAUNCH - COMPLETE SUCCESS!")
        print("✨ All systems verified and operational")
        print("⛓️ Trusted business network is production ready")
        print("🌟 Ready for public deployment and business onboarding")
        
        print("\n🚀 FINAL LAUNCH STATUS:")
        print("   ✅ System Reset and Preparation")
        print("   ✅ Genesis Identity Created (Djuvane Martin)")
        print("   ✅ Genesis Block #0 Established")
        print("   ✅ Founding Validators Registered (5)")
        print("   ✅ Mining System Configured")
        print("   ✅ Database Integrity Verified")
        print("   ✅ Production Network Operational")
        
        print("\n🌐 ONNYX TRUSTED BUSINESS NETWORK:")
        print(f"   👑 Platform Founder: {verification_results['genesis_identity']['name']}")
        print(f"   🏢 Active Validators: {verification_results['validators']['active_count']}")
        print(f"   ⛏️ Mining Power: {verification_results['mining']['total_power']}x")
        print(f"   ⛓️ Genesis Block: {verification_results['genesis_block']['block_hash'][:16]}...")
        
        print("\n🎯 MISSION ACCOMPLISHED!")
        print("The ONNYX trusted business network is officially launched!")
        
        return True
    else:
        print(f"\n⚠️ Production launch incomplete: {total_checks - passed_checks} issues need attention")
        return False

def main():
    """Execute production network verification."""
    print("🚀 ONNYX PHASE 1 PRODUCTION LAUNCH")
    print("🔍 FINAL PRODUCTION NETWORK VERIFICATION")
    print()
    
    # Perform comprehensive verification
    verification_results = production_network_verification()
    
    # Display final status
    success = display_final_status(verification_results)
    
    if success:
        print("\n" + "=" * 70)
        print("🌟 ONNYX PHASE 1 PRODUCTION LAUNCH OFFICIALLY COMPLETE!")
        print("Platform Founder: Djuvane Martin")
        print("Trusted Business Network: OPERATIONAL")
        print("Status: PRODUCTION READY")
        print("=" * 70)
    
    return success

if __name__ == "__main__":
    main()
