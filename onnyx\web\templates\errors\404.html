{% extends "base.html" %}

{% block title %}Page Not Found - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative flex items-center justify-center py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
        <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyber-cyan rounded-full animate-ping"></div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <!-- ONNYX Logo -->
        <div class="mb-8 flex justify-center">
            <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                     alt="ONNYX Logo"
                     class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-all duration-500"
                     style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
            </div>
        </div>

        <!-- Error Display -->
        <div class="glass-card p-12 neuro-card mb-12">
            <div class="w-24 h-24 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg shadow-cyber-cyan/30">
                <svg class="w-12 h-12 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>

            <h1 class="text-8xl md:text-9xl font-orbitron font-bold mb-6">
                <span class="hologram-text">404</span>
            </h1>
            <h2 class="text-3xl md:text-4xl font-orbitron font-bold text-cyber-cyan mb-6">Page Not Found</h2>
            <p class="text-xl text-text-secondary mb-8 max-w-2xl mx-auto leading-relaxed">
                The page you're looking for doesn't exist on the ONNYX platform.
                It might have been moved, deleted, or you entered the wrong URL.
            </p>

            <!-- Primary Action -->
            <div class="mb-8">
                <a href="{{ url_for('index') }}"
                   class="glass-button-primary px-10 py-4 rounded-xl text-lg font-orbitron font-bold transition-all duration-300 hover:scale-105 glow-effect">
                    🏠 Return Home
                </a>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="glass-card p-8 neuro-card">
            <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">Quick Navigation</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <a href="{{ url_for('sela.directory') }}"
                   class="glass-button px-6 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 text-center block">
                    🏢 Validator Directory
                </a>
                <a href="{{ url_for('explorer.index') }}"
                   class="glass-button px-6 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 text-center block">
                    🔍 Blockchain Explorer
                </a>
                <a href="{{ url_for('register_choice') }}"
                   class="glass-button px-6 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 text-center block">
                    🔐 Identity Portal
                </a>
            </div>
        </div>

        <!-- Network Status -->
        <div class="mt-12 flex items-center justify-center space-x-6 text-sm">
            <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-text-tertiary">Network Online</span>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                <span class="text-text-tertiary">Blockchain Active</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
