{% extends "base.html" %}

{% block title %}Page Not Found - Onnyx Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            
            <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
            <h2 class="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">
                The page you're looking for doesn't exist on the Onnyx platform. 
                It might have been moved, deleted, or you entered the wrong URL.
            </p>
            
            <div class="space-y-4">
                <a href="{{ url_for('index') }}" 
                   class="inline-block bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 rounded-lg font-semibold transition-colors">
                    Go Home
                </a>
                
                <div class="text-center">
                    <p class="text-sm text-gray-500 mb-4">Or try one of these:</p>
                    <div class="flex flex-col sm:flex-row gap-2 justify-center">
                        <a href="{{ url_for('sela.directory') }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm">
                            Browse Businesses
                        </a>
                        <a href="{{ url_for('explorer.index') }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm">
                            Blockchain Explorer
                        </a>
                        <a href="{{ url_for('register_choice') }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm">
                            Register
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
