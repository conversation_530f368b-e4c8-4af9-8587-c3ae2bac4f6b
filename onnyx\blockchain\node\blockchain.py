"""
Onnyx Blockchain Module

This module provides the LocalBlockchain class for managing the blockchain.
"""

import os
import json
import time
import hashlib
from typing import Dict, List, Any, Optional

class LocalBlockchain:
    """
    LocalBlockchain manages the blockchain data stored in a JSON file.
    """
    
    def __init__(self, blockchain_file: str = "blockchain.json"):
        """
        Initialize the LocalBlockchain.
        
        Args:
            blockchain_file: Path to the blockchain JSON file
        """
        self.blockchain_file = blockchain_file
        self.chain = self._load()
        
        # Create genesis block if chain is empty
        if not self.chain:
            self._create_genesis_block()
    
    def _load(self) -> List[Dict[str, Any]]:
        """
        Load the blockchain from the JSON file.
        
        Returns:
            The blockchain as a list of blocks
        """
        if os.path.exists(self.blockchain_file):
            try:
                with open(self.blockchain_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []
    
    def _save(self) -> None:
        """Save the blockchain to the JSON file."""
        with open(self.blockchain_file, 'w') as f:
            json.dump(self.chain, f, indent=2)
    
    def _create_genesis_block(self) -> Dict[str, Any]:
        """
        Create the genesis block.
        
        Returns:
            The genesis block
        """
        genesis_block = {
            "index": 0,
            "timestamp": int(time.time()),
            "transactions": [],
            "previous_hash": "",
            "nonce": 0
        }
        
        # Calculate hash
        genesis_block["hash"] = self._calculate_hash(genesis_block)
        
        # Add to chain and save
        self.chain.append(genesis_block)
        self._save()
        
        return genesis_block
    
    def _calculate_hash(self, block: Dict[str, Any]) -> str:
        """
        Calculate the hash of a block.
        
        Args:
            block: The block to hash
        
        Returns:
            The hash of the block
        """
        # Create a copy of the block without the hash field
        block_copy = {
            "index": block["index"],
            "timestamp": block["timestamp"],
            "transactions": block["transactions"],
            "previous_hash": block["previous_hash"],
            "nonce": block["nonce"]
        }
        
        # Convert to string and hash
        block_string = json.dumps(block_copy, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()
    
    def get_latest(self) -> Dict[str, Any]:
        """
        Get the latest block in the chain.
        
        Returns:
            The latest block
        """
        if not self.chain:
            return self._create_genesis_block()
        return self.chain[-1]
    
    def get_block(self, index: int) -> Optional[Dict[str, Any]]:
        """
        Get a block by index.
        
        Args:
            index: The index of the block
        
        Returns:
            The block or None if not found
        """
        if 0 <= index < len(self.chain):
            return self.chain[index]
        return None
    
    def get_block_by_hash(self, block_hash: str) -> Optional[Dict[str, Any]]:
        """
        Get a block by hash.
        
        Args:
            block_hash: The hash of the block
        
        Returns:
            The block or None if not found
        """
        for block in self.chain:
            if block["hash"] == block_hash:
                return block
        return None
    
    def add_block(self, block: Dict[str, Any]) -> bool:
        """
        Add a block to the chain.
        
        Args:
            block: The block to add
        
        Returns:
            True if the block was added, False otherwise
        """
        # Verify that the block's previous_hash matches the latest block's hash
        latest_block = self.get_latest()
        if block["previous_hash"] != latest_block["hash"]:
            return False
        
        # Verify that the block's index is one more than the latest block's index
        if block["index"] != latest_block["index"] + 1:
            return False
        
        # Add the block to the chain
        self.chain.append(block)
        self._save()
        
        return True
    
    def get_chain_length(self) -> int:
        """
        Get the length of the chain.
        
        Returns:
            The length of the chain
        """
        return len(self.chain)
    
    def get_all_blocks(self) -> List[Dict[str, Any]]:
        """
        Get all blocks in the chain.
        
        Returns:
            All blocks in the chain
        """
        return self.chain
    
    def is_valid_chain(self) -> bool:
        """
        Validate the entire chain.
        
        Returns:
            True if the chain is valid, False otherwise
        """
        for i in range(1, len(self.chain)):
            current_block = self.chain[i]
            previous_block = self.chain[i - 1]
            
            # Check if the previous hash matches
            if current_block["previous_hash"] != previous_block["hash"]:
                return False
            
            # Check if the hash is correct
            if current_block["hash"] != self._calculate_hash(current_block):
                return False
        
        return True
