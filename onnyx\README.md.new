# ONNYX: Identity-Centric Blockchain

ONNYX is a new blockchain protocol that puts identity at the center of the token ecosystem where identity, ownership, and contribution are encoded on-chain and protected by mathematics, not governments.

## 🔑 Key Features

- **Identity-Centric**: Every token is linked to an identity
- **Reputation System**: Build reputation through on-chain activity
- **Token Forking**: Create tokens based on existing ones
- **Soulbound Badges**: Non-transferable tokens that represent achievements
- **OnnyxScript**: Stack-based scripting language for token operations
- **Sela Business Registry**: Register and manage business entities on the blockchain
- **Zeman Time/Work Credits**: Convert labor, service, and creativity into on-chain value
- **Yovel Token Mint Limiter**: Cap token minting based on trust, reputation, and Zeman
- **Etzem Trust Score System**: Composite trust score for on-chain integrity
- **Council of Twelve Tribes**: Governance system powered by identity, trust, and reputation
- **Mikvah Token Engine**: Contribution-gated system for launching new tokens
- **Judah Engine**: Economic logic layer for fees, rebates, and staking

## 🏗️ Project Structure

```
onnyx/
├── src/
│   ├── chain/        # Blockchain core (blocks, chain, txlog, mempool)
│   ├── identity/     # Identity management
│   ├── tokens/       # Token management
│   ├── vm/           # Virtual machine for OnnyxScript
│   ├── node/         # Node implementation and P2P networking
│   ├── wallet/       # Wallet implementation
│   ├── business/     # Business registry (Sela)
│   ├── zeman/        # Time/Work credit system
│   ├── yovel/        # Token mint limiter
│   ├── etzem/        # Trust score system
│   ├── governance/   # Voice Scrolls governance proposals
│   ├── council/      # Council of Twelve Tribes authority
│   ├── mikvah/       # Mikvah Token Engine
│   ├── judah/        # Economic logic layer
│   ├── routes/       # API routes
│   └── web/          # Web interface
├── tests/            # Unit tests
├── data/             # Data storage
├── onnyx-cli.py      # Command-line interface
├── onnyx-api.py      # API server
├── onnyx-explorer.py # Web interface
├── run_node.py       # Run a node with P2P networking
├── mine_block.py     # Mine a new block
└── generate_keys.py  # Generate ECDSA keys for a node
```

## 🚀 Getting Started

### Running the API Server

```bash
# Install dependencies
pip install fastapi uvicorn pydantic ecdsa flask

# Run the API server
python onnyx-api.py
```

The API server will be available at http://127.0.0.1:8000. You can access the API documentation at http://127.0.0.1:8000/docs.

### Running the Web Interface

```bash
# Run the web interface
python onnyx-explorer.py
```

The web interface will be available at http://127.0.0.1:8080. You can use it to browse transactions, tokens, identities, and the mempool.

### Running the CLI

```bash
# Run the CLI in interactive mode
python onnyx-cli.py

# Run a specific command
python onnyx-cli.py identity  # Create a new identity
python onnyx-cli.py spawn     # Spawn a new token
python onnyx-cli.py mine      # Mine a new block
```

### Running the Node Server

```bash
# Run a node with default settings
python run_node.py

# Run a node with custom settings
python run_node.py --host localhost --port 8081 --id onnyx_node_1 --peer ws://localhost:8082 --peer ws://localhost:8083

# Run a node with a custom config file
python run_node.py --config data/node_config.json

# Run a node with debug logging
python run_node.py --log-level DEBUG
```

The node server will start a WebSocket server that listens for connections from other nodes. It will also connect to any peers specified in the configuration or command line arguments. The node server will synchronize the blockchain and mempool with its peers, ensuring that all nodes have the same state.

### Mining Blocks

```bash
# Mine a block with default settings
python mine_block.py

# Mine a block with a specific number of dummy transactions
python mine_block.py --transactions 10
```

The mining script will create a new block with transactions from the mempool, perform a simple proof of work, and broadcast the block to all connected peers. The peers will validate the block and add it to their blockchain if it's valid.

### Generating Keys

```bash
# Generate keys with default settings
python generate_keys.py

# Generate keys in a specific directory
python generate_keys.py --path data/node1/keys

# Overwrite existing keys
python generate_keys.py --force
```

The key generation script will create a new ECDSA key pair for the node. The keys are used to sign outgoing messages and verify incoming messages. The public key is shared with peers during the initial hello message.

## 📡 API Endpoints

### Token Endpoints

- `POST /token/forktoken` - Create a new token linked to an identity
- `POST /token/minttoken` - Mint additional tokens
- `POST /token/sendtoken` - Transfer tokens between addresses
- `POST /token/burntoken` - Burn tokens
- `GET /token/gettokenbalance` - Get the balance of a token for an address
- `GET /token/tokenregistry` - Get a list of tokens, optionally filtered by creator or type
- `GET /token/transactions` - Get a list of transactions, optionally filtered by token ID, identity ID, or transaction ID

### Identity Endpoints

- `POST /identity/createidentity` - Create a new identity
- `GET /identity/getidentity` - Get information about an identity
- `POST /identity/updateidentity` - Update an identity's metadata
- `POST /identity/grantreputation` - Grant reputation to an identity
- `GET /identity/identityscore` - Get the composite score and detailed reputation metrics for an identity
- `GET /identity/transactions` - Get transactions for a specific identity
