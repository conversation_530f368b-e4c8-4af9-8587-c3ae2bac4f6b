"""
Onnyx Analytics Routes

This module provides API routes for analytics.
"""

import logging
import time
from datetime import datetime, timedelta
from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional

from shared.utils.event_logger import event_logger
from shared.models.block import Block
from shared.models.transaction import Transaction
from shared.models.identity import Identity
from shared.models.token import Token

# Set up logging
logger = logging.getLogger("onnyx.routes.analytics")

# Create router
router = APIRouter()

@router.get("/analytics/logs")
def get_logs(
    limit: Optional[int] = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get event logs.

    Args:
        limit: Maximum number of logs to return
        offset: Offset for pagination

    Returns:
        A list of event logs
    """
    logger.info(f"Getting event logs with limit {limit} and offset {offset}")

    # Get transactions from the database
    transactions = Transaction.get_all(limit=limit, offset=offset)

    # Format transactions as logs
    logs = []
    for tx in transactions:
        log = {
            "tx_id": tx.tx_id,
            "tx_type": tx.tx_type,
            "sender": tx.sender,
            "recipient": tx.recipient,
            "token_id": tx.token_id,
            "amount": tx.amount,
            "status": tx.status,
            "timestamp": tx.timestamp,
            "block_hash": tx.block_hash,
            "block_index": tx.block_index,
            "data": tx.data
        }
        logs.append(log)

    logger.info(f"Retrieved {len(logs)} event logs")
    return {
        "logs": logs,
        "count": len(logs),
        "limit": limit,
        "offset": offset
    }

@router.get("/analytics/logs/{block_index}")
def get_log_by_block_index(block_index: int) -> Dict[str, Any]:
    """
    Get event logs by block index.

    Args:
        block_index: The block index

    Returns:
        The event logs for the block
    """
    logger.info(f"Getting event logs for block {block_index}")

    # Get the block from the database
    block = Block.get_by_index(block_index)
    if not block:
        logger.warning(f"Block {block_index} not found")
        raise HTTPException(status_code=404, detail=f"Block {block_index} not found")

    # Get transactions for this block
    transactions = Transaction.find_by_block_index(block_index)

    # Format transactions as logs
    logs = []
    for tx in transactions:
        log = {
            "tx_id": tx.tx_id,
            "tx_type": tx.tx_type,
            "sender": tx.sender,
            "recipient": tx.recipient,
            "token_id": tx.token_id,
            "amount": tx.amount,
            "status": tx.status,
            "timestamp": tx.timestamp,
            "block_hash": tx.block_hash,
            "block_index": tx.block_index,
            "data": tx.data
        }
        logs.append(log)

    logger.info(f"Retrieved {len(logs)} event logs for block {block_index}")
    return {
        "block_index": block_index,
        "block_hash": block.block_hash,
        "timestamp": block.timestamp,
        "logs": logs,
        "count": len(logs)
    }

@router.get("/analytics/summary")
def get_summary(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get a summary of event logs.

    Args:
        days: Number of days to include in the summary

    Returns:
        A summary of event logs
    """
    logger.info(f"Generating summary for the last {days} days")

    # Calculate the timestamp for 'days' ago
    start_timestamp = int(time.time()) - (days * 86400)

    # Get blocks and transactions from the database
    blocks = Block.find_by_timestamp_range(start_timestamp, int(time.time()))
    transactions = Transaction.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Calculate summary statistics
    summary = {
        "blocks": len(blocks),
        "transactions": len(transactions),
        "token_mints": len([tx for tx in transactions if tx.tx_type == "MINT"]),
        "token_transfers": len([tx for tx in transactions if tx.tx_type == "TRANSFER"]),
        "token_burns": len([tx for tx in transactions if tx.tx_type == "BURN"]),
        "proposals": len([tx for tx in transactions if tx.tx_type == "SCROLL"]),
        "votes": len([tx for tx in transactions if tx.tx_type == "VOTE"]),
        "identities": len([tx for tx in transactions if tx.tx_type == "IDENTITY"]),
        "reputation_grants": len([tx for tx in transactions if tx.tx_type == "REPUTATION"]),
        "stakes": len([tx for tx in transactions if tx.tx_type == "STAKE"]),
        "rewards": len([tx for tx in transactions if tx.tx_type == "REWARD"]),
        "unique_proposers": list(set([block.miner for block in blocks])),
        "unique_proposer_count": len(set([block.miner for block in blocks])),
        "start_timestamp": start_timestamp,
        "end_timestamp": int(time.time())
    }

    logger.info(f"Generated summary with {summary['blocks']} blocks and {summary['transactions']} transactions")
    return {
        "summary": summary,
        "days": days
    }

@router.get("/analytics/proposers")
def get_proposers(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get a list of unique proposers.

    Args:
        days: Number of days to include

    Returns:
        A list of unique proposers
    """
    logger.info(f"Getting unique proposers for the last {days} days")

    # Calculate the timestamp for 'days' ago
    start_timestamp = int(time.time()) - (days * 86400)

    # Get blocks from the database
    blocks = Block.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Get unique proposers
    proposers = list(set([block.miner for block in blocks]))

    # Get proposer details
    proposer_details = []
    for proposer_id in proposers:
        identity = Identity.get_by_id(proposer_id)
        if identity:
            proposer_details.append({
                "identity_id": identity.identity_id,
                "name": identity.name,
                "blocks_mined": len([block for block in blocks if block.miner == proposer_id])
            })
        else:
            proposer_details.append({
                "identity_id": proposer_id,
                "name": "Unknown",
                "blocks_mined": len([block for block in blocks if block.miner == proposer_id])
            })

    # Sort by blocks mined (descending)
    proposer_details.sort(key=lambda x: x["blocks_mined"], reverse=True)

    logger.info(f"Retrieved {len(proposer_details)} unique proposers")
    return {
        "proposers": proposer_details,
        "count": len(proposer_details),
        "days": days
    }

@router.get("/analytics/transactions")
def get_transactions(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get transaction statistics.

    Args:
        days: Number of days to include

    Returns:
        Transaction statistics
    """
    logger.info(f"Getting transaction statistics for the last {days} days")

    # Calculate the timestamp for 'days' ago
    start_timestamp = int(time.time()) - (days * 86400)

    # Get transactions from the database
    transactions = Transaction.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Calculate transaction statistics
    tx_stats = {
        "total": len(transactions),
        "token_mints": len([tx for tx in transactions if tx.tx_type == "MINT"]),
        "token_transfers": len([tx for tx in transactions if tx.tx_type == "TRANSFER"]),
        "token_burns": len([tx for tx in transactions if tx.tx_type == "BURN"]),
        "proposals": len([tx for tx in transactions if tx.tx_type == "SCROLL"]),
        "votes": len([tx for tx in transactions if tx.tx_type == "VOTE"]),
        "identities": len([tx for tx in transactions if tx.tx_type == "IDENTITY"]),
        "reputation_grants": len([tx for tx in transactions if tx.tx_type == "REPUTATION"]),
        "stakes": len([tx for tx in transactions if tx.tx_type == "STAKE"]),
        "rewards": len([tx for tx in transactions if tx.tx_type == "REWARD"])
    }

    logger.info(f"Retrieved transaction statistics with {tx_stats['total']} total transactions")
    return {
        "transactions": tx_stats,
        "days": days
    }

@router.get("/analytics/blocks")
def get_blocks(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get block statistics.

    Args:
        days: Number of days to include

    Returns:
        Block statistics
    """
    logger.info(f"Getting block statistics for the last {days} days")

    # Calculate the timestamp for 'days' ago
    start_timestamp = int(time.time()) - (days * 86400)

    # Get blocks from the database
    blocks = Block.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Get transactions for these blocks
    transactions = Transaction.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Calculate block statistics
    total_blocks = len(blocks)

    # Calculate average transactions per block
    avg_tx_per_block = 0
    if total_blocks > 0:
        avg_tx_per_block = len(transactions) / total_blocks

    # Calculate blocks per day
    blocks_per_day = 0
    if start_timestamp and int(time.time()):
        days_elapsed = (int(time.time()) - start_timestamp) / 86400
        if days_elapsed > 0:
            blocks_per_day = total_blocks / days_elapsed

    block_stats = {
        "total": total_blocks,
        "avg_tx_per_block": avg_tx_per_block,
        "blocks_per_day": blocks_per_day
    }

    logger.info(f"Retrieved block statistics with {block_stats['total']} total blocks")
    return {
        "blocks": block_stats,
        "days": days
    }

@router.get("/analytics/tokens")
def get_token_statistics(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get token statistics.

    Args:
        days: Number of days to include

    Returns:
        Token statistics
    """
    logger.info(f"Getting token statistics for the last {days} days")

    # Calculate the timestamp for 'days' ago
    start_timestamp = int(time.time()) - (days * 86400)

    # Get tokens from the database
    tokens = Token.get_all()

    # Get transactions for these tokens
    transactions = Transaction.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Calculate token statistics
    token_stats = {
        "total": len(tokens),
        "by_type": {},
        "most_active": []
    }

    # Count tokens by type
    for token in tokens:
        token_type = token.token_type
        if token_type not in token_stats["by_type"]:
            token_stats["by_type"][token_type] = 0
        token_stats["by_type"][token_type] += 1

    # Calculate most active tokens
    token_activity = {}
    for tx in transactions:
        if tx.token_id:
            if tx.token_id not in token_activity:
                token_activity[tx.token_id] = 0
            token_activity[tx.token_id] += 1

    # Sort tokens by activity
    sorted_tokens = sorted(token_activity.items(), key=lambda x: x[1], reverse=True)

    # Get details for the most active tokens
    for token_id, activity in sorted_tokens[:10]:  # Top 10 most active tokens
        token = Token.get_by_id(token_id)
        if token:
            token_stats["most_active"].append({
                "token_id": token.token_id,
                "name": token.name,
                "symbol": token.symbol,
                "activity": activity
            })

    logger.info(f"Retrieved token statistics with {token_stats['total']} total tokens")
    return {
        "tokens": token_stats,
        "days": days
    }

@router.get("/analytics/identities")
def get_identity_statistics(days: int = Query(7, ge=1, le=365)) -> Dict[str, Any]:
    """
    Get identity statistics.

    Args:
        days: Number of days to include

    Returns:
        Identity statistics
    """
    logger.info(f"Getting identity statistics for the last {days} days")

    # Calculate the timestamp for 'days' ago
    start_timestamp = int(time.time()) - (days * 86400)

    # Get identities from the database
    identities = Identity.get_all()

    # Get transactions for these identities
    transactions = Transaction.find_by_timestamp_range(start_timestamp, int(time.time()))

    # Calculate identity statistics
    identity_stats = {
        "total": len(identities),
        "new": 0,
        "most_active": []
    }

    # Count new identities
    for identity in identities:
        if identity.created_at and identity.created_at >= start_timestamp:
            identity_stats["new"] += 1

    # Calculate most active identities
    identity_activity = {}
    for tx in transactions:
        if tx.sender:
            if tx.sender not in identity_activity:
                identity_activity[tx.sender] = 0
            identity_activity[tx.sender] += 1
        if tx.recipient:
            if tx.recipient not in identity_activity:
                identity_activity[tx.recipient] = 0
            identity_activity[tx.recipient] += 1

    # Sort identities by activity
    sorted_identities = sorted(identity_activity.items(), key=lambda x: x[1], reverse=True)

    # Get details for the most active identities
    for identity_id, activity in sorted_identities[:10]:  # Top 10 most active identities
        identity = Identity.get_by_id(identity_id)
        if identity:
            identity_stats["most_active"].append({
                "identity_id": identity.identity_id,
                "name": identity.name,
                "activity": activity
            })

    logger.info(f"Retrieved identity statistics with {identity_stats['total']} total identities")
    return {
        "identities": identity_stats,
        "days": days
    }
