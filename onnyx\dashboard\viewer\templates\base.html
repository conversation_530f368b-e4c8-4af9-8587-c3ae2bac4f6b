<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Onnyx Blockchain Explorer{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="{{ url_for('static', filename='auto-refresh.js') }}"></script>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='favicon.png') }}">
    <meta name="description" content="Onnyx - An identity-centric blockchain where identity, ownership, and contribution are encoded on-chain and protected by mathematics.">
</head>
<body>
    <header>
        <div class="navbar">
            <div class="logo">
                <a href="{{ url_for('index') }}">
                    <span class="logo-icon"><i class="fas fa-cube"></i></span>
                    <h1>ONNYX</h1>
                </a>
                <span class="subtitle">Identity-Centric Blockchain</span>
            </div>

            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>

            <nav id="main-nav">
                <ul class="nav-links">
                    <li><a href="{{ url_for('index') }}" class="nav-link {% if request.path == '/' %}active{% endif %}">
                        <i class="fas fa-home"></i> <span>Home</span>
                    </a></li>
                    <li class="dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if request.path in ['/transactions', '/mempool'] %}active{% endif %}">
                            <i class="fas fa-exchange-alt"></i> <span>Transactions</span> <i class="fas fa-chevron-down"></i>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a href="{{ url_for('transactions') }}" class="{% if request.path == '/transactions' %}active{% endif %}">
                                <i class="fas fa-list"></i> All Transactions
                            </a></li>
                            <li><a href="{{ url_for('mempool') }}" class="{% if request.path == '/mempool' %}active{% endif %}">
                                <i class="fas fa-hourglass-half"></i> Mempool
                            </a></li>
                        </ul>
                    </li>
                    <li><a href="{{ url_for('tokens') }}" class="nav-link {% if request.path == '/tokens' %}active{% endif %}">
                        <i class="fas fa-coins"></i> <span>Tokens</span>
                    </a></li>
                    <li><a href="{{ url_for('identities') }}" class="nav-link {% if request.path == '/identities' %}active{% endif %}">
                        <i class="fas fa-user-circle"></i> <span>Identities</span>
                    </a></li>
                    <li><a href="{{ url_for('nodes') }}" class="nav-link {% if request.path == '/nodes' %}active{% endif %}">
                        <i class="fas fa-network-wired"></i> <span>Network</span>
                    </a></li>
                    <li><a href="{{ url_for('economic_structure') }}" class="nav-link {% if request.path == '/economic-structure' %}active{% endif %}">
                        <i class="fas fa-book"></i> <span>Economics</span>
                    </a></li>
                    <li><a href="{{ url_for('admin') }}" class="nav-link {% if request.path == '/admin' %}active{% endif %}">
                        <i class="fas fa-tools"></i> <span>Admin</span>
                    </a></li>
                </ul>
            </nav>

            <div class="search-container">
                <form action="{{ url_for('search') }}" method="get">
                    <input type="text" name="q" placeholder="Search tx, token, identity..." value="{{ request.args.get('q', '') }}">
                    <button type="submit"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </header>

    <main>
        {% block content %}{% endblock %}
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section about">
                <h3>About Onnyx</h3>
                <p>An identity-centric blockchain where identity, ownership, and contribution are encoded on-chain and protected by mathematics, not governments.</p>
                <div class="contact">
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> Decentralized Worldwide</p>
                </div>
            </div>

            <div class="footer-section links">
                <h3>Quick Links</h3>
                <ul>
                    <li><a href="{{ url_for('index') }}"><i class="fas fa-angle-right"></i> Home</a></li>
                    <li><a href="{{ url_for('transactions') }}"><i class="fas fa-angle-right"></i> Transactions</a></li>
                    <li><a href="{{ url_for('tokens') }}"><i class="fas fa-angle-right"></i> Tokens</a></li>
                    <li><a href="{{ url_for('identities') }}"><i class="fas fa-angle-right"></i> Identities</a></li>
                    <li><a href="{{ url_for('nodes') }}"><i class="fas fa-angle-right"></i> Network</a></li>
                </ul>
            </div>

            <div class="footer-section economic-pillars">
                <h3>Economic Pillars</h3>
                <ul>
                    <li><a href="{{ url_for('economic_structure') }}#zeman-details" title="Time/work credit system"><i class="fas fa-clock"></i> Zeman System</a></li>
                    <li><a href="{{ url_for('economic_structure') }}#sela-details" title="Business registry system"><i class="fas fa-building"></i> Sela Registry</a></li>
                    <li><a href="{{ url_for('economic_structure') }}#yovel-details" title="Token mint limiter"><i class="fas fa-balance-scale"></i> Yovel Limiter</a></li>
                    <li><a href="{{ url_for('economic_structure') }}#etzem-details" title="Trust score system"><i class="fas fa-shield-alt"></i> Etzem Trust</a></li>
                    <li><a href="{{ url_for('economic_structure') }}#voice-details" title="Governance system"><i class="fas fa-scroll"></i> Voice Scrolls</a></li>
                </ul>
                <div class="learn-more-btn">
                    <a href="{{ url_for('economic_structure') }}" class="btn-primary"><i class="fas fa-book"></i> Learn About Onnyx Economics</a>
                </div>
            </div>

            <div class="footer-section connect">
                <h3>Connect With Us</h3>
                <div class="social-links">
                    <a href="#" title="GitHub"><i class="fab fa-github"></i></a>
                    <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" title="Discord"><i class="fab fa-discord"></i></a>
                    <a href="#" title="Telegram"><i class="fab fa-telegram"></i></a>
                    <a href="#" title="Medium"><i class="fab fa-medium"></i></a>
                </div>
                <div class="newsletter">
                    <h4>Subscribe to Newsletter</h4>
                    <form>
                        <input type="email" placeholder="Enter your email">
                        <button type="submit"><i class="fas fa-paper-plane"></i></button>
                    </form>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="copyright">
                <p>&copy; 2025 Onnyx Blockchain. All rights reserved.</p>
            </div>
            <div class="footer-menu">
                <ul>
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">FAQ</a></li>
                    <li><a href="#">Support</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const mainNav = document.getElementById('main-nav');

            if (mobileMenuToggle && mainNav) {
                mobileMenuToggle.addEventListener('click', function() {
                    mainNav.classList.toggle('active');
                    this.classList.toggle('active');
                });
            }

            // Dropdown functionality
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.parentElement.classList.toggle('open');
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                dropdownToggles.forEach(toggle => {
                    if (!toggle.contains(e.target)) {
                        toggle.parentElement.classList.remove('open');
                    }
                });
            });
        });
    </script>
</body>
</html>
