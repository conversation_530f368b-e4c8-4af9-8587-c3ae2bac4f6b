"""
Mine a block on the Onnyx blockchain

This script mines a new block on the Onnyx blockchain.
"""

import os
import sys
import json
import argparse

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set the bypass validation flag for testing
import consensus.miner
consensus.miner.BYPASS_VALIDATION = True

from consensus.miner import BlockMiner
from node.blockchain import LocalBlockchain
from node.mempool import Mempool
from tokens.ledger import TokenLedger

def mine_block(proposer_id, pretty=False):
    """
    Mine a block on the Onnyx blockchain.
    
    Args:
        proposer_id: The identity ID of the block proposer
        pretty: Whether to pretty-print the block
    
    Returns:
        The mined block
    """
    # Create miner
    miner = BlockMiner()
    
    # Get initial stats
    initial_length = miner.chain.get_chain_length()
    initial_balance = miner.ledger.get_balance(proposer_id, "ONX")
    mempool_count = miner.mempool.get_count()
    
    # Mine block
    print(f"Mining block with proposer {proposer_id}...")
    block = miner.create_block(proposer_id)
    
    # Print block
    if pretty:
        print(json.dumps(block, indent=2))
    else:
        print(f"Block {block['index']} mined with hash {block['hash']}")
        print(f"Transactions: {len(block['transactions'])}")
        print(f"Timestamp: {block['timestamp']}")
    
    # Print stats
    new_length = miner.chain.get_chain_length()
    new_balance = miner.ledger.get_balance(proposer_id, "ONX")
    
    print(f"\nChain length: {initial_length} -> {new_length}")
    print(f"Proposer balance: {initial_balance} -> {new_balance} ONX")
    print(f"Mempool transactions: {mempool_count} -> {miner.mempool.get_count()}")
    
    return block

def main():
    """Mine a block on the Onnyx blockchain."""
    parser = argparse.ArgumentParser(description="Mine a block on the Onnyx blockchain")
    parser.add_argument("--proposer", type=str, required=True, help="The identity ID of the block proposer")
    parser.add_argument("--pretty", action="store_true", help="Pretty-print the block")
    
    args = parser.parse_args()
    
    # Mine block
    mine_block(args.proposer, args.pretty)

if __name__ == "__main__":
    main()
