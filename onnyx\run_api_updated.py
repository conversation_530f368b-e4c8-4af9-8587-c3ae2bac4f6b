"""
Run the Onnyx API

This script runs the updated Onnyx API using uvicorn.
"""

import os
import sys
import uvicorn
import argparse
import logging
from pathlib import Path

# Ensure the logs directory exists
logs_dir = Path(__file__).parent / "logs"
logs_dir.mkdir(exist_ok=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, "run_api.log"))
    ]
)
logger = logging.getLogger("onnyx.run_api")

def main():
    """Run the Onnyx API."""
    parser = argparse.ArgumentParser(description="Run the Onnyx API")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--log-level", type=str, default="info", choices=["debug", "info", "warning", "error", "critical"], help="Log level")
    
    args = parser.parse_args()
    
    # Set log level
    log_level = getattr(logging, args.log_level.upper())
    logging.getLogger().setLevel(log_level)
    
    logger.info(f"Starting Onnyx API on {args.host}:{args.port}")
    logger.info(f"Log level: {args.log_level.upper()}")
    
    try:
        uvicorn.run(
            "api.api_updated:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level.lower()
        )
    except Exception as e:
        logger.error(f"Error starting API: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
