"""
Test the Onnyx Etzem Engine

This script tests the Onnyx Etzem Engine by calculating Etzem scores.
"""

import os
import sys
import json
import time

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from identity.registry import IdentityRegistry
from governance.etzem_engine import EtzemEngine

def setup_test_data():
    """Set up test data for the Etzem Engine test."""
    # Create identity registry
    identity_registry = IdentityRegistry()

    # Create or get test identities
    try:
        alice = identity_registry.register_identity("alice", "<PERSON>", "0x123456789abcdef")
        print(f"Created identity: <PERSON>")
    except Exception:
        alice = identity_registry.get_identity("alice")
        print(f"Using existing identity: <PERSON>")

    try:
        bob = identity_registry.register_identity("bob", "<PERSON>", "0x987654321fedcba")
        print(f"Created identity: Bob")
    except Exception:
        bob = identity_registry.get_identity("bob")
        print(f"Using existing identity: <PERSON>")

    try:
        charlie = identity_registry.register_identity("char<PERSON>", "<PERSON>", "0xabcdef123456789")
        print(f"Created identity: Charlie")
    except Exception:
        charlie = identity_registry.get_identity("charlie")
        print(f"Using existing identity: Charlie")

    print("\nTest identities:")
    print(f"  Alice: {alice['identity_id']}")
    print(f"  Bob: {bob['identity_id']}")
    print(f"  Charlie: {charlie['identity_id']}")

    # Create Etzem engine
    etzem_engine = EtzemEngine()

    return identity_registry, etzem_engine

def test_etzem_engine():
    """Test the Etzem Engine functionality."""
    # Set up test data
    identity_registry, etzem_engine = setup_test_data()

    # Log activities for Alice
    print("\nLogging activities for Alice...")

    # Token mints
    for i in range(5):
        etzem_engine.log_activity(
            "alice",
            "TOKEN_MINT",
            {
                "token_id": f"ALICE_TOKEN_{i}",
                "token_type": "LOYALTY",
                "supply": 1000
            }
        )
        time.sleep(1)  # Add a small delay between activities

    # Token transfers
    for i in range(3):
        etzem_engine.log_activity(
            "alice",
            "TOKEN_TRANSFER",
            {
                "token_id": f"ALICE_TOKEN_{i}",
                "to": "bob",
                "amount": 100
            }
        )
        time.sleep(1)  # Add a small delay between activities

    # Log activities for Bob
    print("Logging activities for Bob...")

    # Reputation received
    for i in range(3):
        etzem_engine.log_activity(
            "bob",
            "REPUTATION_RECEIVED",
            {
                "from": "alice",
                "amount": 10
            }
        )
        time.sleep(1)  # Add a small delay between activities

    # Tasks completed
    for i in range(2):
        etzem_engine.log_activity(
            "bob",
            "TASK_COMPLETED",
            {
                "task_id": f"TASK_{i}",
                "hours": 5
            }
        )
        time.sleep(1)  # Add a small delay between activities

    # Log activities for Charlie
    print("Logging activities for Charlie...")

    # Token transfers
    for i in range(2):
        etzem_engine.log_activity(
            "charlie",
            "TOKEN_TRANSFER",
            {
                "token_id": "BOB_TOKEN",
                "to": "alice",
                "amount": 50
            }
        )
        time.sleep(1)  # Add a small delay between activities

    # Calculate Etzem scores
    print("\nCalculating Etzem scores...")

    alice_etzem = etzem_engine.calculate_etzem_score("alice")
    bob_etzem = etzem_engine.calculate_etzem_score("bob")
    charlie_etzem = etzem_engine.calculate_etzem_score("charlie")

    print("\nAlice's Etzem score:")
    print(f"  Consistency: {alice_etzem['consistency_score']:.2f}")
    print(f"  Token Impact: {alice_etzem['token_impact']:.2f}")
    print(f"  Reputation: {alice_etzem['reputation']:.2f}")
    print(f"  Labor Contribution: {alice_etzem['labor_contribution']:.2f}")
    print(f"  Final Etzem: {alice_etzem['final_etzem']:.2f}")

    print("\nBob's Etzem score:")
    print(f"  Consistency: {bob_etzem['consistency_score']:.2f}")
    print(f"  Token Impact: {bob_etzem['token_impact']:.2f}")
    print(f"  Reputation: {bob_etzem['reputation']:.2f}")
    print(f"  Labor Contribution: {bob_etzem['labor_contribution']:.2f}")
    print(f"  Final Etzem: {bob_etzem['final_etzem']:.2f}")

    print("\nCharlie's Etzem score:")
    print(f"  Consistency: {charlie_etzem['consistency_score']:.2f}")
    print(f"  Token Impact: {charlie_etzem['token_impact']:.2f}")
    print(f"  Reputation: {charlie_etzem['reputation']:.2f}")
    print(f"  Labor Contribution: {charlie_etzem['labor_contribution']:.2f}")
    print(f"  Final Etzem: {charlie_etzem['final_etzem']:.2f}")

    print("\nEtzem Engine test completed successfully!")

if __name__ == "__main__":
    test_etzem_engine()
