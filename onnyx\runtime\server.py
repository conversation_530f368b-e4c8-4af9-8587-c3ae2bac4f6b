#!/usr/bin/env python3
"""
Onnyx Server Entry Point

This is the main entry point for running the Onnyx server with all services.
"""

import sys
import os
import argparse
import threading
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def main():
    """Main server entry point."""
    parser = argparse.ArgumentParser(description='Onnyx Platform Server')
    parser.add_argument('--api-host', default='127.0.0.1', help='API host to bind to')
    parser.add_argument('--api-port', type=int, default=8000, help='API port to bind to')
    parser.add_argument('--viewer-port', type=int, default=5000, help='Viewer port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    print("Starting Onnyx Platform Server...")
    
    # Start API server in a separate thread
    def start_api():
        try:
            from api.handlers.api import app
            if args.debug:
                app.config['DEBUG'] = True
            print(f"Starting API server on {args.api_host}:{args.api_port}")
            app.run(host=args.api_host, port=args.api_port, debug=False, use_reloader=False)
        except Exception as e:
            print(f"Error starting API server: {e}")
    
    # Start viewer in a separate thread
    def start_viewer():
        try:
            from dashboard.viewer.app import app as viewer_app
            print(f"Starting viewer on port {args.viewer_port}")
            viewer_app.run(host='127.0.0.1', port=args.viewer_port, debug=False, use_reloader=False)
        except Exception as e:
            print(f"Error starting viewer: {e}")
    
    # Start services
    api_thread = threading.Thread(target=start_api, daemon=True)
    viewer_thread = threading.Thread(target=start_viewer, daemon=True)
    
    api_thread.start()
    viewer_thread.start()
    
    print(f"Onnyx Platform Server running:")
    print(f"  API: http://{args.api_host}:{args.api_port}")
    print(f"  Viewer: http://127.0.0.1:{args.viewer_port}")
    print("Press Ctrl+C to stop the server.")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down Onnyx Platform Server...")

if __name__ == '__main__':
    main()
