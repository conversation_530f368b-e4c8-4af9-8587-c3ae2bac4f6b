#!/usr/bin/env python3
"""
ONNYX Auto-Mining System Comprehensive Test Suite

Tests the complete auto-mining implementation including:
- Core auto-mining manager functionality
- Dashboard integration and web interface
- Validator configuration and management
- Performance monitoring and analytics
- System controls and automation
"""

import os
import sys
import json
import time
import subprocess
import requests
from pathlib import Path

def test_auto_mining_manager():
    """Test the core auto-mining manager functionality."""
    print("🤖 TESTING AUTO-MINING MANAGER CORE FUNCTIONALITY")
    print("=" * 60)
    
    results = []
    
    # Test 1: Auto-mining manager script exists
    manager_path = Path("scripts/auto_mining_manager.py")
    if manager_path.exists():
        print("✅ Auto-mining manager script exists")
        results.append(True)
    else:
        print("❌ Auto-mining manager script missing")
        results.append(False)
    
    # Test 2: Manager help command
    try:
        result = subprocess.run([
            sys.executable, "scripts/auto_mining_manager.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 or "usage:" in result.stdout.lower() or "usage:" in result.stderr.lower():
            print("✅ Auto-mining manager help command works")
            results.append(True)
        else:
            print("❌ Auto-mining manager help command failed")
            results.append(False)
    except Exception as e:
        print(f"❌ Error testing manager help: {e}")
        results.append(False)
    
    # Test 3: Manager status command
    try:
        result = subprocess.run([
            sys.executable, "scripts/auto_mining_manager.py", "status"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            # Try to parse JSON output
            try:
                status_data = json.loads(result.stdout)
                if "running" in status_data and "validators" in status_data:
                    print("✅ Auto-mining manager status command works")
                    results.append(True)
                else:
                    print("❌ Auto-mining manager status output invalid")
                    results.append(False)
            except json.JSONDecodeError:
                print("❌ Auto-mining manager status output not JSON")
                results.append(False)
        else:
            print("❌ Auto-mining manager status command failed")
            results.append(False)
    except Exception as e:
        print(f"❌ Error testing manager status: {e}")
        results.append(False)
    
    # Test 4: Configuration file creation
    try:
        # Run a configure command to create config file
        result = subprocess.run([
            sys.executable, "scripts/auto_mining_manager.py", "configure",
            "--sela-id", "test-validator-id"
        ], capture_output=True, text=True, timeout=15)
        
        config_path = Path("auto_mining_config.json")
        if config_path.exists():
            print("✅ Auto-mining configuration file creation works")
            results.append(True)
            
            # Clean up test config
            try:
                config_path.unlink()
            except:
                pass
        else:
            print("❌ Auto-mining configuration file not created")
            results.append(False)
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        results.append(False)
    
    return results

def test_dashboard_integration():
    """Test the dashboard integration and web interface."""
    print("\n🌐 TESTING DASHBOARD INTEGRATION")
    print("=" * 40)
    
    results = []
    
    # Test 1: Auto-mining routes file exists
    routes_path = Path("web/routes/auto_mining.py")
    if routes_path.exists():
        print("✅ Auto-mining routes file exists")
        results.append(True)
    else:
        print("❌ Auto-mining routes file missing")
        results.append(False)
    
    # Test 2: Dashboard template exists
    dashboard_template = Path("web/templates/auto_mining/dashboard.html")
    if dashboard_template.exists():
        print("✅ Auto-mining dashboard template exists")
        results.append(True)
    else:
        print("❌ Auto-mining dashboard template missing")
        results.append(False)
    
    # Test 3: Configuration template exists
    config_template = Path("web/templates/auto_mining/configure.html")
    if config_template.exists():
        print("✅ Auto-mining configuration template exists")
        results.append(True)
    else:
        print("❌ Auto-mining configuration template missing")
        results.append(False)
    
    # Test 4: Performance template exists
    performance_template = Path("web/templates/auto_mining/performance.html")
    if performance_template.exists():
        print("✅ Auto-mining performance template exists")
        results.append(True)
    else:
        print("❌ Auto-mining performance template missing")
        results.append(False)
    
    # Test 5: Settings template exists
    settings_template = Path("web/templates/auto_mining/settings.html")
    if settings_template.exists():
        print("✅ Auto-mining settings template exists")
        results.append(True)
    else:
        print("❌ Auto-mining settings template missing")
        results.append(False)
    
    # Test 6: Blueprint integration in app.py
    app_path = Path("web/app.py")
    if app_path.exists():
        with open(app_path, 'r') as f:
            app_content = f.read()
        
        if "auto_mining_bp" in app_content and "auto-mining" in app_content:
            print("✅ Auto-mining blueprint integrated in app.py")
            results.append(True)
        else:
            print("❌ Auto-mining blueprint not integrated in app.py")
            results.append(False)
    else:
        print("❌ App.py file not found")
        results.append(False)
    
    # Test 7: Navigation integration
    base_template = Path("web/templates/base.html")
    if base_template.exists():
        with open(base_template, 'r') as f:
            base_content = f.read()
        
        if "auto_mining.dashboard" in base_content:
            print("✅ Auto-mining navigation integrated in base template")
            results.append(True)
        else:
            print("❌ Auto-mining navigation not integrated in base template")
            results.append(False)
    else:
        print("❌ Base template not found")
        results.append(False)
    
    return results

def test_web_interface():
    """Test the web interface accessibility."""
    print("\n🌐 TESTING WEB INTERFACE ACCESSIBILITY")
    print("=" * 45)
    
    results = []
    
    try:
        # Test auto-mining dashboard
        response = requests.get("http://127.0.0.1:5000/auto-mining/", timeout=10)
        if response.status_code == 200:
            print("✅ Auto-mining dashboard accessible")
            results.append(True)
            
            # Check for key elements in response
            content = response.text.lower()
            if "auto-mining dashboard" in content:
                print("✅ Dashboard content loads correctly")
                results.append(True)
            else:
                print("❌ Dashboard content missing")
                results.append(False)
        else:
            print(f"❌ Auto-mining dashboard HTTP {response.status_code}")
            results.extend([False, False])
        
        # Test auto-mining API status
        response = requests.get("http://127.0.0.1:5000/auto-mining/api/status", timeout=10)
        if response.status_code == 200:
            print("✅ Auto-mining API status accessible")
            results.append(True)
            
            # Try to parse JSON response
            try:
                api_data = response.json()
                if "running" in api_data:
                    print("✅ API returns valid status data")
                    results.append(True)
                else:
                    print("❌ API status data incomplete")
                    results.append(False)
            except json.JSONDecodeError:
                print("❌ API response not valid JSON")
                results.append(False)
        else:
            print(f"❌ Auto-mining API status HTTP {response.status_code}")
            results.extend([False, False])
        
        # Test performance page
        response = requests.get("http://127.0.0.1:5000/auto-mining/performance", timeout=10)
        if response.status_code == 200:
            print("✅ Auto-mining performance page accessible")
            results.append(True)
        else:
            print(f"❌ Auto-mining performance page HTTP {response.status_code}")
            results.append(False)
        
        # Test settings page
        response = requests.get("http://127.0.0.1:5000/auto-mining/settings", timeout=10)
        if response.status_code == 200:
            print("✅ Auto-mining settings page accessible")
            results.append(True)
        else:
            print(f"❌ Auto-mining settings page HTTP {response.status_code}")
            results.append(False)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Web server not accessible: {e}")
        results.extend([False] * 7)
    
    return results

def test_validator_configuration():
    """Test validator configuration functionality."""
    print("\n⚙️ TESTING VALIDATOR CONFIGURATION")
    print("=" * 40)
    
    results = []
    
    # Test 1: Check if we have validators in database
    try:
        sys.path.insert(0, os.path.abspath('.'))
        from shared.db.db import db
        
        validators = db.query("SELECT sela_id, name FROM selas WHERE status = 'active' LIMIT 1")
        if validators:
            test_validator = validators[0]
            print(f"✅ Found test validator: {test_validator['name']}")
            results.append(True)
            
            # Test configuration command
            try:
                result = subprocess.run([
                    sys.executable, "scripts/auto_mining_manager.py", "configure",
                    "--sela-id", test_validator['sela_id'],
                    "--interval", "15",
                    "--schedule",
                    "--start-time", "09:00",
                    "--end-time", "17:00"
                ], capture_output=True, text=True, timeout=20)
                
                if result.returncode == 0:
                    print("✅ Validator configuration command works")
                    results.append(True)
                else:
                    print("❌ Validator configuration command failed")
                    results.append(False)
            except Exception as e:
                print(f"❌ Error testing validator configuration: {e}")
                results.append(False)
            
            # Test enable/disable commands
            try:
                # Test enable
                result = subprocess.run([
                    sys.executable, "scripts/auto_mining_manager.py", "enable",
                    "--sela-id", test_validator['sela_id']
                ], capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    print("✅ Validator enable command works")
                    results.append(True)
                else:
                    print("❌ Validator enable command failed")
                    results.append(False)
                
                # Test disable
                result = subprocess.run([
                    sys.executable, "scripts/auto_mining_manager.py", "disable",
                    "--sela-id", test_validator['sela_id']
                ], capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    print("✅ Validator disable command works")
                    results.append(True)
                else:
                    print("❌ Validator disable command failed")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ Error testing enable/disable commands: {e}")
                results.extend([False, False])
        else:
            print("❌ No active validators found for testing")
            results.extend([False, False, False, False])
            
    except Exception as e:
        print(f"❌ Error accessing database: {e}")
        results.extend([False, False, False, False])
    
    return results

def test_system_integration():
    """Test system integration and dependencies."""
    print("\n🔧 TESTING SYSTEM INTEGRATION")
    print("=" * 35)
    
    results = []
    
    # Test 1: Hybrid mining system dependency
    hybrid_miner = Path("scripts/hybrid_production_miner.py")
    if hybrid_miner.exists():
        print("✅ Hybrid production miner dependency exists")
        results.append(True)
    else:
        print("❌ Hybrid production miner dependency missing")
        results.append(False)
    
    # Test 2: Database connectivity
    try:
        sys.path.insert(0, os.path.abspath('.'))
        from shared.db.db import db
        
        # Test basic database operations
        test_query = db.query_one("SELECT COUNT(*) as count FROM selas")
        if test_query is not None:
            print("✅ Database connectivity works")
            results.append(True)
        else:
            print("❌ Database connectivity failed")
            results.append(False)
    except Exception as e:
        print(f"❌ Database error: {e}")
        results.append(False)
    
    # Test 3: Required Python modules
    required_modules = ['psutil', 'threading', 'subprocess', 'json', 'datetime']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if not missing_modules:
        print("✅ All required Python modules available")
        results.append(True)
    else:
        print(f"❌ Missing Python modules: {missing_modules}")
        results.append(False)
    
    # Test 4: File permissions
    try:
        # Test if we can create/write config files
        test_config = Path("test_auto_mining_config.json")
        test_config.write_text('{"test": true}')
        test_config.unlink()
        print("✅ File system permissions OK")
        results.append(True)
    except Exception as e:
        print(f"❌ File system permission error: {e}")
        results.append(False)
    
    return results

def main():
    """Main test runner."""
    print("🚀 ONNYX AUTO-MINING SYSTEM COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    # Run all test categories
    manager_results = test_auto_mining_manager()
    dashboard_results = test_dashboard_integration()
    web_results = test_web_interface()
    config_results = test_validator_configuration()
    integration_results = test_system_integration()
    
    # Calculate overall results
    all_results = manager_results + dashboard_results + web_results + config_results + integration_results
    total_tests = len(all_results)
    passed_tests = sum(all_results)
    failed_tests = total_tests - passed_tests
    
    # Generate summary
    print("\n📊 AUTO-MINING SYSTEM TEST SUMMARY")
    print("=" * 40)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed breakdown
    print(f"\nCore Manager: {sum(manager_results)}/{len(manager_results)} passed")
    print(f"Dashboard Integration: {sum(dashboard_results)}/{len(dashboard_results)} passed")
    print(f"Web Interface: {sum(web_results)}/{len(web_results)} passed")
    print(f"Validator Config: {sum(config_results)}/{len(config_results)} passed")
    print(f"System Integration: {sum(integration_results)}/{len(integration_results)} passed")
    
    # Overall assessment
    if passed_tests >= total_tests * 0.85:  # 85% pass rate
        print("\n🎉 AUTO-MINING SYSTEM IMPLEMENTATION SUCCESSFUL!")
        print("✅ Core auto-mining manager functional")
        print("✅ Dashboard integration complete")
        print("✅ Web interface accessible")
        print("✅ Validator configuration working")
        print("✅ System integration verified")
        print("\n🤖 Auto-mining system ready for production use!")
        return True
    else:
        print("\n⚠️  AUTO-MINING SYSTEM NEEDS ATTENTION")
        print("Some critical components may need fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
