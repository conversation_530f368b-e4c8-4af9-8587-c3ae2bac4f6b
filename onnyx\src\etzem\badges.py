# src/etzem/badges.py

from typing import Dict, Any, List, Optional

from src.db.manager import db_manager
from src.identity.registry import IdentityRegistry
from src.etzem.engine import EtzemEngine

class EtzemBadges:
    """
    Manager for Etzem-based badges.
    """
    
    def __init__(self, 
                 identity_registry: Optional[IdentityRegistry] = None,
                 etzem_engine: Optional[EtzemEngine] = None):
        """
        Initialize the EtzemBadges.
        
        Args:
            identity_registry: The identity registry
            etzem_engine: The Etzem engine
        """
        self.identities = identity_registry or IdentityRegistry()
        self.etzem = etzem_engine or EtzemEngine()
        self.db = db_manager.get_connection()
        
        # Define badge thresholds
        self.badge_thresholds = {
            "ETZEM_TRUSTED": 30,
            "ETZEM_REPUTABLE": 50,
            "ETZEM_ESTEEMED": 70,
            "ETZEM_VENERABLE": 90
        }
    
    def update_badges(self, identity_id: str) -> Optional[Dict[str, Any]]:
        """
        Update the Etzem badges for an identity.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The updated identity or None if the identity does not exist
        """
        # Compute the Etzem score
        etzem_data = self.etzem.compute_etzem(identity_id)
        if not etzem_data:
            return None
        
        # Get the identity
        identity = self.identities.get_identity(identity_id)
        if not identity:
            return None
        
        # Get the Etzem score
        etzem_score = etzem_data["etzem"]
        
        # Update badges based on Etzem thresholds
        badges_to_add = []
        badges_to_remove = []
        
        for badge, threshold in self.badge_thresholds.items():
            if etzem_score >= threshold:
                # Add badge if Etzem score is above threshold
                if badge not in identity.get("badges", []):
                    badges_to_add.append(badge)
            else:
                # Remove badge if Etzem score is below threshold
                if badge in identity.get("badges", []):
                    badges_to_remove.append(badge)
        
        # Add badges
        for badge in badges_to_add:
            try:
                self.identities.add_badge(identity_id, badge)
            except Exception:
                # Badge already exists
                pass
        
        # Remove badges
        for badge in badges_to_remove:
            try:
                self.identities.remove_badge(identity_id, badge)
            except Exception:
                # Badge doesn't exist
                pass
        
        # Get the updated identity
        updated_identity = self.identities.get_identity(identity_id)
        
        return updated_identity
    
    def get_badge_holders(self, badge: str) -> List[Dict[str, Any]]:
        """
        Get all identities with a specific badge.
        
        Args:
            badge: The badge name
        
        Returns:
            A list of identities with the badge
        """
        # This is a placeholder - the actual implementation would query the identity registry
        cursor = self.db.cursor()
        cursor.execute(
            """
            SELECT i.* FROM identities i
            JOIN identity_badges b ON i.identity_id = b.identity_id
            WHERE b.badge = ?
            """,
            (badge,)
        )
        rows = cursor.fetchall()
        
        return [{
            "identity_id": row["identity_id"],
            "name": row["name"],
            "public_key": row["public_key"],
            "created_at": row["created_at"],
            "badges": self.identities.get_badges(row["identity_id"])
        } for row in rows]
    
    def get_badge_count(self, badge: str) -> int:
        """
        Get the number of identities with a specific badge.
        
        Args:
            badge: The badge name
        
        Returns:
            The number of identities with the badge
        """
        cursor = self.db.cursor()
        cursor.execute(
            "SELECT COUNT(*) FROM identity_badges WHERE badge = ?",
            (badge,)
        )
        result = cursor.fetchone()
        
        if result is None or result[0] is None:
            return 0
        
        return result[0]
