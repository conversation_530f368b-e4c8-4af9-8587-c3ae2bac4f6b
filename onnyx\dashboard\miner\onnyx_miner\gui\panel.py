"""
Onnyx Miner GUI Panel Module

This module provides the Flask GUI for the Onnyx Miner.
"""

import os
import sys
import logging
import threading
from typing import Dict, Any, List, Optional
from flask import Flask, render_template, redirect, request, url_for, flash, jsonify

from ..config import get_config
from ..wallet import load_wallet, get_balance, transfer_tokens, stake_tokens, get_transaction_history
from ..sync import get_chain_info, get_latest_block, sync_chain, get_peers, add_peer
from ..miner import mine_block, start_auto_mining, stop_auto_mining, get_mining_status
from ..api import register_identity, register_sela, link_identity_to_sela, create_token, propose_scroll, vote_on_scroll, get_scrolls

# Set up logging
logger = logging.getLogger("onnyx_miner.gui.panel")

# Create the Flask app
app = Flask(__name__)
app.secret_key = os.urandom(24)

@app.route("/")
def home():
    """Home page."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        identity_name = config.get("identity", {}).get("name")
        
        # Get the Sela ID
        sela_id = config.get("sela", {}).get("id")
        sela_name = config.get("sela", {}).get("name")
        
        # Get the chain info
        chain_info = get_chain_info()
        
        # Get the mining status
        mining_status = get_mining_status()
        
        return render_template(
            "index.html",
            identity_id=identity_id,
            identity_name=identity_name,
            sela_id=sela_id,
            sela_name=sela_name,
            chain_info=chain_info,
            mining_status=mining_status
        )
    except Exception as e:
        logger.error(f"Error in home: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return render_template("index.html")

@app.route("/wallet")
def wallet():
    """Wallet page."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            flash("No identity ID configured", "error")
            return redirect(url_for("home"))
        
        # Get the wallet
        wallet_data = load_wallet(identity_id)
        
        # Get the transaction history
        transactions = get_transaction_history(identity_id)
        
        return render_template(
            "wallet.html",
            wallet=wallet_data,
            transactions=transactions
        )
    except Exception as e:
        logger.error(f"Error in wallet: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

@app.route("/mine", methods=["POST"])
def mine():
    """Mine a block."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            flash("No identity ID configured", "error")
            return redirect(url_for("home"))
        
        # Mine a block
        block = mine_block(identity_id)
        
        if block:
            flash(f"Mined block {block.get('index')}", "success")
        else:
            flash("Failed to mine block", "error")
        
        return redirect(url_for("home"))
    except Exception as e:
        logger.error(f"Error in mine: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

@app.route("/auto-mine", methods=["POST"])
def auto_mine():
    """Enable or disable automatic mining."""
    try:
        # Get the enable parameter
        enable = request.form.get("enable") == "true"
        
        if enable:
            start_auto_mining()
            flash("Enabled automatic mining", "success")
        else:
            stop_auto_mining()
            flash("Disabled automatic mining", "success")
        
        return redirect(url_for("home"))
    except Exception as e:
        logger.error(f"Error in auto_mine: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

@app.route("/sync", methods=["POST"])
def sync():
    """Sync the chain."""
    try:
        # Sync the chain
        success = sync_chain()
        
        if success:
            flash("Synced chain with network", "success")
        else:
            flash("Failed to sync chain with network", "error")
        
        return redirect(url_for("home"))
    except Exception as e:
        logger.error(f"Error in sync: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

@app.route("/transfer", methods=["POST"])
def transfer():
    """Transfer tokens."""
    try:
        # Get the parameters
        to_id = request.form.get("to_id")
        token_id = request.form.get("token_id")
        amount = float(request.form.get("amount"))
        
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            flash("No identity ID configured", "error")
            return redirect(url_for("wallet"))
        
        # Transfer the tokens
        success = transfer_tokens(identity_id, to_id, token_id, amount)
        
        if success:
            flash(f"Transferred {amount} {token_id} to {to_id}", "success")
        else:
            flash("Failed to transfer tokens", "error")
        
        return redirect(url_for("wallet"))
    except Exception as e:
        logger.error(f"Error in transfer: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("wallet"))

@app.route("/stake", methods=["POST"])
def stake():
    """Stake tokens."""
    try:
        # Get the parameters
        token_id = request.form.get("token_id")
        amount = float(request.form.get("amount"))
        duration = int(request.form.get("duration"))
        
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            flash("No identity ID configured", "error")
            return redirect(url_for("wallet"))
        
        # Stake the tokens
        success = stake_tokens(identity_id, token_id, amount, duration)
        
        if success:
            flash(f"Staked {amount} {token_id} for {duration} seconds", "success")
        else:
            flash("Failed to stake tokens", "error")
        
        return redirect(url_for("wallet"))
    except Exception as e:
        logger.error(f"Error in stake: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("wallet"))

@app.route("/governance")
def governance():
    """Governance page."""
    try:
        # Get the scrolls
        scrolls = get_scrolls()
        
        return render_template(
            "governance.html",
            scrolls=scrolls
        )
    except Exception as e:
        logger.error(f"Error in governance: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

@app.route("/propose", methods=["POST"])
def propose():
    """Propose a scroll."""
    try:
        # Get the parameters
        title = request.form.get("title")
        description = request.form.get("description")
        category = request.form.get("category")
        param = request.form.get("param")
        value = request.form.get("value")
        
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            flash("No identity ID configured", "error")
            return redirect(url_for("governance"))
        
        # Create the effect
        effect = None
        
        if param and value:
            effect = {
                "param": param,
                "value": value
            }
        
        # Propose the scroll
        scroll = propose_scroll(identity_id, title, description, category, effect)
        
        flash(f"Proposed scroll: {scroll.get('id')}", "success")
        
        return redirect(url_for("governance"))
    except Exception as e:
        logger.error(f"Error in propose: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("governance"))

@app.route("/vote", methods=["POST"])
def vote():
    """Vote on a scroll."""
    try:
        # Get the parameters
        scroll_id = request.form.get("scroll_id")
        decision = request.form.get("decision")
        
        # Get the configuration
        config = get_config()
        
        # Get the identity ID
        identity_id = config.get("identity", {}).get("id")
        
        if not identity_id:
            flash("No identity ID configured", "error")
            return redirect(url_for("governance"))
        
        # Vote on the scroll
        scroll = vote_on_scroll(identity_id, scroll_id, decision)
        
        flash(f"Voted {decision} on scroll {scroll_id}", "success")
        
        return redirect(url_for("governance"))
    except Exception as e:
        logger.error(f"Error in vote: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("governance"))

@app.route("/logs")
def logs():
    """Logs page."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the log file path
        log_file = "onnyx_miner.log"
        
        # Read the log file
        log_content = ""
        
        if os.path.exists(log_file):
            with open(log_file, "r") as f:
                log_content = f.read()
        
        return render_template(
            "logs.html",
            log_content=log_content
        )
    except Exception as e:
        logger.error(f"Error in logs: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

@app.route("/sela")
def sela():
    """Sela page."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the Sela ID
        sela_id = config.get("sela", {}).get("id")
        
        if not sela_id:
            flash("No Sela ID configured", "error")
            return redirect(url_for("home"))
        
        # Get the API URL
        api_url = config.get("node", {}).get("api_url", "http://localhost:8000")
        
        # Get the Sela
        import requests
        sela_url = f"{api_url}/api/sela/{sela_id}"
        sela_response = requests.get(sela_url)
        sela_response.raise_for_status()
        
        sela_data = sela_response.json().get("sela", {})
        
        return render_template(
            "sela.html",
            sela=sela_data
        )
    except Exception as e:
        logger.error(f"Error in sela: {str(e)}")
        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("home"))

def start_gui():
    """Start the GUI."""
    try:
        # Get the configuration
        config = get_config()
        
        # Get the GUI configuration
        gui_config = config.get("gui", {})
        host = gui_config.get("host", "127.0.0.1")
        port = gui_config.get("port", 5005)
        
        # Start the Flask app in a separate thread
        threading.Thread(target=lambda: app.run(host=host, port=port, debug=False)).start()
        
        logger.info(f"Started GUI at http://{host}:{port}")
    except Exception as e:
        logger.error(f"Error starting GUI: {str(e)}")
        raise
