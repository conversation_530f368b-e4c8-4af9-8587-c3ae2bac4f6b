# src/identity/identity.py

import time
import hashlib
import json

class Identity:
    def __init__(self, name, public_key, metadata=None, identity_id=None):
        self.name = name
        self.public_key = public_key
        self.metadata = metadata or {}
        self.created_at = int(time.time())
        self.identity_id = identity_id or self.generate_id()
        self.reputation = {
            "score": 0,
            "transactions": 0,
            "minted_tokens": 0,
            "followers": 0
        }
        self.tokens = []

    def generate_id(self):
        return hashlib.sha256(f"{self.name}{self.public_key}{self.created_at}".encode()).hexdigest()

    def to_dict(self):
        return {
            "identity_id": self.identity_id,
            "name": self.name,
            "public_key": self.public_key,
            "metadata": self.metadata,
            "created_at": self.created_at,
            "reputation": self.reputation,
            "tokens": self.tokens
        }

    @staticmethod
    def from_dict(data):
        identity = Identity(
            name=data["name"],
            public_key=data["public_key"],
            metadata=data.get("metadata", {}),
            identity_id=data["identity_id"]
        )
        identity.reputation = data.get("reputation", {})
        identity.tokens = data.get("tokens", [])
        identity.created_at = data.get("created_at", int(time.time()))
        return identity
